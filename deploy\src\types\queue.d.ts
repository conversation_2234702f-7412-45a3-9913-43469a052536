/**
 * Comprehensive TypeScript interfaces for the voice processing job queue system
 * 
 * These interfaces ensure type safety across the entire pipeline including
 * web app, Azure Functions, and job persistence layer.
 */

// ============================================
// JOB STATUS AND STATES
// ============================================

export type JobStatus = 
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'retrying'
  | 'cancelled'
  | 'timeout';

export type JobType = 
  | 'transcription'
  | 'summarization'
  | 'batch_transcription'
  | 'batch_summarization'
  | 'full_processing';

/**
 * Processing job interface for unified job tracking
 */
export interface ProcessingJob {
  id: string;
  filename: string;
  containerPath: string;
  status: JobStatus;
  retryCount: number;
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  completedAt?: string;
  errorDetails?: JobErrorDetails;
  results?: JobProcessingResults;
  queueMessage?: QueueMessage;
}

/**
 * Job filtering interface for querying jobs
 */
export interface JobFilters {
  status?: JobStatus[];
  createdAfter?: string;
  createdBefore?: string;
  limit?: number;
  filename?: string;
  containerPath?: string;
}

/**
 * Standardized job result interface
 */
export interface JobResult {
  success: boolean;
  jobId?: string;
  error?: string;
  data?: any;
}

/**
 * Queue message interface for Azure Queue Storage integration
 */
export interface QueueMessage {
  jobId: string;
  type: JobType;
  filename: string;
  containerPath: string;
  retryCount: number;
  priority?: number;
  metadata?: {
    userId?: string;
    sessionId?: string;
    source?: string;
  };
}

/**
 * Enhanced error details for job failures
 */
export interface JobErrorDetails {
  code: string;
  message: string;
  category: 'api_error' | 'network_error' | 'file_not_found' | 'quota_exceeded' | 'validation_error' | 'system_error';
  retryable: boolean;
  retryAfter?: number;
  apiResponse?: any;
  stackTrace?: string;
  context?: {
    operation?: string;
    stage?: string;
    attempts?: number;
  };
  recovery?: {
    recoveredAt: string;
    reason: string;
    previousStatus: JobStatus;
  };
}

/**
 * Job processing results interface
 */
export interface JobProcessingResults {
  transcription?: {
    text: string;
    confidence?: number;
    language?: string;
    duration?: number;
    wordCount?: number;
    processingTimeMs?: number;
  };
  summarization?: {
    summary: string;
    keyPoints?: string[];
    confidence?: number;
    model?: string;
    processingTimeMs?: number;
    tokenUsage?: {
      input: number;
      output: number;
    };
  };
  fileInfo?: {
    sizeBytes: number;
    mimeType: string;
    duration?: number;
    bitrate?: number;
  };
}

export type ErrorType = 
  | 'api_error'
  | 'network_error'
  | 'file_not_found'
  | 'file_corrupted'
  | 'quota_exceeded'
  | 'timeout_error'
  | 'validation_error'
  | 'configuration_error'
  | 'unknown_error';

export type ProcessingStage = 
  | 'queued'
  | 'downloading'
  | 'validating'
  | 'transcribing'
  | 'summarizing'
  | 'uploading'
  | 'finalizing';

// ============================================
// CORE JOB INTERFACES
// ============================================

export interface BaseJob {
  id: string;
  type: JobType;
  status: JobStatus;
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  completedAt?: string;
  retryCount: number;
  maxRetries: number;
  priority: number;
  timeoutMs: number;
  metadata: {
    userId?: string;
    practiceId?: string;
    patientId?: string;
    appointmentId?: string;
    source: string; // 'web_upload', 'usb_transfer', 'batch_import', etc.
  };
}

export interface TranscriptionJob extends BaseJob {
  type: 'transcription';
  input: {
    filename: string;
    container: string;
    blobName: string;
    fileSize: number;
    mimeType: string;
    duration?: number;
  };
  options: {
    model: string;
    language?: string;
    prompt?: string;
    temperature?: number;
    responseFormat: 'json' | 'text' | 'srt' | 'verbose_json' | 'vtt';
    timestampGranularities?: ('word' | 'segment')[];
  };
  result?: TranscriptionResult;
}

export interface SummarizationJob extends BaseJob {
  type: 'summarization';
  input: {
    transcriptionText: string;
    transcriptionJobId?: string;
    filename: string;
    container: string;
  };
  options: {
    model: string;
    maxTokens: number;
    temperature: number;
    summaryType: 'clinical' | 'brief' | 'detailed' | 'soap';
    includeTimestamps: boolean;
    patientContext?: {
      name?: string;
      age?: number;
      medicalHistory?: string[];
    };
  };
  result?: SummarizationResult;
}

export interface BatchJob extends BaseJob {
  type: 'batch_transcription' | 'batch_summarization' | 'full_processing';
  input: {
    files: {
      filename: string;
      container: string;
      blobName: string;
      fileSize: number;
      mimeType: string;
    }[];
    batchSize: number;
  };
  progress: {
    total: number;
    completed: number;
    failed: number;
    current?: string;
    stage: ProcessingStage;
  };
  childJobs: string[]; // Array of child job IDs
  results?: BatchResult;
}

// ============================================
// RESULT INTERFACES
// ============================================

export interface TranscriptionResult {
  text: string;
  duration: number;
  language?: string;
  segments?: TranscriptionSegment[];
  words?: TranscriptionWord[];
  confidence?: number;
  processingTime: number;
  modelUsed: string;
  tokenCount?: number;
}

export interface TranscriptionSegment {
  id: number;
  start: number;
  end: number;
  text: string;
  confidence?: number;
  words?: TranscriptionWord[];
}

export interface TranscriptionWord {
  start: number;
  end: number;
  word: string;
  confidence?: number;
}

export interface SummarizationResult {
  summary: string;
  clinicalNotes?: {
    chiefComplaint?: string;
    assessment?: string;
    plan?: string;
    medications?: string[];
    procedures?: string[];
  };
  keyPoints: string[];
  confidence: number;
  processingTime: number;
  modelUsed: string;
  tokenCount: {
    input: number;
    output: number;
  };
}

export interface BatchResult {
  summary: {
    totalFiles: number;
    successfulFiles: number;
    failedFiles: number;
    totalProcessingTime: number;
    averageProcessingTime: number;
  };
  files: {
    filename: string;
    status: JobStatus;
    transcription?: TranscriptionResult;
    summarization?: SummarizationResult;
    error?: JobError;
  }[];
}

// ============================================
// ERROR AND DIAGNOSTICS
// ============================================

export interface JobError {
  type: ErrorType;
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  stage: ProcessingStage;
  retryable: boolean;
  context?: {
    filename?: string;
    jobId?: string;
    apiResponse?: any;
    stackTrace?: string;
  };
}

export interface JobDiagnostics {
  performance: {
    queueTime: number;
    processingTime: number;
    totalTime: number;
  };
  resources: {
    memoryUsed?: number;
    cpuTime?: number;
    apiCalls: number;
    bytesProcessed: number;
  };
  api: {
    provider: 'openai' | 'azure_speech';
    model: string;
    requestId?: string;
    rateLimitRemaining?: number;
  };
}

// ============================================
// QUEUE MANAGEMENT
// ============================================

export interface QueueStatus {
  depth: number;
  processing: number;
  failed: number;
  retrying: number;
  estimatedWaitTime: number;
  throughput: {
    jobsPerMinute: number;
    averageProcessingTime: number;
  };
}

export interface QueueMetrics {
  timestamp: string;
  jobs: {
    total: number;
    byStatus: Record<JobStatus, number>;
    byType: Record<JobType, number>;
  };
  performance: {
    averageQueueTime: number;
    averageProcessingTime: number;
    successRate: number;
    errorRate: number;
  };
  resources: {
    activeWorkers: number;
    memoryUsage: number;
    cpuUsage: number;
  };
}

// ============================================
// API REQUEST/RESPONSE TYPES
// ============================================

export interface CreateJobRequest {
  type: JobType;
  input: any;
  options?: any;
  priority?: number;
  metadata?: any;
}

export interface CreateJobResponse {
  jobId: string;
  status: JobStatus;
  estimatedDuration: number;
  queuePosition: number;
}

export interface JobStatusResponse {
  job: BaseJob;
  progress?: {
    percentage: number;
    stage: ProcessingStage;
    currentOperation?: string;
    estimatedTimeRemaining?: number;
  };
  result?: any;
  error?: JobError;
  diagnostics?: JobDiagnostics;
}

export interface BatchJobStatusResponse {
  job: BatchJob;
  childJobs: JobStatusResponse[];
  overallProgress: {
    percentage: number;
    stage: ProcessingStage;
    estimatedTimeRemaining: number;
  };
}

export interface QueueStatusResponse {
  queue: QueueStatus;
  recentJobs: BaseJob[];
  metrics: QueueMetrics;
  health: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    issues: string[];
    uptime: number;
  };
}

// ============================================
// PERSISTENCE LAYER TYPES
// ============================================

export interface JobPersistenceConfig {
  provider: 'azure_table' | 'sqlite' | 'memory';
  connectionString?: string;
  tableName?: string;
  databasePath?: string;
  retentionDays: number;
  cleanupInterval: number;
}

export interface JobQuery {
  status?: JobStatus[];
  type?: JobType[];
  createdAfter?: string;
  createdBefore?: string;
  metadata?: Record<string, any>;
  limit?: number;
  offset?: number;
  sortBy?: 'createdAt' | 'updatedAt' | 'priority';
  sortOrder?: 'asc' | 'desc';
}

export interface JobQueryResult {
  jobs: BaseJob[];
  total: number;
  hasMore: boolean;
}

// ============================================
// UTILITY TYPES
// ============================================

export type JobUpdate = Partial<Pick<BaseJob, 'status' | 'metadata'>> & {
  result?: any;
  error?: JobError;
  diagnostics?: JobDiagnostics;
  progress?: any;
};

export type JobEventType = 
  | 'job_created'
  | 'job_started'
  | 'job_progress'
  | 'job_completed'
  | 'job_failed'
  | 'job_retrying'
  | 'job_cancelled';

export interface JobEvent {
  type: JobEventType;
  jobId: string;
  timestamp: string;
  data: any;
}

// ============================================
// CONFIGURATION TYPES
// ============================================

export interface ProcessingOptions {
  transcription: {
    defaultModel: string;
    chunkSizeLimit: number;
    supportedFormats: string[];
    maxFileSizeMB: number;
    timeoutMs: number;
  };
  summarization: {
    defaultModel: string;
    maxInputTokens: number;
    maxOutputTokens: number;
    timeoutMs: number;
  };
  batch: {
    maxBatchSize: number;
    concurrency: number;
    retryPolicy: {
      maxAttempts: number;
      backoffMultiplier: number;
      maxBackoffMs: number;
    };
  };
}

export interface WorkerConfig {
  maxConcurrentJobs: number;
  pollIntervalMs: number;
  healthCheckIntervalMs: number;
  gracefulShutdownTimeoutMs: number;
  memoryLimitMB: number;
  enableMetrics: boolean;
}