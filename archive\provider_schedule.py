import requests
import json
from datetime import datetime, timedelta
from collections import defaultdict
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# Set to True to enable debug output
DEBUG = False

def get_request_key(office_id, secret_key, app_id, app_key):
    """Obtain a request key for API authentication."""
    auth_resp = requests.post(
        f"{API_BASE}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": office_id,
            "secret_key": secret_key,
            "app_id": app_id,
            "app_key": app_key
        }
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None

    data = auth_resp.json()
    return data.get("request_key")

def fetch_transactions(request_key, date, provider_id=None):
    """Fetch transactions for a specific date and optionally filter by provider."""
    headers = {"Request-Key": request_key}
    params = {"date": date}

    # Fetch all pages to make sure we get all transactions
    all_items = []
    offset = 0
    limit = 50

    while True:
        paged_params = params.copy()
        paged_params.update({"offset": offset, "limit": limit})

        resp = requests.get(f"{API_BASE}/transactions", headers=headers, params=paged_params)

        if resp.status_code != 200:
            print(f"Error: {resp.status_code}")
            print(resp.text)
            break

        data = resp.json()
        items = data.get("items", [])
        all_items.extend(items)

        if len(items) < limit:
            break

        offset += limit

    # Print transaction data for debugging
    if DEBUG:
        print("\nTransaction data sample:")
        for i, item in enumerate(all_items[:2]):  # Show first 2 transactions
            print(f"Transaction {i+1}:")
            for key, value in item.items():
                print(f"  {key}: {value}")
            print()

    # Filter by provider if specified
    if provider_id:
        all_items = [t for t in all_items if t.get("provider_id") == provider_id]

    # Fetch patient names for transactions that don't have them
    patient_cache = {}
    for item in all_items:
        patient_id = item.get("patient_id")
        if patient_id and not item.get("patient_name"):
            if patient_id in patient_cache:
                item["patient_name"] = patient_cache[patient_id]
            else:
                # Try to find patient name from appointments
                patient_name = get_patient_name_from_id(request_key, patient_id)
                if patient_name:
                    item["patient_name"] = patient_name
                    patient_cache[patient_id] = patient_name

    return all_items

def get_patient_name_from_id(request_key, patient_id):
    """Get patient name from patient ID by querying the patients endpoint."""
    headers = {"Request-Key": request_key}

    try:
        # Try to get patient details
        resp = requests.get(f"{API_BASE}/patients/{patient_id}", headers=headers)

        if resp.status_code == 200:
            data = resp.json()
            first_name = data.get("first_name", "")
            last_name = data.get("last_name", "")
            return f"{last_name} {first_name}".strip()
    except Exception as e:
        print(f"Error fetching patient name: {e}")

    return None

def fetch_appointments(request_key, date, provider_id=None):
    """Fetch appointments for a specific date and optionally filter by provider."""
    headers = {"Request-Key": request_key}
    params = {"date": date}

    # Fetch all pages to make sure we get all appointments
    all_items = []
    offset = 0
    limit = 50

    while True:
        paged_params = params.copy()
        paged_params.update({"offset": offset, "limit": limit})

        resp = requests.get(f"{API_BASE}/appointments", headers=headers, params=paged_params)

        if resp.status_code != 200:
            print(f"Error: {resp.status_code}")
            print(resp.text)
            break

        data = resp.json()
        items = data.get("items", [])
        all_items.extend(items)

        if len(items) < limit:
            break

        offset += limit

    # Print appointment data for debugging
    if DEBUG:
        print("\nAppointment data sample:")
        for i, item in enumerate(all_items[:2]):  # Show first 2 appointments
            print(f"Appointment {i+1}:")
            for key, value in item.items():
                print(f"  {key}: {value}")
            print()

    # Check if we need to look at future appointments
    # Some appointments might be scheduled for future dates but made on our target date
    has_future_appts = any(a.get("appointment_made_date", "").startswith(date) for a in all_items)
    if has_future_appts and DEBUG:
        print(f"Found appointments made on {date} for future dates")

    # Filter by provider if specified
    if provider_id:
        all_items = [a for a in all_items if a.get("provider_id") == provider_id]

    # Filter to only include appointments for the specified date
    all_items = [a for a in all_items if a.get("date", "").startswith(date)]

    return all_items

def create_provider_schedule(transactions, appointments, provider_id, date_str):
    """Create a visual schedule for a specific provider using 10-minute increments."""
    # Filter for procedure transactions by the specified provider
    provider_procedures = [t for t in transactions
                          if t.get("transaction_type") == "Procedure"
                          and t.get("provider_id") == provider_id
                          and float(t.get("amount", "0")) > 0]

    # Create a dictionary of procedures by patient ID and name
    procedures_by_patient = {}
    for proc in provider_procedures:
        patient_id = proc.get("patient_id")
        patient_name = proc.get("patient_name", "Unknown Patient")

        if patient_id not in procedures_by_patient:
            procedures_by_patient[patient_id] = {
                "name": patient_name,
                "procedures": []
            }
        procedures_by_patient[patient_id]["procedures"].append(proc)

    # Print patient matching info for debugging
    if DEBUG:
        print("\nPatients with procedures:")
        for patient_id, data in procedures_by_patient.items():
            print(f"  Patient ID: {patient_id}, Name: {data['name']}")

    # Get patient IDs from procedures
    procedure_patient_ids = set(procedures_by_patient.keys())

    # Filter appointments for the specified provider
    provider_appointments = [a for a in appointments
                            if a.get("provider_id") == provider_id]

    # Create a dictionary of appointments by time
    appt_by_time = {}
    for appt in provider_appointments:
        time_str = appt.get("time", "")
        if time_str:
            # Convert from "HH:MM" to datetime object for proper sorting
            try:
                time_obj = datetime.strptime(time_str, "%H:%M")
                appt_by_time[time_str] = appt
            except ValueError:
                # Skip appointments with invalid time format
                continue

    # Format date for display
    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
    formatted_date = date_obj.strftime("%A, %B %d, %Y")

    # Display the schedule
    print(f"\n{'=' * 80}")
    print(f"{'DAILY SCHEDULE FOR DR. ' + provider_id:^80}")
    print(f"{formatted_date:^80}")
    print(f"{'=' * 80}")

    # Create time slots from 8:00 to 17:00 in 10-minute increments
    start_time = datetime.strptime("08:00", "%H:%M")
    end_time = datetime.strptime("17:00", "%H:%M")
    current_time = start_time

    active_appointments = []  # Track appointments that span multiple time slots

    # Print header
    print(f"\n{'TIME':<8}{'PATIENT':<25}{'PROCEDURE':<35}{'LENGTH':<12}{'STATUS'}")
    print(f"{'-' * 8}{'-' * 25}{'-' * 35}{'-' * 12}{'-' * 10}")

    while current_time <= end_time:
        time_str = current_time.strftime("%H:%M")

        # Check if any appointment starts at this time
        new_appt_started = False
        if time_str in appt_by_time:
            new_appt_started = True
            appt = appt_by_time[time_str]
            length_min = float(appt.get("length", "0"))
            end_time_obj = current_time + timedelta(minutes=length_min)

            patient_id = appt.get("patient_id", "")
            patient_name = appt.get("patient_name", "Unknown Patient")
            description = appt.get("description", "No description")

            # Check if this patient had procedures done by this provider
            has_procedures = patient_id in procedure_patient_ids

            # Add to active appointments
            active_appointments.append({
                "appt": appt,
                "end_time": end_time_obj,
                "has_procedures": has_procedures,
                "start_time": current_time  # Store start time for duration calculation
            })

        # Update and display active appointments
        new_active = []
        for appt_info in active_appointments:
            if appt_info["end_time"] > current_time:
                appt = appt_info["appt"]
                patient_name = appt.get("patient_name", "Unknown Patient")
                description = appt.get("description", "No description")
                length_min = float(appt.get("length", "0"))

                # Calculate how long the appointment has been active
                elapsed = (current_time - appt_info["start_time"]).total_seconds() / 60
                remaining = length_min - elapsed

                # Only display the appointment info when it starts or every 30 minutes
                is_start = elapsed < 10  # First time slot of appointment
                is_30min_mark = elapsed > 0 and elapsed % 30 < 10  # Every 30 minutes

                if is_start or is_30min_mark or new_appt_started:
                    # Mark appointments with procedures
                    status = "COMPLETED" if appt_info["has_procedures"] else "SCHEDULED"

                    # Truncate long descriptions
                    short_desc = description
                    if len(short_desc) > 32:
                        short_desc = short_desc[:29] + "..."

                    # Format patient name
                    short_name = patient_name
                    if len(short_name) > 22:
                        short_name = short_name[:19] + "..."

                    # Display the appointment
                    if is_start:
                        # Show full details at start
                        print(f"{time_str:<8}{short_name:<25}{short_desc:<35}{int(length_min):<12}{status}")
                    else:
                        # Show continuation marker for ongoing appointments
                        print(f"{time_str:<8}{'└─ ' + short_name:<25}{'(continued)':<35}{f'{int(remaining)}m left':<12}{status}")

                new_active.append(appt_info)

        # Only show empty slots at hour and half-hour marks
        if not new_active and (time_str.endswith(":00") or time_str.endswith(":30")):
            print(f"{time_str:<8}{'[OPEN]':<25}{'':<35}{'':<12}{''}")

        active_appointments = new_active

        # Move to next time slot (10 minutes)
        current_time += timedelta(minutes=10)

    print("\n" + "=" * 80)

    # Also display a summary of procedures completed
    print(f"\n{'=' * 80}")
    print(f"{'PROCEDURES COMPLETED BY DR. ' + provider_id:^80}")
    print(f"{formatted_date:^80}")
    print(f"{'=' * 80}")

    # Calculate total production
    total_production = 0
    patient_count = 0

    # Display procedures by patient using our procedures_by_patient dictionary
    for patient_id, data in procedures_by_patient.items():
        patient_name = data["name"]
        procs = data["procedures"]
        patient_count += 1

        print(f"\n{patient_count}. {patient_name}")
        print("-" * 80)

        # Find matching appointment for this patient
        matching_appt = None
        for appt in provider_appointments:
            if appt.get("patient_id") == patient_id:
                matching_appt = appt
                break

        if matching_appt:
            appt_time = matching_appt.get("time", "")
            appt_desc = matching_appt.get("description", "")
            print(f"Appointment: {appt_time} - {appt_desc}")
        else:
            print("Note: No matching appointment found for this patient on this date")

        print("-" * 80)
        print(f"{'CODE':<10}{'DESCRIPTION':<40}{'TOOTH':<10}{'SURFACE':<10}{'AMOUNT':<10}")
        print(f"{'-' * 10}{'-' * 40}{'-' * 10}{'-' * 10}{'-' * 10}")

        # Display procedures
        patient_total = 0
        for proc in procs:
            code = proc.get("procedure_code", "")
            desc = proc.get("procedure_description", "")
            amount = float(proc.get("amount", "0"))
            tooth = proc.get("tooth_from", "") or "-"
            surface = proc.get("surface", "") or "-"

            # Truncate long descriptions
            if len(desc) > 37:
                desc = desc[:34] + "..."

            print(f"{code:<10}{desc:<40}{tooth:<10}{surface:<10}${amount:<9.2f}")
            patient_total += amount
            total_production += amount

        print(f"{'':<60}{'TOTAL:':<10}${patient_total:<9.2f}")

    # Print summary
    if patient_count == 0:
        print("\nNo procedures completed by this provider on this date.")
    else:
        print(f"\n{'=' * 80}")
        print(f"{'SUMMARY':^80}")
        print(f"{'=' * 80}")
        print(f"Total patients with completed procedures: {patient_count}")
        print(f"Total production: ${total_production:.2f}")

def main():
    """Main function to run the provider schedule script."""
    # Check command line arguments
    if len(sys.argv) < 4:
        print("Usage: python provider_schedule.py <date> <provider_id> <office_id> <secret_key> <app_id> <app_key>")
        print("Example: python provider_schedule.py 2023-05-16 LL01 D43989 35442814D4396E20C222 fdd52aaffb0c1bead647874ba551db0c 88254bfa2224607ef425646aafe5f722")
        return

    # Parse command line arguments
    target_date = sys.argv[1]
    target_provider = sys.argv[2]

    # API credentials
    if len(sys.argv) >= 7:
        # Use credentials from command line
        office_id = sys.argv[3]
        secret_key = sys.argv[4]
        app_id = sys.argv[5]
        app_key = sys.argv[6]
    else:
        # Use default credentials
        office_id = "D43989"
        secret_key = "35442814D4396E20C222"
        app_id = "fdd52aaffb0c1bead647874ba551db0c"
        app_key = "88254bfa2224607ef425646aafe5f722"

    # Validate date format
    try:
        datetime.strptime(target_date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        return

    # Get request key
    print("Getting request key...")
    request_key = get_request_key(office_id, secret_key, app_id, app_key)
    if not request_key:
        print("Failed to get request key. Exiting.")
        return

    if DEBUG:
        print(f"Request key obtained: {request_key}")
    else:
        print("Request key obtained successfully")

    # Fetch transactions directly filtered by provider
    print(f"Fetching transactions for provider {target_provider} on {target_date}...")
    transactions = fetch_transactions(request_key, target_date, target_provider)
    print(f"Found {len(transactions)} transactions")

    # Fetch appointments directly filtered by provider
    print(f"Fetching appointments for provider {target_provider} on {target_date}...")
    appointments = fetch_appointments(request_key, target_date, target_provider)
    print(f"Found {len(appointments)} appointments")

    # Create provider schedule
    create_provider_schedule(transactions, appointments, target_provider, target_date)

if __name__ == "__main__":
    main()
