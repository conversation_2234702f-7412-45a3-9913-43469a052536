#!/usr/bin/env python3
"""
Test script to find the best way to fetch clinical notes from Sikka API.
This script tries different endpoints and parameters to find what works best.
"""

import json
import requests
import sys
from datetime import datetime

# API configuration
API_BASE = "https://api.sikkasoft.com/v1"
API_BASE_V2 = "https://api.sikkasoft.com/v2"
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_TIMEOUT = 30  # seconds

def load_credentials():
    """Load API credentials from credentials.json file."""
    try:
        with open("credentials.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: credentials.json file not found.")
        sys.exit(1)
    except json.JSONDecodeError:
        print("Error: Invalid JSON in credentials.json file.")
        sys.exit(1)

def authenticate(app_id, app_key, office_id, secret_key):
    """Authenticate with Sikka API and get request key."""
    print("Authenticating with Sikka API...")
    
    try:
        auth_resp = requests.post(
            f"{API_BASE_V4}/request_key",
            headers={"Content-Type": "application/json"},
            json={
                "grant_type": "request_key",
                "office_id": office_id,
                "secret_key": secret_key,
                "app_id": app_id,
                "app_key": app_key
            },
            timeout=API_TIMEOUT
        )
        
        if auth_resp.status_code == 200:
            data = auth_resp.json()
            request_key = data.get("request_key")
            if request_key:
                print("Authentication successful!")
                return request_key
            else:
                print("Error: No request key in response.")
                print(data)
        else:
            print(f"Authentication failed with status code: {auth_resp.status_code}")
            print(auth_resp.text)
    except Exception as e:
        print(f"Error during authentication: {e}")
    
    sys.exit(1)

def test_notes_endpoints(request_key, target_date):
    """Test different endpoints and parameters to find clinical notes."""
    headers = {"Request-Key": request_key}
    
    # Test different endpoints that might contain clinical notes
    endpoints = [
        (f"{API_BASE_V4}/medical_notes", "v4 medical_notes"),
        (f"{API_BASE_V2}/medical_notes", "v2 medical_notes"),
        (f"{API_BASE_V4}/clinical_notes", "v4 clinical_notes"),
        (f"{API_BASE_V2}/clinical_notes", "v2 clinical_notes"),
        (f"{API_BASE_V4}/notes", "v4 notes"),
        (f"{API_BASE_V2}/notes", "v2 notes"),
        (f"{API_BASE_V4}/patient_notes", "v4 patient_notes"),
        (f"{API_BASE_V2}/patient_notes", "v2 patient_notes")
    ]
    
    # Test different parameter combinations
    param_sets = [
        {"date": target_date},
        {"startdate": target_date, "enddate": target_date},
        {"date": target_date, "type": "clinical"},
        {"date": target_date, "note_type": "clinical"}
    ]
    
    for endpoint, name in endpoints:
        print(f"\nTesting {name} endpoint: {endpoint}")
        
        for params in param_sets:
            print(f"  With parameters: {params}")
            
            try:
                resp = requests.get(
                    endpoint,
                    headers=headers,
                    params=params,
                    timeout=API_TIMEOUT
                )
                
                print(f"  Status code: {resp.status_code}")
                
                if resp.status_code == 200:
                    try:
                        data = resp.json()
                        
                        # Check if response is a list or has 'items' key
                        if isinstance(data, list):
                            items = data
                            if len(data) > 0 and isinstance(data[0], dict) and "items" in data[0]:
                                items = data[0].get("items", [])
                        else:
                            items = data.get("items", [])
                        
                        print(f"  Found {len(items)} items")
                        
                        # Check for notes in the first few items
                        if items:
                            print("  Sample items:")
                            for i, item in enumerate(items[:3]):
                                print(f"    Item {i+1}:")
                                
                                # Look for note content fields
                                note_content = None
                                for key in ["note", "content", "text", "description"]:
                                    if key in item and item[key]:
                                        note_content = item[key]
                                        break
                                
                                if note_content:
                                    # Truncate long notes
                                    if len(note_content) > 100:
                                        note_content = note_content[:100] + "..."
                                    print(f"      Note content: {note_content}")
                                
                                # Look for patient fields
                                patient_id = None
                                for key in ["patient_id", "patientid"]:
                                    if key in item:
                                        patient_id = item[key]
                                        break
                                
                                if patient_id:
                                    print(f"      Patient ID: {patient_id}")
                                
                                # Look for date fields
                                note_date = None
                                for key in ["date", "note_date", "created_date"]:
                                    if key in item:
                                        note_date = item[key]
                                        break
                                
                                if note_date:
                                    print(f"      Date: {note_date}")
                                
                                # Print all keys for debugging
                                print(f"      All keys: {list(item.keys())}")
                    except json.JSONDecodeError:
                        print("  Response is not valid JSON")
                        print(f"  Raw response: {resp.text[:200]}...")
                else:
                    print(f"  Error response: {resp.text[:200]}...")
            except Exception as e:
                print(f"  Error: {e}")

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python test_clinical_notes.py YYYY-MM-DD")
        sys.exit(1)
    
    target_date = sys.argv[1]
    
    # Validate date format
    try:
        datetime.strptime(target_date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        sys.exit(1)
    
    # Load credentials
    credentials = load_credentials()
    app_id = credentials.get("app_id")
    app_key = credentials.get("app_key")
    office_id = credentials.get("office_id")
    secret_key = credentials.get("secret_key")
    
    # Authenticate and get request key
    request_key = authenticate(app_id, app_key, office_id, secret_key)
    
    # Test notes endpoints
    test_notes_endpoints(request_key, target_date)

if __name__ == "__main__":
    main()
