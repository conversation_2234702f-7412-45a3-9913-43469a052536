import { NextRequest, NextResponse } from 'next/server';
import { OpenAI } from 'openai';

// OpenAI client initialized at runtime to avoid build-time errors

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const model = formData.get('model') as string || 'whisper-1';

    if (!file) {
      return NextResponse.json({ error: 'Audio file is required' }, { status: 400 });
    }

    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json({ error: 'OpenAI API key not configured' }, { status: 500 });
    }

    console.log(`🎤 Direct transcription requested for: ${file.name} using model: ${model}`);

    // Validate file type
    const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/m4a', 'audio/webm'];
    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(mp3|wav|m4a|webm|aac)$/i)) {
      return NextResponse.json({ error: 'Unsupported file type. Please use MP3, WAV, M4A, or WebM.' }, { status: 400 });
    }

    // Handle large files with chunking
    const maxSize = 20 * 1024 * 1024; // 20MB (OpenAI limit is 25MB but using 20MB for safety)
    const fileToTranscribe = file;
    
    if (file.size > maxSize) {
      console.log(`📦 File size ${file.size} bytes exceeds 20MB, chunking required`);
      
      // For large files, we'll need to chunk them
      // For now, return an error asking to use the main upload system
      // In a production system, you'd implement audio chunking here
      return NextResponse.json({ 
        error: 'File size exceeds 20MB. Please use the main WebUSB upload system for large files, which supports chunking and processing.',
        needsChunking: true,
        fileSize: file.size 
      }, { status: 413 });
    }

    // Transcribe using OpenAI Whisper
    // Only whisper-1 is supported for transcriptions API
    const validModel = model.includes('whisper') ? model : 'whisper-1';
    
    console.log(`🔄 Using model: ${validModel} (requested: ${model})`);
    
    // Initialize OpenAI client at runtime
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    
    const transcription = await openai.audio.transcriptions.create({
      file: file,
      model: validModel,
      response_format: 'json',
      temperature: 0.2
    });

    console.log(`✅ Direct transcription successful for: ${file.name}`);

    return NextResponse.json({
      fileName: file.name,
      transcription: transcription.text,
      model: validModel,
      duration: transcription.duration || null,
      fileSize: file.size
    });

  } catch (error: any) {
    console.error('❌ Direct transcription error:', error);
    
    if (error?.error?.code === 'invalid_request_error') {
      return NextResponse.json(
        { error: `Invalid request: ${error.error.message}` },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: `Transcription failed: ${error.message}` },
      { status: 500 }
    );
  }
}