import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { AzureStorageService } from '../../../../src/lib/azure-storage';

// File to store copied notes tracking
const COPIED_NOTES_FILE = path.join(process.cwd(), 'data', 'copied-notes.json');

// In-memory storage for copied notes tracking
let copiedNotes = new Map<string, {
  appointmentId: string;
  patientName: string;
  copiedAt: string;
  noteContent: string;
}>();

// Load copied notes from file or Azure
async function loadCopiedNotes() {
  try {
    // Try loading from Azure first if configured
    if (AzureStorageService.isConfigured()) {
      try {
        const notes = await AzureStorageService.downloadJson('metadata/copied-notes.json');
        copiedNotes = new Map(Object.entries(notes));
        console.log(`Loaded ${copiedNotes.size} copied notes from Azure`);
        return;
      } catch (azureError) {
        console.log('No copied notes found in Azure, trying local file...');
      }
    }

    // Fall back to local file
    const data = await fs.readFile(COPIED_NOTES_FILE, 'utf-8');
    const notesObject = JSON.parse(data);
    copiedNotes = new Map(Object.entries(notesObject));
    console.log(`Loaded ${copiedNotes.size} copied notes from local file`);
  } catch (error) {
    console.log('No existing copied notes file found, starting fresh');
    copiedNotes = new Map();
  }
}

// Save copied notes to file and Azure
async function saveCopiedNotes() {
  try {
    const notesObject = Object.fromEntries(copiedNotes);

    // Save to local file
    const dataDir = path.dirname(COPIED_NOTES_FILE);
    await fs.mkdir(dataDir, { recursive: true });
    await fs.writeFile(COPIED_NOTES_FILE, JSON.stringify(notesObject, null, 2));

    // Also save to Azure if configured
    if (AzureStorageService.isConfigured()) {
      await AzureStorageService.uploadJson('metadata/copied-notes.json', notesObject);
      console.log('Copied notes saved to Azure Blob Storage');
    }
  } catch (error) {
    console.error('Failed to save copied notes:', error);
  }
}

// Initialize copied notes on module load
loadCopiedNotes();

export async function POST(request: NextRequest) {
  try {
    const { appointmentId, patientName, noteContent } = await request.json();

    if (!appointmentId || !patientName || !noteContent) {
      return NextResponse.json(
        { error: 'Appointment ID, patient name, and note content are required' },
        { status: 400 }
      );
    }

    // Store the copied note tracking
    copiedNotes.set(appointmentId, {
      appointmentId,
      patientName,
      copiedAt: new Date().toISOString(),
      noteContent
    });

    // Save to file for persistence
    await saveCopiedNotes();

    return NextResponse.json({
      success: true,
      appointmentId,
      patientName,
      message: 'Note marked as copied to chart successfully'
    });

  } catch (error) {
    console.error('Error marking note as copied:', error);
    return NextResponse.json(
      { error: 'Failed to mark note as copied' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const appointmentId = searchParams.get('appointmentId');
    const date = searchParams.get('date');

    if (appointmentId) {
      // Get specific appointment's copied status
      const copiedNote = copiedNotes.get(appointmentId);
      return NextResponse.json({
        isCopied: !!copiedNote,
        copiedAt: copiedNote?.copiedAt,
        noteContent: copiedNote?.noteContent
      });
    }

    if (date) {
      // Get all copied notes for a specific date
      const dateNotes = Array.from(copiedNotes.values()).filter(note => {
        const copiedDate = new Date(note.copiedAt).toISOString().split('T')[0];
        return copiedDate === date;
      });

      return NextResponse.json({
        copiedNotes: dateNotes,
        count: dateNotes.length
      });
    }

    // Get all copied notes
    return NextResponse.json({
      copiedNotes: Array.from(copiedNotes.values()),
      count: copiedNotes.size
    });

  } catch (error) {
    console.error('Error getting copied notes:', error);
    return NextResponse.json(
      { error: 'Failed to get copied notes' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const appointmentId = searchParams.get('appointmentId');

    if (!appointmentId) {
      return NextResponse.json(
        { error: 'Appointment ID is required' },
        { status: 400 }
      );
    }

    // Check if copied note exists
    if (!copiedNotes.has(appointmentId)) {
      return NextResponse.json(
        { error: 'No copied note found for this appointment' },
        { status: 404 }
      );
    }

    // Remove the copied note tracking
    const removedNote = copiedNotes.get(appointmentId);
    copiedNotes.delete(appointmentId);

    // Save to file for persistence
    await saveCopiedNotes();

    return NextResponse.json({
      success: true,
      appointmentId,
      removedNote,
      message: 'Copied note tracking removed successfully'
    });

  } catch (error) {
    console.error('Error removing copied note tracking:', error);
    return NextResponse.json(
      { error: 'Failed to remove copied note tracking' },
      { status: 500 }
    );
  }
}
