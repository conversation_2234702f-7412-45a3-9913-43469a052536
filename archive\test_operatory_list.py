import requests
import json
import sys
from collections import defaultdict

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        }
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def test_endpoints_for_operatories(request_key):
    """Test various endpoints to find operatory information."""
    headers = {"Request-Key": request_key}
    
    # List of endpoints to try
    endpoints = [
        # V2 endpoints
        ("v2", "practice_schedule"),
        ("v2", "appointments"),
        ("v2", "practice"),
        ("v2", "practice/resources"),
        ("v2", "resources"),
        ("v2", "locations"),
        ("v2", "rooms"),
        ("v2", "operatories"),
        ("v2", "settings"),
        ("v2", "configuration"),
        
        # V4 endpoints
        ("v4", "practice"),
        ("v4", "practice/resources"),
        ("v4", "resources"),
        ("v4", "locations"),
        ("v4", "settings"),
        ("v4", "configuration")
    ]
    
    for version, endpoint in endpoints:
        base_url = API_BASE_V4 if version == "v4" else API_BASE_V2
        url = f"{base_url}/{endpoint}"
        
        print(f"\nTrying {version} endpoint: {endpoint}")
        try:
            resp = requests.get(url, headers=headers)
            
            print(f"Response status: {resp.status_code}")
            if resp.status_code == 200:
                try:
                    data = resp.json()
                    print(f"Success! Got data")
                    
                    # Check if response contains operatory-related information
                    if isinstance(data, list):
                        print(f"Response is a list with {len(data)} items")
                        if len(data) > 0:
                            print(f"First item keys: {list(data[0].keys())}")
                            
                            # Look for operatory-related fields
                            operatory_fields = [
                                key for key in data[0].keys() 
                                if any(term in key.lower() for term in ["operatory", "chair", "room", "location"])
                            ]
                            
                            if operatory_fields:
                                print(f"Found operatory-related fields: {operatory_fields}")
                    elif isinstance(data, dict):
                        print(f"Response keys: {list(data.keys())}")
                        
                        # Check if it's a paginated response
                        if "items" in data:
                            items = data.get("items", [])
                            print(f"Contains {len(items)} items")
                            
                            if len(items) > 0:
                                print(f"Item keys: {list(items[0].keys())}")
                                
                                # Look for operatory-related fields
                                operatory_fields = [
                                    key for key in items[0].keys() 
                                    if any(term in key.lower() for term in ["operatory", "chair", "room", "location"])
                                ]
                                
                                if operatory_fields:
                                    print(f"Found operatory-related fields: {operatory_fields}")
                except Exception as e:
                    print(f"Error parsing response: {e}")
                    print(f"Response text: {resp.text[:200]}...")
            elif resp.status_code != 204:  # No content is a valid response
                print("Error response:")
                print(resp.text[:500])
        except Exception as e:
            print(f"Error making request: {e}")

def extract_operatories_from_appointments(request_key):
    """Extract a list of operatories from appointment data."""
    print("\nExtracting operatories from appointment data...")
    headers = {"Request-Key": request_key}
    
    # Try to get appointments for the last 90 days to find as many operatories as possible
    operatories = set()
    
    # Try different date ranges
    from datetime import datetime, timedelta
    
    end_date = datetime.now()
    start_date = end_date - timedelta(days=90)
    
    # Format dates as YYYY-MM-DD
    date_format = "%Y-%m-%d"
    current_date = start_date
    
    # Check every 7 days to avoid too many API calls
    while current_date <= end_date:
        date_str = current_date.strftime(date_format)
        print(f"Checking appointments for {date_str}...")
        
        resp = requests.get(f"{API_BASE_V2}/appointments", headers=headers, params={"date": date_str})
        
        if resp.status_code == 200:
            try:
                data = resp.json()
                
                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    
                    # Extract operatories
                    for item in items:
                        operatory = item.get("operatory")
                        if operatory and operatory not in ["", "N/A"]:
                            operatories.add(operatory)
                    
                    print(f"Found {len(operatories)} unique operatories so far")
            except Exception as e:
                print(f"Error parsing response: {e}")
        
        # Move to next date
        current_date += timedelta(days=7)
    
    # Print the list of operatories
    if operatories:
        print("\nFound the following operatories:")
        for op in sorted(operatories):
            print(f"  - {op}")
    else:
        print("No operatories found in appointment data")
    
    return operatories

def main():
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Test various endpoints to find operatory information
    test_endpoints_for_operatories(request_key)
    
    # Extract operatories from appointment data
    extract_operatories_from_appointments(request_key)
    
    print("\nTesting complete.")

if __name__ == "__main__":
    main()
