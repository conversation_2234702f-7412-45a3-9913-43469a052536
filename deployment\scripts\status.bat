@echo off
echo ========================================
echo    Dental App Service Status
echo ========================================
echo.

cd /d "C:\DentalApp"

echo Checking PM2 service status...
call pm2 list

echo.
echo ========================================
echo    Detailed Status Information
echo ========================================
echo.

:: Check if service is running
call pm2 list | findstr "dental-app" | findstr "online" >nul
if %errorLevel%==0 (
    echo Status: RUNNING ✓
    echo.
    echo The Dental App service is running normally.
    echo Access the app at: http://localhost:3000
    echo.
    
    :: Get process details
    echo Process Details:
    call pm2 show dental-app
    
) else (
    call pm2 list | findstr "dental-app" | findstr "stopped" >nul
    if %errorLevel%==0 (
        echo Status: STOPPED ✗
        echo.
        echo The Dental App service is stopped.
        echo To start it, run: C:\DentalApp\scripts\start-service.bat
        echo.
    ) else (
        call pm2 list | findstr "dental-app" | findstr "error" >nul
        if %errorLevel%==0 (
            echo Status: ERROR ✗
            echo.
            echo The Dental App service has errors.
            echo Check logs: C:\DentalApp\scripts\logs.bat
            echo Try restart: C:\DentalApp\scripts\restart-service.bat
            echo.
        ) else (
            echo Status: NOT FOUND ✗
            echo.
            echo The Dental App service is not configured.
            echo Please run the installation: INSTALL.bat
            echo.
        )
    )
)

echo.
echo ========================================
echo    System Information
echo ========================================
echo.
echo Current Time: %date% %time%
echo Computer: %COMPUTERNAME%
echo User: %USERNAME%
echo.

:: Check port 3000
echo Checking port 3000...
netstat -an | findstr ":3000" >nul
if %errorLevel%==0 (
    echo Port 3000: IN USE ✓
) else (
    echo Port 3000: AVAILABLE ✗
)

:: Check disk space
echo.
echo Disk Space (C: drive):
for /f "tokens=3" %%a in ('dir C:\ /-c ^| findstr "bytes free"') do echo Free Space: %%a bytes

echo.
echo ========================================
echo    Quick Actions
echo ========================================
echo.
echo Available commands:
echo - start-service.bat    : Start the service
echo - stop-service.bat     : Stop the service
echo - restart-service.bat  : Restart the service
echo - logs.bat            : View application logs
echo - backup.bat          : Backup application data
echo - update-remote.bat   : Update from GitHub
echo - update-usb.bat      : Update from USB
echo.

echo Press any key to continue...
pause >nul
