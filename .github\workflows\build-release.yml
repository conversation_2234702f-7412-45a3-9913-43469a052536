name: Build and Release Dental App (DISABLED - Using Azure)

on:
  # Disabled - using Azure App Service deployment instead
  # push:
  #   branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Install dependencies
      run: npm install

    - name: Create Next.js config for deployment
      run: |
        cat > next.config.js << 'EOF'
        /** @type {import('next').NextConfig} */
        const nextConfig = {
          eslint: {
            ignoreDuringBuilds: true,
          },
          typescript: {
            ignoreBuildErrors: true,
          },
          output: 'standalone',
          serverExternalPackages: ['sqlite3'],
          experimental: {
            missingSuspenseWithCSRBailout: false,
          },
        }
        module.exports = nextConfig
        EOF

    - name: Build application
      run: npm run build
      env:
        OPENAI_API_KEY: "dummy-key-for-build"
        SIKKA_API_KEY: "dummy-key-for-build"
        SIKKA_API_URL: "https://dummy-url-for-build.com"
      
    - name: Create release package
      run: |
        # Create release directory structure
        mkdir -p release/app
        mkdir -p release/config
        mkdir -p release/scripts
        
        # Copy application files
        cp -r .next release/app/ || echo "No .next directory found"
        cp -r public release/app/ || echo "No public directory found"
        cp -r src release/app/ || echo "No src directory found"
        cp package.json release/app/ || echo "No package.json found"
        cp package-lock.json release/app/ || echo "No package-lock.json found"
        cp next.config.js release/app/ || echo "No next.config.js found"
        cp tailwind.config.ts release/app/ || echo "No tailwind.config.ts found"
        cp tsconfig.json release/app/ || echo "No tsconfig.json found"
        
        # Copy configuration files
        cp USB-Deploy/config/ecosystem.config.js release/config/ || echo "No ecosystem.config.js found"
        cp USB-Deploy/config/.env.production release/config/ || echo "No .env.production found"

        # Copy management scripts
        cp USB-Deploy/scripts/*.bat release/scripts/ || echo "No .bat scripts found"
        
        # Create version file
        echo "${GITHUB_SHA:0:7}" > release/version.txt
        if [ "${{ github.ref_type }}" = "tag" ]; then
          echo "${{ github.ref_name }}" > release/version.txt
        fi
        
        # Create installation instructions
        cat > release/README.txt << 'EOF'
        Dental App Release Package
        ==========================
        
        INSTALLATION:
        1. Extract this package to C:\dentalappserver\
        2. Run scripts\install-from-github.bat as Administrator
        3. Access app at http://localhost:3000
        
        UPDATE:
        1. Run scripts\update-from-github.bat as Administrator
        
        MANAGEMENT:
        - Start: scripts\start-service.bat
        - Stop: scripts\stop-service.bat
        - Status: scripts\status.bat
        - Logs: scripts\logs.bat
        - Backup: scripts\backup.bat
        
        For support, check logs or contact administrator.
        EOF
        
    - name: Create ZIP package
      run: |
        cd release
        zip -r ../dental-app-release.zip .
        cd ..
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: dental-app-release
        path: dental-app-release.zip
        retention-days: 30

  release:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref_type == 'tag' || github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: dental-app-release
        
    - name: Create Release
      id: create_release
      uses: softprops/action-gh-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: auto-${{ github.sha }}
        name: Auto Release ${{ github.sha }}
        body: |
          ## Dental App Release

          ### Installation
          **New Installation:**
          1. Download `dental-app-release.zip`
          2. Extract to `C:\dentalappserver\`
          3. Run `scripts\install-from-github.bat` as Administrator
          4. Access app at http://localhost:3000

          **Update Existing:**
          1. Run `C:\dentalappserver\scripts\update-from-github.bat` as Administrator

          ### Changes
          ${{ github.event.head_commit.message }}

          ### System Requirements
          - Windows 10/11
          - Administrator access
          - Internet connection (for initial setup)
          - Network access to Dentrix server (***********)

          ### Support
          - Check logs: `C:\dentalappserver\scripts\logs.bat`
          - Check status: `C:\dentalappserver\scripts\status.bat`
          - Backup data: `C:\dentalappserver\scripts\backup.bat`
        files: |
          dental-app-release.zip
        draft: false
        prerelease: false

  notify:
    needs: [build, release]
    runs-on: ubuntu-latest
    if: always() && (needs.build.result == 'success' || needs.release.result == 'success')
    
    steps:
    - name: Notify Success
      run: |
        echo "✅ Dental App release created successfully!"
        echo "📦 Package: dental-app-release.zip"
        echo "🔗 Download: https://github.com/suncoastdc/temp-dental/releases/latest"
        echo "💻 Install command for office PC:"
        echo "   curl -L https://github.com/suncoastdc/temp-dental/releases/latest/download/dental-app-release.zip -o dental-app-release.zip"
