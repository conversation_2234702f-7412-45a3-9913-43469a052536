@echo off
REM Daily Schedule Viewer
REM This batch file runs the daily_schedule.py script with the specified parameters

echo Daily Schedule Viewer
echo --------------------

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Python is not installed or not in your PATH.
    echo Please install Python from https://www.python.org/downloads/
    pause
    exit /b
)

REM Check if required packages are installed
python -c "import requests" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing required packages...
    pip install requests
)

REM Get date (default to today)
set /p date="Enter date (YYYY-MM-DD) [today]: "
if "%date%"=="" (
    for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (
        set mm=%%a
        set dd=%%b
        set yy=%%c
    )
    set date=%yy%-%mm%-%dd%
)

REM Get provider ID
set /p provider="Enter provider ID (e.g., LL01): "
if "%provider%"=="" (
    echo Provider ID is required.
    pause
    exit /b
)

REM Run the script
echo Running daily schedule for %provider% on %date%...
python daily_schedule.py %date% %provider%

pause
