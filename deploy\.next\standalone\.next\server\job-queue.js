"use strict";exports.id=5238,exports.ids=[5238],exports.modules={47894:(t,e,r)=>{r.d(e,{rb:()=>s});class a{constructor(t){this.connectionString=t,this.tableName="ProcessingJobs",this.initialized=!1}async ensureInitialized(){if(!this.initialized)try{let{TableClient:t}=await Promise.resolve().then(r.t.bind(r,43134,23));this.tableClient=t.fromConnectionString(this.connectionString,this.tableName),await this.tableClient.createTable(),this.initialized=!0}catch(t){throw console.error("Failed to initialize Azure Table Storage:",t),Error("Azure Table Storage initialization failed")}}async createJob(t){await this.ensureInitialized();let e=this.generateJobId(),r=new Date().toISOString(),a={id:e,createdAt:r,updatedAt:r,retryCount:0,...t},i={partitionKey:"job",rowKey:e,...this.jobToEntity(a)};try{return await this.tableClient.createEntity(i),e}catch(t){throw console.error("Failed to create job in Azure Table Storage:",t),Error(`Failed to create job: ${t}`)}}async updateJob(t,e){await this.ensureInitialized();let r={partitionKey:"job",rowKey:t,updatedAt:new Date().toISOString(),...this.updatesToEntity(e)};try{await this.tableClient.updateEntity(r,"Merge")}catch(e){throw console.error(`Failed to update job ${t}:`,e),Error(`Failed to update job: ${e}`)}}async getJob(t){await this.ensureInitialized();try{let e=await this.tableClient.getEntity("job",t);return this.entityToJob(e)}catch(e){if(404===e.statusCode)return null;throw console.error(`Failed to get job ${t}:`,e),Error(`Failed to get job: ${e}`)}}async listJobs(t={}){await this.ensureInitialized();let e="PartitionKey eq 'job'";if(t.status&&t.status.length>0){let r=t.status.map(t=>`status eq '${t}'`).join(" or ");e+=` and (${r})`}t.createdAfter&&(e+=` and createdAt ge '${t.createdAfter}'`),t.createdBefore&&(e+=` and createdAt le '${t.createdBefore}'`);try{let r=this.tableClient.listEntities({queryOptions:{filter:e}}),a=[],i=0,s=t.limit||100;for await(let t of r){if(i>=s)break;a.push(this.entityToJob(t)),i++}return a}catch(t){throw console.error("Failed to list jobs:",t),Error(`Failed to list jobs: ${t}`)}}async deleteJob(t){await this.ensureInitialized();try{await this.tableClient.deleteEntity("job",t)}catch(e){throw console.error(`Failed to delete job ${t}:`,e),Error(`Failed to delete job: ${e}`)}}async getJobsForRecovery(){let t=new Date(Date.now()-18e5).toISOString();return this.listJobs({status:["processing"],createdBefore:t})}async getStuckJobs(){let t=new Date(Date.now()-9e5).toISOString();return this.listJobs({status:["processing"],createdBefore:t})}async getNextPendingJob(){let t=await this.listJobs({status:["pending"],limit:1});return t.length>0?t[0]:null}async markJobAsProcessing(t){await this.updateJob(t,{status:"processing",startedAt:new Date().toISOString()})}async cleanup(t){let e=new Date;e.setDate(e.getDate()-t);let r=await this.listJobs({status:["completed","failed"],createdBefore:e.toISOString()}),a=0;for(let t of r)try{await this.deleteJob(t.id),a++}catch(e){console.error(`Failed to delete job ${t.id} during cleanup:`,e)}return a}async getStats(){try{let[t,e,r,a,i]=await Promise.all([this.listJobs({status:["pending"],limit:1e3}),this.listJobs({status:["processing"],limit:1e3}),this.listJobs({status:["completed"],limit:1e3}),this.listJobs({status:["failed"],limit:1e3}),this.listJobs({status:["retrying"],limit:1e3})]);return{total:t.length+e.length+r.length+a.length+i.length,pending:t.length,processing:e.length,completed:r.length,failed:a.length,retrying:i.length}}catch(t){return console.error("Failed to get job stats:",t),{total:0,pending:0,processing:0,completed:0,failed:0,retrying:0}}}generateJobId(){return`job_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}jobToEntity(t){return{id:t.id,filename:t.filename,containerPath:t.containerPath,status:t.status,retryCount:t.retryCount,createdAt:t.createdAt,updatedAt:t.updatedAt,startedAt:t.startedAt||"",completedAt:t.completedAt||"",errorDetails:t.errorDetails?JSON.stringify(t.errorDetails):"",results:t.results?JSON.stringify(t.results):"",queueMessage:t.queueMessage?JSON.stringify(t.queueMessage):"",metadata:t.metadata?JSON.stringify(t.metadata):""}}updatesToEntity(t){let e={};return t.status&&(e.status=t.status),void 0!==t.retryCount&&(e.retryCount=t.retryCount),t.startedAt&&(e.startedAt=t.startedAt),t.completedAt&&(e.completedAt=t.completedAt),t.errorDetails&&(e.errorDetails=JSON.stringify(t.errorDetails)),t.results&&(e.results=JSON.stringify(t.results)),t.queueMessage&&(e.queueMessage=JSON.stringify(t.queueMessage)),t.metadata&&(e.metadata=JSON.stringify(t.metadata)),e}entityToJob(t){return{id:t.id,filename:t.filename,containerPath:t.containerPath,status:t.status,retryCount:t.retryCount||0,createdAt:t.createdAt,updatedAt:t.updatedAt,startedAt:t.startedAt||void 0,completedAt:t.completedAt||void 0,errorDetails:t.errorDetails?JSON.parse(t.errorDetails):void 0,results:t.results?JSON.parse(t.results):void 0,queueMessage:t.queueMessage?JSON.parse(t.queueMessage):void 0,metadata:t.metadata?JSON.parse(t.metadata):void 0}}}class i{async createJob(t){let e=`job_${++this.idCounter}_${Date.now()}`,r=new Date().toISOString(),a={id:e,createdAt:r,updatedAt:r,retryCount:0,...t};return this.jobs.set(e,a),e}async updateJob(t,e){let r=this.jobs.get(t);if(!r)throw Error(`Job ${t} not found`);let a={...r,...e,updatedAt:new Date().toISOString()};this.jobs.set(t,a)}async getJob(t){return this.jobs.get(t)||null}async listJobs(t={}){let e=Array.from(this.jobs.values());return t.status&&t.status.length>0&&(e=e.filter(e=>t.status.includes(e.status))),t.createdAfter&&(e=e.filter(e=>e.createdAt>=t.createdAfter)),t.createdBefore&&(e=e.filter(e=>e.createdAt<=t.createdBefore)),t.limit&&(e=e.slice(0,t.limit)),e}async deleteJob(t){this.jobs.delete(t)}async getJobsForRecovery(){let t=new Date(Date.now()-18e5).toISOString();return this.listJobs({status:["processing"],createdBefore:t})}async getStuckJobs(){let t=new Date(Date.now()-9e5).toISOString();return this.listJobs({status:["processing"],createdBefore:t})}async getNextPendingJob(){let t=await this.listJobs({status:["pending"],limit:1});return t.length>0?t[0]:null}async markJobAsProcessing(t){await this.updateJob(t,{status:"processing",startedAt:new Date().toISOString()})}async cleanup(t){let e=new Date;e.setDate(e.getDate()-t);let r=e.toISOString(),a=Array.from(this.jobs.values()).filter(t=>("completed"===t.status||"failed"===t.status)&&t.createdAt<r);return a.forEach(t=>this.jobs.delete(t.id)),a.length}async getStats(){let t=Array.from(this.jobs.values());return{total:t.length,pending:t.filter(t=>"pending"===t.status).length,processing:t.filter(t=>"processing"===t.status).length,completed:t.filter(t=>"completed"===t.status).length,failed:t.filter(t=>"failed"===t.status).length,retrying:t.filter(t=>"retrying"===t.status).length}}constructor(){this.jobs=new Map,this.idCounter=0}}let s=function(){let t=process.env.AZURE_STORAGE_CONNECTION_STRING;return(!t&&process.env.AZURE_STORAGE_ACCOUNT_NAME&&process.env.AZURE_STORAGE_ACCOUNT_KEY&&(t=`DefaultEndpointsProtocol=https;AccountName=${process.env.AZURE_STORAGE_ACCOUNT_NAME};AccountKey=${process.env.AZURE_STORAGE_ACCOUNT_KEY};EndpointSuffix=core.windows.net`),t&&1)?new a(t):new i}();class o{constructor(t){this.store=t}async recoverInterruptedJobs(){let t=await this.store.getJobsForRecovery();for(let e of t)try{await this.store.updateJob(e.id,{status:"pending",retryCount:e.retryCount+1,errorDetails:{...e.errorDetails,recovery:{recoveredAt:new Date().toISOString(),reason:"Job was stuck in processing state",previousStatus:e.status}}})}catch(t){console.error(`Failed to recover job ${e.id}:`,t)}return t}async cleanupOldJobs(t=30){try{return await this.store.cleanup(t)}catch(t){return console.error("Failed to cleanup old jobs:",t),0}}async getSystemHealth(){try{let t=await this.store.getStats(),e=[];return t.failed>t.completed&&t.total>10&&e.push("High failure rate detected"),t.processing>50&&e.push("Large number of jobs stuck in processing"),{healthy:0===e.length,stats:t,issues:e}}catch(t){return{healthy:!1,stats:null,issues:["Job store unavailable"]}}}}new o(s)},64808:(t,e,r)=>{r.d(e,{JobQueueService:()=>o,w:()=>n});var a=r(47894),i=r(36463),s=r(8248);class o{constructor(){this.isProcessing=!1,this.defaultRetryConfig={maxRetries:3,baseDelay:1e3,maxDelay:6e4,backoffMultiplier:2},this.startProcessing(),this.startCleanupScheduler()}static getInstance(){return o.instance||(o.instance=new o),o.instance}async enqueueJob(t,e={}){let r=(0,i.sn)("enqueue",t.filename);try{r.info("Enqueueing new job",{filename:t.filename,containerPath:t.containerPath,priority:e.priority||1});let i=await a.rb.createJob({...t,status:"pending",retryCount:e.retryCount||0,metadata:{...t.metadata,...e.metadata,priority:e.priority||1,timeout:e.timeout||3e5,enqueuedAt:new Date().toISOString()}});return r.info("Job enqueued successfully",{jobId:i}),this.isProcessing||setImmediate(()=>this.processNextJob()),i}catch(e){throw(0,i.vV)(r,e,"job-enqueue",{filename:t.filename}),e}}async processNextJob(){if(this.isProcessing)return!1;this.isProcessing=!0;let t=(0,i.sn)("processor","queue");try{let t=await a.rb.getNextPendingJob();if(!t)return!1;let e=(0,i.sn)(t.id,t.filename);e.info("Starting job processing",{jobId:t.id,retryCount:t.retryCount,enqueuedAt:t.createdAt}),await a.rb.markJobAsProcessing(t.id);try{return await this.executeJob(t),await a.rb.updateJob(t.id,{status:"completed",completedAt:new Date().toISOString()}),e.info("Job completed successfully",{jobId:t.id}),!0}catch(e){return await this.handleJobError(t,e),!1}}catch(e){return(0,i.vV)(t,e,"job-processing"),!1}finally{this.isProcessing=!1}}async getJobStatus(t){try{return await a.rb.getJob(t)}catch(e){return i.vF.error("Failed to get job status",{jobId:t},e),null}}async retryFailedJob(t,e){let r={...this.defaultRetryConfig,...e},s=(0,i.sn)(t,"retry");try{let e=await a.rb.getJob(t);if(!e)return s.warn("Job not found for retry",{jobId:t}),!1;if("failed"!==e.status)return s.warn("Job is not in failed state for retry",{jobId:t,currentStatus:e.status}),!1;if(e.retryCount>=r.maxRetries)return s.warn("Job has exceeded maximum retry attempts",{jobId:t,retryCount:e.retryCount,maxRetries:r.maxRetries}),!1;let o=Math.min(r.baseDelay*Math.pow(r.backoffMultiplier,e.retryCount),r.maxDelay);return s.info("Scheduling job retry",{jobId:t,retryCount:e.retryCount+1,delayMs:o}),setTimeout(async()=>{try{await a.rb.updateJob(t,{status:"pending",retryCount:e.retryCount+1,updatedAt:new Date().toISOString()}),setImmediate(()=>this.processNextJob())}catch(t){(0,i.vV)(s,t,"job-retry-schedule")}},o),!0}catch(t){return(0,i.vV)(s,t,"job-retry"),!1}}async cleanupOldJobs(t=30){let e=(0,i.sn)("cleanup","queue");try{let r=new Date;r.setDate(r.getDate()-t),e.info("Starting job cleanup",{retentionDays:t,cutoffDate:r.toISOString()});let i=await a.rb.listJobs({status:["completed"],completedBefore:r.toISOString(),limit:1e3}),s=0;for(let t of i)try{await a.rb.deleteJob(t.id),s++}catch(r){e.warn("Failed to delete job",{jobId:t.id},r)}return e.info("Job cleanup completed",{cleanedCount:s,retentionDays:t}),s}catch(t){return(0,i.vV)(e,t,"job-cleanup"),0}}async getQueueStats(){try{let t=await a.rb.getStats(),e=await this.getStuckJobsCount(),r=await a.rb.listJobs({status:["completed"],limit:100}),i=this.calculateAverageProcessingTime(r),s=this.determineQueueHealth(t,e),o=r[0],n=o?.completedAt;return{total:t.total,pending:t.pending,processing:t.processing,completed:t.completed,failed:t.failed,retrying:t.retrying,averageProcessingTime:i,queueHealth:s,lastProcessedAt:n,stuckJobs:e}}catch(t){return i.vF.error("Failed to get queue stats",{},t),{total:0,pending:0,processing:0,completed:0,failed:0,retrying:0,averageProcessingTime:0,queueHealth:"unhealthy",stuckJobs:0}}}async getStuckJobs(){try{return await a.rb.getStuckJobs()}catch(t){return i.vF.error("Failed to get stuck jobs",{},t),[]}}stop(){this.processingInterval&&(clearInterval(this.processingInterval),this.processingInterval=void 0),this.cleanupInterval&&(clearInterval(this.cleanupInterval),this.cleanupInterval=void 0),this.isProcessing=!1,i.vF.info("Job queue service stopped")}async executeJob(t){let e=(0,i.sn)(t.id,t.filename),r=t.metadata?.type||"transcription";switch(e.info("Executing job",{jobType:r,filename:t.filename}),r){case"transcription":await this.executeTranscriptionJob(t);break;case"summarization":await this.executeSummarizationJob(t);break;case"full_processing":await this.executeFullProcessingJob(t);break;default:throw Error(`Unknown job type: ${r}`)}}async executeTranscriptionJob(t){let e=(0,i.sn)(t.id,t.filename);try{let i,s,{OpenAI:o}=await r.e(8096).then(r.bind(r,31630)),{AzureStorageService:n}=await r.e(5772).then(r.bind(r,5772)),{transcribeWithChunking:l,needsChunking:c}=await Promise.all([r.e(8096),r.e(1245)]).then(r.bind(r,61245)),{TranscriptionErrorHandler:u}=await r.e(8689).then(r.bind(r,48689));e.info("Starting transcription job");let d=await n.downloadFile(t.filename);if(!d)throw Error(`Failed to download audio file: ${t.filename}`);let g=new o({apiKey:process.env.OPENAI_API_KEY});if(c(d)){e.info("Using chunked transcription for large file");let r=await l(d,t.filename,g);if(!r.success||!r.fullTranscription)throw Error(`Chunked transcription failed: ${r.error}`);i=r.fullTranscription,s="chunked"}else{e.info("Using direct transcription for regular file");let r=t.filename.split("/").pop()||t.filename,a=Object.assign(d,{name:r,type:"audio/mpeg"});i=(await g.audio.transcriptions.create({file:a,model:"whisper-1",response_format:"json",temperature:.2})).text,s="direct"}let p=t.filename.replace(/\.[^/.]+$/,".json"),h={transcription:i,transcribedAt:new Date().toISOString(),model:"whisper-1",fileName:t.filename,audioFileSize:d.length,transcriptionLength:i.length,transcriptionMethod:s};await n.uploadJson(p,h),await a.rb.updateJob(t.id,{results:{transcription:{text:i,confidence:.95,processingTimeMs:Date.now()-new Date(t.startedAt||t.createdAt).getTime(),method:s,savedTo:p}}}),e.info("Transcription job completed successfully")}catch(t){throw e.error("Transcription job failed",{},t),t}}async executeSummarizationJob(t){let e=(0,i.sn)(t.id,t.filename);try{let i,{OpenAI:s}=await r.e(8096).then(r.bind(r,31630)),{AzureStorageService:o}=await r.e(5772).then(r.bind(r,5772));if(e.info("Starting summarization job"),t.results?.transcription?.text)i=t.results.transcription.text,e.info("Using transcription from job results");else{let r=t.filename.replace(/\.[^/.]+$/,".json"),a=await o.downloadJson(r);if(!a?.transcription)throw Error(`No transcription found for ${t.filename}`);i=a.transcription,e.info("Loaded transcription from Azure storage")}if(!i||0===i.trim().length)throw Error("Empty transcription text provided for summarization");let n=new s({apiKey:process.env.OPENAI_API_KEY}),l=await n.chat.completions.create({model:"gpt-3.5-turbo",messages:[{role:"system",content:"You are a helpful assistant that summarizes clinical notes. Provide concise, professional summaries focusing on key medical information, treatments, and recommendations."},{role:"user",content:`Please summarize the following clinical transcription:

${i}`}],temperature:.3,max_tokens:500}),c=l.choices[0]?.message?.content;if(!c)throw Error("OpenAI returned empty summary");let u=t.filename.replace(/\.[^/.]+$/,".json");try{let t={...await o.downloadJson(u),summary:c,summarizedAt:new Date().toISOString(),summaryModel:"gpt-3.5-turbo"};await o.uploadJson(u,t)}catch{let t={summary:c,summarizedAt:new Date().toISOString(),summaryModel:"gpt-3.5-turbo",transcription:i,transcriptionLength:i.length};await o.uploadJson(u,t)}await a.rb.updateJob(t.id,{results:{...t.results,summarization:{summary:c,confidence:.9,processingTimeMs:Date.now()-new Date(t.startedAt||t.createdAt).getTime(),savedTo:u}}}),e.info("Summarization job completed successfully")}catch(t){throw e.error("Summarization job failed",{},t),t}}async executeFullProcessingJob(t){let e=(0,i.sn)(t.id,t.filename);try{e.info("Starting full processing job (transcription + summarization)"),await this.executeTranscriptionJob(t);let r=await a.rb.getJob(t.id);if(!r)throw Error(`Job ${t.id} not found after transcription`);await this.executeSummarizationJob(r),e.info("Full processing job completed successfully")}catch(t){throw e.error("Full processing job failed",{},t),t}}async handleJobError(t,e){let r=(0,i.sn)(t.id,t.filename),o=s.zc.classifyError(e),n={code:o.code||"UNKNOWN",message:e.message,category:this.mapErrorToCategory(o.type),retryable:o.retryable,stackTrace:e.stack,context:{operation:"job_execution",jobId:t.id,filename:t.filename,retryCount:t.retryCount}};o.retryable&&t.retryCount<this.defaultRetryConfig.maxRetries?(await a.rb.updateJob(t.id,{status:"retrying",errorDetails:n,retryCount:t.retryCount+1,updatedAt:new Date().toISOString()}),r.warn("Job failed, will retry",{jobId:t.id,retryCount:t.retryCount+1,errorType:o.type}),await this.retryFailedJob(t.id)):(await a.rb.updateJob(t.id,{status:"failed",errorDetails:n,completedAt:new Date().toISOString()}),r.error("Job permanently failed",{jobId:t.id,retryCount:t.retryCount,errorType:o.type}))}mapErrorToCategory(t){return({NetworkError:"network_error",TimeoutError:"timeout_error",QuotaExceededError:"quota_exceeded",FileNotFoundError:"file_not_found",ServiceUnavailableError:"api_error",ValidationError:"validation_error",ConfigurationError:"configuration_error",StorageError:"storage_error"})[t]||"system_error"}startProcessing(){this.processingInterval=setInterval(async()=>{try{await this.processNextJob()}catch(t){i.vF.error("Error in processing loop",{},t)}},5e3),i.vF.info("Job queue processing started")}startCleanupScheduler(){this.cleanupInterval=setInterval(async()=>{try{await this.cleanupOldJobs()}catch(t){i.vF.error("Error in cleanup scheduler",{},t)}},36e5),i.vF.info("Job queue cleanup scheduler started")}calculateAverageProcessingTime(t){if(0===t.length)return 0;let e=t.filter(t=>t.startedAt&&t.completedAt);return 0===e.length?0:Math.round(e.reduce((t,e)=>{let r=new Date(e.startedAt).getTime();return t+(new Date(e.completedAt).getTime()-r)},0)/e.length)}async getStuckJobsCount(){try{return(await a.rb.getStuckJobs()).length}catch(t){return 0}}determineQueueHealth(t,e){let r=t.completed+t.failed,a=r>0?t.failed/r:0;return e>10||a>.5?"unhealthy":e>5||a>.2?"degraded":"healthy"}}let n=o.getInstance()}};