import { NextRequest, NextResponse } from 'next/server';
import { OpenAI } from 'openai';

// OpenAI client initialized at runtime to avoid build-time errors

export async function POST(request: NextRequest) {
  try {
    // Initialize OpenAI client at runtime
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const { transcription, summary, patient } = await request.json();

    if (!transcription || !summary) {
      return NextResponse.json({ error: 'Transcription and summary are required' }, { status: 400 });
    }

    console.log('📝 Clinical note generation requested');

    const prompt = `
      Generate a professional clinical note for a dental appointment.

      Patient Information:
      - Name: ${patient?.name || 'N/A'}
      - ID: ${patient?.id || 'N/A'}

      Transcription:
      ${transcription}

      Summary:
      ${summary}

      Based on the information above, please generate a concise and accurate clinical note in a professional format.
    `;

    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant that generates clinical notes for a dental office.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
    });

    const clinicalNote = completion.choices[0].message.content;

    console.log('✅ Clinical note generation successful');

    return NextResponse.json({ clinicalNote });

  } catch (error: any) {
    console.error('❌ Clinical note generation error:', error);
    return NextResponse.json(
      { error: `Clinical note generation failed: ${error.message}` },
      { status: 500 }
    );
  }
}
