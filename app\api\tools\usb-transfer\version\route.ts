import { NextRequest, NextResponse } from 'next/server';

// USB Transfer Tool version information
const USB_TRANSFER_VERSION = {
  version: "3.0.0",
  buildNumber: "202506280920",
  releaseDate: "2025-06-28T09:20:00.000Z",
  downloadUrl: "/api/tools/usb-transfer/download?file=usb-tool-package.zip",
  changelog: [
    "🚀 NEW: PowerShell-based lightweight solution (no installation required)",
    "🔍 ENHANCED: Automatic USB drive detection and scanning",
    "📁 IMPROVED: Smart archive functionality with 30-day auto-cleanup",
    "🧪 ADDED: Dry-run mode with -WhatIf parameter for testing",
    "⚡ OPTIMIZED: Direct Azure Blob Storage upload (faster, more reliable)",
    "🛡️ ENHANCED: Better error handling and progress reporting",
    "🎯 SIMPLIFIED: Single PowerShell script - no complex GUI needed"
  ],
  size: "25 KB",
  requirements: {
    os: "Windows 10 or later",
    powershell: "PowerShell 5.1 or later (built into Windows)",
    storage: "Minimal - just the script file"
  },
  features: [
    "Automatic USB drive detection and scanning",
    "Direct Azure Blob Storage upload with authentication",
    "Smart file archiving after successful upload",
    "Dry-run mode for testing (-WhatIf parameter)",
    "Multiple recorder folder support (FOLDER_A through FOLDER_E)",
    "Detailed progress reporting and error handling",
    "No installation required"
  ]
};

export async function GET(request: NextRequest) {
  try {
    // Get current app version for comparison
    const appVersion = process.env.NEXT_PUBLIC_APP_VERSION || '1.2.0';
    const buildDate = process.env.NEXT_PUBLIC_BUILD_DATE || new Date().toISOString().split('T')[0];
    
    return NextResponse.json({
      success: true,
      version: USB_TRANSFER_VERSION,
      webAppVersion: appVersion,
      webAppBuildDate: buildDate,
      compatibility: {
        minWebAppVersion: "1.0.0",
        maxWebAppVersion: "2.0.0"
      }
    });
  } catch (error: any) {
    console.error('Version check error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get version information',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// POST endpoint to check for updates with installed version
export async function POST(request: NextRequest) {
  try {
    const { installedVersion, buildNumber } = await request.json();
    
    const isOutOfDate = installedVersion !== USB_TRANSFER_VERSION.version;
    const hasUpdate = isOutOfDate;
    
    return NextResponse.json({
      success: true,
      hasUpdate,
      isOutOfDate,
      currentVersion: USB_TRANSFER_VERSION,
      installedVersion,
      updateRequired: isOutOfDate,
      updateRecommended: hasUpdate
    });
  } catch (error: any) {
    console.error('Update check error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check for updates',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
