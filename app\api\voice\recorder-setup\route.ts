import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import { existsSync } from 'fs';
import path from 'path';

interface RecorderDeviceInfo {
  deviceId?: string;
  recorderType: 'original' | 'new';
  hasRecordingsFolder: boolean;
  isConfigured: boolean;
  suggestedDeviceId?: string;
  availableFolders: string[];
  recordingsFound: number;
}

// Function to get the next available device ID
async function getNextDeviceId(): Promise<string> {
  // Check existing device IDs in the server recordings folder
  const serverRecordingsRoot = '\\\\192.168.0.2\\share\\RECORDINGS';
  const localRecordingsRoot = path.join(process.cwd(), 'voice-recordings');
  
  const usedIds = new Set<number>();
  
  // Check both server and local folders for existing device IDs
  const foldersToCheck = [serverRecordingsRoot, localRecordingsRoot];
  
  for (const rootFolder of foldersToCheck) {
    if (existsSync(rootFolder)) {
      try {
        const dateFolders = await fs.readdir(rootFolder, { withFileTypes: true });
        
        for (const dateFolder of dateFolders) {
          if (dateFolder.isDirectory()) {
            const datePath = path.join(rootFolder, dateFolder.name);
            const deviceFolders = await fs.readdir(datePath, { withFileTypes: true });
            
            for (const deviceFolder of deviceFolders) {
              if (deviceFolder.isDirectory()) {
                // Extract device number from device01, device02, etc.
                const match = deviceFolder.name.match(/^device(\d+)$/);
                if (match) {
                  usedIds.add(parseInt(match[1]));
                }
              }
            }
          }
        }
      } catch (error) {
        console.log(`Could not read folder ${rootFolder}:`, error);
      }
    }
  }
  
  // Find the next available ID
  for (let i = 1; i <= 99; i++) {
    if (!usedIds.has(i)) {
      return `device${i.toString().padStart(2, '0')}`;
    }
  }
  
  // Fallback if somehow all IDs are used
  return `device${(usedIds.size + 1).toString().padStart(2, '0')}`;
}

// Function to read device ID from recorder root
async function readDeviceId(recorderRoot: string): Promise<string | null> {
  const deviceIdFile = path.join(recorderRoot, 'device_id.txt');
  
  if (existsSync(deviceIdFile)) {
    try {
      const content = await fs.readFile(deviceIdFile, 'utf-8');
      return content.trim();
    } catch (error) {
      console.error('Error reading device_id.txt:', error);
      return null;
    }
  }
  
  return null;
}

// Function to write device ID to recorder root
async function writeDeviceId(recorderRoot: string, deviceId: string): Promise<void> {
  const deviceIdFile = path.join(recorderRoot, 'device_id.txt');
  
  try {
    await fs.writeFile(deviceIdFile, deviceId, 'utf-8');
    console.log(`Written device ID ${deviceId} to ${deviceIdFile}`);
  } catch (error) {
    console.error('Error writing device_id.txt:', error);
    throw error;
  }
}

// Function to count recordings in a folder
async function countRecordings(folderPath: string): Promise<number> {
  if (!existsSync(folderPath)) return 0;
  
  try {
    const files = await fs.readdir(folderPath);
    return files.filter(file => 
      file.toLowerCase().match(/\.(webm|mp3|wav|m4a|ogg|wma)$/i)
    ).length;
  } catch (error) {
    return 0;
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const recorderPath = searchParams.get('recorderPath') || 'D:\\RECORDER';
  
  try {
    const recorderRoot = recorderPath.endsWith('\\') ? recorderPath : recorderPath + '\\';
    
    // Check if recorder drive exists
    if (!existsSync(recorderRoot)) {
      return NextResponse.json({
        error: `Recorder drive not found: ${recorderRoot}`,
        isConfigured: false
      }, { status: 404 });
    }
    
    // Check available folders (FOLDER_A through FOLDER_E)
    const availableFolders: string[] = [];
    let totalRecordings = 0;
    
    for (const folderLetter of ['A', 'B', 'C', 'D', 'E']) {
      const folderPath = path.join(recorderRoot, `FOLDER_${folderLetter}`);
      if (existsSync(folderPath)) {
        availableFolders.push(`FOLDER_${folderLetter}`);
        totalRecordings += await countRecordings(folderPath);
      }
    }
    
    // Try to read existing device ID
    const existingDeviceId = await readDeviceId(recorderRoot);
    
    // Determine if device is configured
    const isConfigured = existingDeviceId !== null && availableFolders.length > 0;
    
    const deviceInfo: RecorderDeviceInfo = {
      deviceId: existingDeviceId || undefined,
      recorderType: 'new',
      hasRecordingsFolder: availableFolders.length > 0,
      isConfigured,
      suggestedDeviceId: existingDeviceId || await getNextDeviceId(),
      availableFolders,
      recordingsFound: totalRecordings
    };
    
    return NextResponse.json(deviceInfo);
    
  } catch (error) {
    console.error('Recorder setup error:', error);
    return NextResponse.json({
      error: 'Failed to check recorder device',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { recorderPath, deviceId } = await request.json();
    
    if (!recorderPath || !deviceId) {
      return NextResponse.json({
        error: 'Recorder path and device ID are required'
      }, { status: 400 });
    }
    
    const recorderRoot = recorderPath.endsWith('\\') ? recorderPath : recorderPath + '\\';
    
    // Check if recorder drive exists
    if (!existsSync(recorderRoot)) {
      return NextResponse.json({
        error: `Recorder drive not found: ${recorderRoot}`
      }, { status: 404 });
    }
    
    // Write device ID to recorder root
    await writeDeviceId(recorderRoot, deviceId);
    
    // Create a readme file with setup info
    const readmePath = path.join(recorderRoot, 'RECORDER_SETUP.txt');
    const readmeContent = `Voice Recorder Setup
====================

Device ID: ${deviceId}
Setup Date: ${new Date().toISOString()}
Recorder Type: New Voice Recorder
Recording Folders: FOLDER_A, FOLDER_B, FOLDER_C, FOLDER_D, FOLDER_E

This voice recorder is configured for the dental office voice recording system.
Do not delete the device_id.txt file as it identifies this recorder.

Primary recording folder: FOLDER_A
Backup folders: FOLDER_B through FOLDER_E (in case files end up there accidentally)

For support, contact your IT administrator.
`;
    
    await fs.writeFile(readmePath, readmeContent, 'utf-8');
    
    return NextResponse.json({
      success: true,
      deviceId,
      message: `Voice recorder configured as ${deviceId}`,
      setupInfo: {
        recorderType: 'new',
        primaryFolder: 'FOLDER_A',
        backupFolders: ['FOLDER_B', 'FOLDER_C', 'FOLDER_D', 'FOLDER_E']
      }
    });
    
  } catch (error) {
    console.error('Recorder setup error:', error);
    return NextResponse.json({
      error: 'Failed to setup recorder device',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
