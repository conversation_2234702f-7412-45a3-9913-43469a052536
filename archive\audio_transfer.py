import os
import shutil
import hashlib
import json
from datetime import datetime, timedelta
from pathlib import Path
import argparse

USB_ROOT = Path("D:/")
RECORDINGS_DIR = USB_ROOT / "Recordings"
TRASH_DIR = USB_ROOT / "Trash"
DEVICE_ID_FILE = USB_ROOT / "device_id.txt"
LAST_RUN_FILE = USB_ROOT / "last_run.txt"

SERVER_SHARE_ROOT = Path(r"\\***********\share\recordings")

AUDIO_EXTENSIONS = {".wav", ".mp3", ".m4a"}
GROUP_DAILY = False


def get_device_id():
    if DEVICE_ID_FILE.exists():
        return DEVICE_ID_FILE.read_text().strip()
    return None


def get_logical_date(file_path: Path):
    # Try to parse date from filename prefix YYYYMMDD
    stem = file_path.stem
    if len(stem) >= 8 and stem[:8].isdigit():
        try:
            dt = datetime.strptime(stem[:8], "%Y%m%d")
            return dt.strftime("%Y-%m-%d")
        except ValueError:
            pass
    # Fallback to file modified date
    mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
    return mtime.strftime("%Y-%m-%d")


def sha256_checksum(file_path: Path, block_size=65536):
    sha256 = hashlib.sha256()
    with file_path.open("rb") as f:
        for block in iter(lambda: f.read(block_size), b""):
            sha256.update(block)
    return sha256.hexdigest()


def verify_copy(src: Path, dst: Path):
    if not dst.exists():
        return False
    if src.stat().st_size != dst.stat().st_size:
        return False
    # Optional: verify SHA-256
    src_hash = sha256_checksum(src)
    dst_hash = sha256_checksum(dst)
    return src_hash == dst_hash


def write_receipt(dst_audio_path: Path):
    receipt = {
        "file_name": dst_audio_path.name,
        "device_id": get_device_id(),
        "imported_at": datetime.now().isoformat(),
    }
    if not GROUP_DAILY:
        receipt_path = dst_audio_path.with_suffix(".json")
        with receipt_path.open("w", encoding="utf-8") as f:
            json.dump(receipt, f, indent=2)
    else:
        day_folder = dst_audio_path.parent
        receipt_path = day_folder / 'receipts.json'
        receipts = []
        if receipt_path.exists():
            receipts = json.loads(receipt_path.read_text(encoding="utf-8"))
        receipts.append(receipt)
        with receipt_path.open("w", encoding="utf-8") as f:
            json.dump(receipts, f, indent=2)


def move_to_trash(src: Path, logical_date: str, device_id: str):
    trash_folder = TRASH_DIR / logical_date / device_id
    trash_folder.mkdir(parents=True, exist_ok=True)
    dst = trash_folder / src.name
    shutil.move(str(src), str(dst))


def purge_old_trash():
    if not TRASH_DIR.exists():
        return
    now = datetime.now()
    for date_folder in TRASH_DIR.iterdir():
        if not date_folder.is_dir():
            continue
        try:
            folder_date = datetime.strptime(date_folder.name, "%Y-%m-%d")
        except ValueError:
            continue
        age_days = (now - folder_date).days
        if age_days < 30:
            continue
        # Check all files last access
        all_old = True
        for device_folder in date_folder.iterdir():
            if not device_folder.is_dir():
                continue
            for file in device_folder.rglob("*"):
                last_access = datetime.fromtimestamp(file.stat().st_atime)
                if (now - last_access).days < 30:
                    all_old = False
                    break
            if not all_old:
                break
        if all_old:
            shutil.rmtree(date_folder)
            print(f"Purged old trash folder: {date_folder}")


def transfer_audio_files():
    device_id = get_device_id()
    if not device_id:
        print("Device ID file missing or empty.")
        return
    if not RECORDINGS_DIR.exists():
        print(f"Recordings folder not found: {RECORDINGS_DIR}")
        return

    for audio_file in RECORDINGS_DIR.iterdir():
        if audio_file.suffix.lower() not in AUDIO_EXTENSIONS:
            continue
        logical_date = get_logical_date(audio_file)
        server_folder = SERVER_SHARE_ROOT / logical_date / device_id
        server_folder.mkdir(parents=True, exist_ok=True)
        dst_path = server_folder / audio_file.name

        if dst_path.exists():
            print(f"File already exists on server: {dst_path}")
            continue

        print(f"Copying {audio_file} to {dst_path}")
        shutil.copy2(str(audio_file), str(dst_path))

        if verify_copy(audio_file, dst_path):
            write_receipt(dst_path)
            move_to_trash(audio_file, logical_date, device_id)
            print(f"Transferred and verified: {audio_file.name}")
        else:
            print(f"Verification failed for {audio_file.name}")

    # Update last_run.txt
    LAST_RUN_FILE.write_text(datetime.now().isoformat())

    # Purge old trash
    purge_old_trash()


def main():
    parser = argparse.ArgumentParser(description="Transfer audio and generate receipts")
    parser.add_argument('--daily', action='store_true', help='Group receipts into a single JSON per day')
    args = parser.parse_args()
    global GROUP_DAILY
    GROUP_DAILY = args.daily
    transfer_audio_files()


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Error: {e}")
        exit(1)
