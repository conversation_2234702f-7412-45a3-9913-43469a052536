@echo off
echo ========================================
echo    Stopping Dental App Service
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

cd /d "C:\DentalApp"

echo Stopping Dental App service...
call pm2 stop dental-app

:: Wait for service to stop
echo Waiting for service to stop...
timeout /t 3 /nobreak >nul

:: Check service status
call pm2 list | findstr "dental-app" | findstr "stopped" >nul
if %errorLevel%==0 (
    echo.
    echo ========================================
    echo    Service Stopped Successfully!
    echo ========================================
    echo.
    echo Dental App service has been stopped.
    echo.
    echo To start the service again, run:
    echo C:\DentalApp\scripts\start-service.bat
    echo.
) else (
    echo.
    echo ========================================
    echo    Service Stop Status Unknown
    echo ========================================
    echo.
    echo The service may still be running.
    echo Please check the status manually.
    echo.
    echo Run: C:\DentalApp\scripts\status.bat
    echo.
)

echo Press any key to continue...
pause >nul
