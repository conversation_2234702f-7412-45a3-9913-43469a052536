import requests
import json
import sys
from datetime import datetime

# API base URLs
API_BASE_V2 = "https://api.sikkasoft.com/v2"
API_BASE_V4 = "https://api.sikkasoft.com/v4"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        }
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None

    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def test_v2_appointments(request_key, date):
    """Test v2 appointments endpoint with different parameters."""
    headers = {"Request-Key": request_key}

    print(f"\nTesting v2 appointments endpoint for date: {date}")

    # First, check the total count of appointments
    print("\nChecking total count of appointments:")
    try:
        resp = requests.get(
            f"{API_BASE_V2}/appointments",
            headers=headers,
            params={"date": date}
        )

        if resp.status_code == 200:
            data = resp.json()
            if isinstance(data, list) and len(data) > 0:
                total_count = data[0].get("total_count")
                if total_count:
                    print(f"  Total appointments available: {total_count}")
                else:
                    print("  Total count not available in response")
            else:
                print("  No data found in response")
        else:
            print(f"  Error: {resp.status_code}")
            print(f"  {resp.text}")
    except Exception as e:
        print(f"  Error: {e}")

    # Test with date parameter and pagination
    print("\n1. Using 'date' parameter with pagination:")
    try:
        all_items = []
        offset = 0
        limit = 50
        max_pages = 10  # Limit to 10 pages for testing

        for page in range(max_pages):
            print(f"  Fetching page {page+1} (offset: {offset}, limit: {limit})...")

            resp = requests.get(
                f"{API_BASE_V2}/appointments",
                headers=headers,
                params={"date": date, "offset": offset, "limit": limit}
            )

            if resp.status_code == 200:
                data = resp.json()
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    print(f"    Found {len(items)} appointments on page {page+1}")

                    all_items.extend(items)

                    # If we got fewer items than the limit, we've reached the end
                    if len(items) < limit:
                        print("    Reached the end of appointments")
                        break

                    # Move to the next page
                    offset += limit
                else:
                    print("    No appointments found on this page")
                    break
            else:
                print(f"    Error: {resp.status_code}")
                print(f"    {resp.text}")
                break

        print(f"  Total appointments found: {len(all_items)}")

        # Check for appointments on the target date
        target_appointments = [a for a in all_items if a.get("date") == date]
        print(f"  Of these, {len(target_appointments)} are scheduled for {date}")

        # Check for operatory information
        has_operatory = [a for a in target_appointments if a.get("operatory")]
        print(f"  Of these, {len(has_operatory)} have operatory information")

        # Get unique operatories
        operatories = set()
        for appt in target_appointments:
            operatory = appt.get("operatory")
            if operatory and operatory not in ["", "N/A"]:
                operatories.add(operatory)

        print(f"  Operatories found: {', '.join(sorted(operatories))}")

        # Print sample appointments
        if target_appointments:
            print("\n  Sample appointments:")
            for i, appt in enumerate(sorted(target_appointments, key=lambda a: a.get("time", ""))[:5]):
                time = appt.get("time", "")
                patient = appt.get("patient_name", "Unknown")
                operatory = appt.get("operatory", "N/A")
                provider = appt.get("provider_id", "N/A")
                description = appt.get("description", "")
                print(f"    {i+1}. {time} - {patient} - Op: {operatory} - Provider: {provider} - {description}")
    except Exception as e:
        print(f"  Error: {e}")

    # Test with date range and pagination
    print("\n2. Using date range with pagination:")
    try:
        all_items = []
        offset = 0
        limit = 50
        max_pages = 10  # Limit to 10 pages for testing

        # Create a date range string
        date_range = f"{date},{date}"

        for page in range(max_pages):
            print(f"  Fetching page {page+1} (offset: {offset}, limit: {limit})...")

            resp = requests.get(
                f"{API_BASE_V2}/appointments",
                headers=headers,
                params={"date": date_range, "offset": offset, "limit": limit}
            )

            if resp.status_code == 200:
                data = resp.json()
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    print(f"    Found {len(items)} appointments on page {page+1}")

                    all_items.extend(items)

                    # If we got fewer items than the limit, we've reached the end
                    if len(items) < limit:
                        print("    Reached the end of appointments")
                        break

                    # Move to the next page
                    offset += limit
                else:
                    print("    No appointments found on this page")
                    break
            else:
                print(f"    Error: {resp.status_code}")
                print(f"    {resp.text}")
                break

        print(f"  Total appointments found: {len(all_items)}")

        # Check for appointments on the target date
        target_appointments = [a for a in all_items if a.get("date") == date]
        print(f"  Of these, {len(target_appointments)} are scheduled for {date}")

        # Check for operatory information
        has_operatory = [a for a in target_appointments if a.get("operatory")]
        print(f"  Of these, {len(has_operatory)} have operatory information")

        # Get unique operatories
        operatories = set()
        for appt in target_appointments:
            operatory = appt.get("operatory")
            if operatory and operatory not in ["", "N/A"]:
                operatories.add(operatory)

        print(f"  Operatories found: {', '.join(sorted(operatories))}")

        # Print sample appointments
        if target_appointments:
            print("\n  Sample appointments:")
            for i, appt in enumerate(sorted(target_appointments, key=lambda a: a.get("time", ""))[:5]):
                time = appt.get("time", "")
                patient = appt.get("patient_name", "Unknown")
                operatory = appt.get("operatory", "N/A")
                provider = appt.get("provider_id", "N/A")
                description = appt.get("description", "")
                print(f"    {i+1}. {time} - {patient} - Op: {operatory} - Provider: {provider} - {description}")
    except Exception as e:
        print(f"  Error: {e}")

def main():
    # Get date from command line or use default
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        # Use a date from the past
        date = "2023-05-16"

    # Validate date format
    try:
        datetime.strptime(date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        return

    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return

    # Test v2 appointments endpoint
    test_v2_appointments(request_key, date)

    print("\nTest complete.")

if __name__ == "__main__":
    main()
