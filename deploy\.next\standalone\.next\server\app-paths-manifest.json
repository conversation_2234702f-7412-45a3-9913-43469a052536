{"/api/ai/chat/route": "app/api/ai/chat/route.js", "/api/appointments/route": "app/api/appointments/route.js", "/api/cache-bust/route": "app/api/cache-bust/route.js", "/api/appointments/[id]/route": "app/api/appointments/[id]/route.js", "/api/clinical-notes/quick-summary/route": "app/api/clinical-notes/quick-summary/route.js", "/api/clinical-notes/route": "app/api/clinical-notes/route.js", "/api/debug-clinical-search/route": "app/api/debug-clinical-search/route.js", "/api/debug-notes/route": "app/api/debug-notes/route.js", "/api/debug/azure-connection/route": "app/api/debug/azure-connection/route.js", "/api/debug/env/route": "app/api/debug/env/route.js", "/api/debug/system-status/route": "app/api/debug/system-status/route.js", "/api/deployment-info/route": "app/api/deployment-info/route.js", "/api/manifest/route": "app/api/manifest/route.js", "/api/medical-notes/route": "app/api/medical-notes/route.js", "/api/note-types/route": "app/api/note-types/route.js", "/api/openai/config/route": "app/api/openai/config/route.js", "/api/operatories/route": "app/api/operatories/route.js", "/api/patients/[id]/clinical-notes/route": "app/api/patients/[id]/clinical-notes/route.js", "/api/patients/[id]/route": "app/api/patients/[id]/route.js", "/api/patients/[id]/visit-count/route": "app/api/patients/[id]/visit-count/route.js", "/api/patients/[id]/visits/route": "app/api/patients/[id]/visits/route.js", "/api/patients/search/route": "app/api/patients/search/route.js", "/api/settings/route": "app/api/settings/route.js", "/api/sikka/appointments/route": "app/api/sikka/appointments/route.js", "/api/test-azure-fix/route": "app/api/test-azure-fix/route.js", "/api/test-sikka/route": "app/api/test-sikka/route.js", "/api/test-clinical-direct/route": "app/api/test-clinical-direct/route.js", "/api/tools/usb-portable/download/route": "app/api/tools/usb-portable/download/route.js", "/api/tools/usb-portable/version/route": "app/api/tools/usb-portable/version/route.js", "/api/tools/usb-transfer/version/route": "app/api/tools/usb-transfer/version/route.js", "/api/tools/usb-transfer/download/route": "app/api/tools/usb-transfer/download/route.js", "/api/voice/analyze-files/route": "app/api/voice/analyze-files/route.js", "/api/voice/auto-process-azure-direct/route": "app/api/voice/auto-process-azure-direct/route.js", "/api/voice/auto-process/route": "app/api/voice/auto-process/route.js", "/api/voice/background-status/route": "app/api/voice/background-status/route.js", "/api/voice/batch-process-robust/route": "app/api/voice/batch-process-robust/route.js", "/api/voice/batch-summarize/route": "app/api/voice/batch-summarize/route.js", "/api/voice/batch-transcribe-all/route": "app/api/voice/batch-transcribe-all/route.js", "/api/voice/batch-transcribe/route": "app/api/voice/batch-transcribe/route.js", "/api/voice/cache-invalidate/route": "app/api/voice/cache-invalidate/route.js", "/api/voice/cache-status/route": "app/api/voice/cache-status/route.js", "/api/voice/copy-paste-mode/route": "app/api/voice/copy-paste-mode/route.js", "/api/voice/correct-date/route": "app/api/voice/correct-date/route.js", "/api/voice/debug-status/route": "app/api/voice/debug-status/route.js", "/api/voice/debug-transcribe/route": "app/api/voice/debug-transcribe/route.js", "/api/voice/debug-transcription/route": "app/api/voice/debug-transcription/route.js", "/api/voice/generate-professional-note/route": "app/api/voice/generate-professional-note/route.js", "/api/voice/job-monitor/route": "app/api/voice/job-monitor/route.js", "/api/voice/mark-copied/route": "app/api/voice/mark-copied/route.js", "/api/voice/match-recording/route": "app/api/voice/match-recording/route.js", "/api/voice/network-check/route": "app/api/voice/network-check/route.js", "/api/voice/process-all/route": "app/api/voice/process-all/route.js", "/api/voice/next-file/route": "app/api/voice/next-file/route.js", "/api/voice/process-batch-simple/route": "app/api/voice/process-batch-simple/route.js", "/api/voice/process-single/route": "app/api/voice/process-single/route.js", "/api/voice/process-test/route": "app/api/voice/process-test/route.js", "/api/voice/process-unified/route": "app/api/voice/process-unified/route.js", "/api/voice/processing-status/route": "app/api/voice/processing-status/route.js", "/api/voice/recorder-setup/route": "app/api/voice/recorder-setup/route.js", "/api/voice/recording-dates/route": "app/api/voice/recording-dates/route.js", "/api/voice/recordings-cache/route": "app/api/voice/recordings-cache/route.js", "/api/voice/recordings-fast/route": "app/api/voice/recordings-fast/route.js", "/api/voice/recordings/[id]/route": "app/api/voice/recordings/[id]/route.js", "/api/voice/recordings/correct-date/route": "app/api/voice/recordings/correct-date/route.js", "/api/voice/recordings/route": "app/api/voice/recordings/route.js", "/api/voice/smart-sort/route": "app/api/voice/smart-sort/route.js", "/api/voice/status/route": "app/api/voice/status/route.js", "/api/voice/summarize/route": "app/api/voice/summarize/route.js", "/api/voice/sync/route": "app/api/voice/sync/route.js", "/api/voice/test-date-parsing/route": "app/api/voice/test-date-parsing/route.js", "/api/voice/test-pipeline/route": "app/api/voice/test-pipeline/route.js", "/api/voice/transcribe-all/route": "app/api/voice/transcribe-all/route.js", "/api/voice/transcribe-stream/route": "app/api/voice/transcribe-stream/route.js", "/api/voice/transcribe/progress/route": "app/api/voice/transcribe/progress/route.js", "/api/voice/transcription-metadata/route": "app/api/voice/transcription-metadata/route.js", "/api/voice/transfer-stream/route": "app/api/voice/transfer-stream/route.js", "/api/voice/transfer/route": "app/api/voice/transfer/route.js", "/api/voice/unmatch-recording/route": "app/api/voice/unmatch-recording/route.js", "/api/voice/usb-setup/route": "app/api/voice/usb-setup/route.js", "/api/voice/webusb-upload/route": "app/api/voice/webusb-upload/route.js", "/api/voice/smart-match/route": "app/api/voice/smart-match/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/openai/models/route": "app/api/openai/models/route.js", "/api/dental-notes/generate/route": "app/api/dental-notes/generate/route.js", "/api/voice/transcribe-file/route": "app/api/voice/transcribe-file/route.js", "/api/clinical-notes/professionalize/route": "app/api/clinical-notes/professionalize/route.js", "/api/voice/transcribe/route": "app/api/voice/transcribe/route.js", "/ai-assistant/page": "app/ai-assistant/page.js", "/operatories/page": "app/operatories/page.js", "/appointment/[appointmentId]/page": "app/appointment/[appointmentId]/page.js", "/debug-transcription/page": "app/debug-transcription/page.js", "/cache-test/page": "app/cache-test/page.js", "/patient-search/page": "app/patient-search/page.js", "/page": "app/page.js", "/patient/[id]/page": "app/patient/[id]/page.js", "/schedule/page": "app/schedule/page.js", "/settings/page": "app/settings/page.js", "/transcription-control/page": "app/transcription-control/page.js", "/test-webusb/page": "app/test-webusb/page.js", "/voice-workflow/page": "app/voice-workflow/page.js", "/webusb-transfer/page": "app/webusb-transfer/page.js"}