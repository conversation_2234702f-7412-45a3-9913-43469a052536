"use strict";exports.id=4163,exports.ids=[4163],exports.modules={94163:(e,s,r)=>{r.r(s),r.d(s,{VoiceUploadWrapper:()=>h});var t=r(60687),a=r(43210),l=r(80534),d=r(5336),i=r(70334),o=r(45583),c=r(45989),n=r(96882),x=r(90995),m=r(52619),g=r(15177);function h({isDarkMode:e=!1}){let[s,r]=(0,a.useState)(!1),[h,p]=(0,a.useState)([]),[u,j]=(0,a.useState)(null),y=[{step:1,title:"Connect USB Drive",description:"Click to grant browser access to your USB recorder",icon:(0,t.jsx)(l.A,{className:"w-5 h-5"})},{step:2,title:"Auto-Scan Files",description:"Automatically detects audio files in recorder folders",icon:(0,t.jsx)(d.A,{className:"w-5 h-5"})},{step:3,title:"Upload & Process",description:"Bulk upload to Azure with smart patient matching",icon:(0,t.jsx)(i.A,{className:"w-5 h-5"})},{step:4,title:"Archive & Cleanup",description:"Automatically archive files on USB and cleanup old recordings",icon:(0,t.jsx)(d.A,{className:"w-5 h-5"})}];return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700",children:(0,t.jsx)(x.A,{onFilesUploaded:e=>{p(e),j(new Date().toLocaleString())},onError:e=>{console.error("Upload error:",e)},isDarkMode:e})}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,t.jsx)(o.A,{className:"w-5 h-5 mr-2 text-yellow-500"}),"Quick Upload Workflow"]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:y.map(e=>(0,t.jsxs)("div",{className:"flex flex-col items-center text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-3",children:e.icon}),(0,t.jsxs)("div",{className:"text-sm font-medium text-gray-900 dark:text-white mb-1",children:["Step ",e.step,": ",e.title]}),(0,t.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:e.description})]},e.step))})]}),h.length>0&&(0,t.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"w-5 h-5 text-green-600 dark:text-green-400 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Upload Successful"}),(0,t.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-300",children:[h.length," files uploaded successfully",u&&` at ${u}`]})]})]})}),(0,t.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6",children:[(0,t.jsxs)("button",{onClick:()=>r(!s),className:"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors",children:[(0,t.jsx)(c.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[s?"Hide":"Show"," Testing Tools"]})]}),s&&(0,t.jsxs)("div",{className:"mt-4 space-y-4",children:[(0,t.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(n.A,{className:"w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Testing Tools"}),(0,t.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300 mt-1",children:"Use these tools to test WebUSB functionality and troubleshoot issues."})]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Integration Test"}),(0,t.jsx)(m.A,{isDarkMode:e})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Mock Test"}),(0,t.jsx)(g.A,{isDarkMode:e})]})]})]})]})]})}}};