(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{7005:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var a=s(5155),r=s(2115),l=s(5695),n=s(9074),i=s(9869),o=s(7434),c=s(5657),d=s(8313),m=s(6496),u=s(3904);function h(e){let{onRefresh:t,children:s,threshold:l=80,resistance:n=2.5,enabled:i=!0}=e,{isRefreshing:o,pullDistance:c,isPulling:d,containerRef:m,shouldShowIndicator:h,progress:g}=function(e){let{onRefresh:t,threshold:s=80,resistance:a=2.5,enabled:l=!0}=e,[n,i]=(0,r.useState)(!1),[o,c]=(0,r.useState)(0),[d,m]=(0,r.useState)(!1),u=(0,r.useRef)(0),h=(0,r.useRef)(0),g=(0,r.useRef)(!1),x=(0,r.useRef)(null);return(0,r.useEffect)(()=>{let e;if(!l)return;let r=x.current||document.body,d=e=>{window.scrollY>0||n||(u.current=e.touches[0].clientY,g.current=!0)},b=e=>{if(!g.current||n)return;h.current=e.touches[0].clientY;let t=h.current-u.current;if(t>0&&0===window.scrollY){e.preventDefault();let r=Math.min(t/a,1.5*s);c(r),m(r>10)}},p=async()=>{if(g.current){if(g.current=!1,o>=s&&!n){i(!0);try{await t()}catch(e){console.error("Refresh failed:",e)}finally{i(!1)}}c(0),m(!1)}};return r.addEventListener("touchstart",d,{passive:!1}),r.addEventListener("touchmove",b,{passive:!1}),r.addEventListener("touchend",p),r.addEventListener("touchcancel",p),()=>{r.removeEventListener("touchstart",d),r.removeEventListener("touchmove",b),r.removeEventListener("touchend",p),r.removeEventListener("touchcancel",p),e&&cancelAnimationFrame(e)}},[l,s,a,t,n,o]),{isRefreshing:n,pullDistance:o,isPulling:d,containerRef:x,shouldShowIndicator:d||n,progress:Math.min(o/s*100,100)}}({onRefresh:t,threshold:l,resistance:n,enabled:i});return(0,a.jsxs)("div",{ref:m,className:"relative min-h-screen",children:[h&&(0,a.jsx)("div",{className:"fixed top-0 left-0 right-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 transition-all duration-200",style:{height:"".concat(Math.max(c,60*!!o),"px"),transform:"translateY(".concat(o?0:-60+c,"px)")},children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600 dark:text-gray-400",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 ".concat(o?"animate-spin":""),style:{transform:"rotate(".concat(3.6*g,"deg)")}}),(0,a.jsx)("span",{className:"text-sm font-medium",children:o?"Refreshing...":g>=100?"Release to refresh":"Pull to refresh"})]})}),(0,a.jsx)("div",{className:"transition-transform duration-200",style:{transform:"translateY(".concat(h&&!o?c:0,"px)")},children:s})]})}var g=s(1064),x=s(3900),b=s(2355),p=s(3052);function v(e){let{selectedDate:t,onDateChange:s,calendarView:r,onViewChange:l,isDarkMode:n,isMounted:i}=e,o=new Date,c="".concat(o.getFullYear(),"-").concat(String(o.getMonth()+1).padStart(2,"0"),"-").concat(String(o.getDate()).padStart(2,"0")),d=e=>{let a=new Date(t);"month"===r?"prev"===e?a.setMonth(a.getMonth()-1):a.setMonth(a.getMonth()+1):"prev"===e?a.setDate(a.getDate()-7):a.setDate(a.getDate()+7),s(a.toISOString().split("T")[0])};return(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-slate-200 dark:border-slate-700",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("button",{onClick:()=>l("week"===r?"month":"week"),className:"p-2 rounded-md bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 transition-colors border border-gray-400 dark:border-gray-500",title:"week"===r?"Expand to month view":"Collapse to week view",children:"week"===r?(0,a.jsx)(g.A,{className:"w-4 h-4 text-gray-700 dark:text-gray-200"}):(0,a.jsx)(x.A,{className:"w-4 h-4 text-gray-700 dark:text-gray-200"})}),(0,a.jsx)("h4",{className:"text-xl font-bold text-slate-900 dark:text-white text-center",children:(()=>{let[e,s,a]=t.split("-").map(Number),l=new Date(e,s-1,a);return"month"===r?l.toLocaleDateString("en-US",{month:"long",year:"numeric"}):l.toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"})})()}),(0,a.jsx)("button",{onClick:()=>s(c),className:"px-4 py-2 text-sm rounded-lg bg-blue-100 hover:bg-blue-200 text-blue-700 dark:bg-blue-900 dark:hover:bg-blue-800 dark:text-blue-300 transition-colors font-medium",title:"Go to today",children:"Today"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 sm:gap-4 mb-4 sm:mb-6",children:[(0,a.jsx)("button",{onClick:()=>d("prev"),className:"p-2 sm:p-3 rounded-lg bg-slate-200 hover:bg-slate-300 dark:bg-slate-600 dark:hover:bg-slate-500 transition-colors border border-slate-300 dark:border-slate-500",title:"month"===r?"Previous month":"Previous week",children:(0,a.jsx)(b.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-slate-700 dark:text-slate-200"})}),(0,a.jsx)("div",{className:"flex-1",children:"week"===r?(0,a.jsx)("div",{className:"grid grid-cols-5 sm:grid-cols-7 gap-1 sm:gap-2",children:(()=>{let e=new Date(t+"T12:00:00"),r=new Date(e);r.setDate(e.getDate()-e.getDay());let l=[];for(let e=0;e<7;e++){let o=new Date(r);o.setDate(r.getDate()+e);let d=o.getFullYear(),m=String(o.getMonth()+1).padStart(2,"0"),u=String(o.getDate()).padStart(2,"0"),h="".concat(d,"-").concat(m,"-").concat(u),g=h===c,x=h===t,b=0===e||6===e;l.push((0,a.jsxs)("div",{className:"p-1 sm:p-2 md:p-3 rounded-lg text-center cursor-pointer transition-colors ".concat(b?"hidden sm:block":""," ").concat(x?"bg-blue-600 text-white ring-2 ring-blue-300":g?i&&n?"bg-blue-900 text-blue-200 border-2 border-blue-500":"bg-blue-100 text-blue-800 border-2 border-blue-400":i&&n?"bg-slate-700 hover:bg-slate-600 text-slate-300":"bg-slate-100 hover:bg-slate-200 text-slate-700 border border-slate-200"),onClick:()=>s(h),suppressHydrationWarning:!0,children:[(0,a.jsx)("div",{className:"text-xs font-medium mb-1 hidden sm:block",children:o.toLocaleDateString("en-US",{weekday:"short"})}),(0,a.jsx)("div",{className:"text-xs font-medium mb-1 sm:hidden",children:o.toLocaleDateString("en-US",{weekday:"narrow"})}),(0,a.jsx)("div",{className:"text-sm sm:text-lg font-bold ".concat(g?"text-white":""),children:o.getDate()}),(0,a.jsx)("div",{className:"text-xs mt-1 hidden sm:block",children:g?"Today":o.toLocaleDateString("en-US",{month:"short"})})]},e))}return l})()}):(0,a.jsxs)("div",{className:"grid grid-cols-7 gap-1 sm:gap-2",children:[["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map((e,t)=>(0,a.jsxs)("div",{className:"p-1 sm:p-2 text-center text-xs sm:text-sm font-medium text-slate-500 dark:text-slate-400",children:[(0,a.jsx)("span",{className:"hidden sm:inline",children:e}),(0,a.jsx)("span",{className:"sm:hidden",children:e.charAt(0)})]},e)),(()=>{let[e,r]=t.split("-").map(Number),l=new Date(e,r-1,1),o=new Date(l);o.setDate(o.getDate()-l.getDay());let d=[];for(let e=0;e<42;e++){let l=new Date(o);l.setDate(o.getDate()+e);let m=l.getFullYear(),u=String(l.getMonth()+1).padStart(2,"0"),h=String(l.getDate()).padStart(2,"0"),g="".concat(m,"-").concat(u,"-").concat(h),x=g===c,b=g===t,p=l.getMonth()===r-1;d.push((0,a.jsx)("div",{className:"p-1 sm:p-2 md:p-3 text-center cursor-pointer transition-colors rounded-lg ".concat(b?"bg-blue-600 text-white ring-2 ring-blue-300":x?i&&n?"bg-blue-900 text-blue-200 border-2 border-blue-500":"bg-blue-100 text-blue-800 border-2 border-blue-400":p?i&&n?"bg-slate-700 hover:bg-slate-600 text-slate-200":"bg-slate-200 hover:bg-slate-300 text-slate-800 border border-slate-300":"text-slate-400 dark:text-slate-600 bg-slate-50 dark:bg-slate-800"),onClick:()=>s(g),suppressHydrationWarning:!0,children:(0,a.jsx)("div",{className:"text-sm sm:text-lg font-bold ".concat(x&&b?"text-white":""),children:l.getDate()})},e))}return d})()]})}),(0,a.jsx)("button",{onClick:()=>d("next"),className:"p-2 sm:p-3 rounded-lg bg-slate-200 hover:bg-slate-300 dark:bg-slate-600 dark:hover:bg-slate-500 transition-colors border border-slate-300 dark:border-slate-500",title:"month"===r?"Next month":"Next week",children:(0,a.jsx)(p.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-slate-700 dark:text-slate-200"})})]})]})}var j=s(7924),f=s(9836);function N(e){let{selectedDate:t,onNavigate:s}=e;return(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4",children:[(0,a.jsxs)("button",{onClick:()=>{let e=t||(0,f.GP)(new Date,"yyyy-MM-dd");s("/schedule?date=".concat(e,"&operatories=DL01,DL02,DL3A"))},className:"bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center text-sm",children:[(0,a.jsx)(n.A,{className:"w-4 h-4 sm:w-5 sm:h-5 mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Dr. Lowell's Operatories"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Dr. Lowell"})]}),(0,a.jsxs)("button",{onClick:()=>{s("/operatories")},className:"bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center text-sm",children:[(0,a.jsx)(j.A,{className:"w-4 h-4 sm:w-5 sm:h-5 mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Select Operatories"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Operatories"})]}),(0,a.jsxs)("button",{onClick:()=>{s("/schedule")},className:"bg-slate-600 hover:bg-slate-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center text-sm",children:[(0,a.jsx)(n.A,{className:"w-4 h-4 sm:w-5 sm:h-5 mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Full Schedule View"}),(0,a.jsx)("span",{className:"sm:hidden",children:"Schedule"})]})]})}var k=s(1362),w=s(9509);let y={version:"2.1.0-SIMPLE-TRANSCRIPTION",buildDate:"2025-07-16",buildTime:w.env.NEXT_PUBLIC_BUILD_TIME||function(){try{let e=new Date;if(isNaN(e.getTime()))return"1200";return e.toTimeString().slice(0,5).replace(":","")}catch(e){return console.warn("Error formatting time:",e),"1200"}}(),buildNumber:w.env.NEXT_PUBLIC_BUILD_NUMBER||"local",commitHash:"dev"},D=(0,r.lazy)(()=>Promise.all([s.e(8096),s.e(3235),s.e(9715)]).then(s.bind(s,9715)).then(e=>({default:e.VoiceUploadWrapper}))),S=(0,r.lazy)(()=>Promise.all([s.e(8096),s.e(4854),s.e(8836)]).then(s.bind(s,6455)).then(e=>({default:e.VoiceRecordingsTab}))),M=(0,r.lazy)(()=>Promise.all([s.e(8096),s.e(4418)]).then(s.bind(s,4418))),L=(0,r.lazy)(()=>Promise.all([s.e(8096),s.e(4227)]).then(s.bind(s,4227)).then(e=>({default:e.AgentChat}))),T=(0,r.lazy)(()=>Promise.all([s.e(8096),s.e(9233)]).then(s.bind(s,9233)).then(e=>({default:e.ToolsAndSettings}))),C=()=>(0,a.jsxs)("div",{className:"flex justify-center items-center h-64",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"}),(0,a.jsx)("span",{className:"ml-3 text-slate-600 dark:text-slate-400",children:"Loading..."})]});function E(){let e=(0,l.useRouter)(),t=(0,l.useSearchParams)(),[s,u]=(0,r.useState)(()=>{let e=t.get("tab");return e&&["schedule","voice-upload","voice-recordings","clinical-notes","ai-assistant","tools-settings"].includes(e)?e:"tools"===e?"tools-settings":"schedule"}),[g,x]=(0,r.useState)(!1),[b,p]=(0,r.useState)(null),[j,f]=(0,r.useState)("week"),w=new Date,E="".concat(w.getFullYear(),"-").concat(String(w.getMonth()+1).padStart(2,"0"),"-").concat(String(w.getDate()).padStart(2,"0")),[A,P]=(0,r.useState)(E),{theme:R}=(0,k.D)(),U="dark"===R;(0,r.useEffect)(()=>{x(!0)},[]),(0,r.useEffect)(()=>{let e=t.get("tab"),a=e&&["schedule","voice-upload","voice-recordings","clinical-notes","ai-assistant","tools-settings"].includes(e)?e:"tools"===e?"tools-settings":"schedule";a!==s&&u(a)},[t,s]),(0,r.useEffect)(()=>()=>{b&&clearTimeout(b)},[b]),(0,r.useMemo)(()=>[{id:"schedule",label:"Schedule",icon:n.A},{id:"voice-upload",label:"Voice Upload",icon:i.A},{id:"clinical-notes",label:"Clinical Notes",icon:o.A},{id:"ai-assistant",label:"AI Office Manager",icon:c.A},{id:"tools-settings",label:"Tools & Settings",icon:d.A}],[]);let I=(0,r.useCallback)(s=>{b&&clearTimeout(b),p(setTimeout(()=>{u(s);let a=new URLSearchParams(t);a.set("tab",s),e.replace("/?".concat(a.toString()))},150))},[b,e,t]),_=(0,r.useCallback)(async()=>{window.location.reload()},[]);return(0,a.jsx)(h,{onRefresh:_,children:(0,a.jsxs)("div",{className:"min-h-screen bg-slate-50 dark:bg-slate-900",children:[(0,a.jsx)(m.z,{title:"Dentalapp",isHomePage:!0,activeTab:s,onTabChange:e=>I(e)}),(0,a.jsxs)("main",{className:"max-w-4xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6",children:[(()=>{switch(s){case"schedule":return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(v,{selectedDate:A,onDateChange:P,calendarView:j,onViewChange:f,isDarkMode:U,isMounted:g}),(0,a.jsx)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-slate-200 dark:border-slate-700",children:(0,a.jsx)(N,{selectedDate:A,onNavigate:t=>e.push(t)})})]});case"voice-upload":return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)(C,{}),children:(0,a.jsx)(D,{isDarkMode:U})})});case"voice-recordings":return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)(C,{}),children:(0,a.jsx)(S,{isDarkMode:U})})});case"clinical-notes":return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)(C,{}),children:(0,a.jsx)(M,{isDarkMode:U})})});case"ai-assistant":return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)(C,{}),children:(0,a.jsx)(L,{isDarkMode:U})})});case"tools-settings":return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)(C,{}),children:(0,a.jsx)(T,{isDarkMode:U})})});default:return null}})(),(0,a.jsxs)("div",{className:"text-center mt-12 text-slate-500 dark:text-slate-400",children:[(0,a.jsx)("p",{children:"Dentalapp - Powered by Sikka API"}),(0,a.jsxs)("p",{className:"text-xs mt-1 opacity-75",children:[y.version," • #",y.buildNumber," • ",y.buildDate," • ",y.buildTime," • ",y.commitHash.substring(0,7)]})]})]})]})})}function A(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading..."}),children:(0,a.jsx)(E,{})})}},8344:(e,t,s)=>{Promise.resolve().then(s.bind(s,7005))}},e=>{var t=t=>e(e.s=t);e.O(0,[8096,6496,7358],()=>t(8344)),_N_E=e.O()}]);