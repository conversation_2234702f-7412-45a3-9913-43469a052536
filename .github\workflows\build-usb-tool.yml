name: Build USB Transfer Tool

on:
  # Trigger when USB Transfer Tool files change
  push:
    paths:
      - 'USB-Transfer/**'
      - 'build-usb-transfer.py'
      - 'update-usb-version.py'
      - '.github/workflows/build-usb-tool.yml'
  
  # Manual trigger
  workflow_dispatch:
    inputs:
      force_build:
        description: 'Force build even if no changes'
        required: false
        default: 'false'

jobs:
  build-usb-tool:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller requests
    
    - name: Update version information
      run: python update-usb-version.py
    
    - name: Build USB Transfer Tool executable
      run: python build-usb-transfer.py
    
    - name: Verify executable was created
      run: |
        if (Test-Path "public/downloads/usb-dental-transfer.exe") {
          $size = (Get-Item "public/downloads/usb-dental-transfer.exe").Length / 1MB
          Write-Host "✅ Executable created successfully!"
          Write-Host "📦 Size: $([math]::Round($size, 1)) MB"
          Write-Host "📁 Location: public/downloads/usb-dental-transfer.exe"
        } else {
          Write-Host "❌ Executable not found!"
          exit 1
        }
      shell: powershell
    
    - name: Commit and push executable
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add public/downloads/usb-dental-transfer.exe
        
        # Check if there are changes to commit
        if (git diff --staged --quiet) {
          Write-Host "No changes to commit"
        } else {
          $version = python -c "import json; print(json.load(open('package.json'))['version'])"
          git commit -m "Auto-build USB Transfer Tool executable v$version"
          git push
          Write-Host "✅ Executable committed and pushed"
        }
      shell: powershell
    
    - name: Upload artifact (backup)
      uses: actions/upload-artifact@v4
      with:
        name: usb-dental-transfer-exe
        path: public/downloads/usb-dental-transfer.exe
        retention-days: 30

  notify:
    needs: build-usb-tool
    runs-on: ubuntu-latest
    if: success()
    
    steps:
    - name: Notify Success
      run: |
        echo "🎉 USB Transfer Tool built successfully!"
        echo "📥 Available for download from Tools tab"
        echo "🔗 Direct link: /api/tools/usb-transfer/download/usb-dental-transfer.exe"
