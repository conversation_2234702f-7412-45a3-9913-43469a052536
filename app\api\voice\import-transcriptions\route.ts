import { NextRequest, NextResponse } from 'next/server';
import { sql } from '@vercel/postgres';
import { VercelDB } from '@/lib/vercel-db';
import { featureFlags } from '@/lib/feature-flags';

interface ImportResult {
  filename: string;
  status: 'imported' | 'updated' | 'skipped' | 'error';
  transcriptionId?: string;
  error?: string;
}

export async function POST(request: NextRequest) {
  try {
    // Check if new architecture is enabled
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    console.log('📥 Starting transcription import process...');
    
    const { transcriptions, force = false } = await request.json();

    if (!transcriptions || !Array.isArray(transcriptions)) {
      return NextResponse.json({
        error: 'Transcriptions array is required',
        code: 'MISSING_TRANSCRIPTIONS'
      }, { status: 400 });
    }

    const results: ImportResult[] = [];
    let imported = 0;
    let updated = 0;
    let skipped = 0;
    let errors = 0;

    console.log(`📋 Processing ${transcriptions.length} transcriptions for import`);

    for (const transcription of transcriptions) {
      const result: ImportResult = {
        filename: transcription.filename || 'unknown',
        status: 'error'
      };

      try {
        // Validate required fields
        if (!transcription.filename) {
          result.error = 'Missing filename';
          result.status = 'error';
          errors++;
          results.push(result);
          continue;
        }

        // Check if transcription already exists
        const existingResult = await sql`
          SELECT id, updated_at 
          FROM transcriptions 
          WHERE filename = ${transcription.filename}
          AND device_id = ${transcription.deviceId || 'imported'}
        `;

        const exists = existingResult.rows.length > 0;

        if (exists && !force) {
          result.status = 'skipped';
          result.transcriptionId = existingResult.rows[0].id;
          skipped++;
          results.push(result);
          continue;
        }

        // Prepare transcription data
        const transcriptionData = {
          filename: transcription.filename,
          deviceId: transcription.deviceId || 'imported',
          transcriptionText: transcription.transcriptionText || transcription.text || '',
          summaryText: transcription.summaryText || transcription.summary || '',
          confidenceScore: parseFloat(transcription.confidenceScore || transcription.confidence || '0.8'),
          patientId: transcription.patientId,
          metadata: {
            ...transcription.metadata,
            imported: true,
            importDate: new Date().toISOString(),
            originalSource: transcription.source || 'manual_import',
            ...(transcription.fileSize && { fileSize: transcription.fileSize }),
            ...(transcription.duration && { duration: transcription.duration }),
            ...(transcription.processingTime && { processingTime: transcription.processingTime })
          }
        };

        if (exists) {
          // Update existing transcription
          const updateResult = await sql`
            UPDATE transcriptions 
            SET 
              transcription_text = ${transcriptionData.transcriptionText},
              summary_text = ${transcriptionData.summaryText},
              confidence_score = ${transcriptionData.confidenceScore},
              patient_id = ${transcriptionData.patientId},
              metadata = ${JSON.stringify(transcriptionData.metadata)},
              updated_at = NOW()
            WHERE filename = ${transcription.filename}
            AND device_id = ${transcriptionData.deviceId}
            RETURNING id
          `;

          result.transcriptionId = updateResult.rows[0].id;
          result.status = 'updated';
          updated++;

          console.log(`🔄 Updated transcription: ${transcription.filename}`);

        } else {
          // Create new transcription
          const createResult = await VercelDB.createTranscription(transcriptionData);
          
          result.transcriptionId = createResult.id;
          result.status = 'imported';
          imported++;

          console.log(`✅ Imported transcription: ${transcription.filename}`);
        }

        results.push(result);

      } catch (error) {
        console.error(`❌ Failed to import ${transcription.filename}:`, error);
        
        result.error = error instanceof Error ? error.message : 'Unknown error';
        result.status = 'error';
        errors++;
        results.push(result);
      }
    }

    const summary = {
      total: transcriptions.length,
      imported,
      updated,
      skipped,
      errors,
      successRate: Math.round(((imported + updated) / transcriptions.length) * 100)
    };

    console.log(`🎉 Import completed: ${imported} new, ${updated} updated, ${skipped} skipped, ${errors} errors`);

    return NextResponse.json({
      success: errors === 0,
      summary,
      results,
      message: `Import completed: ${summary.successRate}% success rate`
    });

  } catch (error) {
    console.error('❌ Import transcriptions error:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Import failed',
      code: 'IMPORT_ERROR'
    }, { status: 500 });
  }
}

/**
 * GET endpoint to retrieve importable transcriptions from various sources
 */
export async function GET(request: NextRequest) {
  try {
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const source = searchParams.get('source') || 'all';
    const format = searchParams.get('format') || 'json';

    console.log(`📊 Retrieving importable transcriptions from source: ${source}`);

    let transcriptions: any[] = [];

    switch (source) {
      case 'existing':
        // Get existing transcriptions that could be re-imported
        const existingResult = await sql`
          SELECT 
            filename,
            device_id as "deviceId",
            transcription_text as "transcriptionText",
            summary_text as "summaryText",
            confidence_score as "confidenceScore",
            patient_id as "patientId",
            metadata,
            created_at as "createdAt"
          FROM transcriptions
          WHERE metadata->>'imported' = 'true'
          ORDER BY created_at DESC
          LIMIT 100
        `;
        
        transcriptions = existingResult.rows.map(row => ({
          ...row,
          source: 'existing_database'
        }));
        break;

      case 'sample':
        // Generate sample transcription data for testing
        transcriptions = [
          {
            filename: 'sample_20240123_001.wav',
            deviceId: 'sample-device',
            transcriptionText: 'Patient presents for routine cleaning and examination. No complaints of pain or discomfort.',
            summaryText: 'Routine dental cleaning and examination. Patient in good oral health.',
            confidenceScore: 0.92,
            patientId: 'patient-123',
            metadata: {
              source: 'sample_data',
              fileSize: 1024000,
              duration: 45
            }
          },
          {
            filename: 'sample_20240123_002.wav',
            deviceId: 'sample-device',
            transcriptionText: 'Patient reports sensitivity in upper left molar. Examination reveals small cavity requiring filling.',
            summaryText: 'Cavity identified in upper left molar, treatment plan discussed.',
            confidenceScore: 0.88,
            patientId: 'patient-456',
            metadata: {
              source: 'sample_data',
              fileSize: 2048000,
              duration: 120
            }
          }
        ];
        break;

      default:
        // Get all available transcriptions for import validation
        const allResult = await sql`
          SELECT 
            filename,
            device_id as "deviceId",
            transcription_text as "transcriptionText",
            summary_text as "summaryText", 
            confidence_score as "confidenceScore",
            patient_id as "patientId",
            created_at as "createdAt"
          FROM transcriptions
          ORDER BY created_at DESC
          LIMIT 50
        `;
        
        transcriptions = allResult.rows.map(row => ({
          ...row,
          source: 'database'
        }));
    }

    const response = {
      success: true,
      source,
      format,
      count: transcriptions.length,
      transcriptions,
      importEndpoint: '/api/voice/import-transcriptions',
      sampleUsage: {
        method: 'POST',
        body: {
          transcriptions: transcriptions.slice(0, 2),
          force: false
        }
      }
    };

    if (format === 'csv') {
      // Convert to CSV format
      const headers = ['filename', 'deviceId', 'transcriptionText', 'summaryText', 'confidenceScore', 'patientId'];
      const csvRows = [
        headers.join(','),
        ...transcriptions.map(t => headers.map(h => `"${(t[h] || '').toString().replace(/"/g, '""')}"`).join(','))
      ];
      
      return new Response(csvRows.join('\n'), {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="transcriptions_${source}_${Date.now()}.csv"`
        }
      });
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Get importable transcriptions error:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to retrieve transcriptions',
      code: 'RETRIEVE_ERROR'
    }, { status: 500 });
  }
}