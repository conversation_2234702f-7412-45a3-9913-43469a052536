import { NextRequest, NextResponse } from 'next/server';

/**
 * Debug Environment Variables Endpoint
 * Shows which environment variables are available for debugging Vercel issues
 * IMPORTANT: Remove this endpoint after debugging is complete
 */
export async function GET(request: NextRequest) {
  try {
    // Only allow in development or with specific debug header
    const isDevelopment = process.env.NODE_ENV === 'development';
    const hasDebugHeader = request.headers.get('x-debug-auth') === 'dental123';
    
    if (!isDevelopment && !hasDebugHeader) {
      return NextResponse.json({
        error: 'Debug endpoint not available',
        message: 'This endpoint is only available in development or with debug auth header'
      }, { status: 403 });
    }

    // Check feature flag environment variables (safe to expose)
    const featureFlagVars = {
      // Current feature flags (server-side)
      FEATURE_FLAG_TRANSCRIPTION_ONLY: process.env.FEATURE_FLAG_TRANSCRIPTION_ONLY,
      FEATURE_FLAG_VERCEL_POSTGRES: process.env.FEATURE_FLAG_VERCEL_POSTGRES,
      FEATURE_FLAG_USE_AZURE_BLOB_STORAGE: process.env.FEATURE_FLAG_USE_AZURE_BLOB_STORAGE,
      FEATURE_FLAG_USE_AZURE_DATA_TABLES: process.env.FEATURE_FLAG_USE_AZURE_DATA_TABLES,
      FEATURE_FLAG_USE_AZURE_SQL_DATABASE: process.env.FEATURE_FLAG_USE_AZURE_SQL_DATABASE,
      
      // Runtime feature flags (client-side accessible)
      NEXT_PUBLIC_FEATURE_FLAG_TRANSCRIPTION_ONLY: process.env.NEXT_PUBLIC_FEATURE_FLAG_TRANSCRIPTION_ONLY,
      NEXT_PUBLIC_FEATURE_FLAG_VERCEL_POSTGRES: process.env.NEXT_PUBLIC_FEATURE_FLAG_VERCEL_POSTGRES,
      NEXT_PUBLIC_FEATURE_FLAG_USE_AZURE_BLOB_STORAGE: process.env.NEXT_PUBLIC_FEATURE_FLAG_USE_AZURE_BLOB_STORAGE,
      NEXT_PUBLIC_FEATURE_FLAG_USE_AZURE_DATA_TABLES: process.env.NEXT_PUBLIC_FEATURE_FLAG_USE_AZURE_DATA_TABLES,
      NEXT_PUBLIC_FEATURE_FLAG_USE_AZURE_SQL_DATABASE: process.env.NEXT_PUBLIC_FEATURE_FLAG_USE_AZURE_SQL_DATABASE,
    };

    // Check for sensitive environment variables (masked)
    const sensitiveVars = {
      POSTGRES_URL: process.env.POSTGRES_URL ? '***SET***' : 'NOT_SET',
      VERCEL_POSTGRES_URL: process.env.VERCEL_POSTGRES_URL ? '***SET***' : 'NOT_SET',
      NODE_ENV: process.env.NODE_ENV,
      VERCEL: process.env.VERCEL,
      VERCEL_ENV: process.env.VERCEL_ENV,
      VERCEL_GIT_COMMIT_SHA: process.env.VERCEL_GIT_COMMIT_SHA,
    };

    // Import feature flags to see computed values
    const { featureFlags } = await import('@/lib/feature-flags');

    return NextResponse.json({
      debug_info: {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        vercel_env: process.env.VERCEL_ENV,
        deployment_url: process.env.VERCEL_URL || 'localhost'
      },
      feature_flag_env_vars: featureFlagVars,
      sensitive_vars: sensitiveVars,
      computed_feature_flags: featureFlags,
      troubleshooting: {
        vercel_postgres_enabled: featureFlags.enableVercelPostgres,
        transcription_only_enabled: featureFlags.enableTranscriptionOnlyWorkflow,
        next_steps: [
          'If feature flags show as false despite env vars being set, check Vercel dashboard',
          'Ensure environment variables are set for correct environment (production/preview)',
          'Try redeploying after environment variable changes',
          'Check if variables have NEXT_PUBLIC_ prefix for client-side access'
        ]
      }
    });

  } catch (error) {
    console.error('❌ Environment debug check failed:', error);
    
    return NextResponse.json({
      error: 'Environment debug failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}