"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { PageHeader } from "@/components/ui/page-header";
import { EnhancedCalendar } from "@/components/ui/enhanced-calendar";
import { Operatory } from "@/lib/api/types";
import { format } from "date-fns";

function OperatoriesContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dateParam = searchParams.get("date");

  // Initialize date from URL parameter or use today's date
  const today = new Date();

  // Create a new Date object with the time set to noon to avoid timezone issues
  const initialDate = dateParam
    ? new Date(`${dateParam}T12:00:00.000Z`)
    : today;

  console.log(`Operatories page: initializing with date ${dateParam || 'today'}, parsed as ${initialDate.toISOString()}`);

  // Store the original date parameter for later use
  const [originalDateParam] = useState(dateParam || format(today, "yyyy-MM-dd"));
  console.log(`Operatories page: storing original date parameter: ${originalDateParam}`);

  const [date, setDate] = useState<Date>(initialDate);
  const [operatories, setOperatories] = useState<Operatory[]>([]);
  // Initialize selected operatories from URL parameters or default to DL01 and DL02
  const [selectedOperatories, setSelectedOperatories] = useState<string[]>(() => {
    const operatoryParams = searchParams.getAll('operatory');
    return operatoryParams.length > 0 ? operatoryParams : ["DL01", "DL02"];
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch operatories for the selected date and handle URL parameters
  useEffect(() => {
    // Handle pre-selected operatories from URL
    const operatoryParams = searchParams.getAll('operatory');
    if (operatoryParams.length > 0) {
      setSelectedOperatories(operatoryParams);
    }

    const fetchOperatories = async () => {
      setLoading(true);
      setError(null);
      setOperatories([]); // Clear previous operatories

      try {
        // Use the date from the URL parameter or format today's date
        const dateToUse = dateParam || format(new Date(), "yyyy-MM-dd");
        console.log(`Fetching operatories for date: ${dateToUse}`);

        // Add a timestamp to prevent caching
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/operatories?date=${dateToUse}&_t=${timestamp}`);

        if (response.ok) {
          const data = await response.json();
          
          if (!Array.isArray(data)) {
            throw new Error('Invalid response format: expected an array of operatories');
          }
          
          // The API should now only return operatories with appointments
          // But we'll still filter out DAZ1 and DAZ2 just in case
          const filteredOperatories = data.filter(op => 
            op && op.id && op.id !== 'DAZ1' && op.id !== 'DAZ2'
          );

          console.log(`Found ${filteredOperatories.length} operatories with appointments for ${dateToUse}`);
          
          if (filteredOperatories.length === 0) {
            console.warn('No operatories with appointments found for the selected date');
            setError('No operatories with appointments found for the selected date');
          }
          
          setOperatories(filteredOperatories);
        } else {
          const errorText = await response.text();
          console.warn(`API call failed with status: ${response.status}`, errorText);
          throw new Error(`Failed to fetch operatories: ${response.status} - ${errorText}`);
        }
      } catch (err) {
        console.error("Error fetching operatories:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch operatories");
      } finally {
        setLoading(false);
      }
    };

    fetchOperatories();
  }, [dateParam, searchParams]); // Depend on searchParams to detect URL changes

  // Handle date change
  const handleDateChange = (newDate: Date) => {
    console.log(`Operatories page: date changed to ${newDate.toISOString()}`);

    // Update state with the new date
    setDate(newDate);

    // Reset selected operatories when date changes
    setSelectedOperatories(["DL01", "DL02"]); // Default to DL01 and DL02 selected

    // Update URL with new date - this will trigger the useEffect to fetch operatories
    const formattedDate = format(newDate, "yyyy-MM-dd");
    console.log(`Operatories page: updating URL with formatted date ${formattedDate}`);

    // Use replace instead of push to avoid adding to history stack
    router.replace(`/operatories?date=${formattedDate}`);
  };

  // Toggle operatory selection
  const toggleOperatory = (operatoryId: string) => {
    setSelectedOperatories((prev) => {
      if (prev.includes(operatoryId)) {
        return prev.filter(id => id !== operatoryId);
      } else {
        return [...prev, operatoryId];
      }
    });
  };

  // Select all operatories
  const selectAll = () => {
    setSelectedOperatories(operatories.map(op => op.id));
  };

  // Clear all selections
  const clearAll = () => {
    setSelectedOperatories([]);
  };

  // Handle next button click
  const handleNext = () => {
    if (selectedOperatories.length === 0) {
      alert("Please select at least one operatory");
      return;
    }

    // Always use the selected date from the state
    const formattedDate = format(date, "yyyy-MM-dd");
    console.log(`Operatories page: Current date object: ${date.toISOString()}`);
    console.log(`Operatories page: Current URL date parameter: ${dateParam}`);
    console.log(`Operatories page: Original date parameter: ${originalDateParam}`);
    console.log(`Operatories page: Navigating to schedule with date: ${formattedDate}`);

    // Navigate to schedule page with the selected date and operatories
    // Build query string manually
    let url = `/schedule?date=${formattedDate}`;

    // Add each operatory as a separate query parameter
    selectedOperatories.forEach(op => {
      url += `&operatories[]=${encodeURIComponent(op)}`;
    });

    console.log(`Operatories page: Navigating to: ${url}`);

    // Use simple string navigation
    router.push(url);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      <PageHeader
        title="Dentalapp"
        showBackButton
        onBackClick={() => router.push('/')}
        backButtonLabel="Back to Home"
        activeTab="schedule"
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-2xl mx-auto bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="mb-6">
            <EnhancedCalendar initialDate={date} onDateChange={handleDateChange} />
          </div>

          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Select Operatories
          </h2>

          {loading ? (
            <div className="animate-pulse flex space-x-4 mb-6">
              <div className="flex-1 space-y-4 py-1">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
                </div>
              </div>
            </div>
          ) : error ? (
            <div className="text-red-500 dark:text-red-400 mb-6">
              Error: {error}
            </div>
          ) : operatories.length === 0 ? (
            <div className="text-gray-500 dark:text-gray-400 mb-6">
              No operatories found for this date.
            </div>
          ) : (
            <div className="mb-6">
              <div className="flex justify-end mb-2">
                <button
                  onClick={selectAll}
                  className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded mr-2"
                >
                  Select All
                </button>
                <button
                  onClick={clearAll}
                  className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded"
                >
                  Clear All
                </button>
              </div>

              <div className="flex flex-wrap gap-2">
                {operatories.map((operatory) => (
                  <button
                    key={operatory.id}
                    type="button"
                    onClick={() => toggleOperatory(operatory.id)}
                    className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      selectedOperatories.includes(operatory.id)
                        ? "bg-blue-600 text-white"
                        : "bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"
                    }`}
                  >
                    {operatory.name}
                  </button>
                ))}
              </div>
            </div>
          )}

          <div className="flex justify-end">
            <button
              onClick={handleNext}
              disabled={loading || operatories.length === 0 || selectedOperatories.length === 0}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}

export default function OperatoriesPage() {
  return (
    <Suspense fallback={<div>Loading operatories...</div>}>
      <OperatoriesContent />
    </Suspense>
  );
}
