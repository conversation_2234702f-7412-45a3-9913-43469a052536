import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import { existsSync } from 'fs';
import path from 'path';
import crypto from 'crypto';

interface TransferProgress {
  currentFile: string;
  filesProcessed: number;
  totalFiles: number;
  status: 'processing' | 'completed' | 'error';
  errors: string[];
}

const AUDIO_EXTENSIONS = ['.wav', '.mp3', '.m4a', '.aac', '.flac'];
const METADATA_EXTENSIONS = ['.tmk']; // Sony time mark files - transfer with audio files
const ALL_TRANSFER_EXTENSIONS = [...AUDIO_EXTENSIONS, ...METADATA_EXTENSIONS];
const SERVER_SHARE_ROOT = '\\\\192.168.0.2\\share\\RECORDINGS';
const LOCAL_BACKUP_ROOT = path.join(process.cwd(), 'voice-recordings');

// Helper function to calculate SHA-256 checksum
async function calculateChecksum(filePath: string): Promise<string> {
  const fileBuffer = await fs.readFile(filePath);
  const hashSum = crypto.createHash('sha256');
  hashSum.update(fileBuffer);
  return hashSum.digest('hex');
}

// Helper function to verify copy
async function verifyCopy(srcPath: string, dstPath: string): Promise<boolean> {
  try {
    if (!existsSync(dstPath)) return false;
    
    const srcStats = await fs.stat(srcPath);
    const dstStats = await fs.stat(dstPath);
    
    if (srcStats.size !== dstStats.size) return false;
    
    // Verify checksums
    const srcHash = await calculateChecksum(srcPath);
    const dstHash = await calculateChecksum(dstPath);
    
    return srcHash === dstHash;
  } catch (error) {
    console.error('Error verifying copy:', error);
    return false;
  }
}

// Helper function to get logical date from filename
function getLogicalDate(filename: string): string {
  // Try to parse date from filename prefix YYYYMMDD
  if (filename.length >= 8 && /^\d{8}/.test(filename)) {
    try {
      const dateStr = filename.substring(0, 8);
      const year = dateStr.substring(0, 4);
      const month = dateStr.substring(4, 6);
      const day = dateStr.substring(6, 8);
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      return date.toISOString().split('T')[0];
    } catch (error) {
      // Fall through to YYMMDD format
    }
  }

  // Try to parse date from filename prefix YYMMDD (2-digit year)
  if (filename.length >= 6 && /^\d{6}/.test(filename)) {
    try {
      const dateStr = filename.substring(0, 6);
      const year = parseInt(dateStr.substring(0, 2));
      const month = dateStr.substring(2, 4);
      const day = dateStr.substring(4, 6);

      // Handle device clock issues and year parsing
      let fullYear;
      if (year === 26) {
        // Special case: Device was set to 2026 when it should be 2025
        fullYear = 2025;
        console.log(`Date correction: ${filename} - device year 26 corrected to 2025`);
      } else if (year < 50) {
        fullYear = 2000 + year;
      } else {
        fullYear = 1900 + year;
      }
      const date = new Date(fullYear, parseInt(month) - 1, parseInt(day));
      return date.toISOString().split('T')[0];
    } catch (error) {
      // Fall through to current date
    }
  }

  // Fallback to current date
  return new Date().toISOString().split('T')[0];
}

// Helper function to write receipt
async function writeReceipt(dstPath: string, deviceId: string): Promise<void> {
  const receipt = {
    file_name: path.basename(dstPath),
    device_id: deviceId,
    imported_at: new Date().toISOString(),
  };
  
  const receiptPath = dstPath.replace(path.extname(dstPath), '.json');
  await fs.writeFile(receiptPath, JSON.stringify(receipt, null, 2), 'utf-8');
}

// Helper function to move file to trash
async function moveToTrash(srcPath: string, usbFolder01Path: string, logicalDate: string, deviceId: string): Promise<void> {
  // Create trash folder within FOLDER01: D:\REC_FILE\FOLDER01\Trash\YYYYMMDD\device_id\
  const trashDir = path.join(usbFolder01Path, 'Trash', logicalDate, deviceId);
  await fs.mkdir(trashDir, { recursive: true });

  const filename = path.basename(srcPath);
  const trashPath = path.join(trashDir, filename);

  await fs.rename(srcPath, trashPath);
}

// Helper function to purge old trash (30+ days)
async function purgeOldTrash(usbFolder01Path: string): Promise<void> {
  const trashDir = path.join(usbFolder01Path, 'Trash');
  if (!existsSync(trashDir)) return;

  const now = new Date();
  const entries = await fs.readdir(trashDir, { withFileTypes: true });

  for (const entry of entries) {
    if (!entry.isDirectory()) continue;

    try {
      // Try to parse as YYYYMMDD date format
      if (entry.name.length === 8 && /^\d{8}$/.test(entry.name)) {
        const year = parseInt(entry.name.substring(0, 4));
        const month = parseInt(entry.name.substring(4, 6)) - 1; // Month is 0-based
        const day = parseInt(entry.name.substring(6, 8));
        const folderDate = new Date(year, month, day);

        const ageDays = Math.floor((now.getTime() - folderDate.getTime()) / (1000 * 60 * 60 * 24));

        if (ageDays >= 30) {
          const folderPath = path.join(trashDir, entry.name);
          await fs.rm(folderPath, { recursive: true, force: true });
          console.log(`Purged old trash folder: ${folderPath}`);
        }
      }
    } catch (error) {
      console.error(`Error processing trash folder ${entry.name}:`, error);
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    const { usbPath, deviceId } = await request.json();
    
    if (!usbPath || !deviceId) {
      return NextResponse.json({ 
        error: 'USB path and device ID are required' 
      }, { status: 400 });
    }
    
    // For the USB structure D:\REC_FILE\FOLDER01, the recordings are directly in FOLDER01
    const recordingsDir = usbPath; // FOLDER01 contains the recordings directly

    if (!existsSync(recordingsDir)) {
      return NextResponse.json({
        error: `USB recordings folder not found: ${recordingsDir}. Please check if USB drive is connected and contains REC_FILE/FOLDER01.`
      }, { status: 404 });
    }
    
    // Get all files to transfer (audio + metadata)
    const files = await fs.readdir(recordingsDir);
    const transferFiles = files.filter(file =>
      ALL_TRANSFER_EXTENSIONS.includes(path.extname(file).toLowerCase())
    );
    
    if (transferFiles.length === 0) {
      return NextResponse.json({
        message: 'No files found to transfer',
        filesProcessed: 0,
        totalFiles: 0
      });
    }

    const progress: TransferProgress = {
      currentFile: '',
      filesProcessed: 0,
      totalFiles: transferFiles.length,
      status: 'processing',
      errors: []
    };

    // Process each file
    for (const filename of transferFiles) {
      progress.currentFile = filename;
      
      try {
        const srcPath = path.join(recordingsDir, filename);
        const logicalDate = getLogicalDate(filename);
        
        // Try network share first, fallback to local
        let dstPath: string;
        let useNetwork = true;
        
        try {
          const serverFolder = path.join(SERVER_SHARE_ROOT, logicalDate, deviceId);
          await fs.mkdir(serverFolder, { recursive: true });
          dstPath = path.join(serverFolder, filename);
        } catch (networkError) {
          console.log('Network share unavailable, using local backup');
          useNetwork = false;
          const localFolder = path.join(LOCAL_BACKUP_ROOT, logicalDate, deviceId);
          await fs.mkdir(localFolder, { recursive: true });
          dstPath = path.join(localFolder, filename);
        }
        
        // Check if file already exists
        if (existsSync(dstPath)) {
          console.log(`File already exists: ${dstPath}`);
          progress.filesProcessed++;
          continue;
        }
        
        // Copy file
        await fs.copyFile(srcPath, dstPath);
        
        // Verify copy
        if (await verifyCopy(srcPath, dstPath)) {
          // Write receipt
          await writeReceipt(dstPath, deviceId);
          
          // Move original to trash
          await moveToTrash(srcPath, usbPath, logicalDate, deviceId);
          
          console.log(`Transferred and verified: ${filename}`);
        } else {
          progress.errors.push(`Verification failed for ${filename}`);
          // Remove failed copy
          if (existsSync(dstPath)) {
            await fs.unlink(dstPath);
          }
        }
        
        progress.filesProcessed++;
        
      } catch (error) {
        console.error(`Error processing ${filename}:`, error);
        progress.errors.push(`Error processing ${filename}: ${error}`);
        progress.filesProcessed++;
      }
    }
    
    // Purge old trash
    try {
      await purgeOldTrash(usbPath);
    } catch (error) {
      console.error('Error purging old trash:', error);
    }
    
    progress.status = progress.errors.length > 0 ? 'error' : 'completed';
    progress.currentFile = '';
    
    return NextResponse.json(progress);
    
  } catch (error) {
    console.error('Transfer error:', error);
    return NextResponse.json({ 
      error: 'Transfer failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: 'Audio transfer endpoint. Use POST to start transfer.' 
  });
}
