import { NextRequest, NextResponse } from 'next/server';
import { sql } from '@vercel/postgres';
import { VercelDB } from '@/lib/vercel-db';
import { featureFlags } from '@/lib/feature-flags';

interface SmartMatchResult {
  recordingId: string;
  filename: string;
  extractedDate?: string;
  confidence: number;
  patientName?: string;
  visitType?: string;
  suggestedAppointments?: any[];
  reasoning: string[];
  transcriptionText?: string;
  summaryText?: string;
}

interface AppointmentMatch {
  sikka_id: string;
  patient_name: string;
  provider_name: string;
  operatory_name: string;
  appointment_date: string;
  appointment_time: string;
  confidence: number;
  reasoning: string[];
}

export async function POST(request: NextRequest) {
  try {
    // Check if new architecture is enabled
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { recordingIds } = await request.json();

    if (!recordingIds || !Array.isArray(recordingIds)) {
      return NextResponse.json({
        error: 'Recording IDs array is required',
        code: 'MISSING_RECORDING_IDS'
      }, { status: 400 });
    }

    console.log(`🧠 Starting smart matching for ${recordingIds.length} recordings`);

    const results: SmartMatchResult[] = [];

    // Process each recording
    for (const recordingId of recordingIds) {
      try {
        const matchResult = await performSmartMatch(recordingId);
        results.push(matchResult);
      } catch (error) {
        console.error(`❌ Failed to match recording ${recordingId}:`, error);
        results.push({
          recordingId,
          filename: 'unknown',
          confidence: 0,
          reasoning: [`Error: ${error instanceof Error ? error.message : 'Unknown error'}`]
        });
      }
    }

    const successfulMatches = results.filter(r => r.confidence > 0.5).length;
    
    console.log(`✅ Smart matching completed: ${successfulMatches}/${results.length} successful matches`);

    return NextResponse.json({
      success: true,
      results,
      summary: {
        totalRecordings: recordingIds.length,
        successfulMatches,
        averageConfidence: results.reduce((sum, r) => sum + r.confidence, 0) / results.length
      }
    });

  } catch (error) {
    console.error('❌ Smart matching error:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Smart matching failed',
      code: 'SMART_MATCH_ERROR'
    }, { status: 500 });
  }
}

async function performSmartMatch(recordingId: string): Promise<SmartMatchResult> {
  // Get the recording details from Vercel Postgres
  const recordingResult = await sql`
    SELECT 
      id,
      filename,
      device_id,
      transcription_text,
      summary_text,
      confidence_score,
      created_at,
      metadata
    FROM transcriptions 
    WHERE id = ${recordingId}
  `;

  if (recordingResult.rows.length === 0) {
    throw new Error(`Recording ${recordingId} not found`);
  }

  const recording = recordingResult.rows[0];
  const reasoning: string[] = [];

  // Extract date from filename
  const extractedDate = extractDateFromFilename(recording.filename, reasoning);
  
  // Analyze transcription content for patient clues
  const contentAnalysis = analyzeTranscriptionContent(
    recording.transcription_text || '', 
    recording.summary_text || '',
    reasoning
  );

  // Find matching appointments
  const suggestedAppointments = await findMatchingAppointments(
    extractedDate,
    contentAnalysis,
    reasoning
  );

  // Calculate overall confidence
  const confidence = calculateMatchConfidence(
    extractedDate,
    contentAnalysis,
    suggestedAppointments,
    recording.confidence_score
  );

  return {
    recordingId,
    filename: recording.filename,
    extractedDate,
    confidence,
    patientName: contentAnalysis.patientName,
    visitType: contentAnalysis.visitType,
    suggestedAppointments,
    reasoning,
    transcriptionText: recording.transcription_text,
    summaryText: recording.summary_text
  };
}

function extractDateFromFilename(filename: string, reasoning: string[]): string | undefined {
  // Try different date patterns in filename
  const patterns = [
    /(\d{4})(\d{2})(\d{2})/,  // YYYYMMDD
    /(\d{2})(\d{2})(\d{2})/,  // YYMMDD
    /(\d{4})-(\d{2})-(\d{2})/, // YYYY-MM-DD
    /(\d{2})-(\d{2})-(\d{4})/  // MM-DD-YYYY
  ];

  for (const pattern of patterns) {
    const match = filename.match(pattern);
    if (match) {
      let year, month, day;
      
      if (pattern.source.includes('4')) { // YYYY format
        [, year, month, day] = match;
        if (year.length === 2) {
          year = `20${year}`;
        }
      } else { // YY format or other patterns
        if (match.length === 4) { // YYMMDD
          [, year, month, day] = match;
          year = `20${year}`;
        } else { // MM-DD-YYYY or similar
          [, month, day, year] = match;
        }
      }

      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      if (!isNaN(date.getTime())) {
        const dateStr = date.toISOString().split('T')[0];
        reasoning.push(`Extracted date ${dateStr} from filename pattern`);
        return dateStr;
      }
    }
  }

  reasoning.push('No valid date pattern found in filename');
  return undefined;
}

function analyzeTranscriptionContent(transcription: string, summary: string, reasoning: string[]): {
  patientName?: string;
  visitType?: string;
  keywords: string[];
} {
  const content = `${transcription} ${summary}`.toLowerCase();
  const keywords: string[] = [];

  // Look for common dental visit types
  const visitTypes = {
    'cleaning': ['cleaning', 'prophylaxis', 'hygiene'],
    'checkup': ['checkup', 'exam', 'examination', 'check-up'],
    'filling': ['filling', 'restoration', 'cavity', 'decay'],
    'crown': ['crown', 'cap'],
    'extraction': ['extraction', 'pull', 'remove'],
    'root canal': ['root canal', 'endodontic'],
    'consultation': ['consultation', 'consult', 'discuss']
  };

  let visitType: string | undefined;
  for (const [type, terms] of Object.entries(visitTypes)) {
    if (terms.some(term => content.includes(term))) {
      visitType = type;
      keywords.push(...terms.filter(term => content.includes(term)));
      break;
    }
  }

  if (visitType) {
    reasoning.push(`Identified visit type: ${visitType} based on content analysis`);
  }

  // Look for patient name patterns (very basic)
  const namePatterns = [
    /patient\s+(\w+)/i,
    /mr\.?\s+(\w+)/i,
    /mrs\.?\s+(\w+)/i,
    /ms\.?\s+(\w+)/i
  ];

  let patientName: string | undefined;
  for (const pattern of namePatterns) {
    const match = content.match(pattern);
    if (match) {
      patientName = match[1];
      reasoning.push(`Potential patient name found: ${patientName}`);
      break;
    }
  }

  return {
    patientName,
    visitType,
    keywords
  };
}

async function findMatchingAppointments(
  extractedDate: string | undefined,
  contentAnalysis: any,
  reasoning: string[]
): Promise<AppointmentMatch[]> {
  if (!extractedDate) {
    reasoning.push('No date available for appointment matching');
    return [];
  }

  try {
    // Search for appointments on the extracted date
    const appointmentsResult = await sql`
      SELECT 
        sikka_id,
        patient_name,
        provider_name,
        operatory_name,
        appointment_date,
        appointment_time,
        duration,
        status
      FROM appointments 
      WHERE DATE(appointment_date) = ${extractedDate}
      ORDER BY appointment_time
    `;

    const appointments = appointmentsResult.rows;
    reasoning.push(`Found ${appointments.length} appointments on ${extractedDate}`);

    if (appointments.length === 0) {
      return [];
    }

    // Score each appointment based on content analysis
    const matches: AppointmentMatch[] = appointments.map(apt => {
      const matchReasons: string[] = [];
      let confidence = 0.6; // Base confidence for date match

      // Check patient name similarity
      if (contentAnalysis.patientName && apt.patient_name) {
        const nameSimilarity = calculateStringSimilarity(
          contentAnalysis.patientName.toLowerCase(),
          apt.patient_name.toLowerCase()
        );
        
        if (nameSimilarity > 0.8) {
          confidence += 0.3;
          matchReasons.push(`High patient name similarity (${Math.round(nameSimilarity * 100)}%)`);
        } else if (nameSimilarity > 0.5) {
          confidence += 0.1;
          matchReasons.push(`Moderate patient name similarity (${Math.round(nameSimilarity * 100)}%)`);
        }
      }

      // Check visit type relevance (basic implementation)
      if (contentAnalysis.visitType) {
        matchReasons.push(`Visit type: ${contentAnalysis.visitType}`);
        confidence += 0.1;
      }

      return {
        sikka_id: apt.sikka_id,
        patient_name: apt.patient_name,
        provider_name: apt.provider_name,
        operatory_name: apt.operatory_name,
        appointment_date: apt.appointment_date,
        appointment_time: apt.appointment_time,
        confidence: Math.min(confidence, 1.0),
        reasoning: matchReasons
      };
    });

    // Sort by confidence
    matches.sort((a, b) => b.confidence - a.confidence);

    reasoning.push(`Generated ${matches.length} appointment matches`);
    return matches.slice(0, 5); // Return top 5 matches

  } catch (error) {
    reasoning.push(`Error searching appointments: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return [];
  }
}

function calculateMatchConfidence(
  extractedDate: string | undefined,
  contentAnalysis: any,
  suggestedAppointments: AppointmentMatch[],
  transcriptionConfidence: number
): number {
  let confidence = 0;

  // Base confidence from transcription quality
  confidence += (transcriptionConfidence || 0.5) * 0.3;

  // Date extraction confidence
  if (extractedDate) {
    confidence += 0.4;
  }

  // Content analysis confidence
  if (contentAnalysis.patientName) {
    confidence += 0.2;
  }
  if (contentAnalysis.visitType) {
    confidence += 0.1;
  }

  // Appointment match confidence
  if (suggestedAppointments.length > 0) {
    const bestMatch = suggestedAppointments[0];
    confidence += bestMatch.confidence * 0.3;
  }

  return Math.min(confidence, 1.0);
}

function calculateStringSimilarity(str1: string, str2: string): number {
  // Simple string similarity using Levenshtein distance
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i;
  }
  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j;
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const substitutionCost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + substitutionCost // substitution
      );
    }
  }

  const maxLength = Math.max(str1.length, str2.length);
  return maxLength === 0 ? 1 : (maxLength - matrix[str2.length][str1.length]) / maxLength;
}