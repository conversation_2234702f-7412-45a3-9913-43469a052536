import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 Auto-process-new: Checking for newly uploaded files...');
    
    // Check if processing is already running
    const statusResponse = await fetch(`${getBaseUrl()}/api/voice/process-batch-polling`, {
      headers: { 'X-Internal-Call': 'true' }
    });
    
    if (statusResponse.ok) {
      const statusData = await statusResponse.json();
      if (statusData.success && statusData.state?.isProcessing) {
        console.log('✅ Auto-process-new: Batch processing already running, no action needed');
        return NextResponse.json({
          success: true,
          message: 'Batch processing already in progress',
          alreadyRunning: true,
          state: statusData.state
        });
      }
    }
    
    // Check for unprocessed files (recently uploaded)
    const recordingsResponse = await fetch(`${getBaseUrl()}/api/voice/recordings`, {
      headers: { 'X-Internal-Call': 'true' }
    });
    
    if (!recordingsResponse.ok) {
      throw new Error(`Failed to fetch recordings: ${recordingsResponse.status}`);
    }
    
    const recordingsData = await recordingsResponse.json();
    const recordings = recordingsData.recordings || recordingsData;
    
    // Count unprocessed files (uploaded recently - within last hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentlyUploaded = recordings.filter((r: any) => {
      const uploadTime = new Date(r.createdAt || r.date);
      return uploadTime > oneHourAgo && (!r.transcription || r.transcription.length === 0);
    });
    
    const totalUnprocessed = recordings.filter((r: any) => 
      !r.transcription || r.transcription.length === 0 || 
      (r.transcription && (!r.clinical_summary || r.clinical_summary.length === 0))
    ).length;
    
    console.log(`📊 Auto-process-new: Found ${recentlyUploaded.length} recently uploaded files, ${totalUnprocessed} total unprocessed`);
    
    // Only start auto-processing if there are recently uploaded files
    if (recentlyUploaded.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No recently uploaded files need processing',
        recentlyUploaded: 0,
        totalUnprocessed: totalUnprocessed,
        autoProcessingTriggered: false
      });
    }
    
    // Start processing for all unprocessed files (including the new ones)
    console.log(`🚀 Auto-process-new: Starting background processing for ${totalUnprocessed} files (${recentlyUploaded.length} recently uploaded)...`);
    const startResponse = await fetch(`${getBaseUrl()}/api/voice/process-batch-polling`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'X-Internal-Call': 'true' 
      },
      body: JSON.stringify({ action: 'start' })
    });
    
    if (!startResponse.ok) {
      const errorText = await startResponse.text();
      throw new Error(`Failed to start processing: ${startResponse.status} - ${errorText}`);
    }
    
    const startData = await startResponse.json();
    console.log('✅ Auto-process-new: Background processing started successfully');
    
    return NextResponse.json({
      success: true,
      message: `Started processing ${totalUnprocessed} files (${recentlyUploaded.length} recently uploaded)`,
      recentlyUploaded: recentlyUploaded.length,
      totalUnprocessed: totalUnprocessed,
      autoProcessingTriggered: true,
      startResponse: startData
    });
    
  } catch (error) {
    console.error('❌ Auto-process-new error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    // Return status of recent uploads and auto-processing
    const recordingsResponse = await fetch(`${getBaseUrl()}/api/voice/recordings`, {
      headers: { 'X-Internal-Call': 'true' }
    });
    
    if (!recordingsResponse.ok) {
      throw new Error(`Failed to fetch recordings: ${recordingsResponse.status}`);
    }
    
    const recordingsData = await recordingsResponse.json();
    const recordings = recordingsData.recordings || recordingsData;
    
    // Check for recently uploaded files
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentlyUploaded = recordings.filter((r: any) => {
      const uploadTime = new Date(r.createdAt || r.date);
      return uploadTime > oneHourAgo;
    });
    
    const recentlyUploadedUnprocessed = recentlyUploaded.filter((r: any) => 
      !r.transcription || r.transcription.length === 0
    );
    
    return NextResponse.json({
      success: true,
      recentlyUploaded: recentlyUploaded.length,
      recentlyUploadedUnprocessed: recentlyUploadedUnprocessed.length,
      autoProcessingRecommended: recentlyUploadedUnprocessed.length > 0
    });
    
  } catch (error) {
    console.error('❌ Auto-process-new status error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

function getBaseUrl(): string {
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL;
  }
  return 'http://localhost:3000';
}