import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { AzureStorageService } from '../../../src/lib/azure-storage';

// File-based storage for user settings
const SETTINGS_FILE = path.join(process.cwd(), 'data', 'user-settings.json');

interface UserSettings {
  transcriptionModel: string;
  transcriptionPrompt: string;
  summarizationModel: string;
  summarizationPrompt: string;
  customTranscriptionPrompt: string;
  customSummarizationPrompt: string;
}

const DEFAULT_SETTINGS: UserSettings = {
  transcriptionModel: 'whisper-1',
  transcriptionPrompt: 'dental-transcription-context',
  summarizationModel: 'gpt-4o-mini-2024-07-18',
  summarizationPrompt: 'brief-summary',
  customTranscriptionPrompt: '',
  customSummarizationPrompt: ''
};

// Load settings from Azure or local file
async function loadSettings(): Promise<UserSettings> {
  try {
    let settingsData = DEFAULT_SETTINGS;

    // Try loading from Azure first if configured
    if (AzureStorageService.isConfigured()) {
      try {
        settingsData = await AzureStorageService.downloadJson('metadata/user-settings.json');
        console.log('Loaded user settings from Azure');
      } catch (azureError) {
        console.log('No user settings found in Azure, trying local file...');
        // Fall back to local file
        try {
          const data = await fs.readFile(SETTINGS_FILE, 'utf-8');
          settingsData = { ...DEFAULT_SETTINGS, ...JSON.parse(data) };
          console.log('Loaded user settings from local file');
        } catch (localError) {
          console.log('No local settings file found, using defaults');
        }
      }
    } else {
      // Try local file only
      try {
        const data = await fs.readFile(SETTINGS_FILE, 'utf-8');
        settingsData = { ...DEFAULT_SETTINGS, ...JSON.parse(data) };
        console.log('Loaded user settings from local file');
      } catch (error) {
        console.log('No settings file found, using defaults');
      }
    }

    return settingsData;
  } catch (error) {
    console.error('Failed to load settings:', error);
    return DEFAULT_SETTINGS;
  }
}

// Save settings to Azure and local file
async function saveSettings(settings: UserSettings): Promise<void> {
  try {
    // Save to local file
    const dataDir = path.dirname(SETTINGS_FILE);
    await fs.mkdir(dataDir, { recursive: true });
    await fs.writeFile(SETTINGS_FILE, JSON.stringify(settings, null, 2));
    console.log('Settings saved to local file');

    // Also save to Azure if configured
    if (AzureStorageService.isConfigured()) {
      await AzureStorageService.uploadJson('metadata/user-settings.json', settings);
      console.log('Settings saved to Azure Blob Storage');
    }
  } catch (error) {
    console.error('Failed to save settings:', error);
    throw error;
  }
}

// GET - Load user settings
export async function GET(request: NextRequest) {
  try {
    const settings = await loadSettings();
    
    return NextResponse.json({
      success: true,
      settings
    });
  } catch (error: any) {
    console.error('Get settings error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to load settings',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// POST - Save user settings
export async function POST(request: NextRequest) {
  try {
    const { settings } = await request.json();

    if (!settings) {
      return NextResponse.json(
        { error: 'Settings data is required' },
        { status: 400 }
      );
    }

    // Validate settings structure
    const validatedSettings: UserSettings = {
      transcriptionModel: settings.transcriptionModel || DEFAULT_SETTINGS.transcriptionModel,
      transcriptionPrompt: settings.transcriptionPrompt || DEFAULT_SETTINGS.transcriptionPrompt,
      summarizationModel: settings.summarizationModel || DEFAULT_SETTINGS.summarizationModel,
      summarizationPrompt: settings.summarizationPrompt || DEFAULT_SETTINGS.summarizationPrompt,
      customTranscriptionPrompt: settings.customTranscriptionPrompt || '',
      customSummarizationPrompt: settings.customSummarizationPrompt || ''
    };

    await saveSettings(validatedSettings);

    return NextResponse.json({
      success: true,
      message: 'Settings saved successfully',
      settings: validatedSettings
    });
  } catch (error: any) {
    console.error('Save settings error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to save settings',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Helper function to get current settings (for use by other APIs)
export async function getCurrentSettings(): Promise<UserSettings> {
  return await loadSettings();
}
