import { NextRequest, NextResponse } from 'next/server';
import { CreateRecordingRequest, CreateRecordingResponse, RecordingStatus, JobType, JobPriority } from '@/types/recordings';
import { executeQuery, withTransaction, DatabaseConnection } from '@/lib/database/index';

// Validation function
function validateCreateRecordingRequest(data: any): data is CreateRecordingRequest {
  if (!data || typeof data !== 'object') {
    return false;
  }

  const required = ['filename', 'original_filename', 'blob_url', 'blob_path', 'file_size'];
  for (const field of required) {
    if (!data[field]) {
      return false;
    }
  }

  // Validate file_size is a positive number
  if (typeof data.file_size !== 'number' || data.file_size <= 0) {
    return false;
  }

  // Validate duration_seconds if provided
  if (data.duration_seconds !== undefined && (typeof data.duration_seconds !== 'number' || data.duration_seconds < 0)) {
    return false;
  }

  // Validate blob_url format
  try {
    new URL(data.blob_url);
  } catch {
    return false;
  }

  return true;
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body = await request.json();
    
    // Validate request data
    if (!validateCreateRecordingRequest(body)) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          message: 'Required fields: filename, original_filename, blob_url, blob_path, file_size. file_size must be positive number.',
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    const {
      filename,
      original_filename,
      blob_url,
      blob_path,
      file_size,
      duration_seconds,
      metadata
    }: CreateRecordingRequest = body;

    // Use withTransaction for atomicity
    const recordingId = await withTransaction(async (transaction) => {
      // Generate new GUID for recording
      const newRecordingId = crypto.randomUUID();
      const currentTime = new Date();

      // Insert recording record
      await executeQuery(`
        INSERT INTO recordings (
          id, filename, original_filename, blob_url, blob_path, file_size, 
          duration_seconds, status, metadata, created_at, updated_at
        ) VALUES (
          @id, @filename, @original_filename, @blob_url, @blob_path, @file_size,
          @duration_seconds, @status, @metadata, @created_at, @updated_at
        )
      `, {
        id: newRecordingId,
        filename,
        original_filename,
        blob_url,
        blob_path,
        file_size,
        duration_seconds: duration_seconds || null,
        status: RecordingStatus.PENDING,
        metadata: metadata ? JSON.stringify(metadata) : null,
        created_at: currentTime,
        updated_at: currentTime
      }, transaction);

      // Check if auto-processing is enabled
      const autoProcessEnabled = process.env.FEATURE_FLAG_AUTO_PROCESS === 'true' || 
                                process.env.ENABLE_AUTO_PROCESSING === 'true';

      if (autoProcessEnabled) {
        // Create initial job record for auto-processing
        const jobId = crypto.randomUUID();
        
        await executeQuery(`
          INSERT INTO jobs (
            id, recording_id, job_type, status, priority, progress_percentage,
            retry_count, max_retries, created_at, updated_at
          ) VALUES (
            @job_id, @recording_id, @job_type, @status, @priority, @progress_percentage,
            @retry_count, @max_retries, @job_created_at, @job_updated_at
          )
        `, {
          job_id: jobId,
          recording_id: newRecordingId,
          job_type: JobType.FULL_PROCESS,
          status: RecordingStatus.PENDING,
          priority: JobPriority.NORMAL,
          progress_percentage: 0,
          retry_count: 0,
          max_retries: 3,
          job_created_at: currentTime,
          job_updated_at: currentTime
        }, transaction);
      }
      return newRecordingId;
    });

    // Log successful creation
    console.log(`Recording created successfully: ${recordingId}`, {
      filename,
      file_size,
      auto_process_enabled: process.env.FEATURE_FLAG_AUTO_PROCESS === 'true' || process.env.ENABLE_AUTO_PROCESSING === 'true',
      job_created: true, // Assuming job is always created if auto-process is enabled
    });

    // Return success response
    const response: CreateRecordingResponse = {
      recording_id: recordingId,
      status: RecordingStatus.PENDING,
      message: (process.env.FEATURE_FLAG_AUTO_PROCESS === 'true' || process.env.ENABLE_AUTO_PROCESSING === 'true') 
        ? 'Recording created successfully with auto-processing job'
        : 'Recording created successfully',
    };

    return NextResponse.json(response, { status: 201 });

  } catch (error) {
    console.error('Error creating recording:', error);

    // Generic error response
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to create recording',
        code: 'INTERNAL_ERROR',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET(): Promise<NextResponse> {
  try {
    const health = await DatabaseConnection.getInstance().checkDatabaseHealth();
    
    if (health.connected) {
      return NextResponse.json({
        status: 'healthy',
        message: 'Create recording endpoint is operational',
        database_connected: true,
        timestamp: new Date().toISOString(),
      });
    } else {
      return NextResponse.json(
        {
          status: 'unhealthy',
          message: 'Database connection failed',
          database_connected: false,
          error: health.error || 'Unknown error',
          timestamp: new Date().toISOString(),
        },
        { status: 503 }
      );
    }

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        message: 'Database connection failed',
        database_connected: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 503 }
    );
  }
}

// Cleanup function for graceful shutdown
process.on('SIGINT', async () => {
  await DatabaseConnection.getInstance().closeDatabaseConnection();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await DatabaseConnection.getInstance().closeDatabaseConnection();
  process.exit(0);
});