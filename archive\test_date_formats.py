import requests
import json
from datetime import datetime, timedelta
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 30

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def test_date_formats(request_key, date):
    """Test different date formats to see which one works best."""
    headers = {"Request-Key": request_key}
    
    # Try different date formats
    date_formats = [
        date,                                                # YYYY-MM-DD
        date.replace("-", "/"),                              # YYYY/MM/DD
        f"{date.split('-')[1]}/{date.split('-')[2]}/{date.split('-')[0]}",  # MM/DD/YYYY
        f"{date.split('-')[2]}/{date.split('-')[1]}/{date.split('-')[0]}",  # DD/MM/YYYY
        datetime.strptime(date, "%Y-%m-%d").strftime("%m/%d/%Y"),  # MM/DD/YYYY with leading zeros
        datetime.strptime(date, "%Y-%m-%d").strftime("%d/%m/%Y"),  # DD/MM/YYYY with leading zeros
        datetime.strptime(date, "%Y-%m-%d").strftime("%Y%m%d"),    # YYYYMMDD
        datetime.strptime(date, "%Y-%m-%d").strftime("%m-%d-%Y"),  # MM-DD-YYYY
        datetime.strptime(date, "%Y-%m-%d").strftime("%d-%m-%Y"),  # DD-MM-YYYY
    ]
    
    print(f"Testing date formats for {date}:")
    
    for date_format in date_formats:
        print(f"\nTrying date format: {date_format}")
        
        # Try v2 endpoint
        try:
            params = {"date": date_format}
            resp = requests.get(
                f"{API_BASE_V2}/appointments", 
                headers=headers, 
                params=params,
                timeout=API_TIMEOUT
            )
            
            if resp.status_code == 200:
                data = resp.json()
                
                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    print(f"  V2 endpoint: Found {len(items)} appointments")
                    
                    # Print sample appointments
                    if items:
                        print("  Sample appointments:")
                        for i, appt in enumerate(sorted(items, key=lambda a: a.get("time", ""))[:3]):
                            print(f"    {i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('operatory', 'N/A')} - {appt.get('description', '')}")
                else:
                    print("  V2 endpoint: No appointments found")
            else:
                print(f"  V2 endpoint: Error {resp.status_code}")
                print(f"  {resp.text}")
        except Exception as e:
            print(f"  V2 endpoint: Error - {e}")
        
        # Try v4 endpoint
        try:
            params = {"date": date_format}
            resp = requests.get(
                f"{API_BASE_V4}/appointments", 
                headers=headers, 
                params=params,
                timeout=API_TIMEOUT
            )
            
            if resp.status_code == 200:
                data = resp.json()
                items = data.get("items", [])
                print(f"  V4 endpoint: Found {len(items)} appointments")
                
                # Print sample appointments
                if items:
                    print("  Sample appointments:")
                    for i, appt in enumerate(sorted(items, key=lambda a: a.get("time", ""))[:3]):
                        print(f"    {i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('operatory', 'N/A') if 'operatory' in appt else 'No operatory'} - {appt.get('description', '')}")
            else:
                print(f"  V4 endpoint: Error {resp.status_code}")
                print(f"  {resp.text}")
        except Exception as e:
            print(f"  V4 endpoint: Error - {e}")

def test_time_formats(request_key, date):
    """Test different time formats to see which one works best."""
    headers = {"Request-Key": request_key}
    
    # Try different time formats with the best date format
    time_formats = [
        None,           # No time parameter
        "08:00",        # HH:MM
        "08:00:00",     # HH:MM:SS
        "08:00-17:00",  # HH:MM-HH:MM
        "08:00-17:00",  # HH:MM-HH:MM
        "0800-1700",    # HHMM-HHMM
        "8-17",         # H-H
    ]
    
    print(f"\nTesting time formats for {date}:")
    
    for time_format in time_formats:
        print(f"\nTrying time format: {time_format}")
        
        # Try v2 endpoint
        try:
            params = {"date": date}
            if time_format:
                params["time"] = time_format
            
            resp = requests.get(
                f"{API_BASE_V2}/appointments", 
                headers=headers, 
                params=params,
                timeout=API_TIMEOUT
            )
            
            if resp.status_code == 200:
                data = resp.json()
                
                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    print(f"  V2 endpoint: Found {len(items)} appointments")
                    
                    # Print sample appointments
                    if items:
                        print("  Sample appointments:")
                        for i, appt in enumerate(sorted(items, key=lambda a: a.get("time", ""))[:3]):
                            print(f"    {i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('operatory', 'N/A')} - {appt.get('description', '')}")
                else:
                    print("  V2 endpoint: No appointments found")
            else:
                print(f"  V2 endpoint: Error {resp.status_code}")
                print(f"  {resp.text}")
        except Exception as e:
            print(f"  V2 endpoint: Error - {e}")

def test_pagination(request_key, date):
    """Test pagination to see if we can get more than 50 appointments."""
    headers = {"Request-Key": request_key}
    
    print(f"\nTesting pagination for {date}:")
    
    # Try v2 endpoint with different offsets
    offsets = [0, 50, 100, 150, 200]
    
    for offset in offsets:
        print(f"\nTrying offset: {offset}")
        
        try:
            params = {
                "date": date,
                "offset": offset,
                "limit": 50
            }
            
            resp = requests.get(
                f"{API_BASE_V2}/appointments", 
                headers=headers, 
                params=params,
                timeout=API_TIMEOUT
            )
            
            if resp.status_code == 200:
                data = resp.json()
                
                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    print(f"  Found {len(items)} appointments")
                    
                    # Print sample appointments
                    if items:
                        print("  Sample appointments:")
                        for i, appt in enumerate(sorted(items, key=lambda a: a.get("time", ""))[:3]):
                            print(f"    {i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('operatory', 'N/A')} - {appt.get('description', '')}")
                else:
                    print("  No appointments found")
            else:
                print(f"  Error {resp.status_code}")
                print(f"  {resp.text}")
        except Exception as e:
            print(f"  Error - {e}")
    
    # Try v4 endpoint with pagination
    print("\nTesting v4 endpoint pagination:")
    
    offset = 0
    limit = 50
    total_items = 0
    
    while True:
        try:
            params = {
                "date": date,
                "offset": offset,
                "limit": limit
            }
            
            resp = requests.get(
                f"{API_BASE_V4}/appointments", 
                headers=headers, 
                params=params,
                timeout=API_TIMEOUT
            )
            
            if resp.status_code == 200:
                data = resp.json()
                items = data.get("items", [])
                total_items += len(items)
                
                print(f"  Offset {offset}: Found {len(items)} appointments")
                
                if len(items) < limit:
                    break
                
                offset += limit
            else:
                print(f"  Error {resp.status_code}")
                print(f"  {resp.text}")
                break
        except Exception as e:
            print(f"  Error - {e}")
            break
    
    print(f"  Total appointments from v4 endpoint: {total_items}")

def main():
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Get date from command line or use default
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        date = "2023-05-16"  # Default date
    
    # Test different date formats
    test_date_formats(request_key, date)
    
    # Test different time formats
    test_time_formats(request_key, date)
    
    # Test pagination
    test_pagination(request_key, date)
    
    print("\nTesting complete.")

if __name__ == "__main__":
    main()
