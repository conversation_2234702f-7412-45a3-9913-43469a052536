name: Deploy to Dental Server (DISABLED - Using Azure)

on:
  # Disabled - using Azure App Service instead
  # push:
  #   branches: [ main ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: self-hosted
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Stop dental app service
      run: |
        pm2 stop dental-app || echo "Service not running"
        
    - name: Install dependencies
      run: npm install --production
      
    - name: Use prebuilt .next if available, otherwise build
      run: |
        if [ -d .next ]; then
          echo "Using prebuilt .next directory, skipping build."
        else
          npm run build
        fi
      
    - name: Start dental app service
      run: |
        pm2 start .next/standalone/server.js --name dental-app || pm2 restart dental-app
        pm2 save
        
    - name: Verify deployment
      run: |
        timeout /t 10 /nobreak
        pm2 list
        echo "Deployment completed successfully!"
        echo "App available at: http://localhost:3000"
