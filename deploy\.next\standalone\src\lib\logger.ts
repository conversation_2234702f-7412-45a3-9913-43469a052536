/**
 * Structured Logging System for Voice Processing Pipeline
 * 
 * This module provides consistent, searchable logs across all components with
 * support for multiple log levels, context-aware logging, and monitoring service integration.
 */

import { ProcessingJob, JobErrorDetails } from '@/types/queue';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogContext {
  jobId?: string;
  filename?: string;
  operation?: string;
  stage?: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  traceId?: string;
  duration?: number;
  retryCount?: number;
  errorCode?: string;
  apiName?: string;
  model?: string;
  [key: string]: any;
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context: LogContext;
  correlationId?: string;
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
  performance?: {
    startTime: number;
    endTime: number;
    duration: number;
  };
  metadata: {
    version: string;
    environment: string;
    hostname: string;
    pid: number;
  };
}

/**
 * Main logger class with context-aware logging
 */
export class Logger {
  private correlationId: string;
  private baseContext: LogContext;

  constructor(baseContext: LogContext = {}) {
    this.baseContext = baseContext;
    this.correlationId = this.generateCorrelationId();
  }

  /**
   * Create a child logger with additional context
   */
  child(additionalContext: LogContext): Logger {
    const childLogger = new Logger({
      ...this.baseContext,
      ...additionalContext
    });
    childLogger.correlationId = this.correlationId;
    return childLogger;
  }

  /**
   * Log debug message
   */
  debug(message: string, context: LogContext = {}): void {
    this.log('debug', message, context);
  }

  /**
   * Log info message
   */
  info(message: string, context: LogContext = {}): void {
    this.log('info', message, context);
  }

  /**
   * Log warning message
   */
  warn(message: string, context: LogContext = {}): void {
    this.log('warn', message, context);
  }

  /**
   * Log error message
   */
  error(message: string, context: LogContext = {}, error?: Error): void {
    this.log('error', message, context, error);
  }

  /**
   * Log API request start
   */
  apiRequest(apiName: string, context: LogContext = {}): void {
    this.info(`${apiName} API request started`, {
      ...context,
      apiName,
      operation: 'api_request'
    });
  }

  /**
   * Log API response
   */
  apiResponse(apiName: string, duration: number, context: LogContext = {}): void {
    this.info(`${apiName} API request completed`, {
      ...context,
      apiName,
      duration,
      operation: 'api_response'
    });
  }

  /**
   * Log API error
   */
  apiError(apiName: string, error: Error, context: LogContext = {}): void {
    this.error(`${apiName} API request failed`, {
      ...context,
      apiName,
      operation: 'api_error'
    }, error);
  }

  /**
   * Log job lifecycle events
   */
  jobCreated(job: ProcessingJob): void {
    this.info('Job created', {
      jobId: job.id,
      filename: job.filename,
      status: job.status,
      operation: 'job_created'
    });
  }

  jobStarted(job: ProcessingJob): void {
    this.info('Job started', {
      jobId: job.id,
      filename: job.filename,
      retryCount: job.retryCount,
      operation: 'job_started'
    });
  }

  jobProgress(jobId: string, stage: string, progress: any): void {
    this.debug('Job progress update', {
      jobId,
      stage,
      ...progress,
      operation: 'job_progress'
    });
  }

  jobCompleted(job: ProcessingJob, duration?: number): void {
    this.info('Job completed successfully', {
      jobId: job.id,
      filename: job.filename,
      duration,
      operation: 'job_completed'
    });
  }

  jobFailed(job: ProcessingJob, errorDetails: JobErrorDetails): void {
    this.error('Job failed', {
      jobId: job.id,
      filename: job.filename,
      errorCode: errorDetails.code,
      category: errorDetails.category,
      retryable: errorDetails.retryable,
      retryCount: job.retryCount,
      operation: 'job_failed'
    });
  }

  jobRetrying(job: ProcessingJob, retryDelay: number): void {
    this.warn('Job being retried', {
      jobId: job.id,
      filename: job.filename,
      retryCount: job.retryCount,
      retryDelay,
      operation: 'job_retrying'
    });
  }

  /**
   * Log performance metrics
   */
  performance(operation: string, duration: number, context: LogContext = {}): void {
    this.info(`Performance: ${operation}`, {
      ...context,
      operation: 'performance',
      duration,
      performanceMetric: operation
    });
  }

  /**
   * Log security events
   */
  security(event: string, context: LogContext = {}): void {
    this.warn(`Security event: ${event}`, {
      ...context,
      operation: 'security',
      securityEvent: event
    });
  }

  /**
   * Core logging method
   */
  private log(level: LogLevel, message: string, context: LogContext = {}, error?: Error): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: {
        ...this.baseContext,
        ...context
      },
      correlationId: this.correlationId,
      metadata: {
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        hostname: process.env.HOSTNAME || 'unknown',
        pid: process.pid
      }
    };

    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code
      };
    }

    this.writeLog(entry);
  }

  /**
   * Determine if we should log at this level
   */
  private shouldLog(level: LogLevel): boolean {
    const configLevel = process.env.LOG_LEVEL || 'info';
    const levels = ['debug', 'info', 'warn', 'error'];
    const configIndex = levels.indexOf(configLevel);
    const messageIndex = levels.indexOf(level);
    
    return messageIndex >= configIndex;
  }

  /**
   * Write log entry to configured outputs
   */
  private writeLog(entry: LogEntry): void {
    // Console output with appropriate level
    const formatted = this.formatForConsole(entry);
    
    switch (entry.level) {
      case 'debug':
        console.debug(formatted);
        break;
      case 'info':
        console.info(formatted);
        break;
      case 'warn':
        console.warn(formatted);
        break;
      case 'error':
        console.error(formatted);
        break;
    }

    // Send to monitoring services (async, non-blocking)
    this.sendToMonitoring(entry).catch(err => {
      console.error('Failed to send log to monitoring:', err);
    });
  }

  /**
   * Format log entry for console output
   */
  private formatForConsole(entry: LogEntry): string {
    if (process.env.NODE_ENV === 'production') {
      // JSON format for production
      return JSON.stringify(entry);
    } else {
      // Human-readable format for development
      const timestamp = entry.timestamp;
      const level = entry.level.toUpperCase().padEnd(5);
      const message = entry.message;
      
      // Add context if present
      const contextKeys = Object.keys(entry.context);
      const context = contextKeys.length > 0 
        ? ` | ${contextKeys.map(k => `${k}=${entry.context[k]}`).join(' ')}`
        : '';
      
      // Add error if present
      const error = entry.error 
        ? ` | ERROR: ${entry.error.message}`
        : '';

      return `${timestamp} ${level} ${message}${context}${error}`;
    }
  }

  /**
   * Send log to monitoring services
   */
  private async sendToMonitoring(entry: LogEntry): Promise<void> {
    // Application Insights integration
    if (process.env.APPINSIGHTS_INSTRUMENTATIONKEY && entry.level === 'error') {
      try {
        // Would integrate with Application Insights SDK here
        // const appInsights = require('applicationinsights');
        // appInsights.defaultClient.trackException({
        //   exception: entry.error,
        //   properties: {
        //     ...entry.context,
        //     correlationId: entry.correlationId,
        //     environment: entry.metadata.environment
        //   }
        // });
      } catch (error) {
        console.error('Failed to send to Application Insights:', error);
      }
    }

    // Sentry integration
    if (process.env.SENTRY_DSN && (entry.level === 'error' || entry.level === 'warn')) {
      try {
        // Would integrate with Sentry SDK here
        // const Sentry = require('@sentry/node');
        // Sentry.withScope((scope) => {
        //   scope.setLevel(entry.level);
        //   scope.setContext('voice_processing', entry.context);
        //   scope.setTag('correlationId', entry.correlationId);
        //   if (entry.error) {
        //     Sentry.captureException(new Error(entry.error.message));
        //   } else {
        //     Sentry.captureMessage(entry.message);
        //   }
        // });
      } catch (error) {
        console.error('Failed to send to Sentry:', error);
      }
    }
  }

  /**
   * Generate correlation ID for request tracing
   */
  private generateCorrelationId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Performance timer utility
 */
export class PerformanceTimer {
  private startTime: number;
  private logger: Logger;
  private operation: string;
  private context: LogContext;

  constructor(logger: Logger, operation: string, context: LogContext = {}) {
    this.logger = logger;
    this.operation = operation;
    this.context = context;
    this.startTime = Date.now();
  }

  /**
   * End timing and log performance
   */
  end(): number {
    const duration = Date.now() - this.startTime;
    this.logger.performance(this.operation, duration, this.context);
    return duration;
  }

  /**
   * Get elapsed time without logging
   */
  getElapsed(): number {
    return Date.now() - this.startTime;
  }
}

/**
 * Create logger for specific contexts
 */
export function createJobLogger(jobId: string, filename?: string): Logger {
  return new Logger({ jobId, filename });
}

export function createApiLogger(apiName: string, requestId?: string): Logger {
  return new Logger({ apiName, requestId });
}

export function createOperationLogger(operation: string, context: LogContext = {}): Logger {
  return new Logger({ operation, ...context });
}

/**
 * Wrapper function for timing operations
 */
export async function withTiming<T>(
  logger: Logger,
  operation: string,
  fn: () => Promise<T>,
  context: LogContext = {}
): Promise<T> {
  const timer = new PerformanceTimer(logger, operation, context);
  
  try {
    logger.debug(`Starting ${operation}`, context);
    const result = await fn();
    const duration = timer.end();
    logger.debug(`Completed ${operation}`, { ...context, duration });
    return result;
  } catch (error) {
    const duration = timer.getElapsed();
    logger.error(`Failed ${operation}`, { ...context, duration }, error as Error);
    throw error;
  }
}

/**
 * Log structured error with context
 */
export function logError(
  logger: Logger,
  error: any,
  operation: string,
  context: LogContext = {}
): void {
  const errorDetails: JobErrorDetails = {
    code: error.code || 'UNKNOWN',
    message: error.message || 'Unknown error',
    category: classifyError(error),
    retryable: isRetryableError(error),
    stackTrace: error.stack,
    context: {
      operation,
      ...context
    }
  };

  logger.error(`Error in ${operation}`, {
    ...context,
    errorCode: errorDetails.code,
    category: errorDetails.category,
    retryable: errorDetails.retryable
  }, error);
}

/**
 * Classify error for structured logging
 */
function classifyError(error: any): JobErrorDetails['category'] {
  if (error.status === 429 || error.message?.includes('rate limit')) {
    return 'quota_exceeded';
  }
  
  if (error.status >= 400 && error.status < 500) {
    return 'api_error';
  }
  
  if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
    return 'network_error';
  }
  
  if (error.code === 'ENOENT' || error.message?.includes('not found')) {
    return 'file_not_found';
  }
  
  if (error.message?.includes('validation') || error.message?.includes('invalid')) {
    return 'validation_error';
  }
  
  return 'system_error';
}

/**
 * Determine if error is retryable
 */
function isRetryableError(error: any): boolean {
  // Rate limiting is retryable
  if (error.status === 429) return true;
  
  // Server errors are typically retryable
  if (error.status >= 500) return true;
  
  // Network errors are retryable
  if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') return true;
  
  // Client errors are typically not retryable
  if (error.status >= 400 && error.status < 500) return false;
  
  // File not found is not retryable
  if (error.code === 'ENOENT') return false;
  
  // Default to not retryable for safety
  return false;
}

// Export default logger instance
export const logger = new Logger();

// Export utility functions
export { LogLevel, LogContext, LogEntry };