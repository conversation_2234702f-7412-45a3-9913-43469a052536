@echo off
REM Complete Operatory Daily View
REM This batch file runs the complete_operatory_view.py script with the specified parameters

echo Complete Operatory Daily View
echo ----------------------------------

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Python is not installed or not in your PATH.
    echo Please install Python from https://www.python.org/downloads/
    pause
    exit /b
)

REM Check if required packages are installed
python -c "import requests" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing required packages...
    pip install requests
)

REM Get date (default to today)
set /p date="Enter date (YYYY-MM-DD) [today]: "
if "%date%"=="" (
    for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (
        set mm=%%a
        set dd=%%b
        set yy=%%c
    )
    set date=%yy%-%mm%-%dd%
)

REM Run the script to get available operatories
echo Fetching available operatories for %date%...
python complete_operatory_view.py %date% > operatories_temp.txt

REM Extract operatories from the output
findstr /C:"Operatories found in appointments:" operatories_temp.txt > operatories_line.txt
for /f "tokens=*" %%a in (operatories_line.txt) do set operatories_line=%%a
set operatories_list=%operatories_line:Operatories found in appointments: =%

REM Ask if user wants to filter by operatories
set /p filter_ops="Do you want to filter by specific operatories? (y/n) [n]: "
if /i "%filter_ops%"=="y" (
    echo Available operatories: %operatories_list%
    set /p operatories="Enter comma-separated list of operatories (e.g., DL01,DL02): "
    if not "%operatories%"=="" (
        set operatory_flag=--operatories=%operatories%
    ) else (
        set operatory_flag=
    )
) else (
    set operatory_flag=
)

REM Ask if user wants to show MODNO appointments
set /p show_modno="Do you want to show MODNO appointments in the schedule? (y/n) [n]: "
if /i "%show_modno%"=="y" (
    set modno_flag=--show-modno
) else (
    set modno_flag=
)

REM Run the script
echo Running complete operatory view for %date%...
python complete_operatory_view.py %date% %operatory_flag% %modno_flag%

REM Clean up temporary files
del operatories_temp.txt
del operatories_line.txt

pause
