import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import { existsSync } from 'fs';
import path from 'path';
import crypto from 'crypto';

interface TransferProgress {
  currentFile: string;
  filesProcessed: number;
  totalFiles: number;
  status: 'processing' | 'completed' | 'error';
  errors: string[];
  currentStep: string;
}

const AUDIO_EXTENSIONS = ['.wav', '.mp3', '.m4a', '.aac', '.flac'];
const METADATA_EXTENSIONS = ['.tmk']; // Sony time mark files - transfer with audio files
const ALL_TRANSFER_EXTENSIONS = [...AUDIO_EXTENSIONS, ...METADATA_EXTENSIONS];
const SERVER_SHARE_ROOT = '\\\\192.168.0.2\\share\\RECORDINGS';
const LOCAL_BACKUP_ROOT = path.join(process.cwd(), 'voice-recordings');

// Helper function to calculate SHA-256 checksum
async function calculateChecksum(filePath: string): Promise<string> {
  const fileBuffer = await fs.readFile(filePath);
  const hashSum = crypto.createHash('sha256');
  hashSum.update(fileBuffer);
  return hashSum.digest('hex');
}

// Helper function to verify copy
async function verifyCopy(srcPath: string, dstPath: string): Promise<boolean> {
  try {
    console.log(`Verifying copy: ${srcPath} -> ${dstPath}`);

    if (!existsSync(dstPath)) {
      console.log(`Destination file does not exist: ${dstPath}`);
      return false;
    }

    const srcStats = await fs.stat(srcPath);
    const dstStats = await fs.stat(dstPath);

    console.log(`Source size: ${srcStats.size}, Destination size: ${dstStats.size}`);

    if (srcStats.size !== dstStats.size) {
      console.log(`Size mismatch: src=${srcStats.size}, dst=${dstStats.size}`);
      return false;
    }

    // Verify checksums
    console.log('Calculating checksums...');
    const srcHash = await calculateChecksum(srcPath);
    const dstHash = await calculateChecksum(dstPath);

    console.log(`Source hash: ${srcHash}`);
    console.log(`Destination hash: ${dstHash}`);

    const hashMatch = srcHash === dstHash;
    console.log(`Hash match: ${hashMatch}`);

    return hashMatch;
  } catch (error) {
    console.error('Error verifying copy:', error);
    return false;
  }
}

// Helper function to get logical date from filename
function getLogicalDate(filename: string): string {
  console.log(`Parsing date from filename: ${filename}`);

  // Try to parse date from filename prefix YYYYMMDD
  if (filename.length >= 8 && /^\d{8}/.test(filename)) {
    try {
      const dateStr = filename.substring(0, 8);
      const year = dateStr.substring(0, 4);
      const month = dateStr.substring(4, 6);
      const day = dateStr.substring(6, 8);
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      const result = date.toISOString().split('T')[0];
      console.log(`Parsed YYYYMMDD format: ${dateStr} -> ${result}`);
      return result;
    } catch (error) {
      console.log(`Failed to parse YYYYMMDD format: ${error}`);
    }
  }

  // Try to parse date from filename prefix YYMMDD (2-digit year)
  if (filename.length >= 6 && /^\d{6}/.test(filename)) {
    try {
      const dateStr = filename.substring(0, 6);
      const year = parseInt(dateStr.substring(0, 2));
      const month = dateStr.substring(2, 4);
      const day = dateStr.substring(4, 6);

      // Handle device clock issues and year parsing
      let fullYear;
      if (year === 26) {
        // Special case: Device was set to 2026 when it should be 2025
        fullYear = 2025;
        console.log(`Date correction: ${filename} - device year 26 corrected to 2025`);
      } else if (year < 50) {
        fullYear = 2000 + year;
      } else {
        fullYear = 1900 + year;
      }
      const date = new Date(fullYear, parseInt(month) - 1, parseInt(day));
      const result = date.toISOString().split('T')[0];
      console.log(`Parsed YYMMDD format: ${dateStr} -> ${result} (year ${fullYear})`);
      return result;
    } catch (error) {
      console.log(`Failed to parse YYMMDD format: ${error}`);
    }
  }

  // Fallback to current date
  const fallback = new Date().toISOString().split('T')[0];
  console.log(`Using fallback date: ${fallback}`);
  return fallback;
}

// Helper function to write receipt
async function writeReceipt(dstPath: string, deviceId: string): Promise<void> {
  const receipt = {
    file_name: path.basename(dstPath),
    device_id: deviceId,
    imported_at: new Date().toISOString(),
    recording_date: getLogicalDate(path.basename(dstPath)),
  };
  
  const receiptPath = dstPath.replace(path.extname(dstPath), '.json');
  await fs.writeFile(receiptPath, JSON.stringify(receipt, null, 2), 'utf-8');
}

// Helper function to move file to trash
async function moveToTrash(srcPath: string, usbFolder01Path: string, logicalDate: string, deviceId: string): Promise<void> {
  const trashDir = path.join(usbFolder01Path, 'Trash', logicalDate, deviceId);
  await fs.mkdir(trashDir, { recursive: true });

  const filename = path.basename(srcPath);
  const trashPath = path.join(trashDir, filename);

  await fs.rename(srcPath, trashPath);
}

// Helper function to purge old trash (30+ days)
async function purgeOldTrash(usbFolder01Path: string): Promise<void> {
  const trashDir = path.join(usbFolder01Path, 'Trash');
  if (!existsSync(trashDir)) return;

  const now = new Date();
  const entries = await fs.readdir(trashDir, { withFileTypes: true });

  for (const entry of entries) {
    if (!entry.isDirectory()) continue;

    try {
      // Try to parse as YYYY-MM-DD date format first
      let folderDate: Date;
      if (entry.name.includes('-') && entry.name.length === 10) {
        folderDate = new Date(entry.name);
      } else if (entry.name.length === 8 && /^\d{8}$/.test(entry.name)) {
        // Parse YYYYMMDD format
        const year = parseInt(entry.name.substring(0, 4));
        const month = parseInt(entry.name.substring(4, 6)) - 1;
        const day = parseInt(entry.name.substring(6, 8));
        folderDate = new Date(year, month, day);
      } else {
        continue;
      }

      const ageDays = Math.floor((now.getTime() - folderDate.getTime()) / (1000 * 60 * 60 * 24));

      if (ageDays >= 30) {
        const folderPath = path.join(trashDir, entry.name);
        await fs.rm(folderPath, { recursive: true, force: true });
        console.log(`Purged old trash folder: ${folderPath}`);
      }
    } catch (error) {
      console.error(`Error processing trash folder ${entry.name}:`, error);
    }
  }
}

export async function POST(request: NextRequest) {
  const { usbPath, deviceId } = await request.json();
  
  if (!usbPath || !deviceId) {
    return NextResponse.json({ 
      error: 'USB path and device ID are required' 
    }, { status: 400 });
  }

  // Create a readable stream for real-time progress
  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder();
      
      const sendProgress = (progress: TransferProgress) => {
        const data = `data: ${JSON.stringify(progress)}\n\n`;
        controller.enqueue(encoder.encode(data));
      };

      try {
        const recordingsDir = usbPath;

        if (!existsSync(recordingsDir)) {
          sendProgress({
            currentFile: '',
            filesProcessed: 0,
            totalFiles: 0,
            status: 'error',
            errors: [`USB recordings folder not found: ${recordingsDir}`],
            currentStep: 'Error'
          });
          controller.close();
          return;
        }

        // Get all files to transfer (audio + metadata)
        const files = await fs.readdir(recordingsDir);
        const transferFiles = files.filter(file =>
          ALL_TRANSFER_EXTENSIONS.includes(path.extname(file).toLowerCase())
        );

        if (transferFiles.length === 0) {
          sendProgress({
            currentFile: '',
            filesProcessed: 0,
            totalFiles: 0,
            status: 'completed',
            errors: [],
            currentStep: 'No files to transfer'
          });
          controller.close();
          return;
        }

        const progress: TransferProgress = {
          currentFile: '',
          filesProcessed: 0,
          totalFiles: transferFiles.length,
          status: 'processing',
          errors: [],
          currentStep: 'Starting transfer...'
        };

        sendProgress(progress);

        // Process each file (audio and metadata)
        for (const filename of transferFiles) {
          progress.currentFile = filename;
          progress.currentStep = 'Copying file...';
          sendProgress(progress);

          try {
            const srcPath = path.join(recordingsDir, filename);
            console.log(`Processing file: ${filename}, full path: ${srcPath}`);

            // Check if source file exists and get info
            if (!existsSync(srcPath)) {
              console.error(`Source file does not exist: ${srcPath}`);
              progress.errors.push(`Source file not found: ${filename}`);
              progress.filesProcessed++;
              continue;
            }

            const srcStats = await fs.stat(srcPath);
            console.log(`Source file stats: size=${srcStats.size}, extension=${path.extname(filename)}`);

            const logicalDate = getLogicalDate(filename);
            console.log(`Logical date for ${filename}: ${logicalDate}`);

            // Try network share first, fallback to local
            let dstPath: string;
            
            try {
              const serverFolder = path.join(SERVER_SHARE_ROOT, logicalDate, deviceId);
              await fs.mkdir(serverFolder, { recursive: true });
              dstPath = path.join(serverFolder, filename);
            } catch (networkError) {
              console.log('Network share unavailable, using local backup');
              const localFolder = path.join(LOCAL_BACKUP_ROOT, logicalDate, deviceId);
              await fs.mkdir(localFolder, { recursive: true });
              dstPath = path.join(localFolder, filename);
            }

            // Check if file already exists
            if (existsSync(dstPath)) {
              progress.currentStep = 'File already exists, skipping...';
              sendProgress(progress);
              progress.filesProcessed++;
              continue;
            }

            // Copy file
            await fs.copyFile(srcPath, dstPath);
            
            progress.currentStep = 'Verifying copy...';
            sendProgress(progress);

            // Verify copy
            const verificationResult = await verifyCopy(srcPath, dstPath);
            console.log(`Verification result for ${filename}: ${verificationResult}`);

            if (verificationResult) {
              progress.currentStep = 'Writing receipt...';
              sendProgress(progress);

              // Write receipt
              await writeReceipt(dstPath, deviceId);

              progress.currentStep = 'Moving to trash...';
              sendProgress(progress);

              // Move original to trash
              await moveToTrash(srcPath, usbPath, logicalDate, deviceId);

              console.log(`Transferred and verified: ${filename}`);
            } else {
              console.error(`Verification failed for ${filename}`);
              progress.errors.push(`Verification failed for ${filename}`);
              // Remove failed copy
              if (existsSync(dstPath)) {
                await fs.unlink(dstPath);
              }
            }

            progress.filesProcessed++;
            progress.currentStep = `Completed ${progress.filesProcessed}/${progress.totalFiles}`;
            sendProgress(progress);

          } catch (error) {
            console.error(`Error processing ${filename}:`, error);
            progress.errors.push(`Error processing ${filename}: ${error}`);
            progress.filesProcessed++;
            sendProgress(progress);
          }
        }

        // Purge old trash
        progress.currentStep = 'Cleaning up old trash...';
        sendProgress(progress);
        
        try {
          await purgeOldTrash(usbPath);
        } catch (error) {
          console.error('Error purging old trash:', error);
        }

        progress.status = progress.errors.length > 0 ? 'error' : 'completed';
        progress.currentFile = '';
        progress.currentStep = progress.status === 'completed' ? 'Transfer completed successfully!' : 'Transfer completed with errors';
        
        sendProgress(progress);
        controller.close();

      } catch (error) {
        console.error('Transfer error:', error);
        sendProgress({
          currentFile: '',
          filesProcessed: 0,
          totalFiles: 0,
          status: 'error',
          errors: [`Transfer failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
          currentStep: 'Error'
        });
        controller.close();
      }
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  });
}
