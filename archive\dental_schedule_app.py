#!/usr/bin/env python3
"""
Dental Schedule Viewer Web App
A web-based application for viewing dental appointments by operatory
"""

import json
import sys
import os
import requests
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify, send_from_directory

# API configuration
API_BASE_V2 = "https://api.sikkasoft.com/v2"
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_TIMEOUT = 30  # seconds

# We'll fetch the real operatories from the API
OPERATORIES = []

app = Flask(__name__)

def load_credentials():
    """Load API credentials from credentials.json file."""
    try:
        with open("credentials.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: credentials.json file not found.")
        print("Please create a credentials.json file with your Sikka API credentials.")
        sys.exit(1)
    except json.JSONDecodeError:
        print("Error: Invalid JSON in credentials.json file.")
        sys.exit(1)

def authenticate():
    """Authenticate with Sikka API and get request key."""
    credentials = load_credentials()
    app_id = credentials.get("app_id")
    app_key = credentials.get("app_key")
    office_id = credentials.get("office_id")
    secret_key = credentials.get("secret_key")

    try:
        auth_resp = requests.post(
            f"{API_BASE_V4}/request_key",
            headers={"Content-Type": "application/json"},
            json={
                "grant_type": "request_key",
                "office_id": office_id,
                "secret_key": secret_key,
                "app_id": app_id,
                "app_key": app_key
            },
            timeout=API_TIMEOUT
        )

        if auth_resp.status_code == 200:
            data = auth_resp.json()
            request_key = data.get("request_key")
            if request_key:
                return request_key
            else:
                print("Error: No request key in response.")
        else:
            print(f"Authentication failed with status code: {auth_resp.status_code}")
    except Exception as e:
        print(f"Error during authentication: {e}")

    return None

def fetch_operatories(request_key, date=None):
    """Fetch the list of operatories from the API for a specific date."""
    headers = {"Request-Key": request_key}

    try:
        # If a date is provided, use it to fetch operatories for that specific date
        # Otherwise, use a 30-day window to get all operatories
        if date:
            params = {
                "startdate": date,
                "enddate": date,
                "date_filter_on": "appointment_date"
            }
        else:
            params = {
                "startdate": (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"),
                "enddate": datetime.now().strftime("%Y-%m-%d"),
                "date_filter_on": "appointment_date"
            }

        resp = requests.get(
            f"{API_BASE_V2}/appointments",
            headers=headers,
            params=params,
            timeout=API_TIMEOUT
        )

        operatories = set()

        if resp.status_code == 200:
            data = resp.json()

            # Extract operatories from appointments
            if isinstance(data, list) and len(data) > 0:
                all_items = data[0].get("items", [])
                for item in all_items:
                    op = item.get("operatory")
                    if op and op.strip():
                        operatories.add(op.strip())

        # Return the unique operatories
        return sorted(list(operatories))
    except Exception as e:
        print(f"Error fetching operatories: {e}")
        # Return a default list if we can't fetch from API
        return ["OP1", "OP2", "OP3"]

def fetch_appointments(request_key, target_date, selected_operatories):
    """Fetch appointments for a specific date and filter by operatories."""
    headers = {"Request-Key": request_key}

    # Set up parameters with the correct date filtering parameters
    params = {
        "startdate": target_date,
        "enddate": target_date,
        "date_filter_on": "appointment_date"
    }

    try:
        resp = requests.get(
            f"{API_BASE_V2}/appointments",
            headers=headers,
            params=params,
            timeout=API_TIMEOUT
        )

        if resp.status_code == 200:
            data = resp.json()

            # Log the raw API response for debugging
            print(f"API Response: {data}")

            # Extract items from v2 response
            if isinstance(data, list) and len(data) > 0:
                all_items = data[0].get("items", [])

                # Log the extracted items
                print(f"Extracted items: {all_items}")

                # Filter by selected operatories
                if selected_operatories:
                    filtered_items = [a for a in all_items if a.get("operatory") in selected_operatories]
                    print(f"Filtered items: {filtered_items}")
                    return filtered_items
                else:
                    return all_items
            else:
                print("No items found in API response")
                return []
        else:
            print(f"Error fetching appointments: {resp.status_code}")
            return []
    except Exception as e:
        print(f"Error: {e}")
        return []

def format_appointments_for_display(appointments):
    """Format appointments for display in the web UI."""
    formatted_appointments = []

    print(f"Formatting {len(appointments)} appointments for display")

    for appt in appointments:
        # Print the raw appointment data for debugging
        print(f"Raw appointment data: {appt}")

        # Extract appointment details
        operatory = appt.get("operatory", "")
        time = appt.get("time", "")
        length = appt.get("length", "0")
        patient_name = appt.get("patient_name", "Unknown Patient")
        provider_id = appt.get("provider_id", "")
        description = appt.get("description", "")
        appt_sr_no = appt.get("appointment_sr_no", "")

        print(f"Extracted details: operatory={operatory}, time={time}, length={length}, patient={patient_name}")

        # Check if this is a blocked time
        is_blocked = "Blocked" in str(appt_sr_no) or not patient_name or patient_name.strip() == ""

        # Parse time (format: "8:00" or "14:30")
        try:
            hour, minute = map(int, time.split(':'))
            # Convert 24-hour format to 12-hour format
            period = "AM" if hour < 12 else "PM"
            display_hour = hour if hour <= 12 else hour - 12
            if display_hour == 0:
                display_hour = 12
            start_time = f"{display_hour}:{minute:02d} {period}"

            # Calculate end time based on length
            total_minutes = hour * 60 + minute + float(length)
            end_hour = int(total_minutes // 60)
            end_minute = int(total_minutes % 60)
            end_period = "AM" if end_hour < 12 else "PM"
            display_end_hour = end_hour if end_hour <= 12 else end_hour - 12
            if display_end_hour == 0:
                display_end_hour = 12
            end_time = f"{display_end_hour}:{end_minute:02d} {end_period}"

            print(f"Parsed time: start={start_time}, end={end_time}")
        except Exception as e:
            # If time parsing fails, use default values
            print(f"Time parsing error: {e}")
            start_time = time
            end_time = time

        # Determine appointment type based on description
        # Use the actual description as the appointment type
        appt_type = description.strip() if description and description.strip() else "Appointment"

        # For blocked time slots
        if is_blocked:
            appt_type = "Blocked"

        # Skip appointments that only say "Blocked" with no other information
        if description.strip().lower() == "blocked" and is_blocked:
            continue

        # Log the appointment type
        print(f"Appointment type: {appt_type} (from description: {description})")

        formatted_appt = {
            "operatory": operatory,
            "patientName": patient_name if not is_blocked else "BLOCKED",
            "type": appt_type,
            "startTime": start_time,
            "endTime": end_time,
            "description": description,
            "provider": provider_id,
            "isBlocked": is_blocked
        }

        print(f"Formatted appointment: {formatted_appt}")
        formatted_appointments.append(formatted_appt)

    return formatted_appointments

@app.route('/')
def index():
    """Serve the main HTML page."""
    return render_template('index.html')

@app.route('/api/operatories', methods=['GET'])
def get_operatories():
    """Return the list of available operatories from the API for a specific date."""
    # Get the date parameter
    date = request.args.get('date')

    # Authenticate with the API
    request_key = authenticate()
    if not request_key:
        return jsonify({"error": "Authentication failed"}), 401

    # Fetch operatories from the API for the specific date
    operatories = fetch_operatories(request_key, date)

    # Format for the frontend
    operatory_list = [{"id": op, "name": op, "active": True} for op in operatories]

    # Update the global OPERATORIES list
    global OPERATORIES
    OPERATORIES = operatories

    return jsonify(operatory_list)

@app.route('/api/appointments', methods=['GET'])
def get_appointments():
    """Fetch appointments for the specified date and operatories."""
    date = request.args.get('date')
    operatories = request.args.getlist('operatories[]')

    print(f"API Request: /api/appointments?date={date}&operatories={operatories}")

    if not date:
        return jsonify({"error": "Date parameter is required"}), 400

    if not operatories:
        return jsonify({"error": "At least one operatory must be selected"}), 400

    # Authenticate with the API
    request_key = authenticate()
    if not request_key:
        return jsonify({"error": "Authentication failed"}), 401

    # Fetch appointments
    appointments = fetch_appointments(request_key, date, operatories)

    # Format appointments for display
    formatted_appointments = format_appointments_for_display(appointments)

    # Print the final formatted appointments
    print(f"Final formatted appointments: {formatted_appointments}")

    return jsonify(formatted_appointments)

# HTML template is now directly in templates/index.html

if __name__ == '__main__':
    # Check if credentials file exists
    if not os.path.exists('credentials.json'):
        print("Error: credentials.json file not found.")
        print("Please create a credentials.json file with your Sikka API credentials.")
        sys.exit(1)

    # Run the Flask app
    app.run(debug=True, port=8080)
