{"deploymentType": "zipdeploy", "timestamp": "2025-07-16T01:46:05.192Z", "buildInfo": {"nodeVersion": "v22.16.0", "platform": "win32", "arch": "x64"}, "keyVaultIntegration": {"enabled": true, "vaultName": "key1000", "managedIdentity": true, "referencesFound": false}, "packageOptimization": {"enabled": true, "totalFilesCopied": 519, "totalFilesSkipped": 2, "sizeBefore": 108300587, "sizeAfter": 43912015, "sizeReduction": 64388572, "sizeReductionPercent": "59.45", "processingTimeMs": 1967, "customExclusionPatterns": null, "directoryOptimization": {"nextStandaloneUsed": true, "nextStaticIncluded": true, "directoriesProcessed": [".next/standalone", ".next/static", "app", "src", "public"]}}}