import 'package:flutter/material.dart';
import 'dart:developer' as developer;
import 'dart:convert';
import 'dart:math' show min;
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';

void main() {
  // Log when the app starts
  developer.log('Starting Dental Viewer app...', name: 'dental_viewer');

  // Add a delay and then log when the app is ready to render
  Future.delayed(const Duration(milliseconds: 100), () {
    developer.log('App initialized and ready to render', name: 'dental_viewer');
  });

  runApp(const DentalViewerApp());
}

class DentalViewerApp extends StatelessWidget {
  const DentalViewerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Dental Viewer',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const DentalScheduleScreen(title: 'Dental Appointment Viewer'),
    );
  }
}

class DentalScheduleScreen extends StatefulWidget {
  const DentalScheduleScreen({super.key, required this.title});

  final String title;

  @override
  State<DentalScheduleScreen> createState() => _DentalScheduleScreenState();
}

class _DentalScheduleScreenState extends State<DentalScheduleScreen> {
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  List<Map<String, dynamic>> _appointments = [];
  final List<String> _selectedOperatories = [];
  final List<String> _availableOperatories = [
    'DAZ1', 'DAZ2', 'DL01', 'DL02', 'NS02', 'X-PS', 'Y-10', 'ZZ01', 'ZZ02'
  ];
  bool _dataFetched = false;

  @override
  void initState() {
    super.initState();
    // Don't load appointments automatically
    // Wait for user to select date and operatories
  }

  void _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2025),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        // Reset data fetched flag when date changes
        _dataFetched = false;
      });
      // Don't automatically load appointments
      // Wait for user to click the Fetch button
    }
  }

  Future<void> _loadAppointments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Format date as YYYY-MM-DD for the API
      final dateString = DateFormat('yyyy-MM-dd').format(_selectedDate);

      // Log the date we're fetching appointments for
      developer.log('Fetching appointments for date: $dateString', name: 'dental_viewer');

      // Since we're using a local JSON file created by our Python script
      // Use a date-specific JSON file
      final jsonUrl = 'http://localhost:8000/appointments_$dateString.json';

      // Log the selected operatories
      developer.log('Selected operatories: ${_selectedOperatories.join(", ")}', name: 'dental_viewer');

      developer.log('Fetching appointments from: $jsonUrl', name: 'dental_viewer');

      // Make the API call to the local server
      final response = await http.get(
        Uri.parse(jsonUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        developer.log('Response data: $data', name: 'dental_viewer');

        try {
          // Try to parse the response data - adjust this based on your API's actual response format
          List<dynamic> appointmentsList;

          // Handle different possible response formats
          if (data is List) {
            // If the response is directly a list of appointments
            appointmentsList = data;
          } else if (data is Map) {
            // If the response is an object with an appointments property
            if (data.containsKey('appointments')) {
              appointmentsList = data['appointments'] as List<dynamic>;
            } else if (data.containsKey('data')) {
              // Some APIs wrap the data in a 'data' property
              appointmentsList = data['data'] as List<dynamic>;
            } else {
              // If we can't find a known property, log the keys and use an empty list
              developer.log('Unknown response format. Keys: ${data.keys.join(', ')}',
                name: 'dental_viewer');
              appointmentsList = [];
            }
          } else {
            // Fallback for unexpected response format
            developer.log('Unexpected response format: ${data.runtimeType}',
              name: 'dental_viewer');
            appointmentsList = [];
          }

          // Filter appointments by the selected date if needed
          // Our JSON file already contains appointments for a specific date,
          // but if we need to filter further, we can do it here
          final dateString = DateFormat('yyyy-MM-dd').format(_selectedDate);
          final jsonDate = data['date'] as String? ?? '';

          developer.log('JSON date: $jsonDate, Selected date: $dateString', name: 'dental_viewer');

          setState(() {
            // Use all appointments from the JSON file
            // The Python script already filtered them by date
            _appointments = appointmentsList
                .map((item) => item as Map<String, dynamic>)
                .toList();
            _isLoading = false;
          });

          developer.log('Loaded ${_appointments.length} appointments for $dateString',
            name: 'dental_viewer');
        } catch (e) {
          developer.log('Error parsing response data: $e', name: 'dental_viewer');
          setState(() {
            _isLoading = false;
            _appointments = [];
          });

          if (mounted) {
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Data Format Error'),
                content: SingleChildScrollView(
                  child: ListBody(
                    children: [
                      const Text('Could not parse the appointment data.'),
                      const SizedBox(height: 10),
                      Text('Error: $e'),
                      const SizedBox(height: 10),
                      Text('Response: ${response.body.substring(0, min(200, response.body.length))}...',
                        style: const TextStyle(fontSize: 12)),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('OK'),
                  ),
                ],
              ),
            );
          }
        }
      } else {
        setState(() {
          _isLoading = false;
          _appointments = [];
        });

        developer.log('Error loading appointments: ${response.statusCode} - ${response.body}',
          name: 'dental_viewer');

        // Show error message to user if widget is still mounted
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('API Error'),
              content: SingleChildScrollView(
                child: ListBody(
                  children: [
                    Text('The server returned an error (${response.statusCode}).'),
                    const SizedBox(height: 10),
                    const Text('Please check:'),
                    const Text('• Is the API endpoint correct?'),
                    const Text('• Does the API expect different parameters?'),
                    const Text('• Is the server running correctly?'),
                    const SizedBox(height: 10),
                    Text('Response: ${response.body}',
                      style: const TextStyle(fontSize: 12)),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('OK'),
                ),
              ],
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _appointments = [];
      });

      // Log detailed error information
      developer.log('Exception loading appointments: $e',
        name: 'dental_viewer');

      // Show error message to user if widget is still mounted
      if (mounted) {
        // Show a more detailed error message
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Connection Error'),
            content: SingleChildScrollView(
              child: ListBody(
                children: [
                  const Text('Could not connect to the backend server.'),
                  const SizedBox(height: 10),
                  const Text('Please check:'),
                  const Text('• Is your backend server running?'),
                  const Text('• Is the URL correct?'),
                  const Text('• Is the API endpoint correct?'),
                  const SizedBox(height: 10),
                  Text('Error details: $e', style: const TextStyle(fontSize: 12)),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _dataFetched ? _loadAppointments : null,
          ),
        ],
      ),
      body: Column(
        children: [
          // Input form for date and operatories
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Date selector
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Date:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: GestureDetector(
                        onTap: _selectDate,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                DateFormat('EEEE, MMMM d, yyyy').format(_selectedDate),
                                style: const TextStyle(fontSize: 16),
                              ),
                              const Icon(Icons.calendar_today, size: 16),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Operatory selector
                const Text(
                  'Select Operatories:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _availableOperatories.map((operatory) {
                    final isSelected = _selectedOperatories.contains(operatory);
                    return FilterChip(
                      label: Text(operatory),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            _selectedOperatories.add(operatory);
                          } else {
                            _selectedOperatories.remove(operatory);
                          }
                        });
                      },
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),

                // Fetch data button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _selectedOperatories.isNotEmpty
                        ? () {
                            setState(() {
                              _dataFetched = true;
                            });
                            _loadAppointments();
                          }
                        : null,
                    child: const Text('Fetch Appointments'),
                  ),
                ),
              ],
            ),
          ),

          // Appointment list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : !_dataFetched
                    ? const Center(
                        child: Text(
                          'Select date and operatories, then click "Fetch Appointments"',
                          textAlign: TextAlign.center,
                        ),
                      )
                    : _appointments.isEmpty
                        ? const Center(child: Text('No appointments found for the selected criteria'))
                        : ListView.builder(
                            itemCount: _appointments.length,
                            itemBuilder: (context, index) {
                              final appointment = _appointments[index];

                              // Skip appointments for operatories that aren't selected
                              final operatory = appointment['operatory'] ?? '';
                              if (!_selectedOperatories.contains(operatory)) {
                                return const SizedBox.shrink();
                              }

                              return Card(
                                margin: const EdgeInsets.symmetric(
                                  horizontal: 16.0,
                                  vertical: 8.0,
                                ),
                                child: ListTile(
                                  leading: CircleAvatar(
                                    backgroundColor: appointment['isBlocked'] == true
                                        ? Colors.grey
                                        : Colors.blue,
                                    child: Text(appointment['time'].toString().substring(0, 1)),
                                  ),
                                  title: Text(
                                    appointment['isBlocked'] == true
                                        ? 'BLOCKED: ${appointment['description'] ?? ""}'
                                        : appointment['patientName'] ?? "Unknown",
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: appointment['isBlocked'] == true
                                          ? Colors.grey[700]
                                          : Colors.black,
                                    ),
                                  ),
                                  subtitle: Text(
                                    '${appointment['time'] ?? "Unknown"} (${appointment['length'] ?? "?"} min) - ${appointment['operatory'] ?? "Unknown"}',
                                  ),
                                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                                  onTap: () {
                                    // Show appointment details
                                    showDialog(
                                      context: context,
                                      builder: (context) => AlertDialog(
                                        title: const Text('Appointment Details'),
                                        content: SingleChildScrollView(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text('Patient: ${appointment['patientName'] ?? "N/A"}'),
                                              Text('Time: ${appointment['time'] ?? "N/A"}'),
                                              Text('Length: ${appointment['length'] ?? "N/A"} minutes'),
                                              Text('Operatory: ${appointment['operatory'] ?? "N/A"}'),
                                              Text('Provider: ${appointment['provider'] ?? "N/A"}'),
                                              Text('Description: ${appointment['description'] ?? "N/A"}'),
                                            ],
                                          ),
                                        ),
                                        actions: [
                                          TextButton(
                                            onPressed: () => Navigator.of(context).pop(),
                                            child: const Text('Close'),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              );
                            },
                          ),
          ),
        ],
      ),
    );
  }
}
