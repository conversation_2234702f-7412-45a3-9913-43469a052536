@echo off
echo ========================================
echo    Dental App Backup Utility
echo ========================================
echo.

cd /d "C:\DentalApp"

:: Create backup directory with timestamp
set BACKUP_DATE=%date:~-4,4%%date:~-10,2%%date:~-7,2%
set BACKUP_TIME=%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_TIME=%BACKUP_TIME: =0%
set BACKUP_DIR=backups\backup_%BACKUP_DATE%_%BACKUP_TIME%

echo Creating backup: %BACKUP_DIR%
mkdir "%BACKUP_DIR%" 2>nul

echo.
echo [1/6] Backing up application files...
if exist "app" (
    xcopy /E /I /Y "app" "%BACKUP_DIR%\app\" >nul
    echo ✓ Application files backed up
) else (
    echo ✗ Application files not found
)

echo [2/6] Backing up configuration...
if exist "config" (
    xcopy /E /I /Y "config" "%BACKUP_DIR%\config\" >nul
    echo ✓ Configuration files backed up
) else (
    echo ✗ Configuration files not found
)

echo [3/6] Backing up database...
if exist "data\database.sqlite" (
    copy "data\database.sqlite" "%BACKUP_DIR%\database.sqlite" >nul
    echo ✓ Database backed up
) else (
    echo ℹ️ Database not found (may not exist yet)
)

echo [4/6] Backing up voice recordings metadata...
if exist "data\voice-recordings" (
    xcopy /E /I /Y "data\voice-recordings" "%BACKUP_DIR%\voice-recordings\" >nul
    echo ✓ Voice recordings metadata backed up
) else (
    echo ℹ️ Voice recordings folder not found
)

echo [5/6] Backing up logs...
if exist "logs" (
    xcopy /E /I /Y "logs" "%BACKUP_DIR%\logs\" >nul
    echo ✓ Logs backed up
) else (
    echo ℹ️ Logs folder not found
)

echo [6/6] Creating backup manifest...
echo Dental App Backup > "%BACKUP_DIR%\BACKUP_INFO.txt"
echo ==================== >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo. >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo Backup Date: %date% >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo Backup Time: %time% >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo Computer: %COMPUTERNAME% >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo User: %USERNAME% >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo. >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo Backup Contents: >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo - Application files (app\) >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo - Configuration files (config\) >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo - Database (database.sqlite) >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo - Voice recordings metadata >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo - Application logs >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo. >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo To restore this backup: >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo 1. Stop the service: stop-service.bat >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo 2. Copy files back to C:\DentalApp\ >> "%BACKUP_DIR%\BACKUP_INFO.txt"
echo 3. Start the service: start-service.bat >> "%BACKUP_DIR%\BACKUP_INFO.txt"

echo ✓ Backup manifest created

:: Calculate backup size
for /f "tokens=3" %%a in ('dir "%BACKUP_DIR%" /s /-c ^| findstr "bytes"') do set BACKUP_SIZE=%%a

echo.
echo ========================================
echo    Backup Complete!
echo ========================================
echo.
echo Backup Location: C:\DentalApp\%BACKUP_DIR%
echo Backup Size: %BACKUP_SIZE% bytes
echo Backup Date: %date% %time%
echo.

:: Optional: Copy to network location
echo Would you like to copy this backup to the network share?
echo (This will copy to \\192.168.0.2\share\BACKUPS\DentalApp\)
echo.
set /p copy_network="Copy to network? (y/n): "

if /i "%copy_network%"=="y" (
    echo.
    echo Copying backup to network share...
    
    :: Create network backup directory
    set NETWORK_BACKUP=\\192.168.0.2\share\BACKUPS\DentalApp\%BACKUP_DATE%_%BACKUP_TIME%
    mkdir "%NETWORK_BACKUP%" 2>nul
    
    if exist "%NETWORK_BACKUP%" (
        xcopy /E /I /Y "%BACKUP_DIR%" "%NETWORK_BACKUP%\" >nul
        if %errorLevel%==0 (
            echo ✓ Backup copied to network share
            echo Network Location: %NETWORK_BACKUP%
        ) else (
            echo ✗ Failed to copy to network share
            echo Check network connectivity and permissions
        )
    ) else (
        echo ✗ Cannot access network share
        echo Check if \\192.168.0.2\share\BACKUPS\ is accessible
    )
)

echo.
echo ========================================
echo    Backup Management
echo ========================================
echo.

:: Show existing backups
echo Recent backups:
dir /b /od "backups\backup_*" 2>nul | powershell "Get-Content | Select-Object -Last 5"

echo.
echo Backup cleanup options:
echo - Keep last 10 backups automatically
echo - Manual cleanup: Delete old folders in C:\DentalApp\backups\
echo.

:: Auto-cleanup old backups (keep last 10)
echo Cleaning up old backups (keeping last 10)...
for /f "skip=10 delims=" %%i in ('dir /b /od "backups\backup_*" 2^>nul') do (
    rmdir /s /q "backups\%%i" 2>nul
    echo Removed old backup: %%i
)

echo.
echo Backup utility completed successfully!
echo.
echo Press any key to continue...
pause >nul
