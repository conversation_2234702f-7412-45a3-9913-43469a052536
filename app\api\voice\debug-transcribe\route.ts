import { NextRequest, NextResponse } from 'next/server';
import { AzureStorageService } from '../../../../src/lib/azure-storage';

export async function POST(request: NextRequest) {
  try {
    const { filename } = await request.json();

    if (!filename) {
      return NextResponse.json({ error: 'Filename is required' }, { status: 400 });
    }

    console.log(`🔍 Debug transcription for: ${filename}`);

    const debugInfo = {
      step1_azureCheck: 'checking...',
      step2_fileExists: 'checking...',
      step3_transcribeCall: 'checking...',
      step4_result: 'pending...'
    };

    // Step 1: Check Azure Storage
    if (!AzureStorageService.isConfigured()) {
      debugInfo.step1_azureCheck = '❌ Azure Storage not configured';
      return NextResponse.json({ success: false, debugInfo });
    }
    debugInfo.step1_azureCheck = '✅ Azure Storage configured';

    // Step 2: Check if file exists
    try {
      const allFiles = await AzureStorageService.listFiles('');
      const fileExists = allFiles.find(f => f.name === filename);
      
      if (!fileExists) {
        debugInfo.step2_fileExists = `❌ File not found. Available files: ${allFiles.slice(0, 5).map(f => f.name).join(', ')}`;
        return NextResponse.json({ success: false, debugInfo });
      }
      
      debugInfo.step2_fileExists = `✅ File found (${fileExists.size} bytes)`;
    } catch (error) {
      debugInfo.step2_fileExists = `❌ Error checking file: ${error instanceof Error ? error.message : 'Unknown error'}`;
      return NextResponse.json({ success: false, debugInfo });
    }

    // Step 3: Try direct transcription call
    const baseUrl = process.env.VERCEL_URL 
      ? `https://${process.env.VERCEL_URL}`
      : process.env.WEBSITE_HOSTNAME 
      ? `https://${process.env.WEBSITE_HOSTNAME}`
      : 'http://localhost:3000';

    console.log(`🔗 Calling transcription API: ${baseUrl}/api/voice/transcribe`);

    try {
      const transcribeResponse = await fetch(`${baseUrl}/api/voice/transcribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Internal-Call': 'true'
        },
        body: JSON.stringify({
          fileName: filename,
          recordingId: filename,
          forceRetranscribe: true
        })
      });

      const transcribeResult = await transcribeResponse.json();
      debugInfo.step3_transcribeCall = `Status: ${transcribeResponse.status}, OK: ${transcribeResponse.ok}`;

      if (transcribeResponse.ok && transcribeResult.transcription) {
        debugInfo.step4_result = `✅ Success! Transcription length: ${transcribeResult.transcription.length} characters`;
        
        return NextResponse.json({
          success: true,
          debugInfo,
          transcriptionPreview: transcribeResult.transcription.substring(0, 200) + '...',
          fullResult: transcribeResult
        });
      } else {
        debugInfo.step4_result = `❌ Failed: ${transcribeResult.error || 'Unknown error'}`;
        
        return NextResponse.json({
          success: false,
          debugInfo,
          errorDetails: transcribeResult
        });
      }

    } catch (error) {
      debugInfo.step3_transcribeCall = `❌ Fetch error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      debugInfo.step4_result = 'Not attempted due to fetch error';
      
      return NextResponse.json({
        success: false,
        debugInfo,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

  } catch (error) {
    console.error('Debug transcribe error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // List available files for testing
    if (!AzureStorageService.isConfigured()) {
      return NextResponse.json({
        error: 'Azure Storage not configured',
        configured: false
      });
    }

    const files = await AzureStorageService.listFiles('');
    const audioFiles = files
      .filter(f => {
        const ext = f.name.toLowerCase().split('.').pop();
        return ['mp3', 'wav', 'm4a', 'webm', 'ogg', 'flac', 'aac'].includes(ext || '');
      })
      .slice(0, 10); // Just show first 10 for testing

    return NextResponse.json({
      success: true,
      message: 'Use POST with {"filename": "your-file.mp3"} to test transcription',
      availableFiles: audioFiles.map(f => ({
        name: f.name,
        size: f.size,
        path: f.path
      }))
    });

  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}