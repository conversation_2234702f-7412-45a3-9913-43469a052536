import { NextRequest, NextResponse } from 'next/server';
import { readdir } from 'fs/promises';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function GET() {
  try {
    // Try to access the network share
    const networkPath = process.env.NETWORK_SHARE_PATH || '\\\\192.168.0.2\\share\\RECORDINGS';

    const checkResult = await checkNetworkShare(networkPath);

    return NextResponse.json({
      available: checkResult.available,
      path: networkPath,
      method: checkResult.method,
      error: checkResult.error,
      lastChecked: new Date().toISOString()
    });
  } catch (error) {
    console.error('Network check failed:', error);
    return NextResponse.json({
      available: false,
      error: `Network check failed: ${error.message}`,
      lastChecked: new Date().toISOString()
    });
  }
}

async function checkNetworkShare(path: string): Promise<{available: boolean, method?: string, error?: string}> {
  try {
    // First try direct access
    await readdir(path);
    console.log(`Direct access to ${path} successful`);
    return { available: true, method: 'direct' };
  } catch (directError) {
    console.log(`Direct access to ${path} failed, trying authentication...`);

    try {
      // Try with credentials
      const username = process.env.NETWORK_SHARE_USERNAME;
      const password = process.env.NETWORK_SHARE_PASSWORD;
      const domain = process.env.NETWORK_SHARE_DOMAIN || '';

      if (!username || !password) {
        return {
          available: false,
          error: 'Network credentials not configured in environment variables'
        };
      }

      // Build net use command with credentials
      const userParam = domain ? `${domain}\\${username}` : username;
      const netUseCommand = `net use "${path}" /user:"${userParam}" "${password}" /persistent:no`;

      const { stdout, stderr } = await execAsync(netUseCommand);

      if (stderr && stderr.includes('error')) {
        throw new Error(`Authentication failed: ${stderr}`);
      }

      // Test access again after authentication
      await readdir(path);
      console.log(`Authenticated access to ${path} successful`);
      return { available: true, method: 'authenticated' };
    } catch (authError) {
      console.log('Credential authentication failed:', authError.message);
      return {
        available: false,
        error: `Authentication failed: ${authError.message}`
      };
    }
  }
}
