(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3115],{4679:(e,t,a)=>{"use strict";function s(e){if(!e||""===e.trim())return"No Patient";let t=String(e).trim();if(t.includes(",")){let e=t.split(",").map(e=>e.trim());if(e.length>=2){var a;let t=e[0].trim(),s=null==(a=e[1].split(" ")[0])?void 0:a.trim();if(s&&t){let e=s.charAt(0).toUpperCase()+s.slice(1).toLowerCase(),a=t.toUpperCase();return"".concat(e," ").concat(a)}}}let s=t.split(" ").filter(e=>e.trim());if(0===s.length)return"No Patient";if(1===s.length)return s[0].charAt(0).toUpperCase()+s[0].slice(1).toLowerCase();if(s.length>=2){let e="",t="",a=s[0],r=s[s.length-1];2===s.length||a===a.toUpperCase()&&r!==r.toUpperCase()?(e=r,t=a):(r.toUpperCase(),e=a,t=r);let l=e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),i=t.toUpperCase();return"".concat(l," ").concat(i)}return t}function r(e){var t,a;let r=null==(t=e.firstName)?void 0:t.trim(),l=null==(a=e.lastName)?void 0:a.trim();if(r&&l){let e=r.charAt(0).toUpperCase()+r.slice(1).toLowerCase(),t=l.toUpperCase();return"".concat(e," ").concat(t)}if(r)return r.charAt(0).toUpperCase()+r.slice(1).toLowerCase();if(l)return l.toUpperCase();let i=[e.firstName,e.middleName,e.lastName].filter(Boolean);return i.length>0?s(i.join(" ")):"No Patient"}a.d(t,{Qr:()=>s,hG:()=>r})},5654:(e,t,a)=>{Promise.resolve().then(a.bind(a,8609))},8609:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var s=a(5155),r=a(2115),l=a(5695),i=a(1362),n=a(6496),d=a(4822),o=a(7924),c=a(1007),x=a(9074),h=a(7434),m=a(4679);function u(){let e=(0,l.useRouter)(),t=(0,l.useSearchParams)(),{theme:a}=(0,i.D)(),[u,p]=(0,r.useState)(""),[b,g]=(0,r.useState)([]),[j,f]=(0,r.useState)(null),[N,v]=(0,r.useState)([]),[y,k]=(0,r.useState)(null),[w,S]=(0,r.useState)([]),[C,P]=(0,r.useState)(!1),[U,A]=(0,r.useState)(!1),[D,E]=(0,r.useState)(!1),[L,V]=(0,r.useState)(null),[T,I]=(0,r.useState)("timeline"),[B,F]=(0,r.useState)(""),[O,R]=(0,r.useState)("all"),q=t.get("patient"),M=t.get("visit"),_=t.get("q");(0,r.useEffect)(()=>{_&&(p(_),z(_))},[_]),(0,r.useEffect)(()=>{q&&!j?H(q):!q&&j&&(f(null),v([]),k(null),S([]))},[q,j]),(0,r.useEffect)(()=>{M&&j&&!y?J(M):!M&&y&&(k(null),S([]))},[M,j,y]);let G=async()=>{if(!u.trim())return void V("Please enter a search term");let t=new URLSearchParams;t.set("q",u),e.push("/patient-search?".concat(t.toString()))},z=async e=>{P(!0),V(null);try{var t;let a=await fetch("/api/patients/search?q=".concat(encodeURIComponent(e)));if(!a.ok)throw Error("Failed to search patients");let s=await a.json();g(s.patients||[]),(null==(t=s.patients)?void 0:t.length)===0&&V("No patients found matching your search")}catch(e){console.error("Search error:",e),V("Failed to search patients. Please try again.")}finally{P(!1)}},K=t=>{e.push("/patient/".concat(t.id))},Q=t=>{let a=new URLSearchParams;u&&a.set("q",u),j&&a.set("patient",j.id),a.set("visit",t.id),e.push("/patient-search?".concat(a.toString()))},H=async e=>{let t=b.find(t=>t.id===e);if(!t){if(_)try{let s=await fetch("/api/patients/search?q=".concat(encodeURIComponent(_)));if(s.ok){var a;let r=await s.json(),l=null==(a=r.patients)?void 0:a.find(t=>t.id===e);l&&(t=l,g(r.patients||[]))}}catch(e){console.error("Error loading patient from API:",e)}if(!t)return void console.warn("Patient not found:",e)}f(t),k(null),S([]),E(!0),A(!0),V(null);try{let[e,a]=await Promise.all([fetch("/api/patients/".concat(t.id,"/visits")),fetch("/api/patients/".concat(t.id,"/clinical-notes"))]);if(!e.ok)throw console.error("Visits response error:",await e.text()),Error("Failed to load visits");a.ok||(console.error("Notes response error:",await a.text()),console.warn("Clinical notes failed to load, continuing with visits only"));let s=await e.json(),r={notes:[]};a.ok&&(r=await a.json()),v(s.visits||[]),S(r.notes||[])}catch(e){console.error("Patient data loading error:",e),V("Failed to load patient data. Please try again.")}finally{E(!1),A(!1)}},J=async e=>{let t=N.find(t=>t.id===e);if(t){k(t),A(!0),V(null);try{let e=await fetch("/api/patients/".concat(null==j?void 0:j.id,"/visits/").concat(t.id,"/notes"));if(!e.ok)throw Error("Failed to load clinical notes");let a=await e.json();S(a.notes||[])}catch(e){console.error("Notes loading error:",e),V("Failed to load clinical notes. Please try again.")}finally{A(!1)}}},W=e=>{try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch(t){return e}},X=e=>{if(!e)return"";try{let t=new Date(e),a=new Date().getTime()-t.getTime(),s=Math.floor(a/864e5);if(s<30)return 1===s?"1 day":"".concat(s," days");if(s<365){let e=Math.floor(s/30);return 1===e?"1 month":"".concat(e," months")}{let e=Math.floor(s/365),t=Math.floor(s%365/30);if(0===t)return 1===e?"1 year":"".concat(e," years");return"".concat(1===e?"1 year":"".concat(e," years")," and ").concat(1===t?"1 month":"".concat(t," months"))}}catch(e){return""}};return(0,s.jsxs)("div",{className:"min-h-screen bg-slate-50 dark:bg-slate-900",children:[(0,s.jsx)(n.z,{title:"Dentalapp",showBackButton:!0,onBackClick:()=>{if(y){let t=new URLSearchParams;u&&t.set("q",u),j&&t.set("patient",j.id),e.push("/patient-search?".concat(t.toString()))}else if(j){let t=new URLSearchParams;t.set("tab","patient"),u&&t.set("search",u),e.push("/?".concat(t.toString()))}else e.push("/")}}),(0,s.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2",children:j?"Patient: ".concat((0,m.hG)(j)):y?"Visit Details - ".concat(y.date):"Patient Search"}),u&&(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200 text-sm",children:['Search: "',u,'"']})]}),j?y?(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:U?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Loading visit details..."})]}):(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2",children:["Visit Details - ",W(y.date)]}),(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Type:"})," ",y.appointmentType]}),y.provider&&(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Provider:"})," ",y.provider]}),y.procedures&&y.procedures.length>0&&(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Procedures:"})," ",y.procedures.join(", ")]}),(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Status:"})," ",y.status]}),y.operatory&&(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Operatory:"})," ",y.operatory]})]}),(0,s.jsx)(d.r,{patientId:j.id,appointmentDate:y.date,appointmentId:y.id,title:"Clinical Notes for this Visit",showPagination:!1,showFilters:!1,isDarkMode:"dark"===a})]})}):(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:D||U?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Loading patient data..."})]}):(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2",children:"Patient Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Name:"})," ",j.name]}),j.age&&j.genderInitial&&(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Age:"})," ",j.age,j.genderInitial]}),j.dateOfBirth&&(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Date of Birth:"})," ",W(j.dateOfBirth)]})]}),(0,s.jsxs)("div",{children:[j.lastVisit&&(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Last Visit:"})," ",W(j.lastVisit),(()=>{let e=X(j.lastVisit);return e?" (".concat(e," ago)"):""})()]}),j.phone&&(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Phone:"})," ",j.phone]}),j.email&&(0,s.jsxs)("p",{className:"text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Email:"})," ",j.email]})]})]})]}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,s.jsx)("button",{onClick:()=>I("timeline"),className:"py-2 px-1 border-b-2 font-medium text-sm transition-colors ".concat("timeline"===T?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:"Timeline View"}),(0,s.jsxs)("button",{onClick:()=>I("notes"),className:"py-2 px-1 border-b-2 font-medium text-sm transition-colors ".concat("notes"===T?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:["All Clinical Notes (",w.length,")"]})]})})}),"timeline"===T?(()=>{let e=[];return N.forEach(t=>{e.push({id:"visit-".concat(t.id),date:t.date,type:"visit",data:t})}),w.forEach(t=>{e.push({id:"note-".concat(t.id),date:t.date,type:"note",data:t})}),e.sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime()),e.length>0?(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 mr-2"}),"Patient Timeline (",N.length," visits, ",w.length," notes)"]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute left-8 top-0 bottom-0 w-0.5 bg-gray-300 dark:bg-gray-600"}),(0,s.jsx)("div",{className:"space-y-6",children:e.map((e,t)=>(0,s.jsxs)("div",{className:"relative flex items-start",children:[(0,s.jsx)("div",{className:"relative z-10 flex items-center justify-center w-4 h-4 rounded-full border-2 ".concat("visit"===e.type?"bg-blue-500 border-blue-500":"bg-green-500 border-green-500")}),(0,s.jsx)("div",{className:"ml-6 flex-1",children:"visit"===e.type?(0,s.jsxs)("button",{onClick:()=>Q(e.data),className:"w-full p-4 text-left bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-800 transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-blue-600"}),(0,s.jsxs)("span",{className:"font-medium text-blue-900 dark:text-blue-100",children:[W(e.data.date)," - Visit"]})]}),(0,s.jsx)("span",{className:"text-sm text-blue-700 dark:text-blue-300",children:e.data.status})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:[(0,s.jsx)("strong",{children:"Type:"})," ",e.data.appointmentType]}),(0,s.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["Provider: ",e.data.provider]})]}),e.data.operatory&&(0,s.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300 mt-1",children:["Operatory: ",e.data.operatory]})]}):(0,s.jsxs)("div",{className:"p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 text-green-600"}),(0,s.jsxs)("span",{className:"font-medium text-green-900 dark:text-green-100",children:[W(e.data.date)," - Clinical Note"]})]}),(0,s.jsxs)("span",{className:"text-sm text-green-700 dark:text-green-300",children:["Provider: ",e.data.provider]})]}),e.data.appointmentType&&(0,s.jsxs)("p",{className:"text-sm text-green-800 dark:text-green-200 mb-2",children:[(0,s.jsx)("strong",{children:"Appointment Type:"})," ",e.data.appointmentType]}),e.data.procedures&&e.data.procedures.length>0&&(0,s.jsxs)("p",{className:"text-sm text-green-800 dark:text-green-200 mb-2",children:[(0,s.jsx)("strong",{children:"Procedures:"})," ",e.data.procedures.join(", ")]}),(0,s.jsxs)("div",{className:"text-green-900 dark:text-green-100",children:[(0,s.jsx)("strong",{children:"Notes:"}),(0,s.jsx)("p",{className:"mt-1 text-sm whitespace-pre-wrap",children:e.data.notes})]})]})})]},e.id))})]})]}):(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"No visits or clinical notes found for this patient."})]})})():(0,s.jsx)(d.r,{patientId:j.id,title:"All Clinical Notes",showPagination:!0,showFilters:!0,isDarkMode:"dark"===a})]})}):(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-slate-900 dark:text-white mb-4 text-center",children:"Search Patients"}),(0,s.jsx)("p",{className:"text-slate-600 dark:text-slate-300 text-center mb-6",children:"Search by first name, last name, chart number, or partial matches"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[(0,s.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5"}),(0,s.jsx)("input",{type:"text",value:u,onChange:e=>p(e.target.value),onKeyPress:e=>"Enter"===e.key&&G(),placeholder:"Enter patient name or chart number...",autoFocus:!0,className:"w-full pl-10 pr-4 py-3 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-800 text-slate-900 dark:text-white placeholder-slate-500 dark:placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"})]}),(0,s.jsx)("button",{onClick:G,disabled:C,className:"px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:C?"Searching...":"Search"})]})]}),L&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:(0,s.jsx)("p",{className:"text-red-800 dark:text-red-200",children:L})}),b.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-white mb-4",children:["Search Results (",b.length,")"]}),(0,s.jsx)("div",{className:"space-y-2",children:b.map(e=>(0,s.jsx)("button",{onClick:()=>K(e),className:"w-full p-4 text-left bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 rounded-md border border-slate-200 dark:border-slate-700 transition-colors shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 text-slate-400"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("p",{className:"font-medium text-slate-900 dark:text-white",children:(0,m.hG)(e)}),e.age&&e.genderInitial&&(0,s.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded",children:[e.age,e.genderInitial]})]}),e.dateOfBirth&&(0,s.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["DOB: ",W(e.dateOfBirth)]}),e.lastVisit&&(0,s.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Last Visit: ",W(e.lastVisit),(()=>{let t=X(e.lastVisit);return t?" (".concat(t," ago)"):""})()]})]})]})},e.id))})]})]})]})]})}function p(){return(0,s.jsx)(r.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading patient search..."}),children:(0,s.jsx)(u,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8096,6496,4822,7358],()=>t(5654)),_N_E=e.O()}]);