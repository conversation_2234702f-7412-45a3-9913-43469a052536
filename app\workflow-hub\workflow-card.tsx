'use client';

import Link from 'next/link';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { ArrowRight } from 'lucide-react';

interface WorkflowCardProps {
  title: string;
  description: string;
  href: string;
  icon: React.ReactNode;
}

export function WorkflowCard({ title, description, href, icon }: WorkflowCardProps) {
  return (
    <Link href={href} className="block hover:shadow-lg transition-shadow duration-200">
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-4">
            <div className="text-3xl">{icon}</div>
            <div>
              <CardTitle>{title}</CardTitle>
              <CardDescription>{description}</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex justify-end">
          <ArrowRight className="h-5 w-5 text-muted-foreground" />
        </CardContent>
      </Card>
    </Link>
  );
}
