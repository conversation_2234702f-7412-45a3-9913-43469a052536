import { app, InvocationContext } from "@azure/functions";

interface TranscriptionJob {
  id: string;
  filename: string;
  blobPath: string;
  fileSize: number;
  uploadedAt: string;
  priority: 'normal' | 'high';
  settings: {
    model: string;
    promptId: string;
    enableSpeakerSeparation: boolean;
  };
}

/**
 * Azure Function that triggers when new audio files are uploaded to Blob Storage
 * Automatically queues them for transcription processing
 */
export async function transcriptionTrigger(myBlob: Buffer, context: InvocationContext): Promise<void> {
  const blobName = context.triggerMetadata?.name as string;
  const blobPath = context.triggerMetadata?.uri as string;
  
  context.log(`🎵 New audio file detected: ${blobName}`);
  context.log(`📁 Blob path: ${blobPath}`);
  context.log(`📊 File size: ${myBlob.length} bytes`);

  try {
    // Check if this is an audio file
    const audioExtensions = ['.mp3', '.wav', '.m4a', '.webm', '.ogg', '.flac', '.aac'];
    const fileExtension = blobName.toLowerCase().substring(blobName.lastIndexOf('.'));
    
    if (!audioExtensions.includes(fileExtension)) {
      context.log(`⏭️ Skipping non-audio file: ${blobName}`);
      return;
    }

    // Skip metadata files
    if (blobName.includes('metadata/') || 
        blobName.includes('transcriptions.json') || 
        blobName.includes('summaries.json')) {
      context.log(`⏭️ Skipping metadata file: ${blobName}`);
      return;
    }

    // Create transcription job
    const jobId = `auto-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const transcriptionJob: TranscriptionJob = {
      id: jobId,
      filename: blobName,
      blobPath: blobPath,
      fileSize: myBlob.length,
      uploadedAt: new Date().toISOString(),
      priority: 'normal',
      settings: {
        model: 'whisper-1', // Default model, can be overridden by user settings
        promptId: 'dental-transcription-context',
        enableSpeakerSeparation: true
      }
    };

    // Add job to queue for processing
    // TODO: Implement proper v4 queue output binding
    // context.bindings.queueItem = transcriptionJob;
    
    context.log(`✅ Queued transcription job: ${jobId} for file: ${blobName}`);
    context.log(`📋 Job details:`, JSON.stringify(transcriptionJob, null, 2));

  } catch (error) {
    context.log(`❌ Error processing blob trigger for ${blobName}:`, error);
    
    // Don't throw error - we don't want to retry blob triggers indefinitely
    // Instead, log the error and continue
  }
};

// Register the function with Azure Functions v4
app.storageBlob('transcriptionTrigger', {
  path: 'recordings/audio/{name}',
  connection: 'AzureWebJobsStorage',
  handler: transcriptionTrigger
});
