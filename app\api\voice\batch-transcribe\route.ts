import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { recordingIds, date } = await request.json();

    if (!recordingIds || !Array.isArray(recordingIds)) {
      return NextResponse.json(
        { error: 'Recording IDs array is required' },
        { status: 400 }
      );
    }

    console.log(`Starting batch transcription for ${recordingIds.length} recordings`);

    const results = [];
    const errors = [];
    let completed = 0;

    // Process recordings one by one to avoid overwhelming the API
    for (const recordingId of recordingIds) {
      try {
        console.log(`Processing recording ${completed + 1}/${recordingIds.length}: ${recordingId}`);
        
        // Call the individual transcription API
        const transcribeResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/voice/transcribe`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            recordingId,
            promptId: 'dental-transcription-context'
          }),
        });

        const transcribeResult = await transcribeResponse.json();

        if (transcribeResponse.ok) {
          results.push({
            recordingId,
            success: true,
            transcription: transcribeResult.transcription,
            cached: transcribeResult.cached
          });
        } else {
          errors.push({
            recordingId,
            error: transcribeResult.error || 'Unknown error'
          });
        }

        completed++;

        // Add a small delay between requests to be respectful to OpenAI API
        if (completed < recordingIds.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error: any) {
        console.error(`Error processing recording ${recordingId}:`, error);
        errors.push({
          recordingId,
          error: error.message || 'Processing failed'
        });
        completed++;
      }
    }

    console.log(`Batch transcription completed: ${results.length} successful, ${errors.length} failed`);

    return NextResponse.json({
      success: true,
      completed: results.length,
      failed: errors.length,
      total: recordingIds.length,
      results,
      errors,
      date
    });

  } catch (error: any) {
    console.error('Batch transcription error:', error);
    return NextResponse.json(
      { error: `Batch transcription failed: ${error.message}` },
      { status: 500 }
    );
  }
}
