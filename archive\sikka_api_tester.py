import requests
import json
import datetime
import argparse
from typing import Dict, List, Any, Optional

API_BASE = "https://api.sikkasoft.com/v4"

# Default credentials - can be overridden via command line arguments
DEFAULT_APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
DEFAULT_APP_KEY = "88254bfa2224607ef425646aafe5f722"
DEFAULT_OFFICE_ID = "D43989"
DEFAULT_SECRET_KEY = "35442814D4396E20C222"


def safe_json_parse(response, label):
    """Safely parse JSON from response, with error handling."""
    try:
        return response.json()
    except json.JSONDecodeError:
        print(f"Failed to parse JSON from {label}. Raw response:")
        print(response.text)
        return None


def pretty_print_json(data):
    """Print JSON data in a readable format."""
    if data is None:
        print("No data to display")
        return

    print("\n" + "=" * 80)
    print("RESPONSE DATA (COPY BELOW FOR TROUBLESHOOTING):")
    print("=" * 80)
    print(json.dumps(data, indent=2))
    print("=" * 80 + "\n")


def get_request_key(app_id: str, app_key: str, office_id: str, secret_key: str) -> Optional[str]:
    """Obtain request_key for API authentication."""
    print("\n[*] Obtaining request_key...")

    try:
        auth_resp = requests.post(
            f"{API_BASE}/request_key",
            headers={"Content-Type": "application/json"},
            json={
                "grant_type": "request_key",
                "office_id": office_id,
                "secret_key": secret_key,
                "app_id": app_id,
                "app_key": app_key
            }
        )

        if auth_resp.status_code != 200:
            print(f"[!] Error: HTTP {auth_resp.status_code}")
            print(f"[!] Response: {auth_resp.text}")
            return None

        data = safe_json_parse(auth_resp, "request_key API")
        if not data:
            return None

        request_key = data.get("request_key")
        if not request_key:
            print("[!] Error: No request_key found in response")
            return None

        print(f"[+] Successfully obtained request_key: {request_key}")
        pretty_print_json(data)
        return request_key

    except Exception as e:
        print(f"[!] Exception during authentication: {str(e)}")
        return None


def fetch_paginated(endpoint: str, headers: Dict[str, str], params: Dict[str, Any] = None,
                   limit: int = 500, max_pages: int = 5) -> List[Dict[str, Any]]:
    """
    Generic pagination helper. Returns list of items from endpoint.
    Added max_pages parameter to limit the number of pages fetched for testing.
    """
    items = []
    offset = 0
    params = params or {}
    page_count = 0

    print(f"\n[*] Fetching paginated data from {endpoint}...")
    print(f"[*] Headers: {headers}")
    print(f"[*] Params: {params}")

    while page_count < max_pages:
        page_count += 1
        paged = params.copy()
        paged.update({"offset": offset, "limit": limit})

        print(f"\n[*] Fetching page {page_count} (offset: {offset}, limit: {limit})...")

        try:
            resp = requests.get(f"{API_BASE}/{endpoint}", headers=headers, params=paged)

            print(f"[*] Response status: HTTP {resp.status_code}")

            if resp.status_code == 204:
                print("[*] No content (HTTP 204)")
                break

            if resp.status_code != 200:
                print(f"[!] Failed {endpoint} call at offset {offset}: HTTP {resp.status_code}")
                print(f"[!] Response: {resp.text}")
                break

            data = safe_json_parse(resp, f"{endpoint} @ offset {offset}")
            if not data:
                break

            # Display the raw response for the first page
            if page_count == 1:
                pretty_print_json(data)

            batch = data.get("items", []) if data else []
            items.extend(batch)

            print(f"[+] Retrieved {len(batch)} items from {endpoint} at offset {offset}")

            if len(batch) < limit:
                print("[*] Reached end of data (received fewer items than limit)")
                break

            offset += limit

        except Exception as e:
            print(f"[!] Exception during fetch: {str(e)}")
            break

    print(f"[+] Total items retrieved: {len(items)}")
    return items


def get_practice_schedule(request_key: str, office_id: str, target_date: str) -> Dict[str, List[str]]:
    """
    Fetch practice schedule for all operatories on a specific date.
    Returns a dict mapping operatory resource_id to list of appointment_sr_no.
    Uses request_key for authentication.
    """
    print(f"\n[*] Fetching practice schedule for date: {target_date}...")

    params = {"practice_id": office_id, "date": target_date, "resource_type": "operatory"}
    headers = {"Request-Key": request_key}

    try:
        resp = requests.get(f"{API_BASE}/practice_schedule", headers=headers, params=params)

        print(f"[*] Response status: HTTP {resp.status_code}")

        if resp.status_code != 200:
            print(f"[!] Failed practice_schedule call: HTTP {resp.status_code}")
            print(f"[!] Response: {resp.text}")
            return {}

        data = safe_json_parse(resp, "practice_schedule API")
        if not data:
            return {}

        # Display the raw response
        pretty_print_json(data)

        items = data.get("items", []) if data else []

        oper_map = {}
        # Events may not be included here; check 'events' key if present
        for item in items:
            oper_id = item.get("resource_id")
            events = item.get("events") or []
            sr_nos = [ev.get("appointment_sr_no") for ev in events if ev.get("appointment_sr_no")]
            if oper_id and sr_nos:
                oper_map[oper_id] = sr_nos

        print(f"[+] Found {len(oper_map)} operatories with scheduled appointments")
        return oper_map

    except Exception as e:
        print(f"[!] Exception during practice schedule fetch: {str(e)}")
        return {}


def display_menu():
    """Display the main menu options."""
    print("\n" + "=" * 50)
    print("SIKKA API TESTER - MAIN MENU")
    print("=" * 50)
    print("1. Get Request Key (Authentication)")
    print("2. Fetch Appointments")
    print("3. Get Practice Schedule")
    print("4. Display Appointments by Operatory")
    print("5. Change Date")
    print("6. Change Credentials")
    print("0. Exit")
    print("=" * 50)


def main():
    parser = argparse.ArgumentParser(description="Sikka API Tester")
    parser.add_argument("--app-id", default=DEFAULT_APP_ID, help="App ID for Sikka API")
    parser.add_argument("--app-key", default=DEFAULT_APP_KEY, help="App Key for Sikka API")
    parser.add_argument("--office-id", default=DEFAULT_OFFICE_ID, help="Office ID for Sikka API")
    parser.add_argument("--secret-key", default=DEFAULT_SECRET_KEY, help="Secret Key for Sikka API")
    parser.add_argument("--date", default=None, help="Target date in YYYY-MM-DD format")

    args = parser.parse_args()

    # Set up credentials and date
    app_id = args.app_id
    app_key = args.app_key
    office_id = args.office_id
    secret_key = args.secret_key

    # Default to today's date if not specified
    if args.date:
        target_date = args.date
    else:
        target_date = datetime.datetime.now().strftime("%Y-%m-%d")

    # Initialize variables
    request_key = None
    appointments = []
    filtered_appointments = []
    oper_map = {}

    # Main loop
    while True:
        display_menu()
        choice = input("Enter your choice (0-6): ").strip()

        if choice == "0":
            print("\nExiting Sikka API Tester. Goodbye!")
            break

        elif choice == "1":
            # Get Request Key
            request_key = get_request_key(app_id, app_key, office_id, secret_key)
            if not request_key:
                print("[!] Failed to obtain request_key. Please check your credentials.")
            input("\nPress Enter to continue...")

        elif choice == "2":
            # Fetch Appointments
            if not request_key:
                print("[!] No request_key available. Please authenticate first (option 1).")
                input("\nPress Enter to continue...")
                continue

            print(f"\n[*] Fetching appointments for date: {target_date}...")
            appt_headers = {"Request-Key": request_key}
            appointments = fetch_paginated("appointments", appt_headers)

            # Filter appointments by date
            filtered_appointments = [a for a in appointments if a.get("date", "").startswith(target_date)]
            print(f"[+] Filtered {len(filtered_appointments)} appointments on {target_date}")

            # Display first few appointments
            if filtered_appointments:
                print("\n[*] First few appointments:")
                for i, appt in enumerate(filtered_appointments[:5]):
                    print(f"  {i+1}. {appt.get('patient_name')} at {appt.get('time')} (SR: {appt.get('appointment_sr_no')})")
                if len(filtered_appointments) > 5:
                    print(f"  ... and {len(filtered_appointments) - 5} more")
            else:
                print("[!] No appointments found for the selected date")

            input("\nPress Enter to continue...")

        elif choice == "3":
            # Get Practice Schedule
            if not request_key:
                print("[!] No request_key available. Please authenticate first (option 1).")
                input("\nPress Enter to continue...")
                continue

            print(f"\n[*] Fetching practice schedule for date: {target_date}...")
            oper_map = get_practice_schedule(request_key, office_id, target_date)

            if oper_map:
                print(f"\n[+] Operatories with appointments on {target_date}: {list(oper_map.keys())}")
            else:
                print("[!] No operatory schedules found or unauthorized.")

            input("\nPress Enter to continue...")

        elif choice == "4":
            # Display Appointments by Operatory
            if not oper_map:
                print("[!] No operatory data available. Please fetch practice schedule first (option 3).")
                input("\nPress Enter to continue...")
                continue

            if not filtered_appointments:
                print("[!] No appointment data available. Please fetch appointments first (option 2).")
                input("\nPress Enter to continue...")
                continue

            print(f"\n[*] Patients by Operatory on {target_date}:")
            for oper_id, sr_nos in oper_map.items():
                print(f"\nOperatory ID: {oper_id}")
                found = False
                for appt in filtered_appointments:
                    if appt.get("appointment_sr_no") in sr_nos:
                        pname = appt.get("patient_name")
                        time = appt.get("time")
                        print(f"  - {pname} at {time} (SR: {appt.get('appointment_sr_no')})")
                        found = True
                if not found:
                    print("  - No matching appointments found")

            input("\nPress Enter to continue...")

        elif choice == "5":
            # Change Date
            print(f"\n[*] Current date: {target_date}")
            new_date = input("Enter new date (YYYY-MM-DD) or press Enter to keep current: ").strip()

            if new_date:
                try:
                    # Validate date format
                    datetime.datetime.strptime(new_date, "%Y-%m-%d")
                    target_date = new_date
                    print(f"[+] Date changed to: {target_date}")
                    # Reset filtered appointments and operatory map for new date
                    filtered_appointments = []
                    oper_map = {}
                except ValueError:
                    print("[!] Invalid date format. Please use YYYY-MM-DD.")

            input("\nPress Enter to continue...")

        elif choice == "6":
            # Change Credentials
            print("\n[*] Current credentials:")
            print(f"  App ID: {app_id}")
            print(f"  App Key: {app_key}")
            print(f"  Office ID: {office_id}")
            print(f"  Secret Key: {secret_key}")

            print("\n[*] Enter new credentials (or press Enter to keep current):")
            new_app_id = input("  App ID: ").strip() or app_id
            new_app_key = input("  App Key: ").strip() or app_key
            new_office_id = input("  Office ID: ").strip() or office_id
            new_secret_key = input("  Secret Key: ").strip() or secret_key

            # Update credentials
            app_id = new_app_id
            app_key = new_app_key
            office_id = new_office_id
            secret_key = new_secret_key

            # Reset request key since credentials changed
            request_key = None
            print("[*] Credentials updated. You'll need to get a new request key.")

            input("\nPress Enter to continue...")

        else:
            print("[!] Invalid choice. Please try again.")
            input("\nPress Enter to continue...")


if __name__ == "__main__":
    main()
