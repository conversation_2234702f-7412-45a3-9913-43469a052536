import { NextRequest, NextResponse } from 'next/server';

// This endpoint will be called when the app starts up to automatically begin processing
export async function POST(request: NextRequest) {
  return await handleStartupProcessing(request);
}

// Also handle GET requests for browser/manual triggering
export async function GET(request: NextRequest) {
  return await handleStartupProcessing(request);
}

async function handleStartupProcessing(request: NextRequest) {
  try {
    console.log('🚀 Startup: Initializing automatic voice processing...');
    
    // Small delay to ensure all services are ready
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Step 1: Check if database schema exists, if not skip migration
    console.log('📋 Startup: Checking database status...');
    let migrationResult = { success: false, message: 'Migration skipped - database not ready' };
    
    try {
      // Check if we can query the database
      const healthResponse = await fetch(`${getBaseUrl()}/api/system-health`, {
        headers: { 'X-Internal-Call': 'true' }
      });
      
      if (healthResponse.ok) {
        const healthData = await healthResponse.json();
        if (healthData.components?.database?.status === 'healthy') {
          console.log('✅ Startup: Database is healthy, proceeding with processing');
          migrationResult = { success: true, message: 'Database ready for processing' };
        } else {
          console.log('⚠️ Startup: Database not fully ready, but continuing...');
        }
      }
    } catch (error) {
      console.warn('⚠️ Startup: Could not verify database status:', error.message);
    }
    
    // Step 2: Start batch processing directly (bypass deprecated auto-process)
    console.log('🎯 Startup: Starting batch processing...');
    const batchResponse = await fetch(`${getBaseUrl()}/api/voice/cloud-process`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'X-Internal-Call': 'true' 
      },
      body: JSON.stringify({ action: 'start_batch' })
    });
    
    if (!batchResponse.ok) {
      const errorText = await batchResponse.text();
      console.error(`❌ Startup: Batch processing failed: ${batchResponse.status} - ${errorText}`);
      return NextResponse.json({
        success: false,
        error: `Batch processing failed: ${batchResponse.status}`,
        details: errorText
      }, { status: 500 });
    }
    
    const batchData = await batchResponse.json();
    console.log(`✅ Startup: Batch processing started - ${batchData.activeJobs || 0} jobs created`);
    
    return NextResponse.json({
      success: true,
      message: 'Startup auto-processing completed',
      migrationResult: migrationResult,
      batchResult: batchData,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Startup auto-process error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

function getBaseUrl(): string {
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL;
  }
  return 'http://localhost:3000';
}