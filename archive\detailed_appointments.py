#!/usr/bin/env python3
"""
Detailed Appointments Script

This script fetches and displays detailed appointment information including:
- Patient name and chart number
- Appointment serial number
- Appointment length, operatory, provider, time
- Completed procedure codes for the date
- Medical/clinical notes for the patient on that date

Usage:
    python detailed_appointments.py --date YYYY-MM-DD [--operatory OP1,OP2]
"""

import argparse
import json
import os
import sys
import requests
from datetime import datetime, timedelta
from collections import defaultdict

# API configuration
API_BASE = "https://api.sikkasoft.com/v1"
API_BASE_V2 = "https://api.sikkasoft.com/v2"
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_TIMEOUT = 30  # seconds

def load_credentials():
    """Load API credentials from credentials.json file."""
    try:
        with open("credentials.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: credentials.json file not found.")
        print("Please create a credentials.json file with your Sikka API credentials.")
        sys.exit(1)
    except json.JSONDecodeError:
        print("Error: Invalid JSON in credentials.json file.")
        sys.exit(1)

def authenticate(app_id, app_key, office_id, secret_key):
    """Authenticate with Sikka API and get request key."""
    print("Authenticating with Sikka API...")

    try:
        auth_resp = requests.post(
            f"{API_BASE_V4}/request_key",
            headers={"Content-Type": "application/json"},
            json={
                "grant_type": "request_key",
                "office_id": office_id,
                "secret_key": secret_key,
                "app_id": app_id,
                "app_key": app_key
            },
            timeout=API_TIMEOUT
        )

        if auth_resp.status_code == 200:
            data = auth_resp.json()
            request_key = data.get("request_key")
            if request_key:
                print("Authentication successful!")
                return request_key
            else:
                print("Error: No request key in response.")
                print(data)
        else:
            print(f"Authentication failed with status code: {auth_resp.status_code}")
            print(auth_resp.text)
    except Exception as e:
        print(f"Error during authentication: {e}")

    sys.exit(1)

def fetch_appointments_for_date(request_key, target_date):
    """Fetch appointments scheduled for a specific date with pagination."""
    headers = {"Request-Key": request_key}

    print(f"Fetching appointments for {target_date}...")

    # Use the v2 endpoint with pagination
    all_appointments = []
    offset = 0
    limit = 100  # Maximum number of appointments per page
    max_pages = 20  # Maximum number of pages to fetch

    # Set up parameters with the correct date filtering parameters
    params = {
        "startdate": target_date,
        "enddate": target_date,
        "date_filter_on": "appointment_date",
        "limit": limit,
        "offset": offset
    }

    for page in range(max_pages):
        print(f"  Fetching page {page+1} (offset: {offset}, limit: {limit})...")

        # Update offset for pagination
        params["offset"] = offset

        try:
            resp = requests.get(
                f"{API_BASE_V2}/appointments",
                headers=headers,
                params=params,
                timeout=API_TIMEOUT
            )

            if resp.status_code == 200:
                data = resp.json()

                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    print(f"    Found {len(items)} appointments on page {page+1}")

                    all_appointments.extend(items)

                    # If we got fewer items than the limit, we've reached the end
                    if len(items) < limit:
                        print("    Reached the end of appointments")
                        break

                    offset += limit
                else:
                    print("    No appointments found on this page")
                    break
            else:
                print(f"    Error fetching appointments: {resp.status_code}")
                print(resp.text)
                break
        except Exception as e:
            print(f"    Error: {e}")
            break

    print(f"Total appointments fetched: {len(all_appointments)}")
    return all_appointments

def fetch_transactions(request_key, target_date):
    """Fetch all transactions (procedure codes) for a specific date."""
    headers = {"Request-Key": request_key}

    # Try different parameter combinations to get the most complete data
    params_options = [
        {"date": target_date},
        {"startdate": target_date, "enddate": target_date},
        {"date": target_date, "transaction_type": "Procedure"}
    ]

    # Fetch all pages to make sure we get all transactions
    all_items = []

    print(f"Fetching procedure transactions for {target_date}...")

    # Try each parameter combination
    for params in params_options:
        print(f"  Trying with parameters: {params}")
        offset = 0
        limit = 100  # Increased limit to reduce number of API calls
        max_pages = 10  # Limit the number of pages to fetch

        for page in range(max_pages):
            paged_params = params.copy()
            paged_params.update({"offset": offset, "limit": limit})

            try:
                resp = requests.get(
                    f"{API_BASE_V4}/transactions",
                    headers=headers,
                    params=paged_params,
                    timeout=API_TIMEOUT
                )

                if resp.status_code != 200:
                    print(f"  Error fetching transactions: {resp.status_code}")
                    print(f"  {resp.text}")
                    break

                data = resp.json()
                items = data.get("items", [])

                # Only add new items that aren't already in all_items
                new_items = []
                existing_ids = {item.get("id") for item in all_items if item.get("id")}

                for item in items:
                    if item.get("id") and item.get("id") not in existing_ids:
                        new_items.append(item)
                        existing_ids.add(item.get("id"))

                all_items.extend(new_items)

                print(f"    Fetched {len(items)} transactions (page {page+1}), added {len(new_items)} new items")

                if len(items) < limit:
                    break

                offset += limit
            except requests.exceptions.Timeout:
                print(f"    Timeout while fetching transactions. Proceeding with {len(all_items)} transactions.")
                break
            except Exception as e:
                print(f"    Error: {e}")
                break

    # Filter for procedure transactions only - include all procedures, even with $0 amount
    # Based on our testing, we found that $0 procedures are often important (like Perio Charting)
    procedure_items = [t for t in all_items if t.get("transaction_type") == "Procedure"]

    print(f"Found {len(procedure_items)} procedure transactions")
    return procedure_items

def fetch_medical_notes(request_key, target_date):
    """Fetch medical/clinical notes for a specific date."""
    headers = {"Request-Key": request_key}

    # Try different endpoints and parameter combinations
    endpoint_params = [
        # Standard medical notes endpoint
        (f"{API_BASE_V4}/medical_notes", {"date": target_date}),
        # Try with start/end date
        (f"{API_BASE_V4}/medical_notes", {"startdate": target_date, "enddate": target_date}),
        # Try clinical notes endpoint if it exists
        (f"{API_BASE_V4}/clinical_notes", {"date": target_date}),
        # Try notes endpoint if it exists
        (f"{API_BASE_V4}/notes", {"date": target_date})
    ]

    print(f"Fetching medical/clinical notes for {target_date}...")

    # Fetch all pages to make sure we get all notes
    all_notes = []

    for endpoint, params in endpoint_params:
        print(f"  Trying endpoint: {endpoint} with parameters: {params}")
        offset = 0
        limit = 100
        max_pages = 10

        for page in range(max_pages):
            paged_params = params.copy()
            paged_params.update({"offset": offset, "limit": limit})

            try:
                resp = requests.get(
                    endpoint,
                    headers=headers,
                    params=paged_params,
                    timeout=API_TIMEOUT
                )

                # Skip if endpoint doesn't exist (404) or other error
                if resp.status_code != 200:
                    if page == 0:  # Only print error on first page
                        print(f"    Error with endpoint {endpoint}: {resp.status_code}")
                    break

                data = resp.json()
                items = data.get("items", [])

                # Only add new items that aren't already in all_notes
                new_items = []
                existing_ids = {item.get("id") for item in all_notes if item.get("id")}

                for item in items:
                    # Check if this is a valid note with content
                    has_content = False

                    # Check different possible note content fields
                    for field in ["note", "content", "text", "description"]:
                        if item.get(field) and item.get(field).strip():
                            has_content = True
                            # Standardize on "note" field
                            if field != "note":
                                item["note"] = item[field]
                            break

                    if has_content and (not item.get("id") or item.get("id") not in existing_ids):
                        new_items.append(item)
                        if item.get("id"):
                            existing_ids.add(item.get("id"))

                all_notes.extend(new_items)

                print(f"    Fetched {len(items)} notes (page {page+1}), added {len(new_items)} new notes with content")

                if len(items) < limit:
                    break

                offset += limit
            except Exception as e:
                print(f"    Error fetching notes: {e}")
                break

    print(f"Found {len(all_notes)} medical/clinical notes with content")
    return all_notes

def fetch_patient_chart_numbers(request_key, patient_ids):
    """Fetch chart numbers for a list of patient IDs."""
    if not patient_ids:
        return {}

    print(f"Fetching chart numbers for {len(patient_ids)} patients...")

    headers = {"Request-Key": request_key}
    chart_numbers = {}
    batch_size = 10  # Process in batches to avoid overloading the API

    # Process in batches
    for i in range(0, len(patient_ids), batch_size):
        batch = patient_ids[i:i+batch_size]
        print(f"  Processing batch {i//batch_size + 1} ({len(batch)} patients)")

        for patient_id in batch:
            try:
                resp = requests.get(
                    f"{API_BASE_V4}/patients/{patient_id}",
                    headers=headers,
                    timeout=API_TIMEOUT
                )

                if resp.status_code == 200:
                    data = resp.json()
                    chart_number = data.get("chart_number", "")
                    if chart_number:
                        chart_numbers[patient_id] = chart_number
                else:
                    print(f"    Error fetching patient {patient_id}: {resp.status_code}")
            except Exception as e:
                print(f"    Error fetching patient {patient_id}: {e}")

    print(f"Found chart numbers for {len(chart_numbers)} patients")
    return chart_numbers

def display_detailed_appointments(appointments, procedures, medical_notes, chart_numbers, target_date, selected_operatories=None):
    """Display detailed appointment information with procedure codes and notes."""
    if not appointments:
        print(f"No appointments found for {target_date}")
        return

    # Track statistics for summary
    total_appointments = 0
    total_patient_appointments = 0  # Only count actual patient appointments, not blocked time
    total_procedures = 0
    total_notes = 0
    operatory_counts = defaultdict(int)
    provider_counts = defaultdict(int)
    procedure_code_counts = defaultdict(int)

    # Filter by selected operatories if provided
    if selected_operatories:
        filtered_appointments = [
            appt for appt in appointments
            if appt.get("operatory") in selected_operatories
        ]
        print(f"Filtered to {len(filtered_appointments)} appointments in operatories: {', '.join(selected_operatories)}")
    else:
        filtered_appointments = appointments

    # Sort appointments by operatory and time
    sorted_appointments = sorted(
        filtered_appointments,
        key=lambda a: (a.get("operatory", ""), a.get("time", ""))
    )

    # Organize procedures by patient ID, transaction date, and appointment serial number
    procedures_by_patient = defaultdict(list)
    procedures_by_appt_sr_no = defaultdict(list)

    for proc in procedures:
        patient_id = proc.get("patient_id")
        appt_sr_no = proc.get("appointment_sr_no")
        transaction_date = proc.get("transaction_date", "").split("T")[0] if proc.get("transaction_date") else ""
        service_date = proc.get("service_date", "").split("T")[0] if proc.get("service_date") else ""

        # Include procedures from the target date (check both transaction and service date)
        if transaction_date == target_date or service_date == target_date:
            if patient_id:
                procedures_by_patient[patient_id].append(proc)

            if appt_sr_no:
                procedures_by_appt_sr_no[appt_sr_no].append(proc)

        # Also include procedures for patients with appointments on this date
        # This helps match procedures even if the dates don't align perfectly
        elif patient_id:
            for appt in appointments:
                if appt.get("patient_id") == patient_id:
                    procedures_by_patient[patient_id].append(proc)
                    break

    # Organize medical notes by patient ID and date
    notes_by_patient = defaultdict(list)

    for note in medical_notes:
        patient_id = note.get("patient_id")
        note_date = note.get("date", "").split("T")[0] if note.get("date") else ""

        # Include notes from the target date
        if note_date == target_date:
            if patient_id:
                notes_by_patient[patient_id].append(note)

        # Also include notes for patients with appointments on this date
        # This helps match notes even if the dates don't align perfectly
        elif patient_id:
            for appt in appointments:
                if appt.get("patient_id") == patient_id:
                    notes_by_patient[patient_id].append(note)
                    break

    # Display the header
    date_obj = datetime.strptime(target_date, "%Y-%m-%d")
    formatted_date = date_obj.strftime("%A, %B %d, %Y")

    print(f"\n{'=' * 120}")
    print(f"{'DETAILED APPOINTMENTS FOR ' + formatted_date:^120}")
    print(f"{'=' * 120}")

    # Display each appointment with details
    current_operatory = None

    for appt in sorted_appointments:
        operatory = appt.get("operatory", "N/A")

        # Print operatory header when it changes
        if operatory != current_operatory:
            print(f"\n{'-' * 120}")
            print(f"OPERATORY: {operatory}")
            print(f"{'-' * 120}")
            current_operatory = operatory

        # Get appointment details
        time = appt.get("time", "")
        patient_name = appt.get("patient_name", "Unknown Patient")
        patient_id = appt.get("patient_id", "")
        appt_sr_no = appt.get("appointment_sr_no", "")
        length = appt.get("length", "")
        provider_id = appt.get("provider_id", "")
        description = appt.get("description", "")

        # Update statistics
        total_appointments += 1

        # Check if this is a real patient appointment or just blocked time
        is_blocked = "Blocked" in str(appt_sr_no) or not patient_name or patient_name.strip() == ""

        # Only count patient appointments (not blocked time) in patient stats
        if not is_blocked:
            total_patient_appointments += 1
            operatory_counts[operatory] += 1
            if provider_id:
                provider_counts[provider_id] += 1

        # Get chart number from our lookup dictionary if available
        chart_number = chart_numbers.get(patient_id, "")

        # Print appointment details
        print(f"\nTime: {time} | Length: {length} min | Provider: {provider_id}")
        if chart_number:
            print(f"Patient: {patient_name} (Chart: {chart_number})")
        else:
            print(f"Patient: {patient_name}")
        print(f"Appt Serial #: {appt_sr_no}")
        print(f"Description: {description}")

        # Get procedures for this patient/appointment
        patient_procedures = procedures_by_patient.get(patient_id, [])
        appt_procedures = procedures_by_appt_sr_no.get(appt_sr_no, [])

        # Combine and deduplicate procedures
        all_procedures = []
        proc_ids = set()

        for proc in patient_procedures + appt_procedures:
            proc_id = proc.get("id") or proc.get("transaction_sr_no")
            if proc_id and proc_id not in proc_ids:
                all_procedures.append(proc)
                proc_ids.add(proc_id)

        # Print procedures if any
        if all_procedures:
            print("\nProcedures:")
            print(f"{'CODE':<10}{'DESCRIPTION':<40}{'TOOTH':<10}{'SURFACE':<10}{'AMOUNT':<10}{'DATE':<12}")
            print(f"{'-' * 10}{'-' * 40}{'-' * 10}{'-' * 10}{'-' * 10}{'-' * 12}")

            # Sort procedures by code
            sorted_procedures = sorted(all_procedures, key=lambda p: p.get("procedure_code", ""))

            for proc in sorted_procedures:
                code = proc.get("procedure_code", "")
                desc = proc.get("procedure_description", "")
                amount = float(proc.get("amount", "0"))
                tooth = proc.get("tooth_from", "") or "-"
                surface = proc.get("surface", "") or "-"

                # Update statistics
                total_procedures += 1
                procedure_code_counts[code] += 1

                # Get the date (try both transaction_date and service_date)
                proc_date = ""
                if proc.get("transaction_date"):
                    proc_date = proc.get("transaction_date", "").split("T")[0]
                elif proc.get("service_date"):
                    proc_date = proc.get("service_date", "").split("T")[0]

                # Truncate long descriptions
                if len(desc) > 37:
                    desc = desc[:34] + "..."

                print(f"{code:<10}{desc:<40}{tooth:<10}{surface:<10}${amount:<9.2f}{proc_date:<12}")
        else:
            print("\nNo procedures found for this appointment")

        # Get medical notes for this patient
        patient_notes = notes_by_patient.get(patient_id, [])

        # Print notes if any
        if patient_notes:
            print("\nMedical Notes:")

            # Sort notes by date (newest first)
            sorted_notes = sorted(
                patient_notes,
                key=lambda n: n.get("date", ""),
                reverse=True
            )

            for i, note in enumerate(sorted_notes):
                note_date = note.get("date", "")
                note_text = note.get("note", "") or note.get("text", "")
                note_type = note.get("type", "") or "Unknown"
                note_provider = note.get("provider_id", "") or "Unknown"

                # Update statistics
                total_notes += 1

                # Format the date for better readability
                formatted_date = note_date.split("T")[0] if note_date else "Unknown"

                print(f"Note {i+1} (Date: {formatted_date}, Type: {note_type}, Provider: {note_provider}):")

                # Clean up note text (remove special formatting characters)
                clean_text = note_text.replace("{LF}", "\n").replace("{CR}", "\n").replace("{cm}", ",")

                if len(clean_text) > 200:
                    print(f"{clean_text[:200]}...\n[Note truncated, total length: {len(clean_text)} characters]")
                else:
                    print(clean_text)
                print()
        else:
            print("\nNo medical notes found for this patient")

        print(f"{'-' * 120}")

    # Display summary
    print(f"\n{'=' * 120}")
    print(f"{'SUMMARY FOR ' + formatted_date:^120}")
    print(f"{'=' * 120}")

    print(f"\nTotal Appointments: {total_appointments} (including {total_appointments - total_patient_appointments} blocked time slots)")
    print(f"Total Patient Appointments: {total_patient_appointments}")
    print(f"Total Procedures: {total_procedures}")
    print(f"Total Clinical Notes: {total_notes}")

    if operatory_counts:
        print("\nAppointments by Operatory:")
        for operatory, count in sorted(operatory_counts.items(), key=lambda x: x[1], reverse=True):
            if operatory:
                print(f"  {operatory}: {count}")

    if provider_counts:
        print("\nAppointments by Provider:")
        for provider, count in sorted(provider_counts.items(), key=lambda x: x[1], reverse=True):
            if provider:
                print(f"  {provider}: {count}")

    if procedure_code_counts:
        print("\nTop Procedure Codes:")
        top_codes = sorted(procedure_code_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        for code, count in top_codes:
            if code:
                print(f"  {code}: {count}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Display detailed appointment information with procedure codes and notes.")
    parser.add_argument("--date", required=True, help="Date in YYYY-MM-DD format")
    parser.add_argument("--operatories", help="Comma-separated list of operatories to filter by")

    args = parser.parse_args()
    target_date = args.date

    # Validate date format
    try:
        datetime.strptime(target_date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        sys.exit(1)

    # Load credentials
    credentials = load_credentials()
    app_id = credentials.get("app_id")
    app_key = credentials.get("app_key")
    office_id = credentials.get("office_id")
    secret_key = credentials.get("secret_key")

    # Authenticate and get request key
    request_key = authenticate(app_id, app_key, office_id, secret_key)

    # Fetch appointments for the specified date
    appointments = fetch_appointments_for_date(request_key, target_date)

    # Fetch procedures for the specified date
    procedures = fetch_transactions(request_key, target_date)

    # Fetch medical notes for the specified date
    medical_notes = fetch_medical_notes(request_key, target_date)

    # Extract unique patient IDs from appointments
    patient_ids = []
    for appt in appointments:
        patient_id = appt.get("patient_id")
        if patient_id and patient_id not in patient_ids:
            patient_ids.append(patient_id)

    # Fetch chart numbers for all patients
    chart_numbers = fetch_patient_chart_numbers(request_key, patient_ids)

    # Parse operatories if provided
    selected_operatories = None
    if args.operatories:
        selected_operatories = [op.strip() for op in args.operatories.split(",")]

    # Display the detailed appointments
    display_detailed_appointments(
        appointments,
        procedures,
        medical_notes,
        chart_numbers,
        target_date,
        selected_operatories=selected_operatories
    )

if __name__ == "__main__":
    main()
