/**
 * Unified Job Persistence Layer for Voice Processing System
 * 
 * This module replaces scattered in-memory job tracking across the voice processing system
 * with a unified, durable persistence layer that survives server restarts.
 */

import { ProcessingJob, JobFilters, JobResult, QueueMessage } from '@/types/queue';

/**
 * Interface for job storage operations
 */
export interface JobStore {
  /**
   * Create a new job in persistent storage
   */
  createJob(job: Omit<ProcessingJob, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>;
  
  /**
   * Update an existing job with new data
   */
  updateJob(id: string, updates: Partial<ProcessingJob>): Promise<void>;
  
  /**
   * Retrieve a job by ID
   */
  getJob(id: string): Promise<ProcessingJob | null>;
  
  /**
   * List jobs with optional filtering
   */
  listJobs(filters?: JobFilters): Promise<ProcessingJob[]>;
  
  /**
   * Delete a job (typically used for cleanup)
   */
  deleteJob(id: string): Promise<void>;
  
  /**
   * Get jobs that need recovery (stuck in processing state)
   */
  getJobsForRecovery(): Promise<ProcessingJob[]>;
  
  /**
   * Get stuck jobs that may need intervention
   */
  getStuckJobs(): Promise<ProcessingJob[]>;
  
  /**
   * Get next pending job for processing
   */
  getNextPendingJob(): Promise<ProcessingJob | null>;
  
  /**
   * Mark job as processing and set start time
   */
  markJobAsProcessing(id: string): Promise<void>;
  
  /**
   * Cleanup old completed jobs
   */
  cleanup(olderThanDays: number): Promise<number>;
  
  /**
   * Get system health and statistics
   */
  getStats(): Promise<{
    total: number;
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    retrying: number;
  }>;
}

/**
 * Azure Table Storage implementation for production
 */
export class AzureTableJobStore implements JobStore {
  private tableName = 'ProcessingJobs';
  private tableClient: any; // Azure Table Storage client
  private initialized = false;

  constructor(private connectionString: string) {}

  private async ensureInitialized(): Promise<void> {
    if (this.initialized) return;

    try {
      // Import Azure SDK only when needed
      const { TableClient } = await import('@azure/data-tables');
      // Correct syntax: TableClient.fromConnectionString(connectionString, tableName)
      this.tableClient = TableClient.fromConnectionString(this.connectionString, this.tableName);
      
      // Create table if it doesn't exist
      await this.tableClient.createTable();
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize Azure Table Storage:', error);
      throw new Error('Azure Table Storage initialization failed');
    }
  }

  async createJob(job: Omit<ProcessingJob, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    await this.ensureInitialized();
    
    const id = this.generateJobId();
    const now = new Date().toISOString();
    
    const fullJob: ProcessingJob = {
      id,
      createdAt: now,
      updatedAt: now,
      retryCount: 0,
      ...job
    };

    const entity = {
      partitionKey: 'job',
      rowKey: id,
      ...this.jobToEntity(fullJob)
    };

    try {
      await this.tableClient.createEntity(entity);
      return id;
    } catch (error) {
      console.error('Failed to create job in Azure Table Storage:', error);
      throw new Error(`Failed to create job: ${error}`);
    }
  }

  async updateJob(id: string, updates: Partial<ProcessingJob>): Promise<void> {
    await this.ensureInitialized();

    const updatedEntity = {
      partitionKey: 'job',
      rowKey: id,
      updatedAt: new Date().toISOString(),
      ...this.updatesToEntity(updates)
    };

    try {
      await this.tableClient.updateEntity(updatedEntity, 'Merge');
    } catch (error) {
      console.error(`Failed to update job ${id}:`, error);
      throw new Error(`Failed to update job: ${error}`);
    }
  }

  async getJob(id: string): Promise<ProcessingJob | null> {
    await this.ensureInitialized();

    try {
      const entity = await this.tableClient.getEntity('job', id);
      return this.entityToJob(entity);
    } catch (error: any) {
      if (error.statusCode === 404) {
        return null;
      }
      console.error(`Failed to get job ${id}:`, error);
      throw new Error(`Failed to get job: ${error}`);
    }
  }

  async listJobs(filters: JobFilters = {}): Promise<ProcessingJob[]> {
    await this.ensureInitialized();

    let filter = "PartitionKey eq 'job'";
    
    // Add status filter
    if (filters.status && filters.status.length > 0) {
      const statusFilters = filters.status.map(s => `status eq '${s}'`).join(' or ');
      filter += ` and (${statusFilters})`;
    }

    // Add date filters
    if (filters.createdAfter) {
      filter += ` and createdAt ge '${filters.createdAfter}'`;
    }
    if (filters.createdBefore) {
      filter += ` and createdAt le '${filters.createdBefore}'`;
    }

    try {
      const entities = this.tableClient.listEntities({
        queryOptions: { filter }
      });

      const jobs: ProcessingJob[] = [];
      let count = 0;
      const limit = filters.limit || 100;

      for await (const entity of entities) {
        if (count >= limit) break;
        jobs.push(this.entityToJob(entity));
        count++;
      }

      return jobs;
    } catch (error) {
      console.error('Failed to list jobs:', error);
      throw new Error(`Failed to list jobs: ${error}`);
    }
  }

  async deleteJob(id: string): Promise<void> {
    await this.ensureInitialized();

    try {
      await this.tableClient.deleteEntity('job', id);
    } catch (error) {
      console.error(`Failed to delete job ${id}:`, error);
      throw new Error(`Failed to delete job: ${error}`);
    }
  }

  async getJobsForRecovery(): Promise<ProcessingJob[]> {
    // Get jobs that have been processing for too long (over 30 minutes)
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000).toISOString();
    
    return this.listJobs({
      status: ['processing'],
      createdBefore: thirtyMinutesAgo
    });
  }

  async getStuckJobs(): Promise<ProcessingJob[]> {
    // Get jobs that have been processing for too long (over 15 minutes)
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000).toISOString();
    
    return this.listJobs({
      status: ['processing'],
      createdBefore: fifteenMinutesAgo
    });
  }

  async getNextPendingJob(): Promise<ProcessingJob | null> {
    const pendingJobs = await this.listJobs({
      status: ['pending'],
      limit: 1
    });
    
    return pendingJobs.length > 0 ? pendingJobs[0] : null;
  }

  async markJobAsProcessing(id: string): Promise<void> {
    await this.updateJob(id, {
      status: 'processing',
      startedAt: new Date().toISOString()
    });
  }

  async cleanup(olderThanDays: number): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    const oldJobs = await this.listJobs({
      status: ['completed', 'failed'],
      createdBefore: cutoffDate.toISOString()
    });

    let deletedCount = 0;
    for (const job of oldJobs) {
      try {
        await this.deleteJob(job.id);
        deletedCount++;
      } catch (error) {
        console.error(`Failed to delete job ${job.id} during cleanup:`, error);
      }
    }

    return deletedCount;
  }

  async getStats(): Promise<{
    total: number;
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    retrying: number;
  }> {
    try {
      const [pending, processing, completed, failed, retrying] = await Promise.all([
        this.listJobs({ status: ['pending'], limit: 1000 }),
        this.listJobs({ status: ['processing'], limit: 1000 }),
        this.listJobs({ status: ['completed'], limit: 1000 }),
        this.listJobs({ status: ['failed'], limit: 1000 }),
        this.listJobs({ status: ['retrying'], limit: 1000 })
      ]);

      return {
        total: pending.length + processing.length + completed.length + failed.length + retrying.length,
        pending: pending.length,
        processing: processing.length,
        completed: completed.length,
        failed: failed.length,
        retrying: retrying.length
      };
    } catch (error) {
      console.error('Failed to get job stats:', error);
      return { total: 0, pending: 0, processing: 0, completed: 0, failed: 0, retrying: 0 };
    }
  }

  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private jobToEntity(job: ProcessingJob): any {
    return {
      id: job.id,
      filename: job.filename,
      containerPath: job.containerPath,
      status: job.status,
      retryCount: job.retryCount,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
      startedAt: job.startedAt || '',
      completedAt: job.completedAt || '',
      errorDetails: job.errorDetails ? JSON.stringify(job.errorDetails) : '',
      results: job.results ? JSON.stringify(job.results) : '',
      queueMessage: job.queueMessage ? JSON.stringify(job.queueMessage) : '',
      metadata: job.metadata ? JSON.stringify(job.metadata) : ''
    };
  }

  private updatesToEntity(updates: Partial<ProcessingJob>): any {
    const entity: any = {};
    
    if (updates.status) entity.status = updates.status;
    if (updates.retryCount !== undefined) entity.retryCount = updates.retryCount;
    if (updates.startedAt) entity.startedAt = updates.startedAt;
    if (updates.completedAt) entity.completedAt = updates.completedAt;
    if (updates.errorDetails) entity.errorDetails = JSON.stringify(updates.errorDetails);
    if (updates.results) entity.results = JSON.stringify(updates.results);
    if (updates.queueMessage) entity.queueMessage = JSON.stringify(updates.queueMessage);
    if (updates.metadata) entity.metadata = JSON.stringify(updates.metadata);

    return entity;
  }

  private entityToJob(entity: any): ProcessingJob {
    return {
      id: entity.id,
      filename: entity.filename,
      containerPath: entity.containerPath,
      status: entity.status,
      retryCount: entity.retryCount || 0,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      startedAt: entity.startedAt || undefined,
      completedAt: entity.completedAt || undefined,
      errorDetails: entity.errorDetails ? JSON.parse(entity.errorDetails) : undefined,
      results: entity.results ? JSON.parse(entity.results) : undefined,
      queueMessage: entity.queueMessage ? JSON.parse(entity.queueMessage) : undefined,
      metadata: entity.metadata ? JSON.parse(entity.metadata) : undefined
    };
  }
}

/**
 * In-memory implementation for development and testing
 */
export class InMemoryJobStore implements JobStore {
  private jobs = new Map<string, ProcessingJob>();
  private idCounter = 0;

  async createJob(job: Omit<ProcessingJob, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const id = `job_${++this.idCounter}_${Date.now()}`;
    const now = new Date().toISOString();
    
    const fullJob: ProcessingJob = {
      id,
      createdAt: now,
      updatedAt: now,
      retryCount: 0,
      ...job
    };

    this.jobs.set(id, fullJob);
    return id;
  }

  async updateJob(id: string, updates: Partial<ProcessingJob>): Promise<void> {
    const job = this.jobs.get(id);
    if (!job) {
      throw new Error(`Job ${id} not found`);
    }

    const updatedJob = {
      ...job,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    this.jobs.set(id, updatedJob);
  }

  async getJob(id: string): Promise<ProcessingJob | null> {
    return this.jobs.get(id) || null;
  }

  async listJobs(filters: JobFilters = {}): Promise<ProcessingJob[]> {
    let jobs = Array.from(this.jobs.values());

    // Apply status filter
    if (filters.status && filters.status.length > 0) {
      jobs = jobs.filter(job => filters.status!.includes(job.status));
    }

    // Apply date filters
    if (filters.createdAfter) {
      jobs = jobs.filter(job => job.createdAt >= filters.createdAfter!);
    }
    if (filters.createdBefore) {
      jobs = jobs.filter(job => job.createdAt <= filters.createdBefore!);
    }

    // Apply limit
    if (filters.limit) {
      jobs = jobs.slice(0, filters.limit);
    }

    return jobs;
  }

  async deleteJob(id: string): Promise<void> {
    this.jobs.delete(id);
  }

  async getJobsForRecovery(): Promise<ProcessingJob[]> {
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000).toISOString();
    
    return this.listJobs({
      status: ['processing'],
      createdBefore: thirtyMinutesAgo
    });
  }

  async getStuckJobs(): Promise<ProcessingJob[]> {
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000).toISOString();
    
    return this.listJobs({
      status: ['processing'],
      createdBefore: fifteenMinutesAgo
    });
  }

  async getNextPendingJob(): Promise<ProcessingJob | null> {
    const pendingJobs = await this.listJobs({
      status: ['pending'],
      limit: 1
    });
    
    return pendingJobs.length > 0 ? pendingJobs[0] : null;
  }

  async markJobAsProcessing(id: string): Promise<void> {
    await this.updateJob(id, {
      status: 'processing',
      startedAt: new Date().toISOString()
    });
  }

  async cleanup(olderThanDays: number): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    const cutoffIso = cutoffDate.toISOString();
    
    const jobsToDelete = Array.from(this.jobs.values()).filter(job => 
      (job.status === 'completed' || job.status === 'failed') && 
      job.createdAt < cutoffIso
    );

    jobsToDelete.forEach(job => this.jobs.delete(job.id));
    return jobsToDelete.length;
  }

  async getStats(): Promise<{
    total: number;
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    retrying: number;
  }> {
    const jobs = Array.from(this.jobs.values());
    
    return {
      total: jobs.length,
      pending: jobs.filter(j => j.status === 'pending').length,
      processing: jobs.filter(j => j.status === 'processing').length,
      completed: jobs.filter(j => j.status === 'completed').length,
      failed: jobs.filter(j => j.status === 'failed').length,
      retrying: jobs.filter(j => j.status === 'retrying').length
    };
  }
}

/**
 * Factory function to create appropriate job store based on environment
 */
export function createJobStore(): JobStore {
  // Try to get connection string or build it from account name/key
  let azureConnectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
  
  if (!azureConnectionString && process.env.AZURE_STORAGE_ACCOUNT_NAME && process.env.AZURE_STORAGE_ACCOUNT_KEY) {
    // Build connection string from separate values
    azureConnectionString = `DefaultEndpointsProtocol=https;AccountName=${process.env.AZURE_STORAGE_ACCOUNT_NAME};AccountKey=${process.env.AZURE_STORAGE_ACCOUNT_KEY};EndpointSuffix=core.windows.net`;
    console.log('Built Azure connection string from account name and key');
  }
  
  if (azureConnectionString && process.env.NODE_ENV === 'production') {
    console.log('Using Azure Table Storage for job persistence');
    return new AzureTableJobStore(azureConnectionString);
  } else {
    console.log('Using in-memory storage for job persistence (development mode)');
    return new InMemoryJobStore();
  }
}

// Export singleton instance
export const jobStore = createJobStore();

/**
 * Job recovery utilities
 */
export class JobRecovery {
  constructor(private store: JobStore) {}

  /**
   * Recover jobs that were interrupted during processing
   */
  async recoverInterruptedJobs(): Promise<ProcessingJob[]> {
    const jobsToRecover = await this.store.getJobsForRecovery();
    
    for (const job of jobsToRecover) {
      try {
        await this.store.updateJob(job.id, {
          status: 'pending',
          retryCount: job.retryCount + 1,
          errorDetails: {
            ...job.errorDetails,
            recovery: {
              recoveredAt: new Date().toISOString(),
              reason: 'Job was stuck in processing state',
              previousStatus: job.status
            }
          }
        });
        
        console.log(`Recovered job ${job.id} (${job.filename})`);
      } catch (error) {
        console.error(`Failed to recover job ${job.id}:`, error);
      }
    }
    
    return jobsToRecover;
  }

  /**
   * Clean up old completed jobs
   */
  async cleanupOldJobs(olderThanDays: number = 30): Promise<number> {
    try {
      const deletedCount = await this.store.cleanup(olderThanDays);
      console.log(`Cleaned up ${deletedCount} old jobs`);
      return deletedCount;
    } catch (error) {
      console.error('Failed to cleanup old jobs:', error);
      return 0;
    }
  }

  /**
   * Get system health status
   */
  async getSystemHealth(): Promise<{
    healthy: boolean;
    stats: any;
    issues: string[];
  }> {
    try {
      const stats = await this.store.getStats();
      const issues: string[] = [];
      
      // Check for concerning patterns
      if (stats.failed > stats.completed && stats.total > 10) {
        issues.push('High failure rate detected');
      }
      
      if (stats.processing > 50) {
        issues.push('Large number of jobs stuck in processing');
      }
      
      const healthy = issues.length === 0;
      
      return { healthy, stats, issues };
    } catch (error) {
      return {
        healthy: false,
        stats: null,
        issues: ['Job store unavailable']
      };
    }
  }
}

// Export recovery instance
export const jobRecovery = new JobRecovery(jobStore);