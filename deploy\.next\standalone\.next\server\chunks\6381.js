"use strict";exports.id=6381,exports.ids=[6381],exports.modules={86381:(e,t,a)=>{a.r(t),a.d(t,{AgentChat:()=>o});var s=a(60687),r=a(43210),i=a(83753),n=a(27900);function o({isDarkMode:e}){let[t,a]=(0,r.useState)([{id:"1",type:"assistant",content:"Hello! I'm your AI Office Manager. I can help you analyze practice data while keeping all patient information secure and private. What would you like to know about your practice?",timestamp:new Date}]),[o,l]=(0,r.useState)(""),[d,c]=(0,r.useState)(!1),m=(0,r.useRef)(null),u=async e=>{if(e.preventDefault(),!o.trim()||d)return;let s={id:Date.now().toString(),type:"user",content:o.trim(),timestamp:new Date};a(e=>[...e,s]),l(""),c(!0);try{let e={id:(Date.now()+1).toString(),type:"assistant",content:"",timestamp:new Date,isLoading:!0};a(t=>[...t,e]);let r=await fetch("/api/ai/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:s.content,history:t.slice(-10)})});if(!r.ok)throw Error("Failed to get AI response");let i=await r.json();a(e=>e.filter(e=>!e.isLoading).concat({id:(Date.now()+2).toString(),type:"assistant",content:i.response||"Sorry, I couldn't process that request.",timestamp:new Date}))}catch(e){console.error("Error in AI chat:",e),a(e=>e.filter(e=>!e.isLoading).concat({id:(Date.now()+2).toString(),type:"assistant",content:"Sorry, there was an error processing your request. Please try again later.",timestamp:new Date}))}finally{c(!1)}};return(0,s.jsxs)("div",{className:`flex flex-col h-[600px] rounded-lg border ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[(0,s.jsxs)("div",{className:"p-4 border-b flex items-center",children:[(0,s.jsx)(i.A,{className:"w-5 h-5 mr-2 text-blue-500"}),(0,s.jsx)("h2",{className:"font-semibold",children:"AI Office Manager"})]}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[t.map(t=>(0,s.jsx)("div",{className:`flex ${"user"===t.type?"justify-end":"justify-start"}`,children:(0,s.jsx)("div",{className:`max-w-[80%] rounded-lg p-3 ${"user"===t.type?e?"bg-blue-600 text-white":"bg-blue-100 text-gray-800":e?"bg-gray-700 text-white":"bg-gray-100 text-gray-800"} ${t.isLoading?"animate-pulse":""}`,children:t.isLoading?(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}):(0,s.jsx)("div",{className:"whitespace-pre-wrap",children:t.content})})},t.id)),(0,s.jsx)("div",{ref:m})]}),(0,s.jsxs)("form",{onSubmit:u,className:"p-4 border-t flex",children:[(0,s.jsx)("input",{type:"text",value:o,onChange:e=>{l(e.target.value)},placeholder:"Ask about appointments, patients, or practice metrics...",className:`flex-1 p-2 rounded-l-md border-r-0 focus:outline-none ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`,disabled:d}),(0,s.jsx)("button",{type:"submit",disabled:d||!o.trim(),className:`px-4 py-2 rounded-r-md ${d||!o.trim()?"bg-gray-300 dark:bg-gray-600 cursor-not-allowed":"bg-blue-500 hover:bg-blue-600 text-white"}`,children:(0,s.jsx)(n.A,{className:"w-5 h-5"})})]})]})}}};