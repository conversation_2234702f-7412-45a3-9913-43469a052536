import requests
import json
from datetime import datetime, timedelta
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 30

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def fetch_all_appointments(request_key, date):
    """Fetch all appointments for a specific date using multiple date formats."""
    headers = {"Request-Key": request_key}
    
    # Try different date formats
    date_formats = [
        date,  # Try the original format (YYYY-MM-DD)
        date.replace("-", "/"),  # Try YYYY/MM/DD
        f"{date.split('-')[1]}/{date.split('-')[2]}/{date.split('-')[0]}"  # Try MM/DD/YYYY
    ]
    
    all_items = []
    
    for date_format in date_formats:
        try:
            print(f"\nTrying date format: {date_format}")
            params = {"date": date_format}
            
            # Try v2 endpoint
            resp = requests.get(
                f"{API_BASE_V2}/appointments", 
                headers=headers, 
                params=params,
                timeout=API_TIMEOUT
            )
            
            if resp.status_code == 200:
                data = resp.json()
                
                # Extract items from v2 response (which has a different structure)
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    if items:
                        print(f"Found {len(items)} appointments using v2 endpoint with date format: {date_format}")
                        all_items.extend(items)
                        
                        # Print sample appointments
                        print("\nSample appointments:")
                        for i, appt in enumerate(sorted(items, key=lambda a: a.get("time", ""))[:5]):
                            print(f"{i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('operatory', 'N/A')} - {appt.get('description', '')}")
            
            # Try v4 endpoint
            resp = requests.get(
                f"{API_BASE_V4}/appointments", 
                headers=headers, 
                params=params,
                timeout=API_TIMEOUT
            )
            
            if resp.status_code == 200:
                data = resp.json()
                items = data.get("items", [])
                if items:
                    print(f"Found {len(items)} appointments using v4 endpoint with date format: {date_format}")
                    
                    # Check if these appointments have operatory information
                    has_operatory = any(a.get("operatory") for a in items)
                    if has_operatory:
                        print("These appointments have operatory information")
                    else:
                        print("These appointments do NOT have operatory information")
                    
                    # Only add them if they're not duplicates
                    new_items = []
                    existing_ids = set(a.get("appointment_sr_no") for a in all_items if a.get("appointment_sr_no"))
                    for item in items:
                        if item.get("appointment_sr_no") not in existing_ids:
                            new_items.append(item)
                    
                    if new_items:
                        print(f"Adding {len(new_items)} new appointments from v4 endpoint")
                        all_items.extend(new_items)
        except Exception as e:
            print(f"Error with date format {date_format}: {e}")
    
    print(f"\nTotal unique appointments found: {len(all_items)}")
    
    # Print all operatories found in appointments
    operatories_found = set(a.get("operatory") for a in all_items if a.get("operatory"))
    if operatories_found:
        print(f"Operatories found in appointments: {', '.join(sorted(operatories_found))}")
    
    # Group appointments by operatory
    appts_by_operatory = {}
    for appt in all_items:
        operatory = appt.get("operatory")
        if operatory:
            if operatory not in appts_by_operatory:
                appts_by_operatory[operatory] = []
            appts_by_operatory[operatory].append(appt)
    
    # Print appointments by operatory
    print("\nAppointments by operatory:")
    for operatory, appts in sorted(appts_by_operatory.items()):
        print(f"\nOperatory: {operatory} - {len(appts)} appointments")
        for appt in sorted(appts, key=lambda a: a.get("time", "")):
            time = appt.get("time", "")
            patient_name = appt.get("patient_name", "Unknown")
            provider_id = appt.get("provider_id", "N/A")
            description = appt.get("description", "")
            
            # Truncate long descriptions
            if len(description) > 30:
                description = description[:27] + "..."
            
            print(f"  {time} - {patient_name} - {provider_id} - {description}")
    
    return all_items

def main():
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Get date from command line or use default
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        date = "2025-05-16"  # Default date
    
    # Fetch all appointments for the date
    appointments = fetch_all_appointments(request_key, date)
    
    print("\nAnalysis complete.")

if __name__ == "__main__":
    main()
