@echo off
echo ========================================
echo    Dental App Logs Viewer
echo ========================================
echo.

cd /d "C:\DentalApp"

echo Select log type to view:
echo.
echo 1. Recent logs (last 50 lines)
echo 2. Error logs only
echo 3. Full application log
echo 4. PM2 service logs
echo 5. Installation log
echo 6. Open logs folder
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto recent_logs
if "%choice%"=="2" goto error_logs
if "%choice%"=="3" goto full_logs
if "%choice%"=="4" goto pm2_logs
if "%choice%"=="5" goto install_logs
if "%choice%"=="6" goto open_folder

echo Invalid choice. Showing recent logs...

:recent_logs
echo.
echo ========================================
echo    Recent Application Logs (Last 50 lines)
echo ========================================
echo.
if exist "logs\combined.log" (
    powershell "Get-Content 'logs\combined.log' -Tail 50"
) else (
    echo No application logs found.
    echo The service may not have started yet.
)
goto end

:error_logs
echo.
echo ========================================
echo    Error Logs Only
echo ========================================
echo.
if exist "logs\error.log" (
    type "logs\error.log"
) else (
    echo No error logs found. ✓
    echo This is good - no errors have been logged.
)
goto end

:full_logs
echo.
echo ========================================
echo    Full Application Log
echo ========================================
echo.
if exist "logs\combined.log" (
    type "logs\combined.log"
) else (
    echo No application logs found.
)
goto end

:pm2_logs
echo.
echo ========================================
echo    PM2 Service Logs
echo ========================================
echo.
call pm2 logs dental-app --lines 50
goto end

:install_logs
echo.
echo ========================================
echo    Installation Log
echo ========================================
echo.
if exist "logs\install.log" (
    type "logs\install.log"
) else (
    echo No installation log found.
)
goto end

:open_folder
echo.
echo Opening logs folder...
start explorer "C:\DentalApp\logs"
goto end

:end
echo.
echo ========================================
echo    Log Analysis
echo ========================================
echo.

:: Check for common issues
if exist "logs\error.log" (
    findstr /i "error" "logs\error.log" >nul 2>&1
    if %errorLevel%==0 (
        echo ⚠️  Errors detected in error log
        echo    Check error.log for details
    )
)

if exist "logs\combined.log" (
    findstr /i "EADDRINUSE" "logs\combined.log" >nul 2>&1
    if %errorLevel%==0 (
        echo ⚠️  Port conflict detected
        echo    Port 3000 may be in use by another application
    )
    
    findstr /i "ENOENT" "logs\combined.log" >nul 2>&1
    if %errorLevel%==0 (
        echo ⚠️  File not found errors detected
        echo    Check file paths and permissions
    )
    
    findstr /i "database" "logs\combined.log" >nul 2>&1
    if %errorLevel%==0 (
        echo ℹ️  Database activity detected
        echo    Database operations are being logged
    )
)

echo.
echo ========================================
echo    Log File Locations
echo ========================================
echo.
echo Application Logs:
echo - Combined: C:\DentalApp\logs\combined.log
echo - Errors:   C:\DentalApp\logs\error.log
echo - Output:   C:\DentalApp\logs\output.log
echo.
echo System Logs:
echo - Install:  C:\DentalApp\logs\install.log
echo - PM2:      Use 'pm2 logs dental-app'
echo.

echo Press any key to continue...
pause >nul
