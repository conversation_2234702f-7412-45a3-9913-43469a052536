import { NextRequest, NextResponse } from 'next/server';
import { AzureStorageService } from '@/lib/azure-storage';
import { OpenAI } from 'openai';
import { FileResolver } from '@/lib/file-resolver';

// Direct transcription function to avoid HTTP calls
async function transcribeFileDirect(
  fileName: string,
  forceRetranscribe: boolean = false
): Promise<{ success: boolean; transcription?: string; error?: string }> {
  try {
    // Use the FileResolver to find the actual file - STRAIGHTFORWARD!
    console.log(`🎯 BATCH-PROCESS: Starting file resolution for "${fileName}"`);
    const fileResult = await FileResolver.findAudioFile(fileName);
    
    if (!fileResult.found) {
      console.error(`❌ BATCH-PROCESS: File not found - ${fileResult.error}`);
      return { 
        success: false, 
        error: `File not found: ${fileName}. ${fileResult.error}` 
      };
    }
    
    const actualFilePath = fileResult.actualPath!;
    console.log(`✅ BATCH-PROCESS: File resolved - "${fileName}" -> "${actualFilePath}"`)

    // Create JSON file path
    const baseName = actualFilePath.replace(/\.[^/.]+$/, "");
    const jsonPath = `${baseName}.json`;

    // Check if transcription already exists
    if (!forceRetranscribe) {
      try {
        const existingData = await AzureStorageService.downloadJson(jsonPath);
        if (existingData && existingData.transcription) {
          return { success: true, transcription: existingData.transcription };
        }
      } catch (error) {
        // No existing transcription, proceed with new one
      }
    }

    // Get audio file from Azure Blob Storage
    const audioBuffer = await AzureStorageService.downloadFile(actualFilePath);
    
    if (audioBuffer.length < 1024) {
      return { success: false, error: `File too small (${audioBuffer.length} bytes)` };
    }

    // Initialize OpenAI client
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    // Create proper File object for OpenAI
    const fileObj = Object.assign(audioBuffer, {
      name: fileName.split('/').pop() || fileName,
      type: getContentTypeFromFileName(fileName),
      lastModified: Date.now(),
      webkitRelativePath: '',
      size: audioBuffer.length,
      stream: () => new ReadableStream(),
      text: () => Promise.resolve(''),
      arrayBuffer: () => Promise.resolve(audioBuffer.buffer),
      slice: () => audioBuffer
    }) as File;

    // Call OpenAI transcription
    const transcription = await openai.audio.transcriptions.create({
      file: fileObj,
      model: 'whisper-1',
      response_format: 'json',
      temperature: 0.2
    });

    // Save transcription to Azure
    await AzureStorageService.uploadJson(jsonPath, {
      transcription: transcription.text,
      createdAt: new Date().toISOString(),
      model: 'whisper-1',
      fileName: fileName
    });

    return { success: true, transcription: transcription.text };
  } catch (error) {
    console.error(`Direct transcription failed for ${fileName}:`, error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Helper function to get content type from filename
function getContentTypeFromFileName(fileName: string): string {
  const ext = fileName.toLowerCase().split('.').pop();
  switch (ext) {
    case 'mp3': return 'audio/mpeg';
    case 'wav': return 'audio/wav';
    case 'm4a': return 'audio/mp4';
    case 'webm': return 'audio/webm';
    case 'ogg': return 'audio/ogg';
    case 'flac': return 'audio/flac';
    default: return 'audio/mpeg';
  }
}

interface RobustProcessingJob {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  totalFiles: number;
  processedFiles: number;
  failedFiles: number;
  currentBatch: number;
  totalBatches: number;
  startedAt: string;
  lastUpdated: string;
  completedAt?: string;
  results: {
    filename: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    error?: string;
    startedAt?: string;
    completedAt?: string;
  }[];
  error?: string;
}

// Use Azure Storage to persist jobs instead of in-memory
class JobStorage {
  private static JOBS_PATH = 'metadata/processing-jobs.json';

  static async saveJob(job: RobustProcessingJob): Promise<void> {
    try {
      let allJobs: Record<string, RobustProcessingJob> = {};
      
      try {
        allJobs = await AzureStorageService.downloadJson(this.JOBS_PATH);
      } catch (error) {
        // Jobs file doesn't exist yet, start with empty object
      }
      
      allJobs[job.id] = job;
      await AzureStorageService.uploadJson(this.JOBS_PATH, allJobs);
    } catch (error) {
      console.error('Failed to save job:', error);
    }
  }

  static async getJob(jobId: string): Promise<RobustProcessingJob | null> {
    try {
      const allJobs = await AzureStorageService.downloadJson(this.JOBS_PATH);
      return allJobs[jobId] || null;
    } catch (error) {
      return null;
    }
  }

  static async getAllJobs(): Promise<Record<string, RobustProcessingJob>> {
    try {
      return await AzureStorageService.downloadJson(this.JOBS_PATH);
    } catch (error) {
      return {};
    }
  }
}

export async function POST(request: NextRequest) {
  console.warn(`Deprecated endpoint called: ${request.url}`);
  console.warn('This endpoint is deprecated. Use /api/voice/auto-process instead.');
  
  try {
    const { batchSize = 5, forceRetranscribe = false } = await request.json();

    console.log(`🚀 Starting robust batch processing with batch size: ${batchSize}`);

    if (!AzureStorageService.isConfigured()) {
      return NextResponse.json({
        error: 'Azure Storage is not configured'
      }, { status: 400 });
    }

    // Get all audio files
    const allFiles = await AzureStorageService.listFiles('');
    const audioFiles = allFiles.filter(file => {
      const ext = file.name.toLowerCase().split('.').pop();
      const isAudioExtension = ['mp3', 'wav', 'm4a', 'webm', 'ogg', 'flac', 'aac'].includes(ext || '');
      const isSystemFile = file.name.startsWith('.') || file.name.startsWith('_');
      return isAudioExtension && !isSystemFile && file.size > 1024;
    });

    if (audioFiles.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No audio files found to process'
      });
    }

    // Filter out already transcribed files if not force retranscribing
    let filesToProcess = audioFiles;
    if (!forceRetranscribe) {
      try {
        const existingTranscriptions = await AzureStorageService.downloadJson('metadata/transcriptions.json');
        filesToProcess = audioFiles.filter(file => {
          const recordingId = `azure-${file.name}-${file.size}-${Math.floor(file.size / 1024)}`;
          return !existingTranscriptions[recordingId];
        });
      } catch (error) {
        // No existing transcriptions, process all files
      }
    }

    if (filesToProcess.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'All files are already transcribed'
      });
    }

    // Create job with batch organization
    const jobId = `robust-${Date.now()}`;
    const totalBatches = Math.ceil(filesToProcess.length / batchSize);
    
    const job: RobustProcessingJob = {
      id: jobId,
      status: 'pending',
      totalFiles: filesToProcess.length,
      processedFiles: 0,
      failedFiles: 0,
      currentBatch: 0,
      totalBatches,
      startedAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      results: filesToProcess.map(file => ({
        filename: file.name,
        status: 'pending'
      }))
    };

    // Save job to persistent storage
    await JobStorage.saveJob(job);

    // Start processing first batch immediately (asynchronously)
    setImmediate(() => {
      processBatch(job, filesToProcess, 0, batchSize)
        .catch(error => {
          console.error(`❌ Batch processing failed for job ${jobId}:`, error);
          job.status = 'failed';
          job.error = error.message;
          JobStorage.saveJob(job);
        });
    });
    
    return NextResponse.json({
      success: true,
      jobId,
      message: `Started robust processing of ${filesToProcess.length} files in ${totalBatches} batches`,
      monitorUrl: `/api/voice/batch-process-robust?jobId=${jobId}`,
      deprecated: true,
      deprecationMessage: 'This endpoint is deprecated. Use /api/voice/auto-process instead.'
    }, {
      headers: {
        'X-Deprecated': 'true',
        'X-Replacement': '/api/voice/auto-process'
      }
    });

  } catch (error) {
    console.error('Robust batch processing error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      deprecated: true,
      message: 'This endpoint is deprecated. Use /api/voice/auto-process instead.'
    }, { 
      status: 500,
      headers: {
        'X-Deprecated': 'true',
        'X-Replacement': '/api/voice/auto-process'
      }
    });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');
    const action = searchParams.get('action');

    if (action === 'continue' && jobId) {
      // Continue processing next batch
      return await continueProcessing(jobId);
    }

    if (jobId) {
      // Get specific job status
      const job = await JobStorage.getJob(jobId);
      if (!job) {
        return NextResponse.json({
          error: 'Job not found'
        }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        job,
        canContinue: job.status === 'running' && job.currentBatch < job.totalBatches
      });
    } else {
      // Get all jobs
      const allJobs = await JobStorage.getAllJobs();
      return NextResponse.json({
        success: true,
        jobs: Object.values(allJobs).map(job => ({
          id: job.id,
          status: job.status,
          totalFiles: job.totalFiles,
          processedFiles: job.processedFiles,
          progress: Math.round((job.processedFiles / job.totalFiles) * 100),
          startedAt: job.startedAt,
          completedAt: job.completedAt
        }))
      });
    }

  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function continueProcessing(jobId: string): Promise<NextResponse> {
  try {
    const job = await JobStorage.getJob(jobId);
    if (!job || job.status !== 'running') {
      return NextResponse.json({
        error: 'Job not found or not in running state'
      }, { status: 400 });
    }

    if (job.currentBatch >= job.totalBatches) {
      return NextResponse.json({
        success: true,
        message: 'Job already completed all batches'
      });
    }

    // Get files list again
    const allFiles = await AzureStorageService.listFiles('');
    const audioFiles = allFiles.filter(file => {
      const ext = file.name.toLowerCase().split('.').pop();
      return ['mp3', 'wav', 'm4a', 'webm', 'ogg', 'flac', 'aac'].includes(ext || '');
    });

    const batchSize = Math.ceil(job.totalFiles / job.totalBatches);
    const batchResult = await processBatch(job, audioFiles, job.currentBatch, batchSize);

    return NextResponse.json({
      success: true,
      batchResult,
      jobStatus: job.status,
      progress: Math.round((job.processedFiles / job.totalFiles) * 100),
      canContinue: job.currentBatch < job.totalBatches
    });

  } catch (error) {
    console.error('Continue processing error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function processBatch(
  job: RobustProcessingJob, 
  allFiles: any[], 
  batchIndex: number, 
  batchSize: number
): Promise<any> {
  console.log(`🔄 Processing batch ${batchIndex + 1}/${job.totalBatches} for job ${job.id}`);
  
  job.status = 'running';
  job.currentBatch = batchIndex + 1;
  job.lastUpdated = new Date().toISOString();
  await JobStorage.saveJob(job);

  const startIndex = batchIndex * batchSize;
  const endIndex = Math.min(startIndex + batchSize, job.results.length);
  const batchFiles = job.results.slice(startIndex, endIndex);

  const batchResults = {
    batchIndex: batchIndex + 1,
    totalBatches: job.totalBatches,
    processed: 0,
    failed: 0,
    details: [] as any[]
  };

  // Process files in this batch sequentially to avoid overwhelming the API
  for (const fileResult of batchFiles) {
    const resultIndex = job.results.findIndex(r => r.filename === fileResult.filename);
    
    try {
      job.results[resultIndex].status = 'processing';
      job.results[resultIndex].startedAt = new Date().toISOString();
      await JobStorage.saveJob(job);

      console.log(`   🎵 Processing: ${fileResult.filename}`);

      // Call direct transcription function
      const result = await transcribeFileDirect(fileResult.filename, true);

      if (result.success && result.transcription) {
        job.results[resultIndex].status = 'completed';
        job.results[resultIndex].completedAt = new Date().toISOString();
        job.processedFiles++;
        batchResults.processed++;
        
        console.log(`   ✅ Completed: ${fileResult.filename}`);
        batchResults.details.push({
          filename: fileResult.filename,
          status: 'success',
          transcriptionLength: result.transcription.length
        });
      } else {
        throw new Error(result.error || 'Unknown transcription error');
      }

    } catch (error) {
      job.results[resultIndex].status = 'failed';
      job.results[resultIndex].error = error instanceof Error ? error.message : 'Unknown error';
      job.results[resultIndex].completedAt = new Date().toISOString();
      job.failedFiles++;
      batchResults.failed++;
      
      console.error(`   ❌ Failed: ${fileResult.filename}`, error);
      batchResults.details.push({
        filename: fileResult.filename,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    job.lastUpdated = new Date().toISOString();
    await JobStorage.saveJob(job);

    // Small delay between files to be respectful to APIs
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Check if job is complete
  if (job.currentBatch >= job.totalBatches) {
    job.status = 'completed';
    job.completedAt = new Date().toISOString();
    console.log(`🎉 Job ${job.id} completed! Processed: ${job.processedFiles}, Failed: ${job.failedFiles}`);
  }

  job.lastUpdated = new Date().toISOString();
  await JobStorage.saveJob(job);

  return batchResults;
}