import { NextRequest, NextResponse } from 'next/server';
import { AzureStorageService } from '../../../../src/lib/azure-storage';
import { AzureStateManager } from '../../../../src/lib/azure-state-manager';
import { TranscriptionService } from '../../../../src/lib/transcription-service';
import { ErrorHandler } from '../../../../src/lib/error-handler';

export async function POST(request: NextRequest) {
  try {
    const { action, filename } = await request.json();

    console.log(`🧪 Test endpoint called: ${action} for ${filename || 'all files'}`);

    switch (action) {
      case 'check-services':
        return await checkServices();
      
      case 'list-files':
        return await listAudioFiles();
      
      case 'test-state':
        return await testStateManagement(filename);
      
      case 'test-transcription':
        return await testTranscription(filename);
      
      case 'reset-states':
        return await resetStates();
      
      case 'get-stats':
        return await getProcessingStats();
      
      default:
        return NextResponse.json(
          { success: false, error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

  } catch (error) {
    ErrorHandler.logError(error, { operation: 'process-test' });
    return NextResponse.json(
      { success: false, error: ErrorHandler.getUserMessage(error) },
      { status: 500 }
    );
  }
}

async function checkServices() {
  const results = {
    azureStorage: false,
    azureSpeech: false,
    services: {} as any
  };

  try {
    // Check Azure Storage
    results.azureStorage = AzureStorageService.isConfigured();
    if (results.azureStorage) {
      const files = await AzureStorageService.listFiles('');
      results.services.azureStorage = {
        configured: true,
        filesFound: files.length
      };
    }

    // Check Azure Speech Service
    results.azureSpeech = TranscriptionService.isConfigured();
    if (results.azureSpeech) {
      const health = await TranscriptionService.getServiceHealth();
      results.services.azureSpeech = health;
    }

    const overallHealth = results.azureStorage && results.azureSpeech;

    return NextResponse.json({
      success: true,
      healthy: overallHealth,
      message: overallHealth ? 'All services are healthy' : 'Some services are not configured',
      results
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      results
    });
  }
}

async function listAudioFiles() {
  try {
    const files = await AzureStorageService.listFiles('');
    const audioFiles = files.filter(f => f.name.match(/\.(mp3|wav|m4a|webm|ogg|flac|aac)$/i));
    
    // Get first 10 files with their states
    const fileStates = [];
    for (const file of audioFiles.slice(0, 10)) {
      const state = await AzureStateManager.loadState(file.path);
      fileStates.push({
        filename: file.name,
        path: file.path,
        size: file.size,
        lastModified: file.lastModified,
        state: state ? state.status : 'no-state'
      });
    }

    return NextResponse.json({
      success: true,
      totalAudioFiles: audioFiles.length,
      files: fileStates,
      message: `Found ${audioFiles.length} audio files`
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

async function testStateManagement(filename?: string) {
  try {
    if (!filename) {
      return NextResponse.json({
        success: false,
        error: 'Filename required for state management test'
      });
    }

    // Find file
    const actualPath = await AzureStorageService.findFileByName(filename);
    if (!actualPath) {
      return NextResponse.json({
        success: false,
        error: `File not found: ${filename}`
      });
    }

    // Get file metadata
    const metadata = await AzureStorageService.getFileMetadata(actualPath);
    if (!metadata?.exists) {
      return NextResponse.json({
        success: false,
        error: `File metadata not accessible: ${filename}`
      });
    }

    // Initialize or load state
    let state = await AzureStateManager.loadState(actualPath);
    if (!state) {
      const format = filename.toLowerCase().split('.').pop() || 'unknown';
      state = await AzureStateManager.initializeState(actualPath, metadata.size || 0, format);
    }

    return NextResponse.json({
      success: true,
      message: 'State management test successful',
      file: {
        filename,
        path: actualPath,
        size: metadata.size,
        format: state.metadata.format
      },
      state
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

async function testTranscription(filename?: string) {
  try {
    if (!filename) {
      return NextResponse.json({
        success: false,
        error: 'Filename required for transcription test'
      });
    }

    // Check if Azure Speech is configured
    if (!TranscriptionService.isConfigured()) {
      return NextResponse.json({
        success: false,
        error: 'Azure Speech Service not configured. Set AZURE_SPEECH_KEY and AZURE_SPEECH_REGION environment variables.'
      });
    }

    // Find and download file
    const actualPath = await AzureStorageService.findFileByName(filename);
    if (!actualPath) {
      return NextResponse.json({
        success: false,
        error: `File not found: ${filename}`
      });
    }

    const audioBuffer = await AzureStorageService.downloadFile(actualPath);
    
    // Test format validation
    const isValidFormat = await TranscriptionService.validateAudioFormat(audioBuffer, filename);
    if (!isValidFormat) {
      return NextResponse.json({
        success: false,
        error: `Invalid audio format for ${filename}. Supported formats: ${TranscriptionService.getSupportedFormats().join(', ')}`
      });
    }

    // Test transcription (with a timeout for the test)
    const transcriptionResult = await Promise.race([
      TranscriptionService.transcribeAudio(audioBuffer, filename, {
        language: 'en-US',
        enableDiarization: false
      }),
      new Promise<any>(resolve => setTimeout(() => resolve({
        success: false,
        error: 'Transcription test timed out (60s limit)'
      }), 60000))
    ]);

    return NextResponse.json({
      success: transcriptionResult.success,
      message: transcriptionResult.success ? 'Transcription test successful' : 'Transcription test failed',
      result: transcriptionResult,
      file: {
        filename,
        path: actualPath,
        size: audioBuffer.length,
        formatValid: isValidFormat
      }
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

async function resetStates() {
  try {
    const resetCount = await AzureStateManager.resetFailedFiles();
    const cleanedCount = await AzureStateManager.cleanupOrphanedStates();

    return NextResponse.json({
      success: true,
      message: `Reset ${resetCount} failed files and cleaned ${cleanedCount} orphaned states`
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

async function getProcessingStats() {
  try {
    const stats = await AzureStateManager.getProcessingStats();

    return NextResponse.json({
      success: true,
      stats,
      message: `Found ${stats.total} total files: ${stats.complete} complete, ${stats.pending} pending, ${stats.failed} failed`
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}