import requests
import json
from datetime import datetime, timedelta
from collections import defaultdict
import sys
import argparse

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 30

def get_request_key(office_id, secret_key, app_id, app_key):
    """Obtain a request key for API authentication."""
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": office_id,
            "secret_key": secret_key,
            "app_id": app_id,
            "app_key": app_key
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None

    data = auth_resp.json()
    return data.get("request_key")

def fetch_transactions(request_key, date):
    """Fetch all transactions for a specific date."""
    headers = {"Request-Key": request_key}
    params = {"date": date}

    # Fetch all pages to make sure we get all transactions
    all_items = []
    offset = 0
    limit = 100  # Increased limit to reduce number of API calls
    max_pages = 5  # Limit the number of pages to fetch

    print("Fetching transactions (this may take a moment)...")

    for page in range(max_pages):
        paged_params = params.copy()
        paged_params.update({"offset": offset, "limit": limit})

        try:
            resp = requests.get(
                f"{API_BASE_V4}/transactions",
                headers=headers,
                params=paged_params,
                timeout=API_TIMEOUT
            )

            if resp.status_code != 200:
                print(f"Error fetching transactions: {resp.status_code}")
                print(resp.text)
                break

            data = resp.json()
            items = data.get("items", [])
            all_items.extend(items)

            print(f"  Fetched {len(items)} transactions (page {page+1})")

            if len(items) < limit:
                break

            offset += limit
        except requests.exceptions.Timeout:
            print(f"Timeout while fetching transactions. Proceeding with {len(all_items)} transactions.")
            break
        except Exception as e:
            print(f"Error: {e}")
            break

    # Filter for procedure transactions only
    procedure_items = [t for t in all_items
                      if t.get("transaction_type") == "Procedure"
                      and float(t.get("amount", "0")) > 0]

    # Create a dictionary of patient names by patient ID
    patient_names = {}
    for item in all_items:
        patient_id = item.get("patient_id")
        patient_name = item.get("patient_name")
        if patient_id and patient_name and patient_name != "Unknown Patient":
            patient_names[patient_id] = patient_name

    # Update any transactions with missing patient names
    for item in procedure_items:
        patient_id = item.get("patient_id")
        if patient_id and (not item.get("patient_name") or item.get("patient_name") == "Unknown Patient"):
            if patient_id in patient_names:
                item["patient_name"] = patient_names[patient_id]

    print(f"Found {len(procedure_items)} procedure transactions")
    return procedure_items

def fetch_operatories(request_key, date_range=30):
    """Fetch a list of all operatories from appointment data."""
    print("Fetching list of operatories...")
    headers = {"Request-Key": request_key}

    # Try to get appointments for the last X days to find operatories
    operatories = set()

    # Try different date ranges
    end_date = datetime.now()
    start_date = end_date - timedelta(days=date_range)

    # Format dates as YYYY-MM-DD
    date_format = "%Y-%m-%d"
    current_date = start_date

    # Check every 7 days to avoid too many API calls
    while current_date <= end_date:
        date_str = current_date.strftime(date_format)

        try:
            resp = requests.get(
                f"{API_BASE_V2}/appointments",
                headers=headers,
                params={"date": date_str},
                timeout=API_TIMEOUT
            )

            if resp.status_code == 200:
                data = resp.json()

                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])

                    # Extract operatories
                    for item in items:
                        operatory = item.get("operatory")
                        if operatory and operatory not in ["", "N/A"]:
                            operatories.add(operatory)
        except Exception as e:
            print(f"Error fetching operatories: {e}")

        # Move to next date
        current_date += timedelta(days=7)

    # Print the list of operatories
    if operatories:
        print(f"Found {len(operatories)} operatories")
    else:
        print("No operatories found in appointment data")

    return sorted(operatories)

def fetch_appointments(request_key, date, provider_id=None, operatories=None):
    """Fetch appointments for a specific date and optionally filter by provider and operatories."""
    headers = {"Request-Key": request_key}

    # Get all appointments for the day first
    print(f"Fetching all appointments for {date}...")

    # Try different date formats
    date_formats = [
        date,  # Try the original format (YYYY-MM-DD)
        date.replace("-", "/"),  # Try YYYY/MM/DD
        f"{date.split('-')[1]}/{date.split('-')[2]}/{date.split('-')[0]}"  # Try MM/DD/YYYY
    ]

    all_items = []

    for date_format in date_formats:
        try:
            params = {"date": date_format}
            resp = requests.get(
                f"{API_BASE_V2}/appointments",
                headers=headers,
                params=params,
                timeout=API_TIMEOUT
            )

            if resp.status_code == 200:
                data = resp.json()

                # Extract items from v2 response (which has a different structure)
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    if items:
                        all_items.extend(items)
                        print(f"Found {len(items)} appointments using date format: {date_format}")
                        break  # Stop trying different formats if we found appointments
            elif resp.status_code != 204:
                print(f"Error with date format {date_format}: {resp.status_code}")
        except Exception as e:
            print(f"Error with date format {date_format}: {e}")

    if not all_items:
        print("No appointments found. Trying v4 endpoint...")
        return fetch_appointments_v4(request_key, date, provider_id)

    print(f"Total appointments found: {len(all_items)}")

    # Print all operatories found in appointments
    operatories_found = set(a.get("operatory") for a in all_items if a.get("operatory"))
    if operatories_found:
        print(f"Operatories found in appointments: {', '.join(sorted(operatories_found))}")

    # Filter to only include appointments for the specified date
    # (in case we got appointments for other dates)
    date_items = []
    for a in all_items:
        appt_date = a.get("date", "")
        if appt_date:
            # Try to normalize the date format
            try:
                if "/" in appt_date:
                    parts = appt_date.split("/")
                    if len(parts[2]) == 4:  # MM/DD/YYYY
                        normalized_date = f"{parts[2]}-{parts[0]}-{parts[1]}"
                    else:  # YYYY/MM/DD
                        normalized_date = appt_date.replace("/", "-")
                else:
                    normalized_date = appt_date

                if normalized_date.startswith(date):
                    date_items.append(a)
            except:
                # If we can't parse the date, include it anyway
                date_items.append(a)

    # Remove duplicate appointments (the API returns each appointment 3 times)
    unique_items = []
    seen_appointments = set()

    for appt in date_items:
        # Create a unique key for each appointment
        key = (
            appt.get("patient_id", ""),
            appt.get("time", ""),
            appt.get("operatory", ""),
            appt.get("description", "")
        )

        if key not in seen_appointments:
            seen_appointments.add(key)
            unique_items.append(appt)

    print(f"Removed {len(date_items) - len(unique_items)} duplicate appointments")
    date_items = unique_items

    # If operatories are specified, filter by operatories ONLY (ignore provider)
    if operatories:
        filtered_items = [a for a in date_items if a.get("operatory") in operatories]
        print(f"Found {len(filtered_items)} appointments in operatories: {', '.join(operatories)}")

        # Print the appointments found
        if filtered_items:
            print("\nAppointments in specified operatories:")
            for a in sorted(filtered_items, key=lambda x: x.get("time", "")):
                print(f"{a.get('time', '')} - {a.get('patient_name', 'Unknown')} - {a.get('operatory', 'N/A')} - {a.get('description', '')}")

        return filtered_items
    # Otherwise, filter by provider if specified
    elif provider_id:
        filtered_items = [a for a in date_items if a.get("provider_id") == provider_id]
        print(f"Found {len(filtered_items)} appointments for provider: {provider_id}")
        return filtered_items
    else:
        return date_items

    return []

def fetch_appointments_v4(request_key, date, provider_id=None):
    """Fetch appointments from v4 endpoint as a fallback."""
    headers = {"Request-Key": request_key}
    params = {"date": date}

    # Fetch all pages to make sure we get all appointments
    all_items = []
    offset = 0
    limit = 100

    try:
        while True:
            paged_params = params.copy()
            paged_params.update({"offset": offset, "limit": limit})

            resp = requests.get(
                f"{API_BASE_V4}/appointments",
                headers=headers,
                params=paged_params,
                timeout=API_TIMEOUT
            )

            if resp.status_code != 200:
                print(f"Error fetching appointments: {resp.status_code}")
                print(resp.text)
                break

            data = resp.json()
            items = data.get("items", [])
            all_items.extend(items)

            if len(items) < limit:
                break

            offset += limit

        # Filter by provider if specified
        if provider_id:
            all_items = [a for a in all_items if a.get("provider_id") == provider_id]

        # Filter to only include appointments for the specified date
        all_items = [a for a in all_items if a.get("date", "").startswith(date)]

        return all_items
    except Exception as e:
        print(f"Error: {e}")
        return []

def create_office_schedule(transactions, appointments, provider_id, date_str, show_all_providers=False):
    """Create a daily schedule with operatory information."""
    # Get all appointments in the provider's operatories
    provider_operatories = set(appt.get("operatory") for appt in appointments)

    # Identify no-show and cancelled appointments
    modno_appointments = []
    regular_appointments = []

    for appt in appointments:
        description = appt.get("description", "").upper()
        if "MODNO" in description:
            modno_appointments.append(appt)
        else:
            regular_appointments.append(appt)

    # Find no-show transactions (including those with $0 fee)
    no_show_transactions = [
        t for t in transactions
        if t.get("transaction_type") == "Procedure" and
        ("NO SHOW" in t.get("procedure_description", "").upper() or
         "CANCEL" in t.get("procedure_description", "").upper() or
         "MISSED" in t.get("procedure_description", "").upper() or
         "LATE" in t.get("procedure_description", "").upper())
    ]

    # Also check for specific no-show procedure codes (D9986, D9987)
    no_show_codes = ["D9986", "D9987"]  # D9986 = Missed Appointment, D9987 = Cancelled Appointment
    code_no_show_transactions = [
        t for t in transactions
        if t.get("transaction_type") == "Procedure" and
        t.get("procedure_code") in no_show_codes
    ]

    # Combine both lists
    no_show_transactions.extend(code_no_show_transactions)

    # Create a set of patient IDs with no-show transactions
    no_show_patient_ids = set(t.get("patient_id") for t in no_show_transactions)

    # Identify MODNO appointments without a corresponding no-show fee
    modno_without_fee = []
    for appt in modno_appointments:
        patient_id = appt.get("patient_id")
        if patient_id not in no_show_patient_ids:
            modno_without_fee.append(appt)

    # Get all patient IDs from regular appointments in the provider's operatories
    operatory_patient_ids = set(appt.get("patient_id") for appt in regular_appointments)

    # Get all appointment serial numbers from regular appointments in the provider's operatories
    operatory_appt_sr_nos = set(appt.get("appointment_sr_no") for appt in regular_appointments)

    # Filter transactions based on operatory assignments first, then provider ID
    if not show_all_providers:
        # Include transactions for patients who had appointments in the provider's operatories
        # OR transactions that were assigned to the provider
        provider_procedures = [
            t for t in transactions
            if (t.get("patient_id") in operatory_patient_ids) or
               (t.get("appointment_sr_no") in operatory_appt_sr_nos) or
               (t.get("provider_id") == provider_id)
        ]
    else:
        provider_procedures = transactions

    # Create a dictionary of procedures by patient ID
    procedures_by_patient = {}
    for proc in provider_procedures:
        patient_id = proc.get("patient_id")
        if patient_id not in procedures_by_patient:
            procedures_by_patient[patient_id] = {
                "name": proc.get("patient_name", "Unknown Patient"),
                "procedures": []
            }
        procedures_by_patient[patient_id]["procedures"].append(proc)

    # Create a dictionary of procedures by appointment serial number
    procedures_by_appt_sr_no = {}
    for proc in provider_procedures:
        # Check if the procedure has an appointment_sr_no field
        appt_sr_no = proc.get("appointment_sr_no")
        if appt_sr_no:
            if appt_sr_no not in procedures_by_appt_sr_no:
                procedures_by_appt_sr_no[appt_sr_no] = []
            procedures_by_appt_sr_no[appt_sr_no].append(proc)

    # Get patient IDs from procedures
    procedure_patient_ids = set(procedures_by_patient.keys())

    # Filter regular appointments for the specified provider
    provider_appointments = [a for a in regular_appointments if a.get("provider_id") == provider_id]

    # Create a dictionary of appointments by time
    appt_by_time = {}
    for appt in provider_appointments:
        time_str = appt.get("time", "")
        if time_str:
            # Convert from "HH:MM" to datetime object for proper sorting
            try:
                time_obj = datetime.strptime(time_str, "%H:%M")
                appt_by_time[time_str] = appt
            except ValueError:
                # Skip appointments with invalid time format
                continue

    # Format date for display
    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
    formatted_date = date_obj.strftime("%A, %B %d, %Y")

    # Display the schedule
    print(f"\n{'=' * 80}")
    print(f"{'DAILY SCHEDULE FOR DR. ' + provider_id:^80}")
    print(f"{formatted_date:^80}")
    print(f"{'=' * 80}")
    print(f"{'NOTE: This schedule shows all patients in your operatories, regardless of provider assignment':^80}")
    print(f"{'=' * 80}")

    # Create time slots from 8:00 to 17:00 in 10-minute increments
    start_time = datetime.strptime("08:00", "%H:%M")
    end_time = datetime.strptime("17:00", "%H:%M")
    current_time = start_time

    active_appointments = []  # Track appointments that span multiple time slots

    # Print header
    print(f"\n{'TIME':<8}{'PATIENT':<25}{'PROCEDURE':<25}{'OPERATORY':<10}{'LENGTH':<7}{'STATUS'}")
    print(f"{'-' * 8}{'-' * 25}{'-' * 25}{'-' * 10}{'-' * 7}{'-' * 10}")

    while current_time <= end_time:
        time_str = current_time.strftime("%H:%M")

        # Check if any appointment starts at this time
        new_appt_started = False
        if time_str in appt_by_time:
            new_appt_started = True
            appt = appt_by_time[time_str]
            length_min = float(appt.get("length", "0"))
            end_time_obj = current_time + timedelta(minutes=length_min)

            patient_id = appt.get("patient_id", "")
            patient_name = appt.get("patient_name", "Unknown Patient")
            description = appt.get("description", "No description")
            appt_sr_no = appt.get("appointment_sr_no", "")
            operatory = appt.get("operatory", "N/A")

            # Check if this patient had procedures done by this provider
            has_procedures = patient_id in procedure_patient_ids

            # Check if this appointment has linked procedures
            has_linked_procedures = appt_sr_no in procedures_by_appt_sr_no

            # Check if this is a no-show or cancelled appointment
            is_no_show = "MODNO" in description.upper() or patient_id in no_show_patient_ids

            # Add to active appointments
            active_appointments.append({
                "appt": appt,
                "end_time": end_time_obj,
                "has_procedures": has_procedures or has_linked_procedures,
                "start_time": current_time,  # Store start time for duration calculation
                "operatory": operatory,
                "appt_sr_no": appt_sr_no,
                "is_no_show": is_no_show
            })

        # Update and display active appointments
        new_active = []
        for appt_info in active_appointments:
            if appt_info["end_time"] > current_time:
                appt = appt_info["appt"]
                patient_name = appt.get("patient_name", "Unknown Patient")
                description = appt.get("description", "No description")
                length_min = float(appt.get("length", "0"))
                operatory = appt_info["operatory"]

                # Calculate how long the appointment has been active
                elapsed = (current_time - appt_info["start_time"]).total_seconds() / 60
                remaining = length_min - elapsed

                # Only display the appointment info when it starts or every 30 minutes
                is_start = elapsed < 10  # First time slot of appointment
                is_30min_mark = elapsed > 0 and elapsed % 30 < 10  # Every 30 minutes

                if is_start or is_30min_mark or new_appt_started:
                    # Mark appointments with procedures
                    status = "COMPLETED" if appt_info["has_procedures"] else "SCHEDULED"

                    # Truncate long descriptions
                    short_desc = description
                    if len(short_desc) > 22:
                        short_desc = short_desc[:19] + "..."

                    # Format patient name
                    short_name = patient_name
                    if len(short_name) > 22:
                        short_name = short_name[:19] + "..."

                    # Display the appointment
                    if is_start:
                        # Show full details at start
                        print(f"{time_str:<8}{short_name:<25}{short_desc:<25}{operatory:<10}{int(length_min):<7}{status}")
                    else:
                        # Show continuation marker for ongoing appointments
                        print(f"{time_str:<8}{'└─ ' + short_name:<25}{'(continued)':<25}{operatory:<10}{f'{int(remaining)}m':<7}{status}")

                new_active.append(appt_info)

        # Only show empty slots at hour and half-hour marks
        if not new_active and (time_str.endswith(":00") or time_str.endswith(":30")):
            print(f"{time_str:<8}{'[OPEN]':<25}{'':<25}{'':<10}{'':<7}{''}")

        active_appointments = new_active

        # Move to next time slot (10 minutes)
        current_time += timedelta(minutes=10)

    # Also display a summary of procedures completed
    print(f"\n{'=' * 80}")
    title = f"PROCEDURES FOR PATIENTS IN DR. {provider_id}'S OPERATORIES"
    print(f"{title:^80}")
    print(f"{formatted_date:^80}")
    print(f"{'=' * 80}")
    print(f"{'NOTE: Procedures marked with (Dr. X) were coded to a different provider':^80}")
    print(f"{'=' * 80}")

    # Calculate total production
    total_production = 0
    patient_count = 0

    # Display procedures by patient
    for patient_id, data in procedures_by_patient.items():
        patient_name = data["name"]
        procs = data["procedures"]
        patient_count += 1

        print(f"\n{patient_count}. {patient_name}")
        print("-" * 80)

        # Find matching appointment for this patient
        matching_appt = None
        for appt in provider_appointments:
            if appt.get("patient_id") == patient_id:
                matching_appt = appt
                break

        if matching_appt:
            appt_time = matching_appt.get("time", "")
            appt_desc = matching_appt.get("description", "")
            appt_sr_no = matching_appt.get("appointment_sr_no", "")
            operatory = matching_appt.get("operatory", "N/A")
            print(f"Appointment: {appt_time} - {appt_desc}")
            print(f"Operatory: {operatory}")
            print(f"Appointment Serial #: {appt_sr_no}")
        else:
            print("Note: No matching appointment found for this patient on this date")

        print("-" * 80)
        print(f"{'CODE':<10}{'DESCRIPTION':<40}{'TOOTH':<10}{'SURFACE':<10}{'AMOUNT':<10}")
        print(f"{'-' * 10}{'-' * 40}{'-' * 10}{'-' * 10}{'-' * 10}")

        # Display procedures
        patient_total = 0
        for proc in procs:
            code = proc.get("procedure_code", "")
            desc = proc.get("procedure_description", "")
            amount = float(proc.get("amount", "0"))
            tooth = proc.get("tooth_from", "") or "-"
            surface = proc.get("surface", "") or "-"
            proc_provider = proc.get("provider_id", "")

            # Truncate long descriptions
            if len(desc) > 37:
                desc = desc[:34] + "..."

            # Show provider ID if it's different from the selected provider
            provider_info = ""
            if proc_provider and proc_provider != provider_id:
                provider_info = f" (Dr. {proc_provider})"

            print(f"{code:<10}{desc:<40}{tooth:<10}{surface:<10}${amount:<9.2f}{provider_info}")
            patient_total += amount
            total_production += amount

        print(f"{'':<60}{'TOTAL:':<10}${patient_total:<9.2f}")

    # Print summary
    if patient_count == 0:
        print("\nNo procedures completed by this provider on this date.")
    else:
        print(f"\n{'=' * 80}")
        print(f"{'SUMMARY':^80}")
        print(f"{'=' * 80}")
        print(f"Total patients with completed procedures: {patient_count}")
        print(f"Total production: ${total_production:.2f}")

    # Display MODNO appointments without a no-show fee
    if modno_without_fee:
        print(f"\n{'=' * 80}")
        print(f"{'WARNING: MODNO APPOINTMENTS WITHOUT NO-SHOW CODES':^80}")
        print(f"{'=' * 80}")
        print(f"The following patients were marked as MODNO but don't have a no-show transaction posted:")
        print(f"(Even if you waive the fee, please post a $0 no-show code for tracking purposes)")
        print(f"\n{'TIME':<8}{'PATIENT':<25}{'OPERATORY':<10}{'PROVIDER':<10}")
        print(f"{'-' * 8}{'-' * 25}{'-' * 10}{'-' * 10}")

        for appt in sorted(modno_without_fee, key=lambda a: a.get("time", "")):
            time = appt.get("time", "")
            patient_name = appt.get("patient_name", "Unknown Patient")
            operatory = appt.get("operatory", "N/A")
            provider_id = appt.get("provider_id", "N/A")

            # Truncate long patient names
            if len(patient_name) > 22:
                patient_name = patient_name[:19] + "..."

            print(f"{time:<8}{patient_name:<25}{operatory:<10}{provider_id:<10}")

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Generate a daily schedule with operatory information")
    parser.add_argument("date", help="Date in YYYY-MM-DD format")
    parser.add_argument("provider_id", help="Provider ID (e.g., LL01)")
    parser.add_argument("--office-id", default="D43989", help="Office ID")
    parser.add_argument("--secret-key", default="35442814D4396E20C222", help="Secret key")
    parser.add_argument("--app-id", default="fdd52aaffb0c1bead647874ba551db0c", help="App ID")
    parser.add_argument("--app-key", default="88254bfa2224607ef425646aafe5f722", help="App key")
    parser.add_argument("--operatories", help="Comma-separated list of operatories to filter by (e.g., DL01,DL02)")
    parser.add_argument("--list-operatories", action="store_true", help="List all available operatories and exit")
    parser.add_argument("--show-all-providers", action="store_true", help="Show procedures from all providers")

    args = parser.parse_args()

    # Validate date format
    try:
        datetime.strptime(args.date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        return

    # Get request key
    print("Getting request key...")
    request_key = get_request_key(args.office_id, args.secret_key, args.app_id, args.app_key)
    if not request_key:
        print("Failed to get request key. Exiting.")
        return

    print("Request key obtained successfully")

    # If --list-operatories flag is set, just list operatories and exit
    if args.list_operatories:
        operatories = fetch_operatories(request_key)
        print("\nAvailable operatories:")
        for op in operatories:
            print(f"  - {op}")
        return

    # Parse operatories if provided
    selected_operatories = None
    if args.operatories:
        selected_operatories = [op.strip() for op in args.operatories.split(",")]
        print(f"Filtering by operatories: {', '.join(selected_operatories)}")
        print(f"Note: When filtering by operatories, all appointments in those operatories will be shown regardless of provider")

    # Fetch all transactions for the office on the selected date
    print(f"Fetching all transactions for {args.date}...")
    transactions = fetch_transactions(request_key, args.date)
    print(f"Found {len(transactions)} transactions")

    # Fetch appointments
    if selected_operatories:
        print(f"Fetching appointments in operatories {', '.join(selected_operatories)} on {args.date}...")
    else:
        print(f"Fetching appointments for provider {args.provider_id} on {args.date}...")

    appointments = fetch_appointments(request_key, args.date, args.provider_id, selected_operatories)

    # Create provider schedule with operatory information
    create_office_schedule(transactions, appointments, args.provider_id, args.date, args.show_all_providers)

if __name__ == "__main__":
    main()
