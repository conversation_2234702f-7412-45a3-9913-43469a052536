import { NextRequest, NextResponse } from 'next/server';
import { TranscriptionService } from '../../../../src/lib/transcription-service';
import { VercelDB } from '../../../../src/lib/vercel-db';

export interface TranscriptionStatsResponse {
  summary: {
    totalTranscriptions: number;
    matchedTranscriptions: number;
    unmatchedTranscriptions: number;
    completedTranscriptions: number;
    failedTranscriptions: number;
    pendingTranscriptions: number;
  };
  recentActivity: {
    transcriptionsToday: number;
    transcriptionsThisWeek: number;
    transcriptionsThisMonth: number;
    averageConfidenceScore: number;
    successRate: number;
  };
  systemHealth: {
    lastTranscription: string | null;
    openaiConfigured: boolean;
    databaseConnected: boolean;
    systemStatus: 'healthy' | 'degraded' | 'error';
  };
  storage: {
    audioFilesRetained: number;
    totalTextStorage: string; // Human readable size
    estimatedCostSaving: string; // vs blob storage
  };
}

/**
 * GET /api/voice/transcription-stats
 * Returns comprehensive statistics for the new transcription-only workflow
 * Replaces the complex processing dashboard with simple stats
 */
export async function GET(request: NextRequest) {
  try {
    console.log('📊 Fetching transcription statistics...');

    // Get basic stats from database
    const stats = await VercelDB.getStats();

    // Get recent activity (last 30 days)
    const recentTranscriptions = await VercelDB.getTranscriptions({
      limit: 1000, // Get recent transcriptions for analysis
    });

    // Calculate time-based metrics
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const transcriptionsToday = recentTranscriptions.filter(
      t => new Date(t.created_at) >= today
    ).length;

    const transcriptionsThisWeek = recentTranscriptions.filter(
      t => new Date(t.created_at) >= thisWeek
    ).length;

    const transcriptionsThisMonth = recentTranscriptions.filter(
      t => new Date(t.created_at) >= thisMonth
    ).length;

    // Calculate average confidence score
    const transcriptionsWithConfidence = recentTranscriptions.filter(
      t => t.confidence_score && t.confidence_score > 0
    );
    const averageConfidenceScore = transcriptionsWithConfidence.length > 0
      ? transcriptionsWithConfidence.reduce((sum, t) => sum + (t.confidence_score || 0), 0) / transcriptionsWithConfidence.length
      : 0;

    // Calculate success rate
    const successRate = stats.total_count > 0 
      ? (stats.completed_count / stats.total_count) * 100 
      : 100;

    // System health checks
    const openaiConfigured = !!process.env.OPENAI_API_KEY;
    let databaseConnected = true;
    try {
      await VercelDB.getStats(); // Simple connectivity test
    } catch (error) {
      databaseConnected = false;
      console.error('Database connectivity check failed:', error);
    }

    // Get audio retention info
    const retainedAudioCount = recentTranscriptions.filter(t => t.audio_retained).length;

    // Calculate estimated storage savings (rough estimate)
    const avgAudioFileSizeMB = 5; // Assume 5MB average per audio file
    const totalAudioThatWouldBeStored = stats.total_count * avgAudioFileSizeMB;
    const actualAudioStoredMB = retainedAudioCount * avgAudioFileSizeMB;
    const savedStorageMB = totalAudioThatWouldBeStored - actualAudioStoredMB;

    // Estimate text storage size
    const avgTranscriptionLength = recentTranscriptions.length > 0
      ? recentTranscriptions.reduce((sum, t) => sum + (t.transcription_text?.length || 0), 0) / recentTranscriptions.length
      : 0;
    const totalTextStorageKB = (stats.total_count * avgTranscriptionLength) / 1024;

    // Determine system status
    let systemStatus: 'healthy' | 'degraded' | 'error' = 'healthy';
    if (!openaiConfigured || !databaseConnected) {
      systemStatus = 'error';
    } else if (successRate < 90 || stats.failed_count > stats.completed_count * 0.1) {
      systemStatus = 'degraded';
    }

    // Find last transcription
    const lastTranscription = recentTranscriptions.length > 0 
      ? recentTranscriptions[0].created_at 
      : null;

    const response: TranscriptionStatsResponse = {
      summary: {
        totalTranscriptions: stats.total_count,
        matchedTranscriptions: stats.matched_count,
        unmatchedTranscriptions: stats.unmatched_count,
        completedTranscriptions: stats.completed_count,
        failedTranscriptions: stats.failed_count,
        pendingTranscriptions: stats.pending_count,
      },
      recentActivity: {
        transcriptionsToday,
        transcriptionsThisWeek,
        transcriptionsThisMonth,
        averageConfidenceScore: Math.round(averageConfidenceScore * 100) / 100,
        successRate: Math.round(successRate * 10) / 10,
      },
      systemHealth: {
        lastTranscription,
        openaiConfigured,
        databaseConnected,
        systemStatus,
      },
      storage: {
        audioFilesRetained: retainedAudioCount,
        totalTextStorage: formatBytes(totalTextStorageKB * 1024),
        estimatedCostSaving: `${Math.round(savedStorageMB)}MB saved vs blob storage`,
      },
    };

    console.log('✅ Successfully retrieved transcription statistics', {
      total: stats.total_count,
      completed: stats.completed_count,
      failed: stats.failed_count,
      systemStatus,
    });

    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Failed to retrieve transcription statistics:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to retrieve statistics',
        details: error instanceof Error ? error.message : 'Unknown error',
        summary: {
          totalTranscriptions: 0,
          matchedTranscriptions: 0,
          unmatchedTranscriptions: 0,
          completedTranscriptions: 0,
          failedTranscriptions: 0,
          pendingTranscriptions: 0,
        },
        recentActivity: {
          transcriptionsToday: 0,
          transcriptionsThisWeek: 0,
          transcriptionsThisMonth: 0,
          averageConfidenceScore: 0,
          successRate: 0,
        },
        systemHealth: {
          lastTranscription: null,
          openaiConfigured: !!process.env.OPENAI_API_KEY,
          databaseConnected: false,
          systemStatus: 'error' as const,
        },
        storage: {
          audioFilesRetained: 0,
          totalTextStorage: '0 B',
          estimatedCostSaving: 'Unable to calculate',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/voice/transcription-stats
 * Initialize database tables (setup endpoint)
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Initializing transcription database...');
    
    await VercelDB.initializeTables();
    
    console.log('✅ Database initialization completed');
    
    return NextResponse.json({
      success: true,
      message: 'Database tables initialized successfully',
      timestamp: new Date().toISOString(),
    });
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Database initialization failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * Helper function to format bytes into human readable format
 */
function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}