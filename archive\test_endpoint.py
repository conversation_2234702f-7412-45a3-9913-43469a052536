import requests
import json
import argparse
import sys

API_BASE = "https://api.sikkasoft.com/v4"

def pretty_print_json(data):
    """Print JSON data in a readable format."""
    if data is None:
        print("No data to display")
        return
    
    print("\n" + "=" * 80)
    print("RESPONSE DATA:")
    print("=" * 80)
    print(json.dumps(data, indent=2))
    print("=" * 80 + "\n")

def test_endpoint(endpoint, method="GET", headers=None, params=None, data=None):
    """Test a specific API endpoint and display the response."""
    headers = headers or {}
    params = params or {}
    
    url = f"{API_BASE}/{endpoint}"
    print(f"Testing endpoint: {url}")
    print(f"Method: {method}")
    print(f"Headers: {headers}")
    print(f"Params: {params}")
    if data:
        print(f"Data: {data}")
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers, params=params)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, params=params, json=data)
        else:
            print(f"Unsupported method: {method}")
            return
        
        print(f"Status code: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        try:
            json_data = response.json()
            pretty_print_json(json_data)
        except json.JSONDecodeError:
            print("Response is not JSON. Raw response:")
            print(response.text)
            
    except Exception as e:
        print(f"Error: {str(e)}")

def main():
    parser = argparse.ArgumentParser(description="Test a specific Sikka API endpoint")
    parser.add_argument("endpoint", help="API endpoint to test (without the base URL)")
    parser.add_argument("--method", default="GET", choices=["GET", "POST"], help="HTTP method")
    parser.add_argument("--header", action="append", help="Headers in format key:value")
    parser.add_argument("--param", action="append", help="URL parameters in format key:value")
    parser.add_argument("--data", help="JSON data for POST requests")
    
    args = parser.parse_args()
    
    # Process headers
    headers = {}
    if args.header:
        for header in args.header:
            try:
                key, value = header.split(":", 1)
                headers[key.strip()] = value.strip()
            except ValueError:
                print(f"Invalid header format: {header}. Use key:value")
                sys.exit(1)
    
    # Process parameters
    params = {}
    if args.param:
        for param in args.param:
            try:
                key, value = param.split(":", 1)
                params[key.strip()] = value.strip()
            except ValueError:
                print(f"Invalid parameter format: {param}. Use key:value")
                sys.exit(1)
    
    # Process data
    data = None
    if args.data:
        try:
            data = json.loads(args.data)
        except json.JSONDecodeError:
            print(f"Invalid JSON data: {args.data}")
            sys.exit(1)
    
    test_endpoint(args.endpoint, args.method, headers, params, data)

if __name__ == "__main__":
    main()
