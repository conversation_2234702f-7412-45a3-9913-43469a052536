/**
 * Voice Notes Database Utilities
 * Handles database operations for voice recordings and clinical notes
 */

import { Database } from 'sqlite3';
import path from 'path';

export interface VoiceRecording {
  id: number;
  filename: string;
  device_id: string;
  file_path: string;
  file_size?: number;
  duration_seconds?: number;
  imported_at: string;
  json_receipt_path?: string;
  status: 'unassigned' | 'assigned' | 'transcribing' | 'transcribed' | 'reviewing' | 'completed';
  assigned_patient_id?: string;
  assigned_appointment_id?: string;
  assigned_appointment_date?: string;
  assigned_provider?: string;
  assigned_at?: string;
  assigned_by?: string;
  transcription?: string;
  transcription_confidence?: number;
  transcribed_at?: string;
  clinical_summary?: string;
  clinical_notes?: string;
  notes_posted_at?: string;
  posted_to_dentrix: boolean;
  category: 'clinical' | 'administrative' | 'consultation' | 'other';
  tags?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface AppointmentAssignment {
  id: number;
  recording_id: number;
  appointment_id: string;
  patient_id?: string;
  appointment_date?: string;
  provider?: string;
  operatory?: string;
  assigned_at: string;
  assigned_by?: string;
}

export interface ClinicalNote {
  id: number;
  recording_id: number;
  appointment_id?: string;
  summary?: string;
  soap_notes?: string;
  procedure_codes?: string;
  ai_model?: string;
  processing_timestamp?: string;
  confidence_score?: number;
  reviewed_by?: string;
  reviewed_at?: string;
  approved: boolean;
  posted_to_dentrix: boolean;
  dentrix_note_id?: string;
  posted_at?: string;
  posted_by?: string;
  created_at: string;
  updated_at: string;
}

export class VoiceNotesDB {
  private static dbPath = '\\\\192.168.0.2\\share\\RECORDINGS\\voice_notes.db';
  private static localDbPath = path.join(process.cwd(), 'voice_notes.db');

  private static async getDatabase(): Promise<Database> {
    return new Promise((resolve, reject) => {
      // Try network path first, fallback to local
      let db = new Database(this.dbPath, (err) => {
        if (err) {
          console.log('Network database not available, using local fallback');
          db = new Database(this.localDbPath, (localErr) => {
            if (localErr) {
              reject(localErr);
            } else {
              resolve(db);
            }
          });
        } else {
          resolve(db);
        }
      });
    });
  }

  static async getVoiceRecordings(limit = 50, offset = 0): Promise<VoiceRecording[]> {
    const db = await this.getDatabase();
    
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM voice_recordings 
         ORDER BY created_at DESC 
         LIMIT ? OFFSET ?`,
        [limit, offset],
        (err, rows) => {
          db.close();
          if (err) {
            reject(err);
          } else {
            resolve(rows as VoiceRecording[]);
          }
        }
      );
    });
  }

  static async getRecordingsByAppointment(appointmentId: string): Promise<VoiceRecording[]> {
    const db = await this.getDatabase();
    
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT vr.* FROM voice_recordings vr
         JOIN appointment_assignments aa ON vr.id = aa.recording_id
         WHERE aa.appointment_id = ?`,
        [appointmentId],
        (err, rows) => {
          db.close();
          if (err) {
            reject(err);
          } else {
            resolve(rows as VoiceRecording[]);
          }
        }
      );
    });
  }

  static async getRecordingsByDate(date: string): Promise<VoiceRecording[]> {
    const db = await this.getDatabase();
    
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM voice_recordings 
         WHERE assigned_appointment_date = ?
         ORDER BY created_at DESC`,
        [date],
        (err, rows) => {
          db.close();
          if (err) {
            reject(err);
          } else {
            resolve(rows as VoiceRecording[]);
          }
        }
      );
    });
  }

  static async getAppointmentAssignments(recordingId: number): Promise<AppointmentAssignment[]> {
    const db = await this.getDatabase();
    
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM appointment_assignments 
         WHERE recording_id = ?`,
        [recordingId],
        (err, rows) => {
          db.close();
          if (err) {
            reject(err);
          } else {
            resolve(rows as AppointmentAssignment[]);
          }
        }
      );
    });
  }

  static async getClinicalNotes(recordingId: number): Promise<ClinicalNote[]> {
    const db = await this.getDatabase();
    
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM clinical_notes 
         WHERE recording_id = ?
         ORDER BY created_at DESC`,
        [recordingId],
        (err, rows) => {
          db.close();
          if (err) {
            reject(err);
          } else {
            resolve(rows as ClinicalNote[]);
          }
        }
      );
    });
  }

  static async createRecording(recording: Omit<VoiceRecording, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const db = await this.getDatabase();

    return new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO voice_recordings (
          filename, device_id, file_path, file_size, duration_seconds, imported_at,
          status, category, posted_to_dentrix
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          recording.filename, recording.device_id, recording.file_path,
          recording.file_size, recording.duration_seconds, recording.imported_at,
          recording.status, recording.category, recording.posted_to_dentrix
        ],
        function(err) {
          db.close();
          if (err) {
            reject(err);
          } else {
            resolve(this.lastID);
          }
        }
      );
    });
  }

  static async getRecordingByFilename(filename: string): Promise<VoiceRecording | null> {
    const db = await this.getDatabase();

    return new Promise((resolve, reject) => {
      db.get(
        `SELECT * FROM voice_recordings WHERE filename = ?`,
        [filename],
        (err, row) => {
          db.close();
          if (err) {
            reject(err);
          } else {
            resolve(row as VoiceRecording || null);
          }
        }
      );
    });
  }

  static async getRecordingById(id: number): Promise<VoiceRecording | null> {
    const db = await this.getDatabase();

    return new Promise((resolve, reject) => {
      db.get(
        `SELECT * FROM voice_recordings WHERE id = ?`,
        [id],
        (err, row) => {
          db.close();
          if (err) {
            reject(err);
          } else {
            resolve(row as VoiceRecording || null);
          }
        }
      );
    });
  }

  static async updateRecordingTranscription(
    recordingId: number,
    transcription: string,
    confidence?: number,
    aiModel?: string
  ): Promise<void> {
    const db = await this.getDatabase();

    return new Promise((resolve, reject) => {
      db.run(
        `UPDATE voice_recordings
         SET transcription = ?, transcription_confidence = ?, transcribed_at = CURRENT_TIMESTAMP,
             status = 'transcribed', updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [transcription, confidence, recordingId],
        function(err) {
          db.close();
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }

  static async assignRecordingToAppointment(
    recordingId: number,
    appointmentId: string,
    patientId?: string,
    appointmentDate?: string,
    provider?: string,
    operatory?: string,
    assignedBy?: string
  ): Promise<void> {
    const db = await this.getDatabase();

    return new Promise((resolve, reject) => {
      // First update the recording
      db.run(
        `UPDATE voice_recordings
         SET assigned_appointment_id = ?, assigned_patient_id = ?,
             assigned_appointment_date = ?, assigned_provider = ?,
             assigned_at = CURRENT_TIMESTAMP, assigned_by = ?,
             status = 'assigned', updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [appointmentId, patientId, appointmentDate, provider, assignedBy, recordingId],
        function(updateErr) {
          if (updateErr) {
            db.close();
            reject(updateErr);
            return;
          }

          // Then create the assignment record
          db.run(
            `INSERT OR REPLACE INTO appointment_assignments (
              recording_id, appointment_id, patient_id, appointment_date,
              provider, operatory, assigned_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [recordingId, appointmentId, patientId, appointmentDate, provider, operatory, assignedBy],
            function(insertErr) {
              db.close();
              if (insertErr) {
                reject(insertErr);
              } else {
                resolve();
              }
            }
          );
        }
      );
    });
  }

  static async unassignRecording(recordingId: number): Promise<void> {
    const db = await this.getDatabase();

    return new Promise((resolve, reject) => {
      // First remove assignment records
      db.run(
        `DELETE FROM appointment_assignments WHERE recording_id = ?`,
        [recordingId],
        function(deleteErr) {
          if (deleteErr) {
            db.close();
            reject(deleteErr);
            return;
          }

          // Then update the recording
          db.run(
            `UPDATE voice_recordings
             SET assigned_appointment_id = NULL, assigned_patient_id = NULL,
                 assigned_appointment_date = NULL, assigned_provider = NULL,
                 assigned_at = NULL, assigned_by = NULL,
                 status = 'unassigned', updated_at = CURRENT_TIMESTAMP
             WHERE id = ?`,
            [recordingId],
            function(updateErr) {
              db.close();
              if (updateErr) {
                reject(updateErr);
              } else {
                resolve();
              }
            }
          );
        }
      );
    });
  }

  static async createClinicalNote(note: Omit<ClinicalNote, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const db = await this.getDatabase();
    
    return new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO clinical_notes (
          recording_id, appointment_id, summary, soap_notes, procedure_codes,
          ai_model, processing_timestamp, confidence_score, approved, posted_to_dentrix
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          note.recording_id, note.appointment_id, note.summary, note.soap_notes,
          note.procedure_codes, note.ai_model, note.processing_timestamp,
          note.confidence_score, note.approved, note.posted_to_dentrix
        ],
        function(err) {
          db.close();
          if (err) {
            reject(err);
          } else {
            resolve(this.lastID);
          }
        }
      );
    });
  }

  static async getRecordingsWithAssignments(): Promise<(VoiceRecording & { appointments: AppointmentAssignment[] })[]> {
    const recordings = await this.getVoiceRecordings();
    const result = [];

    for (const recording of recordings) {
      const appointments = await this.getAppointmentAssignments(recording.id);
      result.push({ ...recording, appointments });
    }

    return result;
  }
}
