globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/debug-transcription/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"894":{"*":{"id":"86346","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"97173","name":"*","chunks":[],"async":false}},"1386":{"*":{"id":"49326","name":"*","chunks":[],"async":false}},"2501":{"*":{"id":"82837","name":"*","chunks":[],"async":false}},"3287":{"*":{"id":"50283","name":"*","chunks":[],"async":false}},"3574":{"*":{"id":"8402","name":"*","chunks":[],"async":false}},"3765":{"*":{"id":"42459","name":"*","chunks":[],"async":false}},"3871":{"*":{"id":"94226","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"28827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"27924","name":"*","chunks":[],"async":false}},"5535":{"*":{"id":"34103","name":"*","chunks":[],"async":false}},"5659":{"*":{"id":"61530","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"35656","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"40099","name":"*","chunks":[],"async":false}},"7005":{"*":{"id":"82000","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"38243","name":"*","chunks":[],"async":false}},"8012":{"*":{"id":"11132","name":"*","chunks":[],"async":false}},"8214":{"*":{"id":"2217","name":"*","chunks":[],"async":false}},"8327":{"*":{"id":"76619","name":"*","chunks":[],"async":false}},"8411":{"*":{"id":"84567","name":"*","chunks":[],"async":false}},"8609":{"*":{"id":"71534","name":"*","chunks":[],"async":false}},"9452":{"*":{"id":"26748","name":"*","chunks":[],"async":false}},"9454":{"*":{"id":"27804","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"62763","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\dentalapp\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\app\\globals.css":{"id":9324,"name":"*","chunks":["7177","static/chunks/app/layout-d6cf78b7a65a8a3b.js"],"async":false},"C:\\dentalapp\\app\\providers.tsx":{"id":3574,"name":"*","chunks":["7177","static/chunks/app/layout-d6cf78b7a65a8a3b.js"],"async":false},"C:\\dentalapp\\src\\components\\auth\\AuthWrapper.tsx":{"id":9452,"name":"*","chunks":["7177","static/chunks/app/layout-d6cf78b7a65a8a3b.js"],"async":false},"C:\\dentalapp\\app\\ai-assistant\\page.tsx":{"id":2501,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\app\\operatories\\page.tsx":{"id":3871,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\app\\appointment\\[appointmentId]\\page.tsx":{"id":5535,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\app\\debug-transcription\\page.tsx":{"id":5659,"name":"*","chunks":["479","static/chunks/app/debug-transcription/page-8149a09e95027f3a.js"],"async":false},"C:\\dentalapp\\app\\cache-test\\page.tsx":{"id":8411,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\app\\patient-search\\page.tsx":{"id":8609,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\app\\page.tsx":{"id":7005,"name":"*","chunks":["6496","static/chunks/6496-b8ce0d7283f7501a.js","8974","static/chunks/app/page-9da093070dc905da.js"],"async":false},"C:\\dentalapp\\app\\patient\\[id]\\page.tsx":{"id":8327,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\app\\schedule\\page.tsx":{"id":3287,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\app\\settings\\page.tsx":{"id":8012,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\app\\transcription-control\\page.tsx":{"id":9454,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\app\\test-webusb\\page.tsx":{"id":3765,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\app\\voice-workflow\\page.tsx":{"id":1386,"name":"*","chunks":[],"async":false},"C:\\dentalapp\\app\\webusb-transfer\\page.tsx":{"id":8214,"name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\dentalapp\\":[],"C:\\dentalapp\\app\\layout":[{"inlined":false,"path":"static/css/4b8a6b4f6583facd.css"}],"C:\\dentalapp\\app\\page":[],"C:\\dentalapp\\app\\debug-transcription\\page":[]},"rscModuleMapping":{"894":{"*":{"id":"16444","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"31307","name":"*","chunks":[],"async":false}},"1386":{"*":{"id":"42760","name":"*","chunks":[],"async":false}},"2501":{"*":{"id":"31107","name":"*","chunks":[],"async":false}},"3287":{"*":{"id":"15929","name":"*","chunks":[],"async":false}},"3574":{"*":{"id":"78972","name":"*","chunks":[],"async":false}},"3765":{"*":{"id":"22553","name":"*","chunks":[],"async":false}},"3871":{"*":{"id":"48341","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"12089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"16042","name":"*","chunks":[],"async":false}},"5535":{"*":{"id":"57982","name":"*","chunks":[],"async":false}},"5659":{"*":{"id":"53640","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"88170","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"49477","name":"*","chunks":[],"async":false}},"7005":{"*":{"id":"90597","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"29345","name":"*","chunks":[],"async":false}},"8012":{"*":{"id":"98245","name":"*","chunks":[],"async":false}},"8214":{"*":{"id":"96718","name":"*","chunks":[],"async":false}},"8327":{"*":{"id":"1813","name":"*","chunks":[],"async":false}},"8411":{"*":{"id":"16841","name":"*","chunks":[],"async":false}},"8609":{"*":{"id":"8596","name":"*","chunks":[],"async":false}},"9324":{"*":{"id":"82704","name":"*","chunks":[],"async":false}},"9452":{"*":{"id":"76034","name":"*","chunks":[],"async":false}},"9454":{"*":{"id":"54998","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"46577","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}