import requests
import json
from datetime import datetime, timedelta
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 60

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def check_operatory_endpoints(request_key):
    """Check various API endpoints related to operatories."""
    headers = {"Request-Key": request_key}
    
    # Try v4 operatories endpoint
    print("\nTrying v4 operatories endpoint...")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/operatories",
            headers=headers,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v2 operatories endpoint
    print("\nTrying v2 operatories endpoint...")
    try:
        resp = requests.get(
            f"{API_BASE_V2}/operatories",
            headers=headers,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v4 practice_resources endpoint
    print("\nTrying v4 practice_resources endpoint...")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/practice_resources",
            headers=headers,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v2 practice_resources endpoint
    print("\nTrying v2 practice_resources endpoint...")
    try:
        resp = requests.get(
            f"{API_BASE_V2}/practice_resources",
            headers=headers,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v4 practice_schedule endpoint
    print("\nTrying v4 practice_schedule endpoint...")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/practice_schedule",
            headers=headers,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v2 practice_schedule endpoint
    print("\nTrying v2 practice_schedule endpoint...")
    try:
        resp = requests.get(
            f"{API_BASE_V2}/practice_schedule",
            headers=headers,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v4 schedule endpoint
    print("\nTrying v4 schedule endpoint...")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/schedule",
            headers=headers,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v2 schedule endpoint
    print("\nTrying v2 schedule endpoint...")
    try:
        resp = requests.get(
            f"{API_BASE_V2}/schedule",
            headers=headers,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")

def main():
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Check operatory endpoints
    check_operatory_endpoints(request_key)
    
    print("\nCheck complete.")

if __name__ == "__main__":
    main()
