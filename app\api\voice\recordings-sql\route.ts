import { NextRequest, NextResponse } from 'next/server';
import { sql } from '@vercel/postgres';
import { VercelDB } from '@/lib/vercel-db';
import { featureFlags } from '@/lib/feature-flags';

// In-memory cache for API responses (matching legacy behavior)
const apiCache: Record<string, { data: unknown, timestamp: number }> = {};
const CACHE_DURATION = 60 * 60 * 1000; // 1 hour

// Helper function to get cache key
function getCacheKey(url: string, params: URLSearchParams): string {
  const sortedParams = Array.from(params.entries()).sort();
  return `${url}?${new URLSearchParams(sortedParams).toString()}`;
}

// Helper function to check cache
function getFromCache(cacheKey: string): unknown | null {
  const cached = apiCache[cacheKey];
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  delete apiCache[cacheKey];
  return null;
}

// Helper function to set cache
function setCache(cacheKey: string, data: unknown): void {
  apiCache[cacheKey] = {
    data,
    timestamp: Date.now()
  };
}

export async function GET(request: NextRequest) {
  try {
    // Check if new architecture is enabled
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const cacheKey = getCacheKey('/api/voice/recordings-sql', searchParams);

    // Check cache first
    const cachedData = getFromCache(cacheKey);
    if (cachedData) {
      return NextResponse.json(cachedData);
    }

    // Parse query parameters
    const date = searchParams.get('date');
    const status = searchParams.get('status');
    const deviceId = searchParams.get('deviceId');
    const patientId = searchParams.get('patientId');
    const limit = Math.min(parseInt(searchParams.get('limit') || '100'), 1000);
    const offset = parseInt(searchParams.get('offset') || '0');
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'DESC';

    console.log(`📊 Fetching recordings: date=${date}, status=${status}, deviceId=${deviceId}`);

    // Build the SQL query dynamically
    let whereConditions: string[] = [];
    let queryParams: any[] = [];
    let paramIndex = 1;

    if (date) {
      whereConditions.push(`DATE(created_at) = $${paramIndex}`);
      queryParams.push(date);
      paramIndex++;
    }

    if (status) {
      whereConditions.push(`status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    if (deviceId) {
      whereConditions.push(`device_id = $${paramIndex}`);
      queryParams.push(deviceId);
      paramIndex++;
    }

    if (patientId) {
      whereConditions.push(`patient_id = $${paramIndex}`);
      queryParams.push(patientId);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}`
      : '';

    // Execute the query
    const query = `
      SELECT 
        id,
        filename,
        device_id,
        patient_id,
        transcription_text,
        summary_text,
        confidence_score,
        status,
        created_at,
        updated_at,
        metadata
      FROM transcriptions
      ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    const result = await sql.query(query, queryParams);
    const recordings = result.rows;

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM transcriptions
      ${whereClause}
    `;
    
    const countResult = await sql.query(countQuery, queryParams.slice(0, -2)); // Remove limit/offset
    const totalCount = parseInt(countResult.rows[0].total);

    // Format the response to match legacy API structure
    const formattedRecordings = recordings.map(recording => ({
      id: recording.id,
      filename: recording.filename,
      deviceId: recording.device_id,
      patientId: recording.patient_id,
      transcriptionText: recording.transcription_text,
      summaryText: recording.summary_text,
      confidenceScore: recording.confidence_score,
      status: recording.status,
      createdAt: recording.created_at,
      updatedAt: recording.updated_at,
      metadata: recording.metadata,
      // Legacy compatibility fields
      hasTranscription: !!recording.transcription_text,
      hasSummary: !!recording.summary_text,
      processingStatus: mapStatusToFrontend(recording.status)
    }));

    const responseData = {
      success: true,
      recordings: formattedRecordings,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + recordings.length < totalCount
      },
      filters: {
        date,
        status,
        deviceId,
        patientId
      },
      timestamp: new Date().toISOString()
    };

    // Cache the response
    setCache(cacheKey, responseData);

    console.log(`✅ Retrieved ${recordings.length} recordings (${totalCount} total)`);
    return NextResponse.json(responseData);

  } catch (error) {
    console.error('❌ Error fetching recordings:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to fetch recordings',
      code: 'FETCH_ERROR'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if new architecture is enabled
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const body = await request.json();
    const { action, recordingIds, updates } = body;

    switch (action) {
      case 'updateStatus':
        if (!recordingIds || !updates?.status) {
          return NextResponse.json({
            error: 'Recording IDs and status are required',
            code: 'MISSING_PARAMETERS'
          }, { status: 400 });
        }

        // Update multiple recordings
        const updateResults = await Promise.allSettled(
          recordingIds.map((id: string) => 
            VercelDB.updateTranscriptionStatus(id, updates.status, updates.error)
          )
        );

        const successful = updateResults.filter(result => result.status === 'fulfilled').length;
        const failed = updateResults.length - successful;

        return NextResponse.json({
          success: true,
          updated: successful,
          failed,
          message: `Updated ${successful} recordings, ${failed} failed`
        });

      case 'delete':
        if (!recordingIds) {
          return NextResponse.json({
            error: 'Recording IDs are required',
            code: 'MISSING_PARAMETERS'
          }, { status: 400 });
        }

        // Soft delete recordings by updating status
        const deleteResults = await Promise.allSettled(
          recordingIds.map((id: string) => 
            VercelDB.updateTranscriptionStatus(id, 'deleted', 'Deleted by user')
          )
        );

        const deletedCount = deleteResults.filter(result => result.status === 'fulfilled').length;

        return NextResponse.json({
          success: true,
          deleted: deletedCount,
          message: `Deleted ${deletedCount} recordings`
        });

      case 'search':
        const { query, searchType = 'transcription' } = body;
        
        if (!query) {
          return NextResponse.json({
            error: 'Search query is required',
            code: 'MISSING_QUERY'
          }, { status: 400 });
        }

        const searchResults = await VercelDB.searchTranscriptions(query, {
          searchType: searchType as 'transcription' | 'summary' | 'filename',
          limit: 50
        });

        return NextResponse.json({
          success: true,
          results: searchResults,
          query,
          searchType
        });

      default:
        return NextResponse.json({
          error: `Unknown action: ${action}`,
          code: 'INVALID_ACTION'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Error in POST /recordings-sql:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Internal server error',
      code: 'POST_ERROR'
    }, { status: 500 });
  }
}

// Helper function to map database status to frontend-friendly status
function mapStatusToFrontend(status: string): string {
  const statusMap: Record<string, string> = {
    'pending': 'Pending',
    'processing': 'Processing',
    'completed': 'Completed',
    'failed': 'Failed',
    'deleted': 'Deleted'
  };
  
  return statusMap[status] || status;
}

// Cache invalidation endpoint
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const cacheKey = searchParams.get('cacheKey');

    if (cacheKey) {
      delete apiCache[cacheKey];
      return NextResponse.json({ 
        success: true, 
        message: 'Cache key deleted' 
      });
    } else {
      // Clear all cache
      Object.keys(apiCache).forEach(key => delete apiCache[key]);
      return NextResponse.json({ 
        success: true, 
        message: 'All cache cleared' 
      });
    }

  } catch (error) {
    return NextResponse.json({
      error: 'Failed to clear cache',
      code: 'CACHE_ERROR'
    }, { status: 500 });
  }
}