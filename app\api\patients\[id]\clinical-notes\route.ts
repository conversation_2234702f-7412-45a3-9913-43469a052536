import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);

    // Extract query parameters for filtering and pagination
    const appointmentDate = searchParams.get('appointment_date');
    const appointmentId = searchParams.get('appointment_id');
    const dateRange = searchParams.get('date_range') || '7'; // Default 7 days before/after
    const limit = searchParams.get('limit') || '20';
    const page = searchParams.get('page') || '1';

    if (!id) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      );
    }

    console.log(`API: Fetching medical notes for patient: ${id}`, {
      appointmentDate,
      appointmentId,
      dateRange,
      limit,
      page
    });

    const credentials = loadCredentials();
    const sikkaClient = new SikkaApiClient(credentials);
    await sikkaClient.authenticate();

    // Try multiple methods to get actual chart notes
    let chartNotes = [];
    let visits = [];
    const debugInfo = {
      patientId: id,
      searchStrategy: '',
      apiCalls: [],
      filters: {},
      errors: []
    };

    // Strategy 1: If we have an appointment date and appointment ID, get notes for that specific date only
    // Note: Appointments aren't directly linked to notes - notes are linked to patient chart and dates
    if (appointmentDate && appointmentId) {
      try {
        debugInfo.searchStrategy = 'appointment_date_specific';
        debugInfo.apiCalls.push(`getPatientClinicalNotes(${id}) filtered by exact appointment date ${appointmentDate}`);

        // Get all clinical notes for the patient
        const allPatientNotes = await sikkaClient.getPatientClinicalNotes(id);

        // For appointment pages: Filter notes to ONLY the exact appointment date
        const appointmentDateStr = appointmentDate.split('T')[0]; // Ensure YYYY-MM-DD format

        chartNotes = allPatientNotes.filter(note => {
          if (!note.date) return false;

          const noteDate = new Date(note.date);
          const noteDateStr = noteDate.toISOString().split('T')[0];
          const isExactDate = noteDateStr === appointmentDateStr;

          if (isExactDate) {
            console.log(`📅 Note on appointment date: ${note.date} (${note.text?.substring(0, 50)}...)`);
          }

          return isExactDate;
        });

        console.log(`Found ${chartNotes.length} notes for patient ${id} on exact appointment date ${appointmentDate} (filtered from ${allPatientNotes.length} total patient notes)`);
      } catch (error) {
        console.error('Error fetching patient notes by appointment date:', error);
        debugInfo.errors.push(`Appointment date notes error: ${error.message}`);
      }
    }
    // Strategy 1b: If we have appointment date but no appointment ID (patient page context), use date range
    else if (appointmentDate && !appointmentId) {
      try {
        debugInfo.searchStrategy = 'date_range_around_date';
        debugInfo.apiCalls.push(`getPatientClinicalNotes(${id}) filtered by date range around ${appointmentDate}`);

        // Get all clinical notes for the patient
        const allPatientNotes = await sikkaClient.getPatientClinicalNotes(id);

        // For patient pages: Filter notes within the date range
        const appointmentDateObj = new Date(appointmentDate);
        const startDate = new Date(appointmentDateObj);
        startDate.setDate(startDate.getDate() - dateRange);
        const endDate = new Date(appointmentDateObj);
        endDate.setDate(endDate.getDate() + dateRange);

        chartNotes = allPatientNotes.filter(note => {
          if (!note.date) return false;

          const noteDate = new Date(note.date);
          const isInRange = noteDate >= startDate && noteDate <= endDate;

          if (isInRange) {
            console.log(`📅 Note in date range: ${note.date} (${note.text?.substring(0, 50)}...)`);
          }

          return isInRange;
        });

        console.log(`Found ${chartNotes.length} notes for patient ${id} within ${dateRange} days of date ${appointmentDate} (filtered from ${allPatientNotes.length} total patient notes)`);
      } catch (error) {
        console.error('Error fetching patient notes by date range:', error);
        debugInfo.errors.push(`Date range notes error: ${error.message}`);
      }
    }

    // Strategy 2: If no date-specific notes found, get all patient notes (for patient pages) or fallback
    if (chartNotes.length === 0) {
      try {
        // If no appointment ID is provided, this is likely a patient page request - show all notes
        if (!appointmentId) {
          debugInfo.searchStrategy = 'all_patient_notes_for_patient_page';
          debugInfo.apiCalls.push(`getPatientClinicalNotes(${id}) - all notes for patient page`);

          chartNotes = await sikkaClient.getPatientClinicalNotes(id);
          console.log(`Found ${chartNotes.length} total clinical notes for patient page`);
        } else {
          // If appointment ID is provided but no notes found for that date, this might be an appointment with no notes
          debugInfo.searchStrategy = 'no_notes_for_appointment_date';
          console.log(`No clinical notes found for appointment ${appointmentId} on date ${appointmentDate}`);
          chartNotes = [];
        }

      } catch (error) {
        console.log('All note fetching strategies failed:', error);
        debugInfo.errors.push(`All strategies failed: ${error.message}`);
        chartNotes = [];
      }
    }

    // Get visits for procedure codes and check for embedded chart notes
    try {
      visits = await sikkaClient.getPatientVisits(id);

      // Since chart notes aren't linked to appointments in Dentrix,
      // we won't check visits for embedded notes
    } catch (error) {
      console.log('Failed to get visits:', error);
      visits = [];
    }

    // Create a map of visits by date to match with chart notes
    const visitsByDate = new Map();
    visits.forEach(visit => {
      const visitDate = visit.date || visit.appointment_date;
      if (visitDate) {
        visitsByDate.set(visitDate, visit);
      }
    });

    // Enhance chart notes with procedure codes from visits
    const enhancedNotes = chartNotes.map(note => {
      const noteDate = note.date;
      const matchingVisit = visitsByDate.get(noteDate);

      // Extract procedure codes from the visit
      let procedures = note.procedures || [];
      if (matchingVisit) {
        const visitProcedures = [];

        // Check for procedure codes in various fields
        for (let i = 1; i <= 7; i++) {
          const procCode = matchingVisit[`procedure_code${i}`];
          const procAmount = matchingVisit[`procedure_code${i}_amount`];
          if (procCode && procCode !== '' && parseFloat(procAmount || '0') > 0) {
            visitProcedures.push(procCode);
          }
        }

        // Combine procedures from note and visit
        procedures = [...new Set([...procedures, ...visitProcedures])];
      }

      return {
        id: note.id || `${id}-${noteDate}`,
        date: note.date, // Date of the note
        provider: note.provider || note.provider_name || 'Unknown Provider',
        text: note.text || note.notes || note.note_content || note.description || '',
        type: note.type || 'Clinical Note',
        appointment_id: note.appointment_id || note.appt_id,
        appointment_date: matchingVisit ? (matchingVisit.date || matchingVisit.appointment_date) : note.appointment_date,
        tooth_number: note.tooth_number || '',
        surface: note.surface || '',
        procedures: procedures.filter(proc => proc && proc.trim() !== ''),
        appointmentType: note.appointment_type || (matchingVisit ? matchingVisit.description : ''),
        // Legacy field for backward compatibility
        notes: note.text || note.notes || note.note_content || note.description || '',
        rawData: note // Keep raw data for debugging
      };
    });

    // Apply pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;

    const totalCount = enhancedNotes.length;
    const paginatedNotes = enhancedNotes.slice(startIndex, endIndex);

    // Sort by relevance if we have appointment date, otherwise by date
    if (appointmentDate) {
      paginatedNotes.sort((a, b) => {
        // Calculate relevance score based on proximity to appointment date
        const getRelevanceScore = (note) => {
          if (!note.date) return 0;
          const daysDiff = Math.abs(new Date(note.date).getTime() - new Date(appointmentDate).getTime()) / (1000 * 60 * 60 * 24);
          return Math.max(0, 100 - daysDiff);
        };

        return getRelevanceScore(b) - getRelevanceScore(a);
      });
    } else {
      // Sort by date descending (most recent first)
      paginatedNotes.sort((a, b) => new Date(b.date || 0).getTime() - new Date(a.date || 0).getTime());
    }

    console.log(`API: Found ${totalCount} chart notes for patient ${id}, returning ${paginatedNotes.length}`);

    return NextResponse.json({
      patientId: id,
      notes: paginatedNotes,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limitNum),
        hasNext: endIndex < totalCount,
        hasPrev: pageNum > 1
      },
      debug: debugInfo,
      summary: {
        totalNotes: totalCount,
        notesReturned: paginatedNotes.length,
        searchStrategy: debugInfo.searchStrategy,
        hasAppointmentContext: !!appointmentDate || !!appointmentId,
        dateRange: appointmentDate ? `±${dateRange} days from ${appointmentDate}` : 'All notes'
      }
    });

  } catch (error) {
    console.error('Medical notes API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch medical notes' },
      { status: 500 }
    );
  }
}
