import requests
import json
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 30

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    print(f"Office ID: {OFFICE_ID}")
    print(f"Secret Key: {SECRET_KEY}")
    print(f"App ID: {APP_ID}")
    print(f"App Key: {APP_KEY}")
    
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def check_practice_info(request_key):
    """Check practice information."""
    print("\nChecking practice information...")
    headers = {"Request-Key": request_key}
    
    try:
        resp = requests.get(
            f"{API_BASE_V4}/practice",
            headers=headers,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Practice information:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")

def check_providers(request_key):
    """Check providers."""
    print("\nChecking providers...")
    headers = {"Request-Key": request_key}
    
    try:
        resp = requests.get(
            f"{API_BASE_V4}/providers",
            headers=headers,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            items = data.get("items", [])
            print(f"Found {len(items)} providers")
            
            # Print first 10 providers
            for i, provider in enumerate(items[:10]):
                print(f"  {i+1}. {provider.get('provider_id', 'N/A')} - {provider.get('name', 'Unknown')}")
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")

def check_appointments(request_key, date):
    """Check appointments."""
    print(f"\nChecking appointments for {date}...")
    headers = {"Request-Key": request_key}
    
    try:
        resp = requests.get(
            f"{API_BASE_V2}/appointments",
            headers=headers,
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            
            # Extract items from v2 response
            if isinstance(data, list) and len(data) > 0:
                items = data[0].get("items", [])
                print(f"Found {len(items)} appointments")
                
                # Print first 10 appointments
                for i, appt in enumerate(sorted(items, key=lambda a: a.get("time", ""))[:10]):
                    print(f"  {i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('operatory', 'N/A')} - {appt.get('provider_id', 'N/A')} - {appt.get('description', '')}")
            else:
                print("No appointments found")
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")

def main():
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Check practice information
    check_practice_info(request_key)
    
    # Check providers
    check_providers(request_key)
    
    # Check appointments
    check_appointments(request_key, "2023-05-16")
    
    print("\nCheck complete.")

if __name__ == "__main__":
    main()
