import { NextRequest, NextResponse } from 'next/server';
import { AzureStorageService } from '../../../../../src/lib/azure-storage';

export async function POST(request: NextRequest) {
  try {
    const { recordingId, newDate, reason } = await request.json();

    if (!recordingId || !newDate) {
      return NextResponse.json(
        { error: 'Recording ID and new date are required' },
        { status: 400 }
      );
    }

    // Validate the new date
    const correctedDate = new Date(newDate);
    if (isNaN(correctedDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format' },
        { status: 400 }
      );
    }

    // Load existing date corrections from Azure
    let dateCorrections = {};
    try {
      dateCorrections = await AzureStorageService.downloadJson('metadata/date-corrections.json') || {};
    } catch (error) {
      console.log('No existing date corrections found, creating new file');
    }

    // Store the date correction
    dateCorrections[recordingId] = {
      correctedDate: correctedDate.toISOString(),
      reason: reason || 'Manual correction',
      correctedAt: new Date().toISOString(),
      correctedBy: 'user' // Could be enhanced with actual user info
    };

    // Save back to Azure
    await AzureStorageService.uploadJson('metadata/date-corrections.json', dateCorrections);

    console.log(`Date corrected for recording ${recordingId}: ${correctedDate.toISOString()}`);

    return NextResponse.json({
      success: true,
      recordingId,
      correctedDate: correctedDate.toISOString(),
      message: 'Recording date corrected successfully'
    });

  } catch (error: any) {
    console.error('Date correction error:', error);
    return NextResponse.json(
      { error: `Failed to correct date: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const recordingId = searchParams.get('recordingId');

    // Load date corrections from Azure
    let dateCorrections = {};
    try {
      dateCorrections = await AzureStorageService.downloadJson('metadata/date-corrections.json') || {};
    } catch (error) {
      console.log('No date corrections found');
    }

    if (recordingId) {
      // Return specific correction
      const correction = dateCorrections[recordingId];
      return NextResponse.json({
        recordingId,
        correction: correction || null
      });
    } else {
      // Return all corrections
      return NextResponse.json({
        corrections: dateCorrections
      });
    }

  } catch (error: any) {
    console.error('Failed to get date corrections:', error);
    return NextResponse.json(
      { error: `Failed to get date corrections: ${error.message}` },
      { status: 500 }
    );
  }
}
