/**
 * Audio file validation utilities
 */

interface AudioValidationResult {
  isValid: boolean;
  error?: string;
  details?: {
    format?: string;
    duration?: number;
    sampleRate?: number;
    channels?: number;
    bitRate?: number;
  };
}

/**
 * Basic audio file header validation
 */
function validateAudioHeaders(buffer: Buffer, fileName: string): AudioValidationResult {
  const ext = fileName.toLowerCase().split('.').pop();
  
  try {
    // MP3 validation
    if (ext === 'mp3') {
      // Check for MP3 frame header (11 bits of 1s)
      let hasValidMp3Header = false;
      for (let i = 0; i < Math.min(buffer.length - 1, 8192); i++) {
        if ((buffer[i] === 0xFF) && ((buffer[i + 1] & 0xE0) === 0xE0)) {
          hasValidMp3Header = true;
          break;
        }
      }
      
      if (!hasValidMp3Header) {
        return { isValid: false, error: 'Invalid MP3 header - file may be corrupted or not a valid MP3 file' };
      }
      
      // Additional check for ID3 tag or direct audio data
      const hasId3Tag = buffer.slice(0, 3).toString('ascii') === 'ID3';
      const hasDirectAudio = hasValidMp3Header;
      
      if (!hasId3Tag && !hasDirectAudio) {
        return { isValid: false, error: 'MP3 file appears to be corrupted - no valid ID3 tag or audio data found' };
      }
    }
    
    // WAV validation
    else if (ext === 'wav') {
      // Check RIFF header
      const riffHeader = buffer.slice(0, 4).toString('ascii');
      const waveHeader = buffer.slice(8, 12).toString('ascii');
      
      if (riffHeader !== 'RIFF' || waveHeader !== 'WAVE') {
        return { isValid: false, error: 'Invalid WAV header - file may be corrupted' };
      }
    }
    
    // M4A validation
    else if (ext === 'm4a' || ext === 'mp4') {
      // Check for ftyp atom
      const ftypCheck = buffer.slice(4, 8).toString('ascii');
      if (ftypCheck !== 'ftyp') {
        return { isValid: false, error: 'Invalid M4A/MP4 header - file may be corrupted' };
      }
    }
    
    // WEBM validation
    else if (ext === 'webm') {
      // Check EBML header
      const ebmlHeader = buffer.slice(0, 4);
      if (ebmlHeader[0] !== 0x1A || ebmlHeader[1] !== 0x45 || ebmlHeader[2] !== 0xDF || ebmlHeader[3] !== 0xA3) {
        return { isValid: false, error: 'Invalid WebM header - file may be corrupted' };
      }
    }
    
    return { isValid: true };
    
  } catch (error) {
    return { 
      isValid: false, 
      error: `Header validation failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}

/**
 * Check if file size is reasonable for audio
 */
function validateFileSize(buffer: Buffer, fileName: string): AudioValidationResult {
  const size = buffer.length;
  
  // Check for suspiciously small files (less than 1KB)
  if (size < 1024) {
    return { 
      isValid: false, 
      error: 'File too small - likely corrupted or not an audio file' 
    };
  }
  
  // Check for extremely large files (over 500MB)
  if (size > 500 * 1024 * 1024) {
    return { 
      isValid: false, 
      error: 'File too large - exceeds maximum supported size (500MB)' 
    };
  }
  
  return { isValid: true };
}

/**
 * Check file extension against content
 */
function validateFileExtension(fileName: string): AudioValidationResult {
  const ext = fileName.toLowerCase().split('.').pop();
  const supportedFormats = ['mp3', 'wav', 'm4a', 'mp4', 'webm', 'ogg', 'aac', 'flac'];
  
  if (!ext || !supportedFormats.includes(ext)) {
    return { 
      isValid: false, 
      error: `Unsupported file format: ${ext}. Supported formats: ${supportedFormats.join(', ')}` 
    };
  }
  
  return { isValid: true };
}

/**
 * Comprehensive audio file validation
 */
export async function validateAudioFile(buffer: Buffer, fileName: string): Promise<AudioValidationResult> {
  try {
    // 1. Check file extension
    const extResult = validateFileExtension(fileName);
    if (!extResult.isValid) return extResult;
    
    // 2. Check file size
    const sizeResult = validateFileSize(buffer, fileName);
    if (!sizeResult.isValid) return sizeResult;
    
    // 3. Check file headers
    const headerResult = validateAudioHeaders(buffer, fileName);
    if (!headerResult.isValid) return headerResult;
    
    // 4. Additional checks for zero-byte patterns (common in corrupted files)
    const hasContent = buffer.some(byte => byte !== 0);
    if (!hasContent) {
      return { 
        isValid: false, 
        error: 'File appears to be empty or contains only null bytes' 
      };
    }
    
    // 5. Check for minimum data ratio (detect mostly empty files)
    const nonZeroBytes = buffer.filter(byte => byte !== 0).length;
    const dataRatio = nonZeroBytes / buffer.length;
    if (dataRatio < 0.1) {
      return { 
        isValid: false, 
        error: 'File appears to be mostly empty - likely corrupted' 
      };
    }
    
    // 6. OpenAI Whisper-specific validation
    const transcriptionReadiness = validateForTranscription(buffer, fileName);
    if (!transcriptionReadiness.isValid) return transcriptionReadiness;
    
    return { 
      isValid: true, 
      details: {
        format: fileName.toLowerCase().split('.').pop(),
        dataRatio: Math.round(dataRatio * 100) / 100,
        // Add more details if needed in the future
      }
    };
    
  } catch (error) {
    return { 
      isValid: false, 
      error: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
    };
  }
}

/**
 * Validate file for OpenAI Whisper transcription compatibility
 */
function validateForTranscription(buffer: Buffer, fileName: string): AudioValidationResult {
  const ext = fileName.toLowerCase().split('.').pop();
  
  // Check OpenAI supported formats
  const openaiFormats = ['flac', 'm4a', 'mp3', 'mp4', 'mpeg', 'mpga', 'oga', 'ogg', 'wav', 'webm'];
  if (!ext || !openaiFormats.includes(ext)) {
    return {
      isValid: false,
      error: `Format ${ext} not supported by OpenAI Whisper. Supported: ${openaiFormats.join(', ')}`
    };
  }
  
  // Check file size against OpenAI limit (25MB)
  const maxSize = 25 * 1024 * 1024; // 25MB
  if (buffer.length > maxSize) {
    return {
      isValid: true, // Still valid, but will need chunking
      details: {
        needsChunking: true,
        originalSize: buffer.length,
        maxDirectSize: maxSize
      }
    };
  }
  
  return { isValid: true };
}

/**
 * Quick validation for upload preview (before full upload)
 */
export function quickValidateAudioFile(file: File): AudioValidationResult {
  // Check file extension
  const extResult = validateFileExtension(file.name);
  if (!extResult.isValid) return extResult;
  
  // Check file size
  if (file.size < 1024) {
    return { 
      isValid: false, 
      error: 'File too small - likely corrupted or not an audio file' 
    };
  }
  
  if (file.size > 500 * 1024 * 1024) {
    return { 
      isValid: false, 
      error: 'File too large - exceeds maximum supported size (500MB)' 
    };
  }
  
  return { isValid: true };
}