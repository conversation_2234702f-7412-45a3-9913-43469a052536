/**
 * Audio utility functions for extracting metadata from audio files
 */

import { AzureStorageService } from './azure-storage';

interface AudioMetadata {
  duration: number; // in seconds
  size: number;
  format?: string;
}

/**
 * Extract audio duration from a buffer using Web Audio API approach
 * This is a simplified version - in production you might want to use a library like node-ffprobe
 */
export async function getAudioDuration(audioBuffer: Buffer): Promise<number> {
  try {
    // For now, estimate duration based on file size and common bitrates
    // This is a rough approximation - actual duration would require audio analysis
    const fileSizeKB = audioBuffer.length / 1024;
    
    // Estimate based on common WebM/MP3 bitrates (128kbps average)
    // Formula: (file_size_in_bits) / (bitrate_in_bits_per_second)
    const estimatedDuration = (fileSizeKB * 8) / 128; // seconds
    
    // Cap at reasonable limits (1 second minimum, 30 minutes maximum)
    return Math.max(1, Math.min(estimatedDuration, 1800));
  } catch (error) {
    console.error('Error estimating audio duration:', error);
    return 0;
  }
}

/**
 * Get audio metadata from Azure Blob Storage file
 */
export async function getAudioMetadataFromAzure(filePath: string): Promise<AudioMetadata> {
  try {
    // Download the file to analyze it
    const audioBuffer = await AzureStorageService.downloadFile(filePath);
    const duration = await getAudioDuration(audioBuffer);
    
    return {
      duration,
      size: audioBuffer.length,
      format: getFormatFromPath(filePath)
    };
  } catch (error) {
    console.error('Error getting audio metadata:', error);
    return {
      duration: 0,
      size: 0
    };
  }
}

/**
 * Extract format from file path
 */
function getFormatFromPath(filePath: string): string {
  const extension = filePath.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'webm':
      return 'audio/webm';
    case 'mp3':
      return 'audio/mpeg';
    case 'wav':
      return 'audio/wav';
    case 'm4a':
      return 'audio/mp4';
    case 'ogg':
      return 'audio/ogg';
    default:
      return 'audio/unknown';
  }
}

/**
 * Cache audio metadata to avoid repeated processing
 */
const metadataCache = new Map<string, AudioMetadata>();

export async function getCachedAudioMetadata(filePath: string): Promise<AudioMetadata> {
  if (metadataCache.has(filePath)) {
    return metadataCache.get(filePath)!;
  }
  
  const metadata = await getAudioMetadataFromAzure(filePath);
  metadataCache.set(filePath, metadata);
  
  return metadata;
}

/**
 * Better duration estimation based on file type and size
 */
export function estimateAudioDuration(fileSize: number, fileName: string): number {
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  // Different compression ratios for different formats
  let estimatedBitrate = 128; // kbps default
  
  switch (extension) {
    case 'mp3':
      estimatedBitrate = 128; // Standard MP3
      break;
    case 'webm':
      estimatedBitrate = 64; // WebM is typically more compressed
      break;
    case 'wav':
      estimatedBitrate = 1411; // Uncompressed WAV (44.1kHz, 16-bit, stereo)
      break;
    case 'm4a':
      estimatedBitrate = 128; // AAC compression
      break;
    case 'ogg':
      estimatedBitrate = 112; // Ogg Vorbis
      break;
  }
  
  // Convert file size to bits, then divide by bitrate to get duration in seconds
  const fileSizeBits = fileSize * 8;
  const bitratePerSecond = estimatedBitrate * 1000; // Convert kbps to bps
  const estimatedDuration = fileSizeBits / bitratePerSecond;
  
  // Return reasonable bounds (1 second to 30 minutes)
  return Math.max(1, Math.min(Math.round(estimatedDuration), 1800));
}
