export class ProcessingError extends Error {
  public readonly code: string;
  public readonly retryable: boolean;
  public readonly details?: any;

  constructor(message: string, code: string, retryable: boolean = false, details?: any) {
    super(message);
    this.name = 'ProcessingError';
    this.code = code;
    this.retryable = retryable;
    this.details = details;
  }
}

export class FileFormatError extends ProcessingError {
  constructor(filename: string, supportedFormats: string[]) {
    super(
      `Invalid file format for ${filename}. Supported formats: ${supportedFormats.join(', ')}`,
      'INVALID_FILE_FORMAT',
      false,
      { filename, supportedFormats }
    );
  }
}

export class ServiceUnavailableError extends ProcessingError {
  constructor(serviceName: string, details?: any) {
    super(
      `${serviceName} service is currently unavailable`,
      'SERVICE_UNAVAILABLE',
      true,
      { serviceName, ...details }
    );
  }
}

export class QuotaExceededError extends ProcessingError {
  constructor(serviceName: string, resetTime?: Date) {
    super(
      `${serviceName} quota exceeded${resetTime ? `, resets at ${resetTime.toISOString()}` : ''}`,
      'QUOTA_EXCEEDED',
      true,
      { serviceName, resetTime }
    );
  }
}

export class FileNotFoundError extends ProcessingError {
  constructor(filename: string) {
    super(
      `File not found: ${filename}`,
      'FILE_NOT_FOUND',
      false,
      { filename }
    );
  }
}

export class StateCorruptionError extends ProcessingError {
  constructor(filename: string, details?: any) {
    super(
      `Processing state corruption detected for ${filename}`,
      'STATE_CORRUPTION',
      false,
      { filename, ...details }
    );
  }
}

export class ErrorHandler {
  /**
   * Enhanced error classification with more specific error types
   */
  static classifyError(error: any): {
    type: string;
    message: string;
    retryable: boolean;
    retryDelay?: number;
    code?: string;
  } {
    if (error instanceof ProcessingError) {
      return {
        type: error.constructor.name,
        message: error.message,
        retryable: error.retryable,
        retryDelay: error.retryable ? this.calculateRetryDelay(error.code) : undefined,
        code: error.code
      };
    }

    // OpenAI specific errors
    if (error.status === 401) {
      return {
        type: 'AuthenticationError',
        message: 'Invalid API key or authentication failed',
        retryable: false,
        code: 'AUTH_FAILED'
      };
    }

    if (error.status === 429) {
      return {
        type: 'QuotaExceededError',
        message: 'API rate limit exceeded',
        retryable: true,
        retryDelay: 60000,
        code: 'RATE_LIMIT_EXCEEDED'
      };
    }

    if (error.status === 413) {
      return {
        type: 'FileTooLargeError',
        message: 'File size exceeds API limits',
        retryable: false,
        code: 'FILE_TOO_LARGE'
      };
    }

    // Azure Storage errors
    if (error.message?.includes('The specified blob does not exist')) {
      return {
        type: 'FileNotFoundError',
        message: 'File not found in Azure storage',
        retryable: false,
        code: 'BLOB_NOT_FOUND'
      };
    }

    if (error.code === 'BlobNotFound') {
      return {
        type: 'FileNotFoundError',
        message: 'Blob not found in Azure storage',
        retryable: false,
        code: 'BLOB_NOT_FOUND'
      };
    }

    if (error.code === 'ContainerNotFound') {
      return {
        type: 'ConfigurationError',
        message: 'Azure storage container not found',
        retryable: false,
        code: 'CONTAINER_NOT_FOUND'
      };
    }

    // Azure Speech Service errors
    if (error.message?.includes('recognition canceled')) {
      return {
        type: 'ServiceUnavailableError',
        message: 'Speech recognition service unavailable',
        retryable: true,
        retryDelay: 5000,
        code: 'SPEECH_SERVICE_UNAVAILABLE'
      };
    }

    // Handle rate limiting (broader patterns)
    if (error.message?.toLowerCase().includes('rate limit') || 
        error.message?.toLowerCase().includes('quota') ||
        error.message?.toLowerCase().includes('too many requests')) {
      return {
        type: 'QuotaExceededError',
        message: 'Service quota exceeded',
        retryable: true,
        retryDelay: 60000, // 1 minute
        code: 'QUOTA_EXCEEDED'
      };
    }

    // Network errors
    if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT' || 
        error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return {
        type: 'NetworkError',
        message: `Network connection error: ${error.code}`,
        retryable: true,
        retryDelay: 10000,
        code: 'NETWORK_ERROR'
      };
    }

    // Timeout errors
    if (error.name === 'AbortError' || error.message?.includes('timeout')) {
      return {
        type: 'TimeoutError',
        message: 'Operation timed out',
        retryable: true,
        retryDelay: 15000,
        code: 'TIMEOUT'
      };
    }

    // File system errors
    if (error.code === 'ENOENT') {
      return {
        type: 'FileNotFoundError',
        message: 'File not found on local filesystem',
        retryable: false,
        code: 'FILE_NOT_FOUND'
      };
    }

    if (error.code === 'EACCES' || error.code === 'EPERM') {
      return {
        type: 'PermissionError',
        message: 'Insufficient permissions to access file',
        retryable: false,
        code: 'PERMISSION_DENIED'
      };
    }

    // Default to unknown error
    return {
      type: 'UnknownError',
      message: error.message || 'An unknown error occurred',
      retryable: false,
      code: 'UNKNOWN'
    };
  }

  /**
   * Calculate retry delay based on error type
   */
  private static calculateRetryDelay(errorCode: string): number {
    switch (errorCode) {
      case 'QUOTA_EXCEEDED':
      case 'RATE_LIMIT_EXCEEDED':
        return 60000; // 1 minute
      case 'SERVICE_UNAVAILABLE':
      case 'SPEECH_SERVICE_UNAVAILABLE':
        return 5000; // 5 seconds
      case 'NETWORK_ERROR':
        return 10000; // 10 seconds
      case 'TIMEOUT':
      case 'API_TIMEOUT':
        return 15000; // 15 seconds
      case 'BLOB_NOT_FOUND':
      case 'CONTAINER_NOT_FOUND':
      case 'FILE_NOT_FOUND':
      case 'AUTH_FAILED':
      case 'FILE_TOO_LARGE':
      case 'INVALID_FILE_FORMAT':
      case 'PERMISSION_DENIED':
        return 0; // Non-retryable errors
      case 'STATE_CORRUPTION':
        return 30000; // 30 seconds for corruption issues
      case 'API_ERROR':
        return 5000; // 5 seconds for general API errors
      case 'MAX_RETRIES_EXHAUSTED':
        return 0; // Should not retry
      case 'NON_RETRYABLE':
        return 0; // Should not retry
      default:
        return 3000; // 3 seconds default
    }
  }

  /**
   * Execute a function with retry logic
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    operationName: string = 'operation'
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 Attempt ${attempt}/${maxRetries} for ${operationName}`);
        return await operation();
      } catch (error) {
        lastError = error;
        const errorInfo = this.classifyError(error);
        
        console.error(`❌ Attempt ${attempt} failed for ${operationName}:`, {
          type: errorInfo.type,
          message: errorInfo.message,
          retryable: errorInfo.retryable
        });

        // Don't retry if error is not retryable
        if (!errorInfo.retryable) {
          throw error;
        }

        // Don't wait on the last attempt
        if (attempt < maxRetries && errorInfo.retryDelay) {
          console.log(`⏱️ Waiting ${errorInfo.retryDelay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, errorInfo.retryDelay));
        }
      }
    }

    throw lastError;
  }

  /**
   * Log error with structured information
   */
  static logError(error: any, context: { filename?: string; operation?: string; jobId?: string } = {}): void {
    const errorInfo = this.classifyError(error);
    
    console.error('❌ Processing Error:', {
      ...errorInfo,
      context,
      timestamp: new Date().toISOString(),
      stack: error.stack
    });
  }

  /**
   * Convert error to user-friendly message
   */
  static getUserMessage(error: any): string {
    const errorInfo = this.classifyError(error);
    
    switch (errorInfo.code) {
      case 'INVALID_FILE_FORMAT':
        return 'The audio file format is not supported. Please use MP3, WAV, M4A, or other supported formats.';
      
      case 'FILE_NOT_FOUND':
        return 'The audio file could not be found. Please check that the file exists and try again.';
      
      case 'SERVICE_UNAVAILABLE':
        return 'The transcription service is temporarily unavailable. Please try again in a few minutes.';
      
      case 'QUOTA_EXCEEDED':
        return 'Service usage limit reached. Please wait a few minutes before trying again.';
      
      case 'NETWORK_ERROR':
        return 'Network connection error. Please check your internet connection and try again.';
      
      case 'TIMEOUT':
        return 'The operation took too long to complete. Please try again with a smaller file.';
      
      default:
        return 'An unexpected error occurred. Please try again or contact support if the problem persists.';
    }
  }

  /**
   * Check if an error indicates the operation should be retried later
   */
  static shouldRetryLater(error: any): boolean {
    const errorInfo = this.classifyError(error);
    return errorInfo.retryable;
  }

  /**
   * Get recommended retry delay for an error
   */
  static getRetryDelay(error: any): number {
    const errorInfo = this.classifyError(error);
    return errorInfo.retryDelay || 3000;
  }

  /**
   * Execute operation with exponential backoff retry
   */
  static async withExponentialBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000,
    maxDelay: number = 30000,
    operationName: string = 'operation',
    context?: { jobId?: string; filename?: string }
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (context?.jobId) {
          console.log(`🔄 [Job ${context.jobId}] Attempt ${attempt}/${maxRetries} for ${operationName}`);
        } else {
          console.log(`🔄 Attempt ${attempt}/${maxRetries} for ${operationName}`);
        }
        
        return await operation();
      } catch (error) {
        lastError = error;
        const errorInfo = this.classifyError(error);
        
        const logContext = {
          attempt,
          maxRetries,
          operationName,
          ...context,
          error: {
            type: errorInfo.type,
            message: errorInfo.message,
            code: errorInfo.code,
            retryable: errorInfo.retryable
          }
        };

        if (context?.jobId) {
          console.error(`❌ [Job ${context.jobId}] Attempt ${attempt} failed:`, logContext);
        } else {
          console.error(`❌ Attempt ${attempt} failed for ${operationName}:`, logContext);
        }

        // Don't retry if error is not retryable
        if (!errorInfo.retryable) {
          throw new ProcessingError(
            `Non-retryable error in ${operationName}: ${errorInfo.message}`,
            errorInfo.code || 'NON_RETRYABLE',
            false,
            { originalError: error, context: logContext }
          );
        }

        // Don't wait on the last attempt
        if (attempt < maxRetries) {
          // Calculate exponential backoff delay
          const exponentialDelay = Math.min(
            baseDelay * Math.pow(2, attempt - 1),
            maxDelay
          );
          
          // Add some jitter to prevent thundering herd
          const jitter = Math.random() * 1000;
          const totalDelay = Math.min(exponentialDelay + jitter, maxDelay);
          
          if (context?.jobId) {
            console.log(`⏱️ [Job ${context.jobId}] Waiting ${Math.round(totalDelay)}ms before retry...`);
          } else {
            console.log(`⏱️ Waiting ${Math.round(totalDelay)}ms before retry...`);
          }
          
          await new Promise(resolve => setTimeout(resolve, totalDelay));
        }
      }
    }

    // All retries exhausted
    throw new ProcessingError(
      `Max retries (${maxRetries}) exhausted for ${operationName}: ${lastError.message}`,
      'MAX_RETRIES_EXHAUSTED',
      false,
      { originalError: lastError, context }
    );
  }

  /**
   * Wrap API calls with automatic retry and error transformation
   */
  static async wrapApiCall<T>(
    apiCall: () => Promise<T>,
    apiName: string,
    context?: { jobId?: string; filename?: string; model?: string }
  ): Promise<T> {
    const operationName = `${apiName} API call`;
    
    return this.withExponentialBackoff(
      async () => {
        try {
          const startTime = Date.now();
          const result = await apiCall();
          const duration = Date.now() - startTime;
          
          if (context?.jobId) {
            console.log(`✅ [Job ${context.jobId}] ${apiName} API call completed in ${duration}ms`);
          } else {
            console.log(`✅ ${apiName} API call completed in ${duration}ms`);
          }
          
          return result;
        } catch (error: any) {
          // Transform common API errors into our structured errors
          if (error.status === 429 || error.message?.includes('rate limit')) {
            throw new QuotaExceededError(apiName, this.extractResetTime(error));
          }
          
          if (error.status >= 500 && error.status < 600) {
            throw new ServiceUnavailableError(apiName, { 
              status: error.status, 
              statusText: error.statusText 
            });
          }
          
          if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT' || error.code === 'ENOTFOUND') {
            throw new ProcessingError(
              `Network error calling ${apiName}: ${error.message}`,
              'NETWORK_ERROR',
              true,
              { originalError: error }
            );
          }
          
          if (error.name === 'AbortError' || error.message?.includes('timeout')) {
            throw new ProcessingError(
              `Timeout calling ${apiName}: ${error.message}`,
              'API_TIMEOUT',
              true,
              { originalError: error }
            );
          }
          
          // Re-throw as-is if it's already a ProcessingError
          if (error instanceof ProcessingError) {
            throw error;
          }
          
          // Wrap unknown errors
          throw new ProcessingError(
            `API error from ${apiName}: ${error.message}`,
            'API_ERROR',
            false,
            { originalError: error, status: error.status }
          );
        }
      },
      3, // maxRetries
      1000, // baseDelay
      30000, // maxDelay
      operationName,
      context
    );
  }

  /**
   * Create standardized error for job processing
   */
  static createJobError(
    error: any,
    stage: string,
    context: { jobId: string; filename?: string; operation?: string }
  ): any {
    const errorInfo = this.classifyError(error);
    
    return {
      type: this.mapErrorTypeToJobError(errorInfo.type),
      code: errorInfo.code || 'UNKNOWN',
      message: errorInfo.message,
      details: error.details || error,
      timestamp: new Date().toISOString(),
      stage,
      retryable: errorInfo.retryable,
      context: {
        jobId: context.jobId,
        filename: context.filename,
        operation: context.operation,
        stackTrace: error.stack
      }
    };
  }

  /**
   * Map error types to job error types
   */
  private static mapErrorTypeToJobError(errorType: string): string {
    const mapping: Record<string, string> = {
      'FileNotFoundError': 'file_not_found',
      'FileFormatError': 'validation_error',
      'ServiceUnavailableError': 'api_error',
      'QuotaExceededError': 'quota_exceeded',
      'NetworkError': 'network_error',
      'TimeoutError': 'timeout_error',
      'ProcessingError': 'api_error',
      'UnknownError': 'unknown_error'
    };
    
    return mapping[errorType] || 'unknown_error';
  }

  /**
   * Extract rate limit reset time from error
   */
  private static extractResetTime(error: any): Date | undefined {
    // Try to extract reset time from various API error formats
    if (error.headers && error.headers['x-ratelimit-reset']) {
      return new Date(parseInt(error.headers['x-ratelimit-reset']) * 1000);
    }
    
    if (error.headers && error.headers['retry-after']) {
      const retryAfter = parseInt(error.headers['retry-after']);
      return new Date(Date.now() + retryAfter * 1000);
    }
    
    // Default to 1 minute from now
    return new Date(Date.now() + 60000);
  }

  /**
   * Handle batch operation errors
   */
  static async processBatchWithErrorHandling<T, R>(
    items: T[],
    processor: (item: T, index: number) => Promise<R>,
    options: {
      batchSize?: number;
      continueOnError?: boolean;
      maxConcurrent?: number;
      operationName?: string;
      context?: { jobId?: string };
    } = {}
  ): Promise<{
    results: (R | null)[];
    errors: Array<{ index: number; item: T; error: any }>;
    summary: { total: number; successful: number; failed: number };
  }> {
    const {
      batchSize = 10,
      continueOnError = true,
      maxConcurrent = 3,
      operationName = 'batch operation',
      context
    } = options;

    const results: (R | null)[] = [];
    const errors: Array<{ index: number; item: T; error: any }> = [];
    
    console.log(`🚀 Starting ${operationName} for ${items.length} items (batch size: ${batchSize}, max concurrent: ${maxConcurrent})`);

    // Process items in batches
    for (let batchStart = 0; batchStart < items.length; batchStart += batchSize) {
      const batchEnd = Math.min(batchStart + batchSize, items.length);
      const batch = items.slice(batchStart, batchEnd);
      
      console.log(`📦 Processing batch ${Math.floor(batchStart / batchSize) + 1}/${Math.ceil(items.length / batchSize)} (items ${batchStart + 1}-${batchEnd})`);

      // Process batch items with controlled concurrency
      const batchPromises = batch.map(async (item, batchIndex) => {
        const globalIndex = batchStart + batchIndex;
        
        try {
          const result = await processor(item, globalIndex);
          results[globalIndex] = result;
          
          if (context?.jobId) {
            console.log(`✅ [Job ${context.jobId}] Item ${globalIndex + 1}/${items.length} completed`);
          } else {
            console.log(`✅ Item ${globalIndex + 1}/${items.length} completed`);
          }
          
          return { index: globalIndex, result, error: null };
        } catch (error) {
          const errorInfo = this.classifyError(error);
          
          if (context?.jobId) {
            console.error(`❌ [Job ${context.jobId}] Item ${globalIndex + 1}/${items.length} failed:`, errorInfo);
          } else {
            console.error(`❌ Item ${globalIndex + 1}/${items.length} failed:`, errorInfo);
          }
          
          results[globalIndex] = null;
          errors.push({ index: globalIndex, item, error });
          
          if (!continueOnError) {
            throw error;
          }
          
          return { index: globalIndex, result: null, error };
        }
      });

      // Wait for batch completion with concurrency control
      const batchResults = await Promise.allSettled(batchPromises);
      
      // Check for any rejected promises (shouldn't happen with continueOnError=true)
      batchResults.forEach((result, batchIndex) => {
        if (result.status === 'rejected') {
          const globalIndex = batchStart + batchIndex;
          console.error(`💥 Unexpected batch error for item ${globalIndex + 1}:`, result.reason);
          
          if (!continueOnError) {
            throw result.reason;
          }
        }
      });
    }

    const summary = {
      total: items.length,
      successful: results.filter(r => r !== null).length,
      failed: errors.length
    };

    console.log(`📊 ${operationName} completed:`, summary);

    return { results, errors, summary };
  }
}