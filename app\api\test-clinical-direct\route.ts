import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const patientId = searchParams.get('patient_id') || '41848';
    
    console.log(`Testing direct Sikka endpoint for patient ${patientId}`);

    const credentials = loadCredentials();
    const sikkaClient = new SikkaApiClient(credentials);
    await sikkaClient.authenticate();
    const requestKey = await sikkaClient.getRequestKey();

    const results: any = {
      patientId,
      tests: {}
    };

    // Test 1: Exact format you showed - "Clinical note" with space, no encoding
    try {
      const url1 = `https://api.sikkasoft.com/v4/medical_notes/Clinical note?patient_id=${patientId}`;
      console.log(`Testing: ${url1}`);
      
      const response1 = await fetch(url1, {
        method: 'GET',
        headers: { 'Request-Key': requestKey },
      });

      if (response1.ok) {
        const data1 = await response1.json();
        results.tests.clinicalNoteWithSpace = {
          status: response1.status,
          count: Array.isArray(data1) ? data1.length : (data1.items?.length || 0),
          data: data1
        };
      } else {
        results.tests.clinicalNoteWithSpace = {
          status: response1.status,
          error: await response1.text()
        };
      }
    } catch (error) {
      results.tests.clinicalNoteWithSpace = { error: error.message };
    }

    // Test 2: URL encoded version "Clinical%20Note"
    try {
      const url2 = `https://api.sikkasoft.com/v4/medical_notes/Clinical%20Note?patient_id=${patientId}`;
      console.log(`Testing: ${url2}`);
      
      const response2 = await fetch(url2, {
        method: 'GET',
        headers: { 'Request-Key': requestKey },
      });

      if (response2.ok) {
        const data2 = await response2.json();
        results.tests.clinicalNoteEncoded = {
          status: response2.status,
          count: Array.isArray(data2) ? data2.length : (data2.items?.length || 0),
          data: data2
        };
      } else {
        results.tests.clinicalNoteEncoded = {
          status: response2.status,
          error: await response2.text()
        };
      }
    } catch (error) {
      results.tests.clinicalNoteEncoded = { error: error.message };
    }

    // Test 3: Try with the working patient ID 45953
    try {
      const url3 = `https://api.sikkasoft.com/v4/medical_notes/Clinical note?patient_id=45953`;
      console.log(`Testing with working patient: ${url3}`);
      
      const response3 = await fetch(url3, {
        method: 'GET',
        headers: { 'Request-Key': requestKey },
      });

      if (response3.ok) {
        const data3 = await response3.json();
        results.tests.workingPatient45953 = {
          status: response3.status,
          count: Array.isArray(data3) ? data3.length : (data3.items?.length || 0),
          hasData: Array.isArray(data3) ? data3.length > 0 : !!(data3.items?.length)
        };
      } else {
        results.tests.workingPatient45953 = {
          status: response3.status,
          error: await response3.text()
        };
      }
    } catch (error) {
      results.tests.workingPatient45953 = { error: error.message };
    }

    // Test 4: Try different note type variations
    const noteTypes = ['Clinical note', 'Clinical Note', 'clinical note', 'CLINICAL NOTE'];
    results.tests.noteTypeVariations = {};

    for (const noteType of noteTypes) {
      try {
        const url = `https://api.sikkasoft.com/v4/medical_notes/${encodeURIComponent(noteType)}?patient_id=${patientId}`;
        console.log(`Testing note type: "${noteType}" -> ${url}`);
        
        const response = await fetch(url, {
          method: 'GET',
          headers: { 'Request-Key': requestKey },
        });

        if (response.ok) {
          const data = await response.json();
          results.tests.noteTypeVariations[noteType] = {
            status: response.status,
            count: Array.isArray(data) ? data.length : (data.items?.length || 0)
          };
        } else {
          results.tests.noteTypeVariations[noteType] = {
            status: response.status,
            error: 'Failed'
          };
        }
      } catch (error) {
        results.tests.noteTypeVariations[noteType] = { error: error.message };
      }
    }

    return NextResponse.json(results);

  } catch (error) {
    console.error('Test Clinical Direct error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to test direct endpoints' },
      { status: 500 }
    );
  }
}
