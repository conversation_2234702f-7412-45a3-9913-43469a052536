(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9201],{5860:(e,t,a)=>{Promise.resolve().then(a.bind(a,8214))},8214:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var s=a(5155),r=a(2115),l=a(1539),i=a(3286),n=a(5525),c=a(4186),o=a(646),d=a(2138),m=a(381),x=a(5021),g=a(5690),h=a(6496),u=a(3563),p=a(8126),b=a(1243),y=a(1284),w=a(1788);function j(e){let{onCapabilitiesDetected:t,showDetails:a=!0,isDarkMode:l=!1}=e,[i,n]=(0,r.useState)(null),c=(0,r.useCallback)(()=>{let e=navigator.userAgent,a="Unknown",s="Unknown";if(e.includes("Chrome")&&!e.includes("Edg")){a="Chrome";let t=e.match(/Chrome\/(\d+)/);s=t?t[1]:"Unknown"}else if(e.includes("Edg")){a="Edge";let t=e.match(/Edg\/(\d+)/);s=t?t[1]:"Unknown"}else if(e.includes("Firefox")){a="Firefox";let t=e.match(/Firefox\/(\d+)/);s=t?t[1]:"Unknown"}else if(e.includes("Safari")&&!e.includes("Chrome")){a="Safari";let t=e.match(/Version\/(\d+)/);s=t?t[1]:"Unknown"}let r="showDirectoryPicker"in window,l="usb"in navigator,i="ondrop"in window,c="File"in window&&"FileReader"in window,o="none",d=!1;r&&l?(o="full",d=!0):r||i&&c?(o="partial",d=!0):c&&(o="minimal",d=!0);let m={fileSystemAccess:r,webUSB:l,dragAndDrop:i,fileAPI:c,browserName:a,browserVersion:s,isSupported:d,supportLevel:o};return n(m),null==t||t(m),m},[t]);if((0,r.useEffect)(()=>{c()},[c]),!i)return(0,s.jsx)("div",{className:"p-4 rounded-lg ".concat(l?"bg-gray-800":"bg-gray-50"),children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-4 rounded w-3/4 mb-2 ".concat(l?"bg-gray-700":"bg-gray-300")}),(0,s.jsx)("div",{className:"h-3 rounded w-1/2 ".concat(l?"bg-gray-700":"bg-gray-300")})]})});let o=(e=>{switch(e.supportLevel){case"full":return{type:"success",title:"Full WebUSB Support",message:"Your browser supports all WebUSB features including direct USB file access and automatic archiving.",icon:(0,s.jsx)(p.A,{className:"w-5 h-5 text-green-600"})};case"partial":return{type:"warning",title:"Partial Support",message:e.fileSystemAccess?"File System Access supported. USB archiving available but WebUSB features limited.":"Drag-and-drop upload supported. USB archiving not available.",icon:(0,s.jsx)(b.A,{className:"w-5 h-5 text-yellow-600"})};case"minimal":return{type:"info",title:"Basic File Upload",message:"Basic file upload supported. Advanced USB features not available.",icon:(0,s.jsx)(y.A,{className:"w-5 h-5 text-blue-600"})};default:return{type:"error",title:"Limited Support",message:"Your browser has limited file handling capabilities. Please upgrade to a modern browser.",icon:(0,s.jsx)(b.A,{className:"w-5 h-5 text-red-600"})}}})(i),d=i.fileSystemAccess?[]:[{title:"PowerShell USB Tool (Recommended)",description:"Complete automation with USB archiving - same workflow as WebUSB",icon:(0,s.jsx)(w.A,{className:"w-5 h-5 text-purple-600"}),available:!0,features:["Automatic USB scanning and archiving","Direct Azure Storage upload","Smart patient matching integration","Auto-transcription triggering","Complete workflow automation"],instructions:["Download the PowerShell USB Transfer Tool","Run the script on your computer","Same automated workflow as WebUSB version"]},{title:"Upgrade Browser (Recommended)",description:"Get the full WebUSB experience with zero installation",icon:(0,s.jsx)(p.A,{className:"w-5 h-5 text-blue-600"}),available:!0,features:["Zero software installation","Complete USB file management","Automatic archiving and cleanup","Corporate environment friendly"],instructions:["Install Chrome 86+ or Edge 86+","Access this page in the new browser","Full WebUSB functionality available"]}];return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"p-4 rounded-lg border-l-4 ".concat("success"===o.type?"border-green-500 bg-green-50 dark:bg-green-900/20":"warning"===o.type?"border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20":"info"===o.type?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-red-500 bg-red-50 dark:bg-red-900/20"),children:(0,s.jsxs)("div",{className:"flex items-start",children:[o.icon,(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"font-medium ".concat(l?"text-white":"text-gray-900"),children:o.title}),(0,s.jsx)("p",{className:"text-sm mt-1 ".concat("success"===o.type?"text-green-700 dark:text-green-200":"warning"===o.type?"text-yellow-700 dark:text-yellow-200":"info"===o.type?"text-blue-700 dark:text-blue-200":"text-red-700 dark:text-red-200"),children:o.message})]})]})}),a&&(0,s.jsxs)("div",{className:"p-4 rounded-lg border ".concat(l?"border-gray-700 bg-gray-800":"border-gray-200 bg-white"),children:[(0,s.jsx)("h4",{className:"font-medium mb-3 ".concat(l?"text-white":"text-gray-900"),children:"Browser Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium ".concat(l?"text-gray-300":"text-gray-600"),children:"Browser:"}),(0,s.jsxs)("span",{className:"ml-2 ".concat(l?"text-white":"text-gray-900"),children:[i.browserName," ",i.browserVersion]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium ".concat(l?"text-gray-300":"text-gray-600"),children:"Support Level:"}),(0,s.jsx)("span",{className:"ml-2 capitalize ".concat("full"===i.supportLevel?"text-green-600":"partial"===i.supportLevel?"text-yellow-600":"minimal"===i.supportLevel?"text-blue-600":"text-red-600"),children:i.supportLevel})]})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("h5",{className:"font-medium mb-2 ".concat(l?"text-gray-300":"text-gray-600"),children:"Feature Support:"}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[{name:"File System Access API",supported:i.fileSystemAccess},{name:"WebUSB API",supported:i.webUSB},{name:"Drag & Drop",supported:i.dragAndDrop},{name:"File API",supported:i.fileAPI}].map(e=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full mr-2 ".concat(e.supported?"bg-green-500":"bg-red-500")}),(0,s.jsx)("span",{className:l?"text-gray-300":"text-gray-600",children:e.name})]},e.name))})]})]}),"full"!==i.supportLevel&&(0,s.jsxs)("div",{className:"p-4 rounded-lg border ".concat(l?"border-gray-700 bg-gray-800":"border-gray-200 bg-white"),children:[(0,s.jsx)("h4",{className:"font-medium mb-3 ".concat(l?"text-white":"text-gray-900"),children:"Recommended Browsers for Full Support"}),(0,s.jsx)("div",{className:"grid gap-3",children:[{name:"Google Chrome",version:"86+",features:["Full WebUSB Support","File System Access API","USB Archiving"],downloadUrl:"https://www.google.com/chrome/",recommended:!0},{name:"Microsoft Edge",version:"86+",features:["Full WebUSB Support","File System Access API","USB Archiving"],downloadUrl:"https://www.microsoft.com/edge/",recommended:!0},{name:"Firefox",version:"Latest",features:["Drag & Drop Upload","Basic File API"],downloadUrl:"https://www.mozilla.org/firefox/",recommended:!1},{name:"Safari",version:"Latest",features:["Basic File Upload"],downloadUrl:"https://www.apple.com/safari/",recommended:!1}].filter(e=>e.recommended).map(e=>(0,s.jsx)("div",{className:"p-3 rounded border ".concat(l?"border-gray-600 bg-gray-700":"border-gray-200 bg-gray-50"),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h5",{className:"font-medium ".concat(l?"text-white":"text-gray-900"),children:[e.name," ",e.version]}),(0,s.jsx)("p",{className:"text-sm ".concat(l?"text-gray-400":"text-gray-600"),children:e.features.join(", ")})]}),(0,s.jsx)("a",{href:e.downloadUrl,target:"_blank",rel:"noopener noreferrer",className:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700",children:"Download"})]})},e.name))})]}),"full"!==i.supportLevel&&d.length>0&&(0,s.jsxs)("div",{className:"p-4 rounded-lg border ".concat(l?"border-yellow-700 bg-yellow-900/20":"border-yellow-200 bg-yellow-50"),children:[(0,s.jsx)("h4",{className:"font-medium mb-3 ".concat(l?"text-yellow-200":"text-yellow-800"),children:"Recommended Solutions for Complete Automation"}),(0,s.jsx)("p",{className:"text-sm mb-4 ".concat(l?"text-yellow-300":"text-yellow-700"),children:"For the complete automated workflow with USB archiving, please use one of these options:"}),(0,s.jsx)("div",{className:"space-y-3",children:d.map((e,t)=>(0,s.jsx)("div",{className:"p-4 rounded border ".concat(l?"border-blue-600 bg-blue-900/20":"border-blue-200 bg-blue-50"),children:(0,s.jsxs)("div",{className:"flex items-start",children:[e.icon,(0,s.jsxs)("div",{className:"ml-3 flex-1",children:[(0,s.jsx)("h5",{className:"font-medium ".concat(l?"text-blue-200":"text-blue-800"),children:e.title}),(0,s.jsx)("p",{className:"text-sm mt-1 ".concat(l?"text-blue-300":"text-blue-700"),children:e.description}),(0,s.jsxs)("div",{className:"mt-3",children:[(0,s.jsx)("h6",{className:"text-xs font-medium mb-1 ".concat(l?"text-blue-300":"text-blue-700"),children:"Features:"}),(0,s.jsx)("ul",{className:"text-xs space-y-1 ".concat(l?"text-blue-400":"text-blue-600"),children:e.features.map((e,t)=>(0,s.jsxs)("li",{children:["• ",e]},t))})]}),(0,s.jsxs)("div",{className:"mt-3",children:[(0,s.jsx)("h6",{className:"text-xs font-medium mb-1 ".concat(l?"text-blue-300":"text-blue-700"),children:"Instructions:"}),(0,s.jsx)("ul",{className:"text-xs space-y-1 ".concat(l?"text-blue-400":"text-blue-600"),children:e.instructions.map((e,t)=>(0,s.jsxs)("li",{children:["• ",e]},t))})]})]})]})},t))}),(0,s.jsx)("div",{className:"mt-4 p-3 rounded border-l-4 border-red-500 ".concat(l?"bg-red-900/20":"bg-red-50"),children:(0,s.jsxs)("p",{className:"text-sm ".concat(l?"text-red-200":"text-red-800"),children:[(0,s.jsx)("strong",{children:"Important:"})," Manual file upload methods are not recommended as they bypass the automatic USB archiving workflow, which could lead to duplicate files and manual cleanup requirements."]})})]})]})}var f=a(4077),v=a(5721);function N(e){let{}=e,[t,a]=(0,r.useState)(!1),[p,b]=(0,r.useState)(null),[y,w]=(0,r.useState)(!1),[N,S]=(0,r.useState)([]),[A,U]=(0,r.useState)(!1),[k,B]=(0,r.useState)(!1),[C,F]=(0,r.useState)(!1),[D,E]=(0,r.useState)({enableSmartSort:!0,enableAutoTranscription:!0,enableUSBArchiving:!0,archiveRetentionDays:30});(0,r.useEffect)(()=>{let e=window.matchMedia("(prefers-color-scheme: dark)");a(e.matches);let t=e=>a(e.matches);return e.addEventListener("change",t),()=>e.removeEventListener("change",t)},[]);let P=[{icon:(0,s.jsx)(l.A,{className:"w-6 h-6 text-blue-600"}),title:"Zero Installation",description:"Pure browser-based solution. No PowerShell scripts or executables required.",available:(null==p?void 0:p.fileSystemAccess)||!1},{icon:(0,s.jsx)(i.A,{className:"w-6 h-6 text-green-600"}),title:"Full USB Control",description:"Direct USB drive access with automatic file archiving and cleanup.",available:(null==p?void 0:p.fileSystemAccess)||!1},{icon:(0,s.jsx)(n.A,{className:"w-6 h-6 text-purple-600"}),title:"Corporate Friendly",description:"No admin rights needed. Works within standard browser security policies.",available:!0},{icon:(0,s.jsx)(c.A,{className:"w-6 h-6 text-orange-600"}),title:"Complete Automation",description:"Upload, transcribe, summarize, and archive - all in one click.",available:(null==p?void 0:p.isSupported)||!1}],I=[{step:1,title:"Connect USB Drive",description:"Click to grant browser access to your USB recorder",icon:(0,s.jsx)(i.A,{className:"w-5 h-5"})},{step:2,title:"Auto-Scan Files",description:"Automatically detects audio files in recorder folders",icon:(0,s.jsx)(o.A,{className:"w-5 h-5"})},{step:3,title:"Upload & Process",description:"Bulk upload to Azure with smart patient matching",icon:(0,s.jsx)(d.A,{className:"w-5 h-5"})},{step:4,title:"Archive & Cleanup",description:"Automatically archive files on USB and cleanup old recordings",icon:(0,s.jsx)(o.A,{className:"w-5 h-5"})}];return(0,s.jsxs)("div",{className:"min-h-screen ".concat(t?"bg-gray-900 text-white":"bg-gray-50 text-gray-900"),children:[(0,s.jsx)(h.z,{title:"Dentalapp",isHomePage:!1}),(0,s.jsxs)("main",{className:"max-w-6xl mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,s.jsx)(i.A,{className:"w-12 h-12 text-blue-600 mr-3"}),(0,s.jsx)("h1",{className:"text-4xl font-bold ".concat(t?"text-white":"text-gray-900"),children:"WebUSB File Transfer"})]}),(0,s.jsx)("p",{className:"text-xl ".concat(t?"text-gray-300":"text-gray-600"),children:"Direct browser-based USB recorder file transfer with automatic archiving"}),(0,s.jsxs)("div",{className:"flex items-center justify-center mt-4 space-x-3",children:[(0,s.jsxs)("button",{onClick:()=>w(!y),className:"px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:[y?"Hide":"Show"," Browser Info"]}),(0,s.jsxs)("button",{onClick:()=>U(!A),className:"px-4 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center space-x-2",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Settings"})]}),(0,s.jsxs)("button",{onClick:()=>B(!k),className:"px-4 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center space-x-2",children:[(0,s.jsx)(x.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Test Integration"})]}),(0,s.jsxs)("button",{onClick:()=>F(!C),className:"px-4 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2",children:[(0,s.jsx)(g.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Mock Test"})]})]})]}),y&&(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)(j,{onCapabilitiesDetected:e=>{b(e),"full"!==e.supportLevel&&w(!0)},showDetails:!0,isDarkMode:t})}),k&&(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)(f.A,{isDarkMode:t})}),C&&(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)(v.A,{isDarkMode:t})}),A&&(0,s.jsxs)("div",{className:"mb-8 p-6 rounded-lg border ".concat(t?"border-gray-700 bg-gray-800":"border-gray-200 bg-white"),children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4 ".concat(t?"text-white":"text-gray-900"),children:"Transfer Settings"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:D.enableSmartSort,onChange:e=>E(t=>({...t,enableSmartSort:e.target.checked})),className:"w-4 h-4 text-blue-600 rounded"}),(0,s.jsx)("span",{className:t?"text-gray-300":"text-gray-700",children:"Enable Smart Patient Matching"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:D.enableAutoTranscription,onChange:e=>E(t=>({...t,enableAutoTranscription:e.target.checked})),className:"w-4 h-4 text-blue-600 rounded"}),(0,s.jsx)("span",{className:t?"text-gray-300":"text-gray-700",children:"Enable Auto-Transcription"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:D.enableUSBArchiving,onChange:e=>E(t=>({...t,enableUSBArchiving:e.target.checked})),className:"w-4 h-4 text-blue-600 rounded"}),(0,s.jsx)("span",{className:t?"text-gray-300":"text-gray-700",children:"Enable USB Archiving"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("label",{className:"text-sm ".concat(t?"text-gray-300":"text-gray-700"),children:"Archive Retention (days):"}),(0,s.jsx)("input",{type:"number",min:"1",max:"365",value:D.archiveRetentionDays,onChange:e=>E(t=>({...t,archiveRetentionDays:parseInt(e.target.value)||30})),className:"w-20 px-2 py-1 text-sm border rounded ".concat(t?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900")})]})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:P.map((e,a)=>(0,s.jsxs)("div",{className:"p-6 rounded-lg border ".concat(e.available?t?"border-green-700 bg-green-900/20":"border-green-200 bg-green-50":t?"border-gray-700 bg-gray-800":"border-gray-200 bg-gray-100"),children:[(0,s.jsxs)("div",{className:"flex items-center mb-3",children:[e.icon,(0,s.jsx)("h3",{className:"ml-3 font-semibold ".concat(e.available?t?"text-green-200":"text-green-800":t?"text-gray-400":"text-gray-600"),children:e.title})]}),(0,s.jsx)("p",{className:"text-sm ".concat(e.available?t?"text-green-300":"text-green-700":"text-gray-500"),children:e.description}),!e.available&&(0,s.jsx)("p",{className:"text-xs mt-2 ".concat(t?"text-red-400":"text-red-600"),children:"Requires compatible browser"})]},a))}),(0,s.jsxs)("div",{className:"mb-8 p-6 rounded-lg border ".concat(t?"border-gray-700 bg-gray-800":"border-gray-200 bg-white"),children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-6 text-center ".concat(t?"text-white":"text-gray-900"),children:"How It Works"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:I.map((e,a)=>(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 ".concat((null==p?void 0:p.isSupported)?"bg-blue-600 text-white":t?"bg-gray-700 text-gray-400":"bg-gray-200 text-gray-500"),children:e.icon}),(0,s.jsx)("h4",{className:"font-medium mb-2 ".concat(t?"text-white":"text-gray-900"),children:e.title}),(0,s.jsx)("p",{className:"text-sm ".concat(t?"text-gray-400":"text-gray-600"),children:e.description})]},a))})]}),(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)(u.A,{onFilesUploaded:e=>{S(t=>[...t,...e])},onError:e=>{console.error("WebUSB Transfer Error:",e)},isDarkMode:t})}),N.length>0&&(0,s.jsxs)("div",{className:"p-6 rounded-lg border ".concat(t?"border-gray-700 bg-gray-800":"border-gray-200 bg-white"),children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold mb-4 ".concat(t?"text-white":"text-gray-900"),children:["Recent Uploads (",N.length,")"]}),(0,s.jsx)("div",{className:"space-y-2",children:N.slice(-5).map((e,a)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded border ".concat(t?"border-gray-600 bg-gray-700":"border-gray-200 bg-gray-50"),children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium ".concat(t?"text-white":"text-gray-900"),children:e.name}),(0,s.jsxs)("p",{className:"text-sm ".concat(t?"text-gray-400":"text-gray-600"),children:[e.folder," • ",(e.size/1024/1024).toFixed(1)," MB"]})]}),(0,s.jsx)(o.A,{className:"w-5 h-5 text-green-600"})]},a))}),N.length>5&&(0,s.jsxs)("p",{className:"text-sm mt-3 text-center ".concat(t?"text-gray-400":"text-gray-600"),children:["Showing last 5 uploads. Total: ",N.length," files uploaded."]})]}),(0,s.jsx)("div",{className:"mt-8 text-center text-sm ".concat(t?"text-gray-400":"text-gray-600"),children:(0,s.jsxs)("p",{children:["WebUSB File Transfer requires Chrome 86+ or Edge 86+ for full functionality.",(0,s.jsx)("br",{}),"For other browsers, fallback upload methods are available."]})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8096,6496,3235,7358],()=>t(5860)),_N_E=e.O()}]);