========================================
    DENTAL APP INSTALLATION GUIDE
========================================

QUICK START (5 minutes):
1. Copy this folder to C:\DentalApp\
2. Right-click INSTALL.bat → "Run as administrator"
3. Wait for installation to complete
4. Open browser to http://localhost:3000

========================================
    DETAILED INSTALLATION STEPS
========================================

STEP 1: PREPARE THE OFFICE PC
- Ensure you have administrator access
- Close any applications using port 3000
- Connect to office network (Dentrix server access)

STEP 2: COPY FILES
- Copy entire "DentalApp-Deploy" folder to C:\
- Rename to "DentalApp" (final path: C:\DentalApp\)

STEP 3: RUN INSTALLATION
- Right-click "INSTALL.bat"
- Select "Run as administrator"
- Follow the installation prompts
- Installation takes 5-10 minutes

STEP 4: VERIFY INSTALLATION
- Open web browser
- Go to: http://localhost:3000
- You should see the Dental Schedule Application
- Check that all tabs work (Schedule, Voice Recordings, etc.)

========================================
    WHAT GETS INSTALLED
========================================

SOFTWARE:
- Node.js (JavaScript runtime)
- PM2 (Process manager for Windows service)
- Dental App (Your application)

FOLDERS CREATED:
C:\DentalApp\
├── app\                    (Application files)
├── scripts\                (Management scripts)
├── config\                 (Configuration files)
├── data\                   (Database and recordings)
├── logs\                   (Application logs)
└── backups\                (Automatic backups)

WINDOWS SERVICE:
- Service Name: "DentalApp"
- Auto-starts with Windows
- Auto-restarts if it crashes
- Runs in background

DESKTOP SHORTCUT:
- "Dental App" shortcut created
- Opens http://localhost:3000

========================================
    DAILY USAGE
========================================

NORMAL OPERATION:
- App runs automatically
- Access at: http://localhost:3000
- No daily maintenance required

MANAGEMENT SCRIPTS:
- start-service.bat         Start the service
- stop-service.bat          Stop the service
- restart-service.bat       Restart the service
- status.bat               Check if running
- logs.bat                 View application logs
- backup.bat               Backup all data
- update-remote.bat        Update from GitHub
- update-usb.bat           Update from USB stick

========================================
    UPDATING THE APPLICATION
========================================

METHOD 1: REMOTE UPDATE (Preferred)
1. Double-click: C:\DentalApp\scripts\update-remote.bat
2. Wait for update to complete (30 seconds)
3. App automatically restarts with new version

METHOD 2: USB UPDATE (Backup method)
1. Copy new app files to USB:\DentalApp-Update\
2. Insert USB into office PC
3. Double-click: C:\DentalApp\scripts\update-usb.bat
4. Follow prompts

========================================
    NETWORK CONFIGURATION
========================================

VOICE RECORDINGS:
- Network Share: \\***********\share\RECORDINGS
- Local Backup: C:\DentalApp\data\voice-recordings
- USB Path: D:\REC_FILE\FOLDER01

ACCESS POINTS:
- Your Desk PC: http://localhost:3000
- Other Office PCs: http://[your-pc-ip]:3000
- External Access: Disabled for security

REQUIRED NETWORK ACCESS:
- Dentrix Server (***********)
- Internet (for AI features and updates)
- GitHub (for remote updates)

========================================
    TROUBLESHOOTING
========================================

APP WON'T START:
1. Run: C:\DentalApp\scripts\status.bat
2. Check: C:\DentalApp\scripts\logs.bat
3. Try: C:\DentalApp\scripts\restart-service.bat

PORT 3000 IN USE:
1. Stop other applications using port 3000
2. Or change PORT in .env.production file
3. Restart service

VOICE RECORDINGS NOT WORKING:
1. Check USB drive is connected (D:\REC_FILE\FOLDER01)
2. Verify network share access (\\***********\share\RECORDINGS)
3. Check logs for specific errors

UPDATES FAILING:
1. Check internet connection
2. Try USB update method instead
3. Check logs for specific errors

CAN'T ACCESS FROM OTHER PCs:
1. Check Windows Firewall settings
2. Verify office PC IP address
3. Ensure port 3000 is not blocked

========================================
    BACKUP AND RECOVERY
========================================

AUTOMATIC BACKUPS:
- Daily backups to C:\DentalApp\backups\
- Keeps last 10 backups automatically
- Includes app, data, and configuration

MANUAL BACKUP:
- Run: C:\DentalApp\scripts\backup.bat
- Optionally copy to network share

RECOVERY:
1. Stop service: stop-service.bat
2. Restore files from backup folder
3. Start service: start-service.bat

========================================
    SECURITY NOTES
========================================

ACCESS CONTROL:
- App runs under your Windows account
- Uses Windows domain authentication
- No additional passwords required

NETWORK SECURITY:
- Only accessible from office network
- No external internet access
- Firewall rules recommended

DATA PROTECTION:
- Voice recordings stored on Dentrix server
- Local backups encrypted at rest
- Regular backup verification

========================================
    SUPPORT CONTACTS
========================================

SELF-SERVICE:
1. Check status.bat for service status
2. Check logs.bat for error details
3. Try restart-service.bat for quick fix

REMOTE SUPPORT:
- TeamViewer or Remote Desktop access
- GitHub repository for updates
- Email support for configuration

EMERGENCY:
- USB update method available
- Backup restoration procedures
- Service manual restart options

========================================
    INSTALLATION COMPLETE!
========================================

Your Dental Schedule Application is now installed and running.

Access the app: http://localhost:3000

For questions or support, check the logs first:
C:\DentalApp\scripts\logs.bat

Thank you for using the Dental Schedule Application!
