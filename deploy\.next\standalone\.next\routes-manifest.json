{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/api/voice/:path*", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "X-Content-Type-Options", "value": "nosniff"}], "regex": "^/api/voice(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/api/:path*", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}], "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/(.*)", "headers": [{"key": "X-DNS-Prefetch-Control", "value": "on"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubDomains; preload"}], "regex": "^(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/api/appointments/[id]", "regex": "^/api/appointments/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/appointments/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/patients/[id]", "regex": "^/api/patients/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/patients/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/patients/[id]/clinical-notes", "regex": "^/api/patients/([^/]+?)/clinical\\-notes(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/patients/(?<nxtPid>[^/]+?)/clinical\\-notes(?:/)?$"}, {"page": "/api/patients/[id]/visit-count", "regex": "^/api/patients/([^/]+?)/visit\\-count(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/patients/(?<nxtPid>[^/]+?)/visit\\-count(?:/)?$"}, {"page": "/api/patients/[id]/visits", "regex": "^/api/patients/([^/]+?)/visits(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/patients/(?<nxtPid>[^/]+?)/visits(?:/)?$"}, {"page": "/api/voice/recordings/[id]", "regex": "^/api/voice/recordings/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/voice/recordings/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/appointment/[appointmentId]", "regex": "^/appointment/([^/]+?)(?:/)?$", "routeKeys": {"nxtPappointmentId": "nxtPappointmentId"}, "namedRegex": "^/appointment/(?<nxtPappointmentId>[^/]+?)(?:/)?$"}, {"page": "/patient/[id]", "regex": "^/patient/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/patient/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/ai-assistant", "regex": "^/ai\\-assistant(?:/)?$", "routeKeys": {}, "namedRegex": "^/ai\\-assistant(?:/)?$"}, {"page": "/cache-test", "regex": "^/cache\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/cache\\-test(?:/)?$"}, {"page": "/debug-transcription", "regex": "^/debug\\-transcription(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-transcription(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/operatories", "regex": "^/operatories(?:/)?$", "routeKeys": {}, "namedRegex": "^/operatories(?:/)?$"}, {"page": "/patient-search", "regex": "^/patient\\-search(?:/)?$", "routeKeys": {}, "namedRegex": "^/patient\\-search(?:/)?$"}, {"page": "/schedule", "regex": "^/schedule(?:/)?$", "routeKeys": {}, "namedRegex": "^/schedule(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/test-webusb", "regex": "^/test\\-webusb(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-webusb(?:/)?$"}, {"page": "/transcription-control", "regex": "^/transcription\\-control(?:/)?$", "routeKeys": {}, "namedRegex": "^/transcription\\-control(?:/)?$"}, {"page": "/voice-workflow", "regex": "^/voice\\-workflow(?:/)?$", "routeKeys": {}, "namedRegex": "^/voice\\-workflow(?:/)?$"}, {"page": "/webusb-transfer", "regex": "^/webusb\\-transfer(?:/)?$", "routeKeys": {}, "namedRegex": "^/webusb\\-transfer(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}