/**
 * Environment Variable Validation Utility
 * 
 * This utility validates required and optional environment variables
 * and provides helpful error messages for missing configuration.
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  missingRequired: string[];
  missingOptional: string[];
}

export interface EnvironmentConfig {
  // Required for core functionality
  openai: {
    apiKey: string | null;
    organization?: string;
  };
  
  // Required for production storage
  azure: {
    connectionString: string | null;
    containerName: string;
  };
  
  // Required for practice integration
  sikka: {
    apiKey: string | null;
    practiceId: string | null;
    baseUrl: string;
    timeout: number;
  };
  
  // Optional network sync (legacy)
  networkSync: {
    sharePath?: string;
    username?: string;
    password?: string;
    domain?: string;
  };
  
  // Application settings
  app: {
    nodeEnv: string;
    port: number;
    host: string;
    debugMode: boolean;
    verboseLogging: boolean;
  };
  
  // Optional monitoring
  monitoring: {
    appInsightsKey?: string;
    sentryDsn?: string;
  };
}

/**
 * Parse and validate environment variables
 */
export function validateEnvironment(env: NodeJS.ProcessEnv = process.env): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const missingRequired: string[] = [];
  const missingOptional: string[] = [];

  // Helper function to check required variables
  const checkRequired = (key: string, description: string): string | null => {
    const value = env[key];
    if (!value || value.trim() === '' || value === 'your_' + key.toLowerCase() + '_here') {
      missingRequired.push(key);
      errors.push(`${key} is required for ${description}`);
      return null;
    }
    return value.trim();
  };

  // Helper function to check optional variables
  const checkOptional = (key: string, description: string): string | undefined => {
    const value = env[key];
    if (!value || value.trim() === '' || value === 'your_' + key.toLowerCase() + '_here') {
      missingOptional.push(key);
      warnings.push(`${key} is recommended for ${description}`);
      return undefined;
    }
    return value.trim();
  };

  // Validate OpenAI configuration
  const openaiApiKey = checkRequired('OPENAI_API_KEY', 'AI transcription and clinical note generation');
  const openaiOrg = checkOptional('OPENAI_ORGANIZATION', 'OpenAI billing organization');

  // Validate Azure Storage configuration
  const azureConnectionString = env.NODE_ENV === 'production' 
    ? checkRequired('AZURE_STORAGE_CONNECTION_STRING', 'production voice recording storage')
    : checkOptional('AZURE_STORAGE_CONNECTION_STRING', 'voice recording storage');
  
  const azureContainerName = env.AZURE_STORAGE_CONTAINER_NAME || 'dentalrecordings';

  // Validate Azure connection string format
  if (azureConnectionString && !azureConnectionString.includes('AccountName=') && !azureConnectionString.includes('AccountKey=')) {
    errors.push('AZURE_STORAGE_CONNECTION_STRING appears to be invalid (missing AccountName or AccountKey)');
  }

  // Validate Sikka API configuration
  const sikkaApiKey = checkRequired('SIKKA_API_KEY', 'dental practice management integration');
  const sikkaPracticeId = checkRequired('SIKKA_PRACTICE_ID', 'practice-specific data access');
  const sikkaBaseUrl = env.SIKKA_API_BASE_URL || 'https://api.sikka.com/v1';
  const sikkaTimeout = parseInt(env.SIKKA_API_TIMEOUT || '30000');

  // Validate Sikka timeout
  if (sikkaTimeout < 5000 || sikkaTimeout > 120000) {
    warnings.push('SIKKA_API_TIMEOUT should be between 5000 and 120000 milliseconds');
  }

  // Network sync configuration (optional, legacy)
  const networkSharePath = checkOptional('NETWORK_SHARE_PATH', 'network share synchronization');
  const networkUsername = checkOptional('NETWORK_SHARE_USERNAME', 'network share authentication');
  const networkPassword = checkOptional('NETWORK_SHARE_PASSWORD', 'network share authentication');
  const networkDomain = checkOptional('NETWORK_SHARE_DOMAIN', 'network share domain authentication');

  // Validate network share configuration consistency
  if (networkSharePath && (!networkUsername || !networkPassword)) {
    warnings.push('Network share path specified but missing username/password');
  }

  // Application configuration
  const nodeEnv = env.NODE_ENV || 'development';
  const port = parseInt(env.PORT || '3000');
  const host = env.HOST || '0.0.0.0';
  const debugMode = env.DEBUG_MODE === 'true';
  const verboseLogging = env.VERBOSE_LOGGING === 'true';

  // Validate port number
  if (port < 1 || port > 65535) {
    errors.push('PORT must be between 1 and 65535');
  }

  // Monitoring configuration (optional)
  const appInsightsKey = checkOptional('APPINSIGHTS_INSTRUMENTATIONKEY', 'application monitoring');
  const sentryDsn = checkOptional('SENTRY_DSN', 'error tracking');

  // Environment-specific validations
  if (nodeEnv === 'production') {
    if (!azureConnectionString) {
      errors.push('Azure Storage is required for production deployments');
    }
    
    if (!openaiApiKey) {
      errors.push('OpenAI API key is required for production functionality');
    }
    
    if (!sikkaApiKey || !sikkaPracticeId) {
      errors.push('Sikka API credentials are required for production practice integration');
    }
  }

  // Development-specific warnings
  if (nodeEnv === 'development') {
    if (!openaiApiKey) {
      warnings.push('OpenAI API key is recommended for testing AI features');
    }
    
    if (!azureConnectionString) {
      warnings.push('Azure Storage is recommended for testing cloud storage features');
    }
  }

  const isValid = errors.length === 0;

  return {
    isValid,
    errors,
    warnings,
    missingRequired,
    missingOptional
  };
}

/**
 * Get parsed environment configuration
 */
export function getEnvironmentConfig(env: NodeJS.ProcessEnv = process.env): EnvironmentConfig {
  return {
    openai: {
      apiKey: env.OPENAI_API_KEY || null,
      organization: env.OPENAI_ORGANIZATION,
    },
    azure: {
      connectionString: env.AZURE_STORAGE_CONNECTION_STRING || null,
      containerName: env.AZURE_STORAGE_CONTAINER_NAME || 'dentalrecordings',
    },
    sikka: {
      apiKey: env.SIKKA_API_KEY || null,
      practiceId: env.SIKKA_PRACTICE_ID || null,
      baseUrl: env.SIKKA_API_BASE_URL || 'https://api.sikka.com/v1',
      timeout: parseInt(env.SIKKA_API_TIMEOUT || '30000'),
    },
    networkSync: {
      sharePath: env.NETWORK_SHARE_PATH,
      username: env.NETWORK_SHARE_USERNAME,
      password: env.NETWORK_SHARE_PASSWORD,
      domain: env.NETWORK_SHARE_DOMAIN,
    },
    app: {
      nodeEnv: env.NODE_ENV || 'development',
      port: parseInt(env.PORT || '3000'),
      host: env.HOST || '0.0.0.0',
      debugMode: env.DEBUG_MODE === 'true',
      verboseLogging: env.VERBOSE_LOGGING === 'true',
    },
    monitoring: {
      appInsightsKey: env.APPINSIGHTS_INSTRUMENTATIONKEY,
      sentryDsn: env.SENTRY_DSN,
    },
  };
}

/**
 * Log validation results
 */
export function logValidationResults(result: ValidationResult): void {
  if (result.isValid) {
    console.log('✅ Environment validation passed');
  } else {
    console.error('❌ Environment validation failed');
    result.errors.forEach(error => console.error(`  ERROR: ${error}`));
  }

  if (result.warnings.length > 0) {
    console.warn('⚠️  Environment warnings:');
    result.warnings.forEach(warning => console.warn(`  WARNING: ${warning}`));
  }

  if (result.missingRequired.length > 0) {
    console.error('📋 Missing required environment variables:');
    result.missingRequired.forEach(key => console.error(`  - ${key}`));
  }

  if (result.missingOptional.length > 0) {
    console.info('💡 Missing optional environment variables:');
    result.missingOptional.forEach(key => console.info(`  - ${key}`));
  }
}

/**
 * Validate environment and exit if critical errors
 */
export function validateEnvironmentOrExit(): EnvironmentConfig {
  const result = validateEnvironment();
  logValidationResults(result);

  if (!result.isValid) {
    console.error('\n🔧 Please check your environment configuration:');
    console.error('   1. Copy .env.example to .env.local');
    console.error('   2. Update the placeholder values with actual credentials');
    console.error('   3. Restart the application\n');
    
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    } else {
      console.warn('⚠️  Continuing in development mode with incomplete configuration...\n');
    }
  }

  return getEnvironmentConfig();
}