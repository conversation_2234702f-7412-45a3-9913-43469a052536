// Note: Azure Speech SDK will be imported dynamically for compatibility
// import { SpeechConfig, AudioConfig, SpeechRecog<PERSON><PERSON>, ResultReason } from 'microsoft-cognitiveservices-speech-sdk';

export class AzureSpeechService {
  private static speechKey = process.env.AZURE_SPEECH_KEY;
  private static speechRegion = process.env.AZURE_SPEECH_REGION || 'centralus';

  /**
   * Check if Azure Speech Service is configured
   */
  static isConfigured(): boolean {
    // For now, use OpenAI as fallback since Azure Speech SDK may have compatibility issues
    return !!process.env.OPENAI_API_KEY;
  }

  /**
   * Get supported audio formats
   */
  static getSupportedFormats(): string[] {
    return ['wav', 'mp3', 'm4a', 'ogg', 'flac', 'webm'];
  }

  /**
   * Validate audio format
   */
  static async validateAudioFormat(audioBuffer: Buffer, filename: string): Promise<boolean> {
    const extension = filename.toLowerCase().split('.').pop();
    
    if (!extension || !this.getSupportedFormats().includes(extension)) {
      return false;
    }

    // Basic file size validation
    if (audioBuffer.length < 1024) {
      return false;
    }

    // Check for common audio file headers
    const header = audioBuffer.slice(0, 12);
    const headerString = header.toString('hex');
    
    // WAV header: 52494646 (RIFF)
    if (headerString.startsWith('52494646')) {
      return true;
    }
    
    // MP3 header: ID3 tag or frame sync
    if (headerString.startsWith('494433') || headerString.startsWith('fff')) {
      return true;
    }
    
    // OGG header: 4f676753 (OggS)
    if (headerString.startsWith('4f676753')) {
      return true;
    }
    
    // M4A/MP4 header: ftyp
    if (headerString.includes('66747970')) {
      return true;
    }
    
    // For other formats, assume valid if extension matches
    return true;
  }

  /**
   * Transcribe audio using OpenAI Whisper (fallback implementation)
   */
  static async transcribeAudio(
    audioBuffer: Buffer,
    filename: string,
    options: {
      language?: string;
      enableDiarization?: boolean;
      enableProfanityFilter?: boolean;
    } = {}
  ): Promise<{
    success: boolean;
    transcription?: string;
    error?: string;
    confidence?: number;
    duration?: number;
  }> {
    if (!this.isConfigured()) {
      return {
        success: false,
        error: 'Transcription service not configured. Please set OPENAI_API_KEY environment variable.'
      };
    }

    try {
      console.log(`🎤 Starting OpenAI Whisper transcription for: ${filename}`);
      
      // Validate audio format
      const isValidFormat = await this.validateAudioFormat(audioBuffer, filename);
      if (!isValidFormat) {
        return {
          success: false,
          error: `Invalid audio format for file: ${filename}. Supported formats: ${this.getSupportedFormats().join(', ')}`
        };
      }

      // Use OpenAI Whisper API
      const { OpenAI } = await import('openai');
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
      
      const startTime = Date.now();
      
      // Create a File object from the buffer
      const file = new File([audioBuffer], filename, {
        type: this.getContentTypeFromFileName(filename)
      });

      console.log(`🎵 Sending ${filename} to OpenAI Whisper (${audioBuffer.length} bytes)`);

      const transcription = await openai.audio.transcriptions.create({
        file: file,
        model: 'whisper-1',
        response_format: 'json',
        temperature: 0.2,
        language: options.language?.split('-')[0] || 'en' // Convert 'en-US' to 'en'
      });

      const duration = Date.now() - startTime;
      const transcriptionText = transcription.text;

      if (!transcriptionText || transcriptionText.trim().length === 0) {
        return {
          success: false,
          error: 'No speech recognized in audio file'
        };
      }

      console.log(`✅ OpenAI Whisper transcription completed for ${filename}: ${transcriptionText.length} characters`);

      return {
        success: true,
        transcription: transcriptionText.trim(),
        confidence: 0.85, // OpenAI doesn't provide confidence scores, use default
        duration: duration
      };

    } catch (error: any) {
      console.error('❌ OpenAI Whisper transcription error:', error);
      
      // Handle specific OpenAI errors
      if (error.status === 413) {
        return {
          success: false,
          error: 'Audio file too large for transcription service. Maximum size is 25MB.'
        };
      }
      
      if (error.status === 429) {
        return {
          success: false,
          error: 'Transcription service rate limit exceeded. Please try again in a few minutes.'
        };
      }
      
      if (error.message?.includes('rate limit')) {
        return {
          success: false,
          error: 'Transcription service rate limit exceeded. Please try again in a few minutes.'
        };
      }

      return {
        success: false,
        error: `Transcription failed: ${error.message || 'Unknown error'}`
      };
    }
  }

  /**
   * Get content type from filename
   */
  private static getContentTypeFromFileName(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop();
    const contentTypes: Record<string, string> = {
      'mp3': 'audio/mpeg',
      'wav': 'audio/wav',
      'm4a': 'audio/mp4',
      'webm': 'audio/webm',
      'ogg': 'audio/ogg',
      'aac': 'audio/aac',
      'flac': 'audio/flac'
    };
    return contentTypes[ext || ''] || 'audio/mpeg';
  }

  /**
   * Transcribe audio with automatic format detection and conversion
   */
  static async transcribeAudioWithFallback(
    audioBuffer: Buffer,
    filename: string,
    options: {
      language?: string;
      enableDiarization?: boolean;
      enableProfanityFilter?: boolean;
      maxRetries?: number;
    } = {}
  ): Promise<{
    success: boolean;
    transcription?: string;
    error?: string;
    confidence?: number;
    duration?: number;
    method?: string;
  }> {
    const maxRetries = options.maxRetries || 3;
    let lastError = '';

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      console.log(`🔄 Transcription attempt ${attempt}/${maxRetries} for: ${filename}`);
      
      try {
        const result = await this.transcribeAudio(audioBuffer, filename, options);
        
        if (result.success) {
          return {
            ...result,
            method: `azure-speech-attempt-${attempt}`
          };
        }
        
        lastError = result.error || 'Unknown error';
        
        // If it's a format error, don't retry
        if (lastError.includes('Invalid audio format')) {
          break;
        }
        
        // Wait before retry
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
        
      } catch (error) {
        lastError = error instanceof Error ? error.message : 'Unknown error';
        
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    return {
      success: false,
      error: `Failed after ${maxRetries} attempts. Last error: ${lastError}`,
      method: 'azure-speech-failed'
    };
  }

  /**
   * Get service health status
   */
  static async getServiceHealth(): Promise<{
    healthy: boolean;
    region: string;
    error?: string;
  }> {
    if (!this.isConfigured()) {
      return {
        healthy: false,
        region: 'openai-fallback',
        error: 'OpenAI API key not configured'
      };
    }

    try {
      // Test OpenAI API connectivity
      const { OpenAI } = await import('openai');
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
      
      // Make a simple API call to check health
      await openai.models.list();
      
      return {
        healthy: true,
        region: 'openai-fallback'
      };
      
    } catch (error) {
      return {
        healthy: false,
        region: 'openai-fallback',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}