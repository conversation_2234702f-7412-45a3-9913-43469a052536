import requests
import json
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 30

def check_practice_id(office_id, secret_key):
    """Check if the practice ID is valid."""
    print(f"Checking practice ID: {office_id}")
    print(f"Secret key: {secret_key}")
    
    # Try to get a request key
    try:
        auth_resp = requests.post(
            f"{API_BASE_V4}/request_key",
            headers={"Content-Type": "application/json"},
            json={
                "grant_type": "request_key",
                "office_id": office_id,
                "secret_key": secret_key,
                "app_id": APP_ID,
                "app_key": APP_KEY
            },
            timeout=API_TIMEOUT
        )
        
        if auth_resp.status_code == 200:
            data = auth_resp.json()
            request_key = data.get("request_key")
            print(f"Request key obtained: {request_key}")
            print("Practice ID is valid!")
            
            # Try to get practice information
            headers = {"Request-Key": request_key}
            
            try:
                resp = requests.get(
                    f"{API_BASE_V4}/practice",
                    headers=headers,
                    timeout=API_TIMEOUT
                )
                
                if resp.status_code == 200:
                    data = resp.json()
                    print("\nPractice information:")
                    print(json.dumps(data, indent=2))
                else:
                    print(f"Error getting practice information: {resp.status_code}")
                    print(resp.text)
            except Exception as e:
                print(f"Error getting practice information: {e}")
            
            return True
        else:
            print(f"Error: {auth_resp.status_code}")
            print(auth_resp.text)
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def list_practices():
    """Try to list available practices."""
    print("Trying to list available practices...")
    
    try:
        resp = requests.get(
            f"{API_BASE_V4}/practices",
            headers={
                "Content-Type": "application/json",
                "app_id": APP_ID,
                "app_key": APP_KEY
            },
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("\nAvailable practices:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error listing practices: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error listing practices: {e}")

def try_common_practice_ids():
    """Try some common practice ID formats."""
    print("\nTrying some common practice ID formats...")
    
    # Try different formats
    practice_ids = [
        "D43989",  # Current ID
        "43989",   # Without D prefix
        "D-43989", # With hyphen
        "43-989",  # Different format
        "D43-989", # Different format
        "D43989-1" # With suffix
    ]
    
    secret_keys = [
        "35442814D4396E20C222",  # Current key
        "35442814D4396E20C222",  # Same key for all
    ]
    
    for office_id in practice_ids:
        for secret_key in secret_keys:
            print(f"\nTrying {office_id} with secret key {secret_key}")
            if check_practice_id(office_id, secret_key):
                return True
    
    return False

def main():
    # Check current practice ID
    office_id = "D43989"
    secret_key = "35442814D4396E20C222"
    
    print("Checking current practice ID...")
    if check_practice_id(office_id, secret_key):
        print("Current practice ID is valid!")
    else:
        print("Current practice ID is not valid.")
        
        # Try to list practices
        list_practices()
        
        # Try common practice ID formats
        if try_common_practice_ids():
            print("\nFound a valid practice ID!")
        else:
            print("\nCould not find a valid practice ID.")
            print("Please contact Sikka support to get the correct practice ID and secret key.")

if __name__ == "__main__":
    main()
