"use strict";exports.id=6463,exports.ids=[6463],exports.modules={36463:(e,r,t)=>{t.d(r,{VT:()=>i,fY:()=>a,sn:()=>s,vF:()=>c,vV:()=>d});class o{constructor(e={}){this.baseContext=e,this.correlationId=this.generateCorrelationId()}child(e){let r=new o({...this.baseContext,...e});return r.correlationId=this.correlationId,r}debug(e,r={}){this.log("debug",e,r)}info(e,r={}){this.log("info",e,r)}warn(e,r={}){this.log("warn",e,r)}error(e,r={},t){this.log("error",e,r,t)}apiRequest(e,r={}){this.info(`${e} API request started`,{...r,apiName:e,operation:"api_request"})}apiResponse(e,r,t={}){this.info(`${e} API request completed`,{...t,apiName:e,duration:r,operation:"api_response"})}apiError(e,r,t={}){this.error(`${e} API request failed`,{...t,apiName:e,operation:"api_error"},r)}jobCreated(e){this.info("Job created",{jobId:e.id,filename:e.filename,status:e.status,operation:"job_created"})}jobStarted(e){this.info("Job started",{jobId:e.id,filename:e.filename,retryCount:e.retryCount,operation:"job_started"})}jobProgress(e,r,t){this.debug("Job progress update",{jobId:e,stage:r,...t,operation:"job_progress"})}jobCompleted(e,r){this.info("Job completed successfully",{jobId:e.id,filename:e.filename,duration:r,operation:"job_completed"})}jobFailed(e,r){this.error("Job failed",{jobId:e.id,filename:e.filename,errorCode:r.code,category:r.category,retryable:r.retryable,retryCount:e.retryCount,operation:"job_failed"})}jobRetrying(e,r){this.warn("Job being retried",{jobId:e.id,filename:e.filename,retryCount:e.retryCount,retryDelay:r,operation:"job_retrying"})}performance(e,r,t={}){this.info(`Performance: ${e}`,{...t,operation:"performance",duration:r,performanceMetric:e})}security(e,r={}){this.warn(`Security event: ${e}`,{...r,operation:"security",securityEvent:e})}log(e,r,t={},o){if(!this.shouldLog(e))return;let n={timestamp:new Date().toISOString(),level:e,message:r,context:{...this.baseContext,...t},correlationId:this.correlationId,metadata:{version:process.env.npm_package_version||"1.0.0",environment:"production",hostname:process.env.HOSTNAME||"unknown",pid:process.pid}};o&&(n.error={name:o.name,message:o.message,stack:o.stack,code:o.code}),this.writeLog(n)}shouldLog(e){let r=process.env.LOG_LEVEL||"info",t=["debug","info","warn","error"],o=t.indexOf(r);return t.indexOf(e)>=o}writeLog(e){let r=this.formatForConsole(e);switch(e.level){case"debug":case"info":break;case"warn":console.warn(r);break;case"error":console.error(r)}this.sendToMonitoring(e).catch(e=>{console.error("Failed to send log to monitoring:",e)})}formatForConsole(e){return JSON.stringify(e)}async sendToMonitoring(e){process.env.APPINSIGHTS_INSTRUMENTATIONKEY&&e.level,process.env.SENTRY_DSN&&("error"===e.level||e.level)}generateCorrelationId(){return`${Date.now()}-${Math.random().toString(36).substr(2,9)}`}}class n{constructor(e,r,t={}){this.logger=e,this.operation=r,this.context=t,this.startTime=Date.now()}end(){let e=Date.now()-this.startTime;return this.logger.performance(this.operation,e,this.context),e}getElapsed(){return Date.now()-this.startTime}}function s(e,r){return new o({jobId:e,filename:r})}function i(e,r){return new o({apiName:e,requestId:r})}async function a(e,r,t,o={}){let s=new n(e,r,o);try{e.debug(`Starting ${r}`,o);let n=await t(),i=s.end();return e.debug(`Completed ${r}`,{...o,duration:i}),n}catch(n){let t=s.getElapsed();throw e.error(`Failed ${r}`,{...o,duration:t},n),n}}function d(e,r,t,o={}){var n,s;let i={code:r.code||"UNKNOWN",message:r.message||"Unknown error",category:429===(n=r).status||n.message?.includes("rate limit")?"quota_exceeded":n.status>=400&&n.status<500?"api_error":"ENOTFOUND"===n.code||"ECONNREFUSED"===n.code||"ETIMEDOUT"===n.code?"network_error":"ENOENT"===n.code||n.message?.includes("not found")?"file_not_found":n.message?.includes("validation")||n.message?.includes("invalid")?"validation_error":"system_error",retryable:429===(s=r).status||s.status>=500||"ECONNREFUSED"===s.code||"ETIMEDOUT"===s.code||(!(s.status>=400)||!(s.status<500))&&(s.code,!1),stackTrace:r.stack,context:{operation:t,...o}};e.error(`Error in ${t}`,{...o,errorCode:i.code,category:i.category,retryable:i.retryable},r)}let c=new o}};