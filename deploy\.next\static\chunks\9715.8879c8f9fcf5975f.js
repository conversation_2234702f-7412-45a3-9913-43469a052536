"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9715],{9715:(e,s,r)=>{r.r(s),r.d(s,{VoiceUploadWrapper:()=>h});var t=r(5155),a=r(2115),l=r(3286),d=r(646),i=r(2138),c=r(1539),o=r(5021),n=r(1284),x=r(3563),m=r(4077),g=r(5721);function h(e){let{isDarkMode:s=!1}=e,[r,h]=(0,a.useState)(!1),[u,p]=(0,a.useState)([]),[j,y]=(0,a.useState)(null),b=[{step:1,title:"Connect USB Drive",description:"Click to grant browser access to your USB recorder",icon:(0,t.jsx)(l.A,{className:"w-5 h-5"})},{step:2,title:"Auto-Scan Files",description:"Automatically detects audio files in recorder folders",icon:(0,t.jsx)(d.A,{className:"w-5 h-5"})},{step:3,title:"Upload & Process",description:"Bulk upload to Azure with smart patient matching",icon:(0,t.jsx)(i.A,{className:"w-5 h-5"})},{step:4,title:"Archive & Cleanup",description:"Automatically archive files on USB and cleanup old recordings",icon:(0,t.jsx)(d.A,{className:"w-5 h-5"})}];return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700",children:(0,t.jsx)(x.A,{onFilesUploaded:e=>{p(e),y(new Date().toLocaleString())},onError:e=>{console.error("Upload error:",e)},isDarkMode:s})}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,t.jsx)(c.A,{className:"w-5 h-5 mr-2 text-yellow-500"}),"Quick Upload Workflow"]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:b.map(e=>(0,t.jsxs)("div",{className:"flex flex-col items-center text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,t.jsx)("div",{className:"flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-3",children:e.icon}),(0,t.jsxs)("div",{className:"text-sm font-medium text-gray-900 dark:text-white mb-1",children:["Step ",e.step,": ",e.title]}),(0,t.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:e.description})]},e.step))})]}),u.length>0&&(0,t.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"w-5 h-5 text-green-600 dark:text-green-400 mr-2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Upload Successful"}),(0,t.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-300",children:[u.length," files uploaded successfully",j&&" at ".concat(j)]})]})]})}),(0,t.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6",children:[(0,t.jsxs)("button",{onClick:()=>h(!r),className:"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors",children:[(0,t.jsx)(o.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[r?"Hide":"Show"," Testing Tools"]})]}),r&&(0,t.jsxs)("div",{className:"mt-4 space-y-4",children:[(0,t.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(n.A,{className:"w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Testing Tools"}),(0,t.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300 mt-1",children:"Use these tools to test WebUSB functionality and troubleshoot issues."})]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Integration Test"}),(0,t.jsx)(m.A,{isDarkMode:s})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Mock Test"}),(0,t.jsx)(g.A,{isDarkMode:s})]})]})]})]})]})}}}]);