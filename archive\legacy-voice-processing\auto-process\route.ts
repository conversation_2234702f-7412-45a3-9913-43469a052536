import { NextRequest, NextResponse } from 'next/server';
import { AzureStorageService } from '@/lib/azure-storage';
import { FileResolver } from '@/lib/file-resolver';
import { jobQueueService } from '@/lib/job-queue-service';
import { createJobLogger, logger, logError, withTiming } from '@/lib/logger';
import { <PERSON><PERSON>r<PERSON>andler } from '@/lib/error-handler';
import { ProcessingJob, JobResult, QueueMessage } from '@/types/queue';

/**
 * Enhanced auto-process route with reliable job queue service integration
 * 
 * This replaces the unreliable in-memory job tracking with the new job queue service
 * that provides persistent storage, retry logic, and monitoring capabilities.
 * Jobs survive server restarts and can be recovered reliably.
 */

/**
 * Create processing jobs for batch voice processing
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  const requestLogger = createJobLogger('batch-create', 'auto-process');
  
  try {
    const { recordingIds, source = 'manual', settings } = await request.json();

    if (!recordingIds || !Array.isArray(recordingIds)) {
      requestLogger.error('Invalid request: recordingIds array required');
      return NextResponse.json({
        error: 'Recording IDs array is required'
      }, { status: 400 });
    }

    requestLogger.info('Starting batch job creation', {
      recordingCount: recordingIds.length,
      source,
      settings
    });

    return await withTiming(requestLogger, 'batch-job-creation', async () => {
      const jobIds: string[] = [];
      const errors: Array<{ recordingId: string; error: string }> = [];

      // Create jobs for each recording using the job queue service
      for (const recordingId of recordingIds) {
        try {
          // Validate recording exists before creating job
          const fileResult = await FileResolver.findAudioFile(recordingId);
          
          if (!fileResult.found) {
            errors.push({
              recordingId,
              error: `File not found: ${fileResult.error}`
            });
            continue;
          }

          // Enqueue job using the reliable job queue service
          const jobId = await jobQueueService.enqueueJob({
            filename: recordingId.split('/').pop() || recordingId,
            containerPath: recordingId,
            retryCount: 0,
            metadata: {
              type: 'full_processing',
              source,
              userId: settings?.userId,
              sessionId: request.headers.get('x-session-id') || undefined,
              enqueuedAt: new Date().toISOString()
            }
          }, {
            priority: 1,
            timeout: settings?.timeout || 300000, // 5 minutes default
            metadata: {
              batchId: `batch-${Date.now()}`,
              requestSource: 'auto-process-api'
            }
          });

          jobIds.push(jobId);
          
          requestLogger.info('Job enqueued successfully', {
            jobId,
            recordingId,
            filename: recordingId.split('/').pop() || recordingId
          });

        } catch (error) {
          logError(requestLogger, error, 'job-creation', { recordingId });
          errors.push({
            recordingId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // Return results with both successful jobs and errors
      const response: JobResult = {
        success: jobIds.length > 0,
        data: {
          jobIds,
          created: jobIds.length,
          failed: errors.length,
          errors: errors.length > 0 ? errors : undefined,
          message: `Created ${jobIds.length} jobs${errors.length > 0 ? `, ${errors.length} failed` : ''}`
        }
      };

      requestLogger.info('Batch job creation completed', {
        created: jobIds.length,
        failed: errors.length,
        totalRequested: recordingIds.length
      });

      return NextResponse.json(response, { 
        status: jobIds.length > 0 ? 202 : 400 
      });
    });

  } catch (error) {
    logError(requestLogger, error, 'auto-process-request');
    return NextResponse.json({
      success: false,
      error: 'Failed to start auto-processing',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Get job status and queue statistics using job queue service
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  const requestLogger = createJobLogger('status-query', 'auto-process');
  
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');
    const includeStats = searchParams.get('includeStats') !== 'false';
    const includeStuckJobs = searchParams.get('includeStuckJobs') === 'true';

    if (jobId) {
      // Get specific job status using job queue service
      return await withTiming(requestLogger, 'get-job-by-id', async () => {
        const job = await jobQueueService.getJobStatus(jobId);
        
        if (!job) {
          return NextResponse.json({
            success: false,
            error: 'Job not found'
          }, { status: 404 });
        }

        requestLogger.debug('Retrieved job status', {
          jobId: job.id,
          status: job.status,
          filename: job.filename
        });

        return NextResponse.json({
          success: true,
          job: {
            id: job.id,
            filename: job.filename,
            containerPath: job.containerPath,
            status: job.status,
            retryCount: job.retryCount,
            createdAt: job.createdAt,
            updatedAt: job.updatedAt,
            startedAt: job.startedAt,
            completedAt: job.completedAt,
            errorDetails: job.errorDetails,
            results: job.results
          }
        });
      });
    } else {
      // Get queue statistics and health information
      return await withTiming(requestLogger, 'get-queue-stats', async () => {
        const stats = await jobQueueService.getQueueStats();
        
        const response: any = {
          success: true,
          queueStats: stats,
          timestamp: new Date().toISOString()
        };

        // Include stuck jobs if requested
        if (includeStuckJobs) {
          const stuckJobs = await jobQueueService.getStuckJobs();
          response.stuckJobs = stuckJobs.map(job => ({
            id: job.id,
            filename: job.filename,
            status: job.status,
            startedAt: job.startedAt,
            retryCount: job.retryCount
          }));
        }

        requestLogger.debug('Retrieved queue statistics', {
          totalJobs: stats.total,
          pendingJobs: stats.pending,
          queueHealth: stats.queueHealth,
          stuckJobs: stats.stuckJobs
        });

        return NextResponse.json(response);
      });
    }

  } catch (error) {
    logError(requestLogger, error, 'status-query');
    return NextResponse.json({
      success: false,
      error: 'Failed to get queue status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Manual retry endpoint for failed jobs
 */
export async function PATCH(request: NextRequest): Promise<NextResponse> {
  const requestLogger = createJobLogger('manual-retry', 'auto-process');
  
  try {
    const { jobId, retryConfig } = await request.json();

    if (!jobId) {
      return NextResponse.json({
        success: false,
        error: 'Job ID is required'
      }, { status: 400 });
    }

    requestLogger.info('Manual retry requested', { jobId });

    const success = await jobQueueService.retryFailedJob(jobId, retryConfig);
    
    if (success) {
      requestLogger.info('Job retry scheduled successfully', { jobId });
      return NextResponse.json({
        success: true,
        message: 'Job retry scheduled successfully'
      });
    } else {
      requestLogger.warn('Job retry failed - job not eligible', { jobId });
      return NextResponse.json({
        success: false,
        error: 'Job is not eligible for retry'
      }, { status: 400 });
    }

  } catch (error) {
    logError(requestLogger, error, 'manual-retry');
    return NextResponse.json({
      success: false,
      error: 'Failed to retry job',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Cleanup old jobs endpoint
 */
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  const requestLogger = createJobLogger('cleanup', 'auto-process');
  
  try {
    const { searchParams } = new URL(request.url);
    const retentionDays = parseInt(searchParams.get('retentionDays') || '30');

    requestLogger.info('Job cleanup requested', { retentionDays });

    const cleanedCount = await jobQueueService.cleanupOldJobs(retentionDays);
    
    requestLogger.info('Job cleanup completed', {
      cleanedCount,
      retentionDays
    });

    return NextResponse.json({
      success: true,
      cleanedCount,
      retentionDays,
      message: `Cleaned up ${cleanedCount} old jobs`
    });

  } catch (error) {
    logError(requestLogger, error, 'job-cleanup');
    return NextResponse.json({
      success: false,
      error: 'Failed to cleanup jobs',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}