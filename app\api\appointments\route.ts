/**
 * @deprecated This legacy API route is deprecated. Use /api/appointments-sql instead.
 * API route for fetching appointments
 */

import { NextRequest, NextResponse } from 'next/server';

export async function GET(_request: NextRequest) {
  // This endpoint is deprecated - return 410 Gone with migration instructions
  return NextResponse.json({
    error: 'Endpoint deprecated',
    message: 'This legacy appointments endpoint is deprecated and has been removed.',
    migration: {
      newEndpoint: '/api/appointments-sql',
      instructions: 'Please update your application to use the new SQL-based appointments endpoint.',
      documentation: 'See CLAUDE.md for migration guidance'
    }
  }, { 
    status: 410,
    headers: {
      'X-Deprecated-Endpoint': 'true',
      'X-Replacement-Endpoint': '/api/appointments-sql'
    }
  });
}

