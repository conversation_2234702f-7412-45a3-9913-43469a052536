import { AzureStorageService } from './azure-storage';

export interface QueueMessage {
  id: string;
  filePath: string;
  operation: 'transcribe' | 'summarize' | 'complete';
  priority: 'low' | 'normal' | 'high';
  options: {
    forceRetranscribe?: boolean;
    language?: string;
    enableDiarization?: boolean;
    summaryType?: string;
  };
  enqueuedAt: string;
  attempts: number;
  maxAttempts: number;
  nextRetryAt?: string;
  processingStartedAt?: string;
  error?: string;
}

export interface QueueStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  totalProcessed: number;
}

/**
 * Simple queue implementation using Azure Blob Storage
 * In production, this would use Azure Service Bus or similar
 */
export class AzureQueueService {
  private static readonly QUEUE_CONTAINER = 'processing-queue';
  private static readonly PROCESSING_CONTAINER = 'processing-active';
  private static readonly COMPLETED_CONTAINER = 'processing-completed';
  
  /**
   * Initialize queue containers
   */
  static async initialize(): Promise<void> {
    // In a real implementation, these would be Service Bus queues
    // For now, we'll use blob storage as a simple queue mechanism
    console.log('📋 Queue service initialized (using blob storage simulation)');
  }

  /**
   * Add a file to the processing queue
   */
  static async enqueueFile(
    filePath: string,
    operation: QueueMessage['operation'] = 'complete',
    options: QueueMessage['options'] = {},
    priority: QueueMessage['priority'] = 'normal'
  ): Promise<string> {
    const messageId = `${operation}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const message: QueueMessage = {
      id: messageId,
      filePath,
      operation,
      priority,
      options,
      enqueuedAt: new Date().toISOString(),
      attempts: 0,
      maxAttempts: 3
    };

    // Store queue message in blob storage
    const queuePath = `queue/pending/${priority}/${messageId}.json`;
    await AzureStorageService.uploadJson(queuePath, message);
    
    console.log(`📝 Enqueued ${operation} for ${filePath} with priority ${priority}`);
    return messageId;
  }

  /**
   * Enqueue multiple files for batch processing
   */
  static async enqueueBatch(
    filePaths: string[],
    operation: QueueMessage['operation'] = 'complete',
    options: QueueMessage['options'] = {},
    priority: QueueMessage['priority'] = 'normal'
  ): Promise<string[]> {
    const messageIds: string[] = [];
    
    for (const filePath of filePaths) {
      const messageId = await this.enqueueFile(filePath, operation, options, priority);
      messageIds.push(messageId);
      
      // Small delay to avoid overwhelming the storage
      await new Promise(resolve => setTimeout(resolve, 10));
    }
    
    console.log(`📦 Enqueued batch of ${filePaths.length} files for ${operation}`);
    return messageIds;
  }

  /**
   * Get next message from queue (prioritized)
   */
  static async dequeueMessage(): Promise<QueueMessage | null> {
    try {
      // Check high priority first, then normal, then low
      const priorities = ['high', 'normal', 'low'];
      
      for (const priority of priorities) {
        const message = await this.getNextMessageByPriority(priority);
        if (message) {
          // Move message to processing state
          await this.moveToProcessing(message);
          return message;
        }
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error dequeuing message:', error);
      return null;
    }
  }

  /**
   * Get next message by priority
   */
  private static async getNextMessageByPriority(priority: string): Promise<QueueMessage | null> {
    try {
      const queuePrefix = `queue/pending/${priority}/`;
      const files = await AzureStorageService.listFiles(queuePrefix);
      
      if (files.length === 0) {
        return null;
      }
      
      // Get the oldest message
      const oldestFile = files.sort((a, b) => a.lastModified.getTime() - b.lastModified.getTime())[0];
      
      // Download and parse message
      const message = await AzureStorageService.downloadJson(oldestFile.path);
      
      // Check if it's time to retry (for failed attempts)
      if (message.nextRetryAt && new Date(message.nextRetryAt) > new Date()) {
        return null; // Not time to retry yet
      }
      
      return message as QueueMessage;
    } catch (error) {
      console.error(`❌ Error getting ${priority} priority message:`, error);
      return null;
    }
  }

  /**
   * Move message to processing state
   */
  private static async moveToProcessing(message: QueueMessage): Promise<void> {
    const pendingPath = `queue/pending/${message.priority}/${message.id}.json`;
    const processingPath = `queue/processing/${message.id}.json`;
    
    // Update message with processing start time
    message.processingStartedAt = new Date().toISOString();
    message.attempts++;
    
    // Save to processing folder
    await AzureStorageService.uploadJson(processingPath, message);
    
    // Remove from pending
    try {
      await AzureStorageService.deleteFile(pendingPath);
    } catch (error) {
      console.warn('⚠️ Could not delete pending message (may have been processed already):', error);
    }
  }

  /**
   * Mark message as completed
   */
  static async completeMessage(messageId: string, result?: any): Promise<void> {
    const processingPath = `queue/processing/${messageId}.json`;
    const completedPath = `queue/completed/${messageId}.json`;
    
    try {
      // Get the processing message
      const message = await AzureStorageService.downloadJson(processingPath);
      
      // Add completion info
      message.completedAt = new Date().toISOString();
      message.result = result;
      
      // Move to completed
      await AzureStorageService.uploadJson(completedPath, message);
      
      // Remove from processing
      await AzureStorageService.deleteFile(processingPath);
      
      console.log(`✅ Message ${messageId} completed`);
    } catch (error) {
      console.error(`❌ Error completing message ${messageId}:`, error);
    }
  }

  /**
   * Mark message as failed
   */
  static async failMessage(messageId: string, error: string): Promise<void> {
    const processingPath = `queue/processing/${messageId}.json`;
    
    try {
      // Get the processing message
      const message = await AzureStorageService.downloadJson(processingPath);
      
      message.error = error;
      
      // Check if we should retry
      if (message.attempts < message.maxAttempts) {
        // Move back to pending with retry delay
        const retryDelay = Math.min(Math.pow(2, message.attempts) * 1000, 300000); // Exponential backoff, max 5 minutes
        message.nextRetryAt = new Date(Date.now() + retryDelay).toISOString();
        
        const pendingPath = `queue/pending/${message.priority}/${messageId}.json`;
        await AzureStorageService.uploadJson(pendingPath, message);
        
        console.log(`🔄 Message ${messageId} will retry in ${retryDelay}ms (attempt ${message.attempts}/${message.maxAttempts})`);
      } else {
        // Move to failed (completed with error)
        const failedPath = `queue/failed/${messageId}.json`;
        message.failedAt = new Date().toISOString();
        await AzureStorageService.uploadJson(failedPath, message);
        
        console.log(`❌ Message ${messageId} failed permanently after ${message.attempts} attempts`);
      }
      
      // Remove from processing
      await AzureStorageService.deleteFile(processingPath);
      
    } catch (error) {
      console.error(`❌ Error handling failed message ${messageId}:`, error);
    }
  }

  /**
   * Get queue statistics
   */
  static async getQueueStats(): Promise<QueueStats> {
    try {
      const [pendingFiles, processingFiles, completedFiles, failedFiles] = await Promise.all([
        AzureStorageService.listFiles('queue/pending/'),
        AzureStorageService.listFiles('queue/processing/'),
        AzureStorageService.listFiles('queue/completed/'),
        AzureStorageService.listFiles('queue/failed/')
      ]);

      return {
        pending: pendingFiles.length,
        processing: processingFiles.length,
        completed: completedFiles.length,
        failed: failedFiles.length,
        totalProcessed: completedFiles.length + failedFiles.length
      };
    } catch (error) {
      console.error('❌ Error getting queue stats:', error);
      return {
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        totalProcessed: 0
      };
    }
  }

  /**
   * Process queue messages (background worker)
   */
  static async processQueue(
    processor: (message: QueueMessage) => Promise<void>,
    options: {
      maxConcurrent?: number;
      pollInterval?: number;
      stopAfterEmpty?: boolean;
    } = {}
  ): Promise<void> {
    const { maxConcurrent = 8, pollInterval = 5000, stopAfterEmpty = false } = options;
    
    console.log(`🚀 Starting queue processor (max ${maxConcurrent} concurrent, poll every ${pollInterval}ms)`);
    
    const activeWorkers = new Set<Promise<void>>();
    let shouldStop = false;
    
    while (!shouldStop) {
      try {
        // Clean up completed workers
        const completedWorkers = Array.from(activeWorkers).filter(async worker => {
          try {
            await Promise.race([worker, Promise.resolve()]);
            return true;
          } catch {
            return true;
          }
        });
        
        completedWorkers.forEach(worker => activeWorkers.delete(worker));
        
        // Start new workers if we have capacity
        while (activeWorkers.size < maxConcurrent) {
          const message = await this.dequeueMessage();
          
          if (!message) {
            if (stopAfterEmpty && activeWorkers.size === 0) {
              shouldStop = true;
            }
            break; // No more messages
          }
          
          // Start worker for this message
          const worker = this.processMessage(message, processor);
          activeWorkers.add(worker);
        }
        
        // Wait before next poll
        if (!shouldStop) {
          await new Promise(resolve => setTimeout(resolve, pollInterval));
        }
        
      } catch (error) {
        console.error('❌ Error in queue processor:', error);
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    }
    
    // Wait for all active workers to complete
    await Promise.allSettled(Array.from(activeWorkers));
    console.log('🏁 Queue processor stopped');
  }

  /**
   * Process a single message
   */
  private static async processMessage(
    message: QueueMessage,
    processor: (message: QueueMessage) => Promise<void>
  ): Promise<void> {
    try {
      console.log(`🔄 Processing message ${message.id} for ${message.filePath}`);
      
      await processor(message);
      await this.completeMessage(message.id);
      
      console.log(`✅ Successfully processed message ${message.id}`);
    } catch (error) {
      console.error(`❌ Error processing message ${message.id}:`, error);
      await this.failMessage(message.id, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Clear all queue data (for testing/reset)
   */
  static async clearQueue(): Promise<void> {
    try {
      const queueFiles = await AzureStorageService.listFiles('queue/');
      
      for (const file of queueFiles) {
        await AzureStorageService.deleteFile(file.path);
      }
      
      console.log(`🧹 Cleared ${queueFiles.length} queue messages`);
    } catch (error) {
      console.error('❌ Error clearing queue:', error);
    }
  }
}