import requests
import json
from datetime import datetime, timedelta
from collections import defaultdict
import sys
import argparse

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

def get_request_key(office_id, secret_key, app_id, app_key):
    """Obtain a request key for API authentication."""
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": office_id,
            "secret_key": secret_key,
            "app_id": app_id,
            "app_key": app_key
        }
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None

    data = auth_resp.json()
    return data.get("request_key")

def fetch_transactions(request_key, date, provider_id=None, timeout=30):
    """Fetch transactions for a specific date and optionally filter by provider."""
    headers = {"Request-Key": request_key}

    # If provider_id is specified, add it to the params to filter at the API level
    params = {"date": date}
    if provider_id:
        params["provider_id"] = provider_id

    # Fetch all pages to make sure we get all transactions
    all_items = []
    offset = 0
    limit = 100  # Increase limit to reduce number of API calls
    max_pages = 5  # Limit the number of pages to fetch

    print("Fetching transactions (this may take a moment)...")

    for page in range(max_pages):
        paged_params = params.copy()
        paged_params.update({"offset": offset, "limit": limit})

        try:
            resp = requests.get(
                f"{API_BASE_V4}/transactions",
                headers=headers,
                params=paged_params,
                timeout=timeout  # Add timeout to prevent hanging
            )

            if resp.status_code != 200:
                print(f"Error fetching transactions: {resp.status_code}")
                print(resp.text)
                break

            data = resp.json()
            items = data.get("items", [])
            all_items.extend(items)

            print(f"  Fetched {len(items)} transactions (page {page+1})")

            if len(items) < limit:
                break

            offset += limit
        except requests.exceptions.Timeout:
            print(f"Timeout while fetching transactions. Proceeding with {len(all_items)} transactions.")
            break
        except Exception as e:
            print(f"Error: {e}")
            break

    # Additional filter by provider if needed (in case API-level filtering didn't work)
    if provider_id and any(t.get("provider_id") != provider_id for t in all_items):
        all_items = [t for t in all_items if t.get("provider_id") == provider_id]

    # Filter for procedure transactions only
    procedure_items = [t for t in all_items
                      if t.get("transaction_type") == "Procedure"
                      and float(t.get("amount", "0")) > 0]

    return procedure_items

def fetch_operatories(request_key, date_range=90):
    """Fetch a list of all operatories from appointment data."""
    print("Fetching list of operatories...")
    headers = {"Request-Key": request_key}

    # Try to get appointments for the last X days to find as many operatories as possible
    operatories = set()

    # Try different date ranges
    from datetime import datetime, timedelta

    end_date = datetime.now()
    start_date = end_date - timedelta(days=date_range)

    # Format dates as YYYY-MM-DD
    date_format = "%Y-%m-%d"
    current_date = start_date

    # Check every 7 days to avoid too many API calls
    while current_date <= end_date:
        date_str = current_date.strftime(date_format)

        resp = requests.get(f"{API_BASE_V2}/appointments", headers=headers, params={"date": date_str})

        if resp.status_code == 200:
            try:
                data = resp.json()

                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])

                    # Extract operatories
                    for item in items:
                        operatory = item.get("operatory")
                        if operatory and operatory not in ["", "N/A"]:
                            operatories.add(operatory)
            except Exception as e:
                print(f"Error parsing response: {e}")

        # Move to next date
        current_date += timedelta(days=7)

    # Print the list of operatories
    if operatories:
        print(f"Found {len(operatories)} operatories:")
        for op in sorted(operatories):
            print(f"  - {op}")
    else:
        print("No operatories found in appointment data")

    return sorted(operatories)

def fetch_appointments(request_key, date, provider_id=None, operatories=None):
    """Fetch appointments for a specific date and optionally filter by provider and operatories."""
    headers = {"Request-Key": request_key}
    params = {"date": date}

    # Use v2 appointments endpoint to get operatory information
    print("Using v2 appointments endpoint to get operatory information...")
    resp = requests.get(f"{API_BASE_V2}/appointments", headers=headers, params=params)

    if resp.status_code != 200:
        print(f"Error fetching appointments from v2 endpoint: {resp.status_code}")
        print(resp.text)

        # Fall back to v4 endpoint if v2 fails
        print("Falling back to v4 appointments endpoint...")
        return fetch_appointments_v4(request_key, date, provider_id)

    data = resp.json()

    # Extract items from v2 response (which has a different structure)
    if isinstance(data, list) and len(data) > 0:
        all_items = data[0].get("items", [])
    else:
        all_items = []

    # Filter by provider if specified
    if provider_id:
        all_items = [a for a in all_items if a.get("provider_id") == provider_id]

    # Filter by operatories if specified
    if operatories:
        all_items = [a for a in all_items if a.get("operatory") in operatories]

    # Filter to only include appointments for the specified date
    all_items = [a for a in all_items if a.get("date", "").startswith(date)]

    return all_items

def fetch_appointments_v4(request_key, date, provider_id=None):
    """Fetch appointments from v4 endpoint as a fallback."""
    headers = {"Request-Key": request_key}
    params = {"date": date}

    # Fetch all pages to make sure we get all appointments
    all_items = []
    offset = 0
    limit = 50

    while True:
        paged_params = params.copy()
        paged_params.update({"offset": offset, "limit": limit})

        resp = requests.get(f"{API_BASE_V4}/appointments", headers=headers, params=paged_params)

        if resp.status_code != 200:
            print(f"Error fetching appointments: {resp.status_code}")
            print(resp.text)
            break

        data = resp.json()
        items = data.get("items", [])
        all_items.extend(items)

        if len(items) < limit:
            break

        offset += limit

    # Filter by provider if specified
    if provider_id:
        all_items = [a for a in all_items if a.get("provider_id") == provider_id]

    # Filter to only include appointments for the specified date
    all_items = [a for a in all_items if a.get("date", "").startswith(date)]

    return all_items

def create_operatory_schedule(transactions, appointments, provider_id, date_str):
    """Create a daily schedule with operatory information."""
    # Filter for procedure transactions by the specified provider
    provider_procedures = [t for t in transactions
                          if t.get("transaction_type") == "Procedure"
                          and t.get("provider_id") == provider_id
                          and float(t.get("amount", "0")) > 0]

    # Create a dictionary of procedures by patient ID
    procedures_by_patient = {}
    for proc in provider_procedures:
        patient_id = proc.get("patient_id")
        if patient_id not in procedures_by_patient:
            procedures_by_patient[patient_id] = {
                "name": proc.get("patient_name", "Unknown Patient"),
                "procedures": []
            }
        procedures_by_patient[patient_id]["procedures"].append(proc)

    # Create a dictionary of procedures by appointment serial number
    procedures_by_appt_sr_no = {}
    for proc in provider_procedures:
        # Check if the procedure has an appointment_sr_no field
        appt_sr_no = proc.get("appointment_sr_no")
        if appt_sr_no:
            if appt_sr_no not in procedures_by_appt_sr_no:
                procedures_by_appt_sr_no[appt_sr_no] = []
            procedures_by_appt_sr_no[appt_sr_no].append(proc)

    # Get patient IDs from procedures
    procedure_patient_ids = set(procedures_by_patient.keys())

    # Filter appointments for the specified provider
    provider_appointments = [a for a in appointments
                            if a.get("provider_id") == provider_id]

    # Create a dictionary of appointments by time
    appt_by_time = {}
    for appt in provider_appointments:
        time_str = appt.get("time", "")
        if time_str:
            # Convert from "HH:MM" to datetime object for proper sorting
            try:
                time_obj = datetime.strptime(time_str, "%H:%M")
                appt_by_time[time_str] = appt
            except ValueError:
                # Skip appointments with invalid time format
                continue

    # Format date for display
    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
    formatted_date = date_obj.strftime("%A, %B %d, %Y")

    # Display the schedule
    print(f"\n{'=' * 80}")
    print(f"{'DAILY SCHEDULE FOR DR. ' + provider_id:^80}")
    print(f"{formatted_date:^80}")
    print(f"{'=' * 80}")

    # Create time slots from 8:00 to 17:00 in 10-minute increments
    start_time = datetime.strptime("08:00", "%H:%M")
    end_time = datetime.strptime("17:00", "%H:%M")
    current_time = start_time

    active_appointments = []  # Track appointments that span multiple time slots

    # Print header
    print(f"\n{'TIME':<8}{'PATIENT':<25}{'PROCEDURE':<25}{'OPERATORY':<10}{'LENGTH':<7}{'STATUS'}")
    print(f"{'-' * 8}{'-' * 25}{'-' * 25}{'-' * 10}{'-' * 7}{'-' * 10}")

    while current_time <= end_time:
        time_str = current_time.strftime("%H:%M")

        # Check if any appointment starts at this time
        new_appt_started = False
        if time_str in appt_by_time:
            new_appt_started = True
            appt = appt_by_time[time_str]
            length_min = float(appt.get("length", "0"))
            end_time_obj = current_time + timedelta(minutes=length_min)

            patient_id = appt.get("patient_id", "")
            patient_name = appt.get("patient_name", "Unknown Patient")
            description = appt.get("description", "No description")
            appt_sr_no = appt.get("appointment_sr_no", "")
            operatory = appt.get("operatory", "N/A")

            # Check if this patient had procedures done by this provider
            has_procedures = patient_id in procedure_patient_ids

            # Check if this appointment has linked procedures
            has_linked_procedures = appt_sr_no in procedures_by_appt_sr_no

            # Add to active appointments
            active_appointments.append({
                "appt": appt,
                "end_time": end_time_obj,
                "has_procedures": has_procedures or has_linked_procedures,
                "start_time": current_time,  # Store start time for duration calculation
                "operatory": operatory,
                "appt_sr_no": appt_sr_no
            })

        # Update and display active appointments
        new_active = []
        for appt_info in active_appointments:
            if appt_info["end_time"] > current_time:
                appt = appt_info["appt"]
                patient_name = appt.get("patient_name", "Unknown Patient")
                description = appt.get("description", "No description")
                length_min = float(appt.get("length", "0"))
                operatory = appt_info["operatory"]

                # Calculate how long the appointment has been active
                elapsed = (current_time - appt_info["start_time"]).total_seconds() / 60
                remaining = length_min - elapsed

                # Only display the appointment info when it starts or every 30 minutes
                is_start = elapsed < 10  # First time slot of appointment
                is_30min_mark = elapsed > 0 and elapsed % 30 < 10  # Every 30 minutes

                if is_start or is_30min_mark or new_appt_started:
                    # Mark appointments with procedures
                    status = "COMPLETED" if appt_info["has_procedures"] else "SCHEDULED"

                    # Truncate long descriptions
                    short_desc = description
                    if len(short_desc) > 22:
                        short_desc = short_desc[:19] + "..."

                    # Format patient name
                    short_name = patient_name
                    if len(short_name) > 22:
                        short_name = short_name[:19] + "..."

                    # Display the appointment
                    if is_start:
                        # Show full details at start
                        print(f"{time_str:<8}{short_name:<25}{short_desc:<25}{operatory:<10}{int(length_min):<7}{status}")
                    else:
                        # Show continuation marker for ongoing appointments
                        print(f"{time_str:<8}{'└─ ' + short_name:<25}{'(continued)':<25}{operatory:<10}{f'{int(remaining)}m':<7}{status}")

                new_active.append(appt_info)

        # Only show empty slots at hour and half-hour marks
        if not new_active and (time_str.endswith(":00") or time_str.endswith(":30")):
            print(f"{time_str:<8}{'[OPEN]':<25}{'':<25}{'':<10}{'':<7}{''}")

        active_appointments = new_active

        # Move to next time slot (10 minutes)
        current_time += timedelta(minutes=10)

    # Also display a summary of procedures completed
    print(f"\n{'=' * 80}")
    print(f"{'PROCEDURES COMPLETED BY DR. ' + provider_id:^80}")
    print(f"{formatted_date:^80}")
    print(f"{'=' * 80}")

    # Calculate total production
    total_production = 0
    patient_count = 0

    # Display procedures by patient
    for patient_id, data in procedures_by_patient.items():
        patient_name = data["name"]
        procs = data["procedures"]
        patient_count += 1

        print(f"\n{patient_count}. {patient_name}")
        print("-" * 80)

        # Find matching appointment for this patient
        matching_appt = None
        for appt in provider_appointments:
            if appt.get("patient_id") == patient_id:
                matching_appt = appt
                break

        if matching_appt:
            appt_time = matching_appt.get("time", "")
            appt_desc = matching_appt.get("description", "")
            appt_sr_no = matching_appt.get("appointment_sr_no", "")
            operatory = matching_appt.get("operatory", "N/A")
            print(f"Appointment: {appt_time} - {appt_desc}")
            print(f"Operatory: {operatory}")
            print(f"Appointment Serial #: {appt_sr_no}")
        else:
            print("Note: No matching appointment found for this patient on this date")

        print("-" * 80)
        print(f"{'CODE':<10}{'DESCRIPTION':<40}{'TOOTH':<10}{'SURFACE':<10}{'AMOUNT':<10}")
        print(f"{'-' * 10}{'-' * 40}{'-' * 10}{'-' * 10}{'-' * 10}")

        # Display procedures
        patient_total = 0
        for proc in procs:
            code = proc.get("procedure_code", "")
            desc = proc.get("procedure_description", "")
            amount = float(proc.get("amount", "0"))
            tooth = proc.get("tooth_from", "") or "-"
            surface = proc.get("surface", "") or "-"

            # Truncate long descriptions
            if len(desc) > 37:
                desc = desc[:34] + "..."

            print(f"{code:<10}{desc:<40}{tooth:<10}{surface:<10}${amount:<9.2f}")
            patient_total += amount
            total_production += amount

        print(f"{'':<60}{'TOTAL:':<10}${patient_total:<9.2f}")

    # Print summary
    if patient_count == 0:
        print("\nNo procedures completed by this provider on this date.")
    else:
        print(f"\n{'=' * 80}")
        print(f"{'SUMMARY':^80}")
        print(f"{'=' * 80}")
        print(f"Total patients with completed procedures: {patient_count}")
        print(f"Total production: ${total_production:.2f}")

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Generate a daily schedule with operatory information")
    parser.add_argument("date", help="Date in YYYY-MM-DD format")
    parser.add_argument("provider_id", help="Provider ID (e.g., LL01)")
    parser.add_argument("--office-id", default="D43989", help="Office ID")
    parser.add_argument("--secret-key", default="35442814D4396E20C222", help="Secret key")
    parser.add_argument("--app-id", default="fdd52aaffb0c1bead647874ba551db0c", help="App ID")
    parser.add_argument("--app-key", default="88254bfa2224607ef425646aafe5f722", help="App key")
    parser.add_argument("--operatories", help="Comma-separated list of operatories to filter by (e.g., DL01,DL02)")
    parser.add_argument("--list-operatories", action="store_true", help="List all available operatories and exit")

    args = parser.parse_args()

    # Validate date format
    try:
        datetime.strptime(args.date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        return

    # Get request key
    print("Getting request key...")
    request_key = get_request_key(args.office_id, args.secret_key, args.app_id, args.app_key)
    if not request_key:
        print("Failed to get request key. Exiting.")
        return

    print("Request key obtained successfully")

    # If --list-operatories flag is set, just list operatories and exit
    if args.list_operatories:
        operatories = fetch_operatories(request_key)
        print("\nAvailable operatories:")
        for op in operatories:
            print(f"  - {op}")
        return

    # Parse operatories if provided
    selected_operatories = None
    if args.operatories:
        selected_operatories = [op.strip() for op in args.operatories.split(",")]
        print(f"Filtering by operatories: {', '.join(selected_operatories)}")

    # Fetch transactions directly filtered by provider
    print(f"Fetching transactions for provider {args.provider_id} on {args.date}...")
    transactions = fetch_transactions(request_key, args.date, args.provider_id)
    print(f"Found {len(transactions)} transactions")

    # Fetch appointments directly filtered by provider and operatories
    print(f"Fetching appointments for provider {args.provider_id} on {args.date}...")
    appointments = fetch_appointments(request_key, args.date, args.provider_id, selected_operatories)
    print(f"Found {len(appointments)} appointments")

    # Create provider schedule with operatory information
    create_operatory_schedule(transactions, appointments, args.provider_id, args.date)

if __name__ == "__main__":
    main()
