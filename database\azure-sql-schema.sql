-- Azure SQL Database Schema for Cloud-First Voice Processing Architecture
-- This schema supports persistent job tracking and eliminates in-memory storage issues

-- Create database (run separately if creating new database)
-- CREATE DATABASE DentalAppCloudDB;
-- GO

-- Use the database
-- USE DentalAppCloudDB;
-- GO

-- Enable snapshot isolation for better concurrency
ALTER DATABASE CURRENT SET ALLOW_SNAPSHOT_ISOLATION ON;
ALTER DATABASE CURRENT SET READ_COMMITTED_SNAPSHOT ON;
GO

-- =====================================================
-- RECORDINGS TABLE
-- Stores voice recording metadata and processing status
-- =====================================================
CREATE TABLE recordings (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    filename NVARCHAR(255) NOT NULL,
    original_filename NVARCHAR(500) NOT NULL,
    blob_url NVARCHAR(1000) NOT NULL,
    blob_path NVARCHAR(500) NOT NULL UNIQUE,
    file_size BIGINT NOT NULL CHECK (file_size > 0),
    duration_seconds FLOAT NULL CHECK (duration_seconds >= 0),
    sikka_id NVARCHAR(100) NULL,
    patient_name NVARCHAR(200) NULL,
    appointment_date DATETIME2 NULL,
    operatory NVARCHAR(100) NULL,
    provider NVARCHAR(200) NULL,
    status NVARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'transcribing', 'generating_summary', 'completed', 'failed', 'cancelled')),
    metadata NVARCHAR(MAX) NULL, -- JSON metadata
    created_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    processed_at DATETIME2 NULL
);

-- Create indexes for recordings table
CREATE INDEX IX_recordings_status ON recordings(status);
CREATE INDEX IX_recordings_sikka_id ON recordings(sikka_id) WHERE sikka_id IS NOT NULL;
CREATE INDEX IX_recordings_patient_name ON recordings(patient_name) WHERE patient_name IS NOT NULL;
CREATE INDEX IX_recordings_appointment_date ON recordings(appointment_date) WHERE appointment_date IS NOT NULL;
CREATE INDEX IX_recordings_created_at ON recordings(created_at);
CREATE INDEX IX_recordings_blob_path ON recordings(blob_path);

-- =====================================================
-- JOBS TABLE
-- Persistent job tracking with automatic recovery
-- =====================================================
CREATE TABLE jobs (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    recording_id UNIQUEIDENTIFIER NOT NULL,
    job_type NVARCHAR(50) NOT NULL CHECK (job_type IN ('transcription', 'summary', 'full_process')),
    status NVARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'transcribing', 'generating_summary', 'completed', 'failed', 'cancelled')),
    priority NVARCHAR(50) NOT NULL DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    progress_percentage INT NOT NULL DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    started_at DATETIME2 NULL,
    completed_at DATETIME2 NULL,
    error_message NVARCHAR(1000) NULL,
    retry_count INT NOT NULL DEFAULT 0 CHECK (retry_count >= 0),
    max_retries INT NOT NULL DEFAULT 3 CHECK (max_retries >= 0),
    metadata NVARCHAR(MAX) NULL, -- JSON metadata
    created_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_jobs_recording_id FOREIGN KEY (recording_id) REFERENCES recordings(id) ON DELETE CASCADE
);

-- Create indexes for jobs table
CREATE INDEX IX_jobs_recording_id ON jobs(recording_id);
CREATE INDEX IX_jobs_status ON jobs(status);
CREATE INDEX IX_jobs_priority_created ON jobs(priority DESC, created_at ASC);
CREATE INDEX IX_jobs_created_at ON jobs(created_at);
CREATE INDEX IX_jobs_status_priority ON jobs(status, priority DESC);

-- =====================================================
-- TRANSCRIPTIONS TABLE
-- Stores OpenAI Whisper transcription results
-- =====================================================
CREATE TABLE transcriptions (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    recording_id UNIQUEIDENTIFIER NOT NULL,
    job_id UNIQUEIDENTIFIER NOT NULL,
    transcription_text NVARCHAR(MAX) NOT NULL,
    confidence_score FLOAT NULL CHECK (confidence_score >= 0 AND confidence_score <= 1),
    language NVARCHAR(50) NULL,
    segments NVARCHAR(MAX) NULL, -- JSON array of transcription segments
    openai_response NVARCHAR(MAX) NULL, -- Full OpenAI response for debugging
    created_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_transcriptions_recording_id FOREIGN KEY (recording_id) REFERENCES recordings(id) ON DELETE CASCADE,
    CONSTRAINT FK_transcriptions_job_id FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    CONSTRAINT UQ_transcriptions_recording UNIQUE (recording_id) -- One transcription per recording
);

-- Create indexes for transcriptions table
CREATE INDEX IX_transcriptions_recording_id ON transcriptions(recording_id);
CREATE INDEX IX_transcriptions_job_id ON transcriptions(job_id);
CREATE INDEX IX_transcriptions_created_at ON transcriptions(created_at);

-- =====================================================
-- SUMMARIES TABLE
-- Stores AI-generated clinical summaries and notes
-- =====================================================
CREATE TABLE summaries (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    recording_id UNIQUEIDENTIFIER NOT NULL,
    transcription_id UNIQUEIDENTIFIER NOT NULL,
    job_id UNIQUEIDENTIFIER NOT NULL,
    summary_text NVARCHAR(MAX) NOT NULL,
    clinical_notes NVARCHAR(MAX) NULL,
    recommendations NVARCHAR(MAX) NULL,
    openai_response NVARCHAR(MAX) NULL, -- Full OpenAI response for debugging
    created_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_summaries_recording_id FOREIGN KEY (recording_id) REFERENCES recordings(id) ON DELETE CASCADE,
    CONSTRAINT FK_summaries_transcription_id FOREIGN KEY (transcription_id) REFERENCES transcriptions(id) ON DELETE CASCADE,
    CONSTRAINT FK_summaries_job_id FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE,
    CONSTRAINT UQ_summaries_recording UNIQUE (recording_id) -- One summary per recording
);

-- Create indexes for summaries table
CREATE INDEX IX_summaries_recording_id ON summaries(recording_id);
CREATE INDEX IX_summaries_transcription_id ON summaries(transcription_id);
CREATE INDEX IX_summaries_job_id ON summaries(job_id);
CREATE INDEX IX_summaries_created_at ON summaries(created_at);

-- =====================================================
-- APPOINTMENTS TABLE (CACHED SIKKA DATA)
-- Cached appointment data from Sikka API for faster lookups
-- =====================================================
CREATE TABLE appointments (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    sikka_id NVARCHAR(100) NOT NULL UNIQUE,
    patient_name NVARCHAR(200) NOT NULL,
    appointment_date DATETIME2 NOT NULL,
    appointment_time TIME NOT NULL,
    operatory NVARCHAR(100) NULL,
    provider NVARCHAR(200) NULL,
    appointment_type NVARCHAR(100) NULL,
    status NVARCHAR(50) NULL,
    notes NVARCHAR(MAX) NULL,
    patient_phone NVARCHAR(50) NULL,
    patient_email NVARCHAR(100) NULL,
    sikka_data NVARCHAR(MAX) NULL, -- Full Sikka JSON response
    last_sync DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    created_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

-- Create indexes for appointments table
CREATE INDEX IX_appointments_sikka_id ON appointments(sikka_id);
CREATE INDEX IX_appointments_patient_name ON appointments(patient_name);
CREATE INDEX IX_appointments_appointment_date ON appointments(appointment_date);
CREATE INDEX IX_appointments_provider ON appointments(provider) WHERE provider IS NOT NULL;
CREATE INDEX IX_appointments_operatory ON appointments(operatory) WHERE operatory IS NOT NULL;
CREATE INDEX IX_appointments_last_sync ON appointments(last_sync);

-- =====================================================
-- PROCESSING_LOGS TABLE
-- Comprehensive audit trail and debugging logs
-- =====================================================
CREATE TABLE processing_logs (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    recording_id UNIQUEIDENTIFIER NULL,
    job_id UNIQUEIDENTIFIER NULL,
    log_level NVARCHAR(50) NOT NULL CHECK (log_level IN ('info', 'warning', 'error', 'debug')),
    message NVARCHAR(1000) NOT NULL,
    error_details NVARCHAR(MAX) NULL,
    stack_trace NVARCHAR(MAX) NULL,
    metadata NVARCHAR(MAX) NULL, -- JSON metadata
    created_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_processing_logs_recording_id FOREIGN KEY (recording_id) REFERENCES recordings(id) ON DELETE SET NULL,
    CONSTRAINT FK_processing_logs_job_id FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE SET NULL
);

-- Create indexes for processing_logs table
CREATE INDEX IX_processing_logs_recording_id ON processing_logs(recording_id) WHERE recording_id IS NOT NULL;
CREATE INDEX IX_processing_logs_job_id ON processing_logs(job_id) WHERE job_id IS NOT NULL;
CREATE INDEX IX_processing_logs_log_level ON processing_logs(log_level);
CREATE INDEX IX_processing_logs_created_at ON processing_logs(created_at);

-- =====================================================
-- SYSTEM_SETTINGS TABLE
-- Application configuration and feature flags
-- =====================================================
CREATE TABLE system_settings (
    id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    setting_key NVARCHAR(100) NOT NULL UNIQUE,
    setting_value NVARCHAR(MAX) NULL,
    setting_type NVARCHAR(50) NOT NULL DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    description NVARCHAR(500) NULL,
    is_encrypted BIT NOT NULL DEFAULT 0,
    created_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    updated_at DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

-- Create index for system_settings table
CREATE INDEX IX_system_settings_key ON system_settings(setting_key);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('openai_model_transcription', 'whisper-1', 'string', 'OpenAI model for voice transcription'),
('openai_model_summary', 'gpt-4', 'string', 'OpenAI model for clinical summary generation'),
('max_file_size_mb', '500', 'number', 'Maximum file size for voice recordings in MB'),
('max_concurrent_jobs', '5', 'number', 'Maximum concurrent processing jobs'),
('job_timeout_minutes', '30', 'number', 'Job timeout in minutes'),
('enable_auto_retry', 'true', 'boolean', 'Enable automatic retry for failed jobs'),
('retention_days_logs', '90', 'number', 'Days to retain processing logs'),
('retention_days_completed', '365', 'number', 'Days to retain completed recordings');

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATED_AT
-- =====================================================

-- Trigger for recordings table
CREATE TRIGGER tr_recordings_updated_at
ON recordings
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE recordings 
    SET updated_at = GETUTCDATE()
    FROM recordings r
    INNER JOIN inserted i ON r.id = i.id;
END;
GO

-- Trigger for jobs table
CREATE TRIGGER tr_jobs_updated_at
ON jobs
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE jobs 
    SET updated_at = GETUTCDATE()
    FROM jobs j
    INNER JOIN inserted i ON j.id = i.id;
END;
GO

-- Trigger for transcriptions table
CREATE TRIGGER tr_transcriptions_updated_at
ON transcriptions
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE transcriptions 
    SET updated_at = GETUTCDATE()
    FROM transcriptions t
    INNER JOIN inserted i ON t.id = i.id;
END;
GO

-- Trigger for summaries table
CREATE TRIGGER tr_summaries_updated_at
ON summaries
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE summaries 
    SET updated_at = GETUTCDATE()
    FROM summaries s
    INNER JOIN inserted i ON s.id = i.id;
END;
GO

-- Trigger for appointments table
CREATE TRIGGER tr_appointments_updated_at
ON appointments
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE appointments 
    SET updated_at = GETUTCDATE()
    FROM appointments a
    INNER JOIN inserted i ON a.id = i.id;
END;
GO

-- Trigger for system_settings table
CREATE TRIGGER tr_system_settings_updated_at
ON system_settings
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE system_settings 
    SET updated_at = GETUTCDATE()
    FROM system_settings s
    INNER JOIN inserted i ON s.id = i.id;
END;
GO

-- =====================================================
-- STORED PROCEDURES FOR COMMON OPERATIONS
-- =====================================================

-- Get job queue ordered by priority and created date
CREATE PROCEDURE sp_GetJobQueue
    @Status NVARCHAR(50) = 'pending',
    @Limit INT = 50
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP (@Limit) 
        j.*,
        r.filename,
        r.patient_name,
        r.blob_path
    FROM jobs j
    INNER JOIN recordings r ON j.recording_id = r.id
    WHERE j.status = @Status
    ORDER BY 
        CASE j.priority
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2
            WHEN 'normal' THEN 3
            WHEN 'low' THEN 4
        END,
        j.created_at ASC;
END;
GO

-- Get processing statistics
CREATE PROCEDURE sp_GetProcessingStats
    @FromDate DATETIME2 = NULL,
    @ToDate DATETIME2 = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @FromDate IS NULL SET @FromDate = DATEADD(day, -30, GETUTCDATE());
    IF @ToDate IS NULL SET @ToDate = GETUTCDATE();
    
    SELECT 
        COUNT(*) as total_recordings,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_recordings,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_recordings,
        SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing_recordings,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_recordings,
        AVG(CASE 
            WHEN processed_at IS NOT NULL AND created_at IS NOT NULL 
            THEN DATEDIFF(second, created_at, processed_at) 
            ELSE NULL 
        END) as avg_processing_time_seconds,
        SUM(file_size) as total_file_size_bytes
    FROM recordings
    WHERE created_at BETWEEN @FromDate AND @ToDate;
END;
GO

-- Clean up old logs and completed recordings based on retention settings
CREATE PROCEDURE sp_CleanupOldData
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @LogRetentionDays INT, @CompletedRetentionDays INT;
    
    SELECT @LogRetentionDays = CAST(setting_value AS INT)
    FROM system_settings 
    WHERE setting_key = 'retention_days_logs';
    
    SELECT @CompletedRetentionDays = CAST(setting_value AS INT)
    FROM system_settings 
    WHERE setting_key = 'retention_days_completed';
    
    -- Clean up old logs
    DELETE FROM processing_logs 
    WHERE created_at < DATEADD(day, -@LogRetentionDays, GETUTCDATE());
    
    -- Archive old completed recordings (optional - may want to keep for compliance)
    -- DELETE FROM recordings 
    -- WHERE status = 'completed' 
    --   AND processed_at < DATEADD(day, -@CompletedRetentionDays, GETUTCDATE());
    
    -- Log cleanup operation
    INSERT INTO processing_logs (log_level, message, metadata, created_at)
    VALUES ('info', 'Automated cleanup completed', 
            JSON_OBJECT('log_retention_days', @LogRetentionDays, 'completed_retention_days', @CompletedRetentionDays),
            GETUTCDATE());
END;
GO

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- Complete recording information with latest job status
CREATE VIEW vw_RecordingStatus AS
SELECT 
    r.id as recording_id,
    r.filename,
    r.original_filename,
    r.patient_name,
    r.sikka_id,
    r.appointment_date,
    r.operatory,
    r.provider,
    r.file_size,
    r.status as recording_status,
    r.created_at as recording_created,
    r.processed_at,
    j.id as latest_job_id,
    j.job_type,
    j.status as job_status,
    j.priority,
    j.progress_percentage,
    j.error_message,
    j.started_at as job_started,
    j.completed_at as job_completed,
    t.transcription_text,
    s.summary_text,
    s.clinical_notes,
    s.recommendations
FROM recordings r
LEFT JOIN (
    SELECT recording_id, MAX(created_at) as latest_created
    FROM jobs
    GROUP BY recording_id
) latest_job ON r.id = latest_job.recording_id
LEFT JOIN jobs j ON r.id = j.recording_id AND j.created_at = latest_job.latest_created
LEFT JOIN transcriptions t ON r.id = t.recording_id
LEFT JOIN summaries s ON r.id = s.recording_id;
GO

-- Failed jobs with error details for monitoring
CREATE VIEW vw_FailedJobs AS
SELECT 
    j.id as job_id,
    j.recording_id,
    r.filename,
    r.patient_name,
    j.job_type,
    j.status,
    j.error_message,
    j.retry_count,
    j.max_retries,
    j.created_at,
    j.started_at,
    DATEDIFF(minute, j.created_at, GETUTCDATE()) as minutes_since_created
FROM jobs j
INNER JOIN recordings r ON j.recording_id = r.id
WHERE j.status = 'failed';
GO

-- Processing queue view for monitoring
CREATE VIEW vw_ProcessingQueue AS
SELECT 
    j.id as job_id,
    j.recording_id,
    r.filename,
    r.patient_name,
    r.file_size,
    j.job_type,
    j.status,
    j.priority,
    j.progress_percentage,
    j.created_at,
    j.started_at,
    DATEDIFF(minute, j.created_at, GETUTCDATE()) as minutes_in_queue,
    CASE 
        WHEN j.started_at IS NOT NULL 
        THEN DATEDIFF(minute, j.started_at, GETUTCDATE())
        ELSE NULL 
    END as minutes_processing
FROM jobs j
INNER JOIN recordings r ON j.recording_id = r.id
WHERE j.status IN ('pending', 'processing', 'transcribing', 'generating_summary')
ORDER BY 
    CASE j.priority
        WHEN 'urgent' THEN 1
        WHEN 'high' THEN 2
        WHEN 'normal' THEN 3
        WHEN 'low' THEN 4
    END,
    j.created_at ASC;
GO

-- =====================================================
-- SECURITY AND PERMISSIONS
-- =====================================================

-- Create application user (run after creating login)
-- CREATE USER [dental_app_user] FOR LOGIN [dental_app_login];

-- Grant necessary permissions to application user
-- GRANT SELECT, INSERT, UPDATE, DELETE ON recordings TO [dental_app_user];
-- GRANT SELECT, INSERT, UPDATE, DELETE ON jobs TO [dental_app_user];
-- GRANT SELECT, INSERT, UPDATE, DELETE ON transcriptions TO [dental_app_user];
-- GRANT SELECT, INSERT, UPDATE, DELETE ON summaries TO [dental_app_user];
-- GRANT SELECT, INSERT, UPDATE, DELETE ON appointments TO [dental_app_user];
-- GRANT SELECT, INSERT, UPDATE, DELETE ON processing_logs TO [dental_app_user];
-- GRANT SELECT, INSERT, UPDATE, DELETE ON system_settings TO [dental_app_user];
-- GRANT EXECUTE ON sp_GetJobQueue TO [dental_app_user];
-- GRANT EXECUTE ON sp_GetProcessingStats TO [dental_app_user];
-- GRANT EXECUTE ON sp_CleanupOldData TO [dental_app_user];
-- GRANT SELECT ON vw_RecordingStatus TO [dental_app_user];
-- GRANT SELECT ON vw_FailedJobs TO [dental_app_user];
-- GRANT SELECT ON vw_ProcessingQueue TO [dental_app_user];

-- =====================================================
-- SAMPLE DATA FOR TESTING (OPTIONAL)
-- =====================================================

-- Uncomment to insert sample data for testing
/*
-- Sample recording
INSERT INTO recordings (filename, original_filename, blob_url, blob_path, file_size, sikka_id, patient_name, status)
VALUES ('test_recording.mp3', 'appointment_audio.mp3', 'https://storage.blob.core.windows.net/container/test.mp3', 'test/test_recording.mp3', 1024000, 'SIKKA123', 'John Doe', 'pending');

-- Sample job
DECLARE @recording_id UNIQUEIDENTIFIER = (SELECT TOP 1 id FROM recordings);
INSERT INTO jobs (recording_id, job_type, status, priority)
VALUES (@recording_id, 'full_process', 'pending', 'normal');
*/

PRINT 'Azure SQL Database schema created successfully!';
PRINT 'Tables created: recordings, jobs, transcriptions, summaries, appointments, processing_logs, system_settings';
PRINT 'Views created: vw_RecordingStatus, vw_FailedJobs, vw_ProcessingQueue';
PRINT 'Stored procedures created: sp_GetJobQueue, sp_GetProcessingStats, sp_CleanupOldData';
PRINT 'Remember to configure user permissions and connection strings in your application.';
GO