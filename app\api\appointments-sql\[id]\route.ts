/**
 * SQL-based API route for fetching a single appointment by ID from cached database
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAppointmentBySikkaId } from '@/lib/database/index';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;

  if (!id) {
    return NextResponse.json(
      { error: 'Appointment ID is required' },
      { status: 400 }
    );
  }

  try {
    console.log(`API: Fetching appointment from database for ID: ${id}`);

    const appointment = await getAppointmentBySikkaId(id);

    if (!appointment) {
      return NextResponse.json(
        { error: 'Appointment not found' },
        { status: 404 }
      );
    }

    console.log(`API: Successfully fetched appointment from database`);

    // Transform database result to match existing API format
    const transformedAppointment = {
      id: appointment.sikka_id,
      sikka_id: appointment.sikka_id,
      patient_name: appointment.patient_name,
      patientName: appointment.patient_name, // alias for compatibility
      appointment_date: appointment.appointment_date,
      appointmentDate: appointment.appointment_date, // alias for compatibility
      appointment_time: appointment.appointment_time,
      appointmentTime: appointment.appointment_time, // alias for compatibility
      operatory: appointment.operatory,
      provider: appointment.provider,
      appointment_type: appointment.appointment_type,
      appointmentType: appointment.appointment_type, // alias for compatibility
      status: appointment.status,
      notes: appointment.notes,
      patient_phone: appointment.patient_phone,
      patientPhone: appointment.patient_phone, // alias for compatibility
      patient_email: appointment.patient_email,
      patientEmail: appointment.patient_email, // alias for compatibility
      last_sync: appointment.last_sync,
      lastSync: appointment.last_sync, // alias for compatibility
      created_at: appointment.created_at,
      updated_at: appointment.updated_at
    };

    return NextResponse.json(transformedAppointment);

  } catch (error) {
    console.error('Database error fetching appointment:', error);
    
    let errorMessage = 'Failed to fetch appointment from database';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('connect')) {
        errorMessage = 'Database connection failed';
        statusCode = 503;
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Database query timeout';
        statusCode = 504;
      }
    }

    return NextResponse.json(
      { 
        error: errorMessage,
        details: error instanceof Error ? error.message : 'Unknown database error'
      },
      { status: statusCode }
    );
  }
}
