/**
 * Clinical Prompts Management System
 * Handles prompt templates and OpenAI model configurations for clinical note processing
 */

export interface ClinicalPrompt {
  id: string;
  name: string;
  description: string;
  type: 'transcription' | 'professionalization' | 'summarization';
  prompt: string;
  openaiModel: string;
  isDefault: boolean;
  variables?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface OpenAIModel {
  id: string;
  name: string;
  description: string;
  maxTokens: number;
  costPer1kTokens: number;
  recommended: boolean;
}

export const AVAILABLE_OPENAI_MODELS: OpenAIModel[] = [
  // Current GPT-4o Models (Real OpenAI Models)
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    description: 'Latest GPT-4o model for complex dental documentation',
    maxTokens: 128000,
    costPer1kTokens: 2.5,
    recommended: true
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    description: 'Cost-effective model for routine dental transcription and notes',
    maxTokens: 128000,
    costPer1kTokens: 0.15,
    recommended: true
  },
  {
    id: 'gpt-4o-2024-11-20',
    name: 'GPT-4o (2024-11-20)',
    description: 'Specific GPT-4o model version for enhanced capabilities',
    maxTokens: 128000,
    costPer1kTokens: 2.5,
    recommended: false
  },
  {
    id: 'gpt-4o-2024-08-06',
    name: 'GPT-4o (2024-08-06)',
    description: 'Stable GPT-4o model version for reliable documentation',
    maxTokens: 128000,
    costPer1kTokens: 2.5,
    recommended: false
  },
  {
    id: 'gpt-4o-mini-2024-07-18',
    name: 'GPT-4o Mini (2024-07-18)',
    description: 'Specific GPT-4o Mini model version',
    maxTokens: 128000,
    costPer1kTokens: 0.15,
    recommended: false
  },
  // Reasoning Models (Real OpenAI Models)
  {
    id: 'o1',
    name: 'o1',
    description: 'Advanced reasoning model for complex clinical decisions',
    maxTokens: 128000,
    costPer1kTokens: 15.0,
    recommended: false
  },
  {
    id: 'o1-mini',
    name: 'o1 Mini',
    description: 'Cost-effective reasoning model for clinical analysis',
    maxTokens: 128000,
    costPer1kTokens: 3.0,
    recommended: false
  },
  {
    id: 'o1-2024-12-17',
    name: 'o1 (2024-12-17)',
    description: 'Specific o1 model version',
    maxTokens: 128000,
    costPer1kTokens: 15.0,
    recommended: false
  },
  {
    id: 'o1-mini-2024-09-12',
    name: 'o1-mini (2024-09-12)',
    description: 'Specific o1-mini model version',
    maxTokens: 128000,
    costPer1kTokens: 3.0,
    recommended: false
  },
  // Transcription Models (Real OpenAI Models)
  {
    id: 'whisper-1',
    name: 'Whisper-1',
    description: 'OpenAI Whisper model for audio transcription',
    maxTokens: 25000000, // 25MB file limit
    costPer1kTokens: 0.006,
    recommended: true
  },
  // Legacy Models (Real OpenAI Models)
  {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    description: 'Previous generation high performance model',
    maxTokens: 128000,
    costPer1kTokens: 10.0,
    recommended: false
  },
  {
    id: 'gpt-4',
    name: 'GPT-4',
    description: 'Original GPT-4 model',
    maxTokens: 8192,
    costPer1kTokens: 30.0,
    recommended: false
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    description: 'Fast and cost-effective model for simple tasks',
    maxTokens: 16385,
    costPer1kTokens: 0.5,
    recommended: false
  }
];

const DEFAULT_PROMPTS: ClinicalPrompt[] = [
  {
    id: 'dental-transcription-context',
    name: 'Dental Transcription with Context',
    description: 'Dental-specific transcription with US tooth numbering and clinical context',
    type: 'transcription',
    openaiModel: 'whisper-1',
    isDefault: true,
    variables: [],
    prompt: `Context: Dental visit in the United States. Universal tooth numbering system (1-32) is used. Expect numbers, millimetre pocket depths, dental materials, medication names, and consent language. Focus on Doctor and Patient voices primarily.

UNCERTAINTY HANDLING: Use [UNCLEAR] for inaudible words, [UNCERTAIN: content] when not confident, and [INAUDIBLE] for completely unclear sections. Always indicate uncertainty rather than guessing.`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'speaker-identification',
    name: 'Speaker Identification with Dental Context',
    description: 'Transcription with speaker identification and comprehensive dental terminology',
    type: 'transcription',
    openaiModel: 'gpt-4o-mini-transcribe',
    isDefault: false,
    variables: [],
    prompt: `Context: This is a dental appointment recording in the United States. Please transcribe with speaker identification.

DENTAL CONTEXT:
- Universal tooth numbering system (1-32): #1-16 upper jaw, #17-32 lower jaw
- Common procedures: cleaning, filling, crown, root canal, extraction, scaling, polishing
- Materials: composite, amalgam, porcelain, gold, ceramic, resin
- Measurements: pocket depths in millimeters (1-10mm), bleeding on probing
- Anatomy: mesial, distal, buccal, lingual, occlusal, cervical, apex, pulp
- Conditions: caries, gingivitis, periodontitis, plaque, tartar, calculus
- Tools: probe, explorer, scaler, curette, handpiece, suction

SPEAKER LABELS:
[DOCTOR] - Dentist/oral surgeon
[PATIENT] - Patient receiving treatment
[HYGIENIST] - Dental hygienist
[ASSISTANT] - Dental assistant
[STAFF] - Other office staff
[UNKNOWN] - When speaker cannot be identified

FORMAT: "[SPEAKER]: dialogue text"

UNCERTAINTY HANDLING:
- Use [UNKNOWN] when speaker identity is unclear
- Use [UNCLEAR] for inaudible words: "tooth [UNCLEAR] needs attention"
- Use [UNCERTAIN: word/phrase] for questionable content: "pocket depth [UNCERTAIN: 4mm]"
- Use [BACKGROUND] for distant/unclear voices: "[BACKGROUND: muffled conversation]"
- Use [INAUDIBLE] for completely unclear sections: "[INAUDIBLE - 3 seconds]"

Focus on accurate dental terminology, tooth numbers, procedure details, patient concerns, treatment plans, and clinical observations. Include speaker names when mentioned. Always indicate uncertainty rather than guessing.`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'detailed-medical',
    name: 'Detailed Medical Documentation',
    description: 'Comprehensive medical transcription for detailed clinical notes',
    type: 'transcription',
    openaiModel: 'gpt-4o-transcribe',
    isDefault: false,
    variables: [],
    prompt: `Context: Detailed dental medical documentation. Transcribe with maximum accuracy for clinical records.

MEDICAL DOCUMENTATION FOCUS:
- Patient medical history and medications
- Allergies and contraindications
- Vital signs and patient condition
- Detailed procedure descriptions
- Post-operative instructions
- Treatment outcomes and complications
- Insurance and billing discussions
- Consent forms and patient education

DENTAL SPECIFICS:
- Precise tooth numbering (Universal 1-32 system)
- Exact measurements (pocket depths, crown lengths, etc.)
- Material specifications and lot numbers
- Anesthesia types and dosages
- Radiographic findings
- Periodontal charting details
- Occlusion and bite analysis

ACCURACY REQUIREMENTS:
- Spell out numbers when unclear
- Include all medical terminology
- Capture dosages and measurements precisely
- Note any patient concerns or questions
- Document all staff interactions
- Include time references when mentioned

UNCERTAINTY HANDLING:
- Use [UNCLEAR] for inaudible words or phrases
- Use [UNCERTAIN: content] when not confident about specific terms
- Use [INAUDIBLE - duration] for completely unclear sections
- Use [BACKGROUND] for distant conversations
- Use [MEDICATION UNCLEAR] when drug names are uncertain
- Use [DOSAGE UNCERTAIN] when measurements are questionable
- Always indicate uncertainty rather than making assumptions

Transcribe everything spoken, including background conversations that may contain clinical information. Prioritize accuracy over completeness - it's better to mark something as unclear than to guess incorrectly.`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'speaker-identification',
    name: 'Speaker Identification',
    description: 'Transcription with speaker identification and naming',
    type: 'transcription',
    openaiModel: 'gpt-4o-mini-transcribe',
    isDefault: false,
    variables: [],
    prompt: `Context: Dental appointment recording. Please transcribe the audio and identify speakers. Use these speaker labels: [DOCTOR], [PATIENT], [ASSISTANT], [HYGIENIST]. Include speaker names when mentioned. Format: "[SPEAKER]: dialogue text". Focus on dental terminology, tooth numbers (1-32), procedures, and clinical observations.`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'default-transcription',
    name: 'Default Dental Transcription',
    description: 'Professional medical transcription for dental appointments',
    type: 'transcription',
    openaiModel: 'whisper-1',
    isDefault: false,
    variables: [],
    prompt: `Professional dental transcription. Use proper medical terminology, correct speech errors, include all clinical details.`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'detailed-transcription',
    name: 'Detailed Clinical Transcription',
    description: 'Comprehensive transcription with emphasis on clinical accuracy',
    type: 'transcription',
    openaiModel: 'whisper-1',
    isDefault: false,
    variables: [],
    prompt: `Detailed medical transcription for dental procedures. Focus on clinical accuracy, proper terminology, treatment details, patient responses.`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'dental-notes-clarifier-draft',
    name: 'Dental Notes Clarifier + Draft Writer (Prompt A)',
    description: 'Primary dental note generation with clarification and escalation flagging',
    type: 'professionalization',
    openaiModel: 'gpt-4o-mini',
    isDefault: true,
    variables: ['VOICE_NOTES', 'DENTRIX_NOTES', 'PATIENT_INFO', 'APPOINTMENT_INFO'],
    prompt: `You are Dental Note AI, an expert dental scribe and compliance officer.
Mission: produce a precise, legally safe note **only after** every required detail is confirmed.

Required fields ─ Doctor, Assistant, Visit reason, Medical history review, Radiographs (type + justification), Clinical findings, Diagnoses, Treatment plan (with alternatives), Procedures (step-by-step), Patient communication (consent / risks / benefits / instructions), Next visit.

Rules
1. If any required item is missing or unclear, ask ONE direct question and wait.
2. Repeat until all items are complete.
3. Draft the note using the template below.
4. Add \`EscalationFlag:\` at the very end in ALL-CAPS:
     LOW    – single straightforward procedure, no complications
     MEDIUM – multi-procedure, moderate medical history, or extra insurance justification
     HIGH   – contradictions, medico-legal red flags, advanced surgeries, unclear info

Formatting
• plain ASCII (no smart quotes)
• one blank line between sections
• "- " bullets, two-space indents, no asterisks
• **Use universal (USA) tooth numbering 1 – 32 whenever you reference teeth.**

Template

Doctor:
Assistant:
Reason for visit:
Medical history review:
Radiographs taken (type and justification):
Clinical findings (tooth-specific):
Diagnoses (dental / pulpal / apical):
Treatment plan (with alternatives):
Procedures performed (step-by-step):
Patient communication (consent, risks / benefits, instructions):
Next visit / follow-up:

**Voice Transcription:**
[VOICE_NOTES]

**Existing Dentrix Notes:**
[DENTRIX_NOTES]

**Patient Information:**
[PATIENT_INFO]

**Appointment Information:**
[APPOINTMENT_INFO]

EscalationFlag: LOW`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'default-professionalization',
    name: 'Default Clinical Note Professionalization',
    description: 'Combines voice transcriptions and existing notes into professional clinical documentation',
    type: 'professionalization',
    openaiModel: 'gpt-4o-mini-2024-07-18',
    isDefault: false,
    variables: ['VOICE_NOTES', 'DENTRIX_NOTES', 'PATIENT_INFO', 'APPOINTMENT_INFO'],
    prompt: `You are a professional dental clinical documentation specialist. Your task is to create a comprehensive, professional clinical note by combining voice transcriptions from dental appointments with existing Dentrix medical notes.

**Instructions:**
1. Combine the voice transcription and existing notes into a cohesive, professional clinical record
2. Use proper dental terminology and SOAP format when appropriate
3. Maintain accuracy - do not add information not present in the source materials
4. Organize information logically (chief complaint, examination findings, diagnosis, treatment, plan)
5. Ensure the note is suitable for inclusion in a patient's permanent dental record
6. Remove any redundant information between voice notes and existing notes
7. Maintain professional tone throughout

**Voice Transcription:**
[VOICE_NOTES]

**Existing Dentrix Notes:**
[DENTRIX_NOTES]

**Patient Information:**
[PATIENT_INFO]

**Appointment Information:**
[APPOINTMENT_INFO]

Please create a professional clinical note that combines this information appropriately:`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'dental-notes-senior-reviewer',
    name: 'Senior Risk Reviewer (Prompt B)',
    description: 'O1 model for complex dental case review and escalation',
    type: 'professionalization',
    openaiModel: 'o1-mini',
    isDefault: false,
    variables: ['RAW_NOTES', 'DRAFT_NOTE', 'ESCALATION_FLAG'],
    prompt: `You are the Senior Risk Reviewer for dental clinical documentation. Your role is to review complex cases that have been escalated from the primary AI system.

**Your Task:**
Review the draft note and original voice transcription for:
1. Medical-legal compliance and risk factors
2. Clinical accuracy and completeness
3. Proper documentation standards
4. Risk mitigation recommendations

**Original Voice Notes:**
[RAW_NOTES]

**Draft Note from Primary AI:**
[DRAFT_NOTE]

**Escalation Reason:**
[ESCALATION_FLAG]

**Instructions:**
- If the draft is acceptable with minor edits, provide the corrected version
- If significant issues exist, flag for human review with specific concerns
- Ensure all high-risk procedures are properly documented
- Verify consent documentation is adequate
- Check for any contradictions or missing critical information

**Response Format:**
Status: OK | NEEDS_HUMAN_REVIEW
Final Note: [corrected note or original if acceptable]
Review Comments: [specific feedback for improvement]
Risk Factors: [any identified risks that need attention]`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'soap-format',
    name: 'SOAP Format Clinical Note',
    description: 'Structures clinical information using SOAP (Subjective, Objective, Assessment, Plan) format',
    type: 'professionalization',
    openaiModel: 'gpt-4o-mini',
    isDefault: false,
    variables: ['VOICE_NOTES', 'DENTRIX_NOTES', 'PATIENT_INFO'],
    prompt: `Create a professional dental clinical note using SOAP format (Subjective, Objective, Assessment, Plan) based on the provided information.

**SOAP Format Guidelines:**
- **Subjective:** Patient's chief complaint, symptoms, history as reported by patient
- **Objective:** Clinical findings, examination results, measurements, observations
- **Assessment:** Diagnosis, clinical impression, risk factors
- **Plan:** Treatment recommendations, follow-up, patient education, prescriptions

**Source Information:**

Voice Transcription:
[VOICE_NOTES]

Existing Notes:
[DENTRIX_NOTES]

Patient Info:
[PATIENT_INFO]

Format the clinical note using clear SOAP headings:`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'brief-summary',
    name: 'Brief Clinical Summary',
    description: 'Creates concise clinical summaries for quick reference',
    type: 'summarization',
    openaiModel: 'gpt-4o-mini-2024-07-18',
    isDefault: false,
    variables: ['VOICE_NOTES', 'DENTRIX_NOTES'],
    prompt: `Create a brief, professional clinical summary (2-3 sentences) that captures the essential information from this dental appointment.

Voice Notes: [VOICE_NOTES]
Existing Notes: [DENTRIX_NOTES]

Focus on: chief complaint, key findings, treatment provided, and follow-up needed.`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

export class ClinicalPromptsManager {
  private static prompts: ClinicalPrompt[] = [...DEFAULT_PROMPTS];

  static getAllPrompts(): ClinicalPrompt[] {
    return this.prompts;
  }

  static getPromptById(id: string): ClinicalPrompt | undefined {
    return this.prompts.find(p => p.id === id);
  }

  static getPromptsByType(type: ClinicalPrompt['type']): ClinicalPrompt[] {
    return this.prompts.filter(p => p.type === type);
  }

  static getDefaultPrompt(type: ClinicalPrompt['type']): ClinicalPrompt | undefined {
    return this.prompts.find(p => p.type === type && p.isDefault);
  }

  static addPrompt(prompt: Omit<ClinicalPrompt, 'id' | 'createdAt' | 'updatedAt'>): ClinicalPrompt {
    const newPrompt: ClinicalPrompt = {
      ...prompt,
      id: `custom-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    this.prompts.push(newPrompt);
    return newPrompt;
  }

  static updatePrompt(id: string, updates: Partial<ClinicalPrompt>): ClinicalPrompt | null {
    const index = this.prompts.findIndex(p => p.id === id);
    if (index === -1) return null;

    this.prompts[index] = {
      ...this.prompts[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    return this.prompts[index];
  }

  static deletePrompt(id: string): boolean {
    const index = this.prompts.findIndex(p => p.id === id);
    if (index === -1) return false;

    this.prompts.splice(index, 1);
    return true;
  }

  static processPromptVariables(
    prompt: string, 
    variables: {
      voiceNotes?: string[];
      dentrixNotes?: string[];
      patientInfo?: any;
      appointmentInfo?: any;
    }
  ): string {
    let processedPrompt = prompt;

    // Replace voice notes
    if (variables.voiceNotes) {
      const voiceNotesText = variables.voiceNotes.join('\n\n');
      processedPrompt = processedPrompt.replace(/\[VOICE_NOTES\]/g, voiceNotesText);
    }

    // Replace Dentrix notes
    if (variables.dentrixNotes) {
      const dentrixNotesText = variables.dentrixNotes.join('\n\n');
      processedPrompt = processedPrompt.replace(/\[DENTRIX_NOTES\]/g, dentrixNotesText);
    }

    // Replace patient info
    if (variables.patientInfo) {
      const patientInfoText = JSON.stringify(variables.patientInfo, null, 2);
      processedPrompt = processedPrompt.replace(/\[PATIENT_INFO\]/g, patientInfoText);
    }

    // Replace appointment info
    if (variables.appointmentInfo) {
      const appointmentInfoText = JSON.stringify(variables.appointmentInfo, null, 2);
      processedPrompt = processedPrompt.replace(/\[APPOINTMENT_INFO\]/g, appointmentInfoText);
    }

    return processedPrompt;
  }
}
