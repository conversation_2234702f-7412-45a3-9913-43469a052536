{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../transcription-trigger/index.ts"], "names": [], "mappings": ";;;;;;;;;;;AAoBA,oDAwDC;AA5ED,gDAA0D;AAgB1D;;;GAGG;AACH,SAAsB,oBAAoB,CAAC,MAAc,EAAE,OAA0B;;;QACnF,MAAM,QAAQ,GAAG,MAAA,OAAO,CAAC,eAAe,0CAAE,IAAc,CAAC;QACzD,MAAM,QAAQ,GAAG,MAAA,OAAO,CAAC,eAAe,0CAAE,GAAa,CAAC;QAExD,OAAO,CAAC,GAAG,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,iBAAiB,QAAQ,EAAE,CAAC,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,iBAAiB,MAAM,CAAC,MAAM,QAAQ,CAAC,CAAC;QAEpD,IAAI,CAAC;YACH,iCAAiC;YACjC,MAAM,eAAe,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YACnF,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;YAElF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;gBACvD,OAAO;YACT,CAAC;YAED,sBAAsB;YACtB,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAC9B,QAAQ,CAAC,QAAQ,CAAC,qBAAqB,CAAC;gBACxC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACxC,OAAO,CAAC,GAAG,CAAC,8BAA8B,QAAQ,EAAE,CAAC,CAAC;gBACtD,OAAO;YACT,CAAC;YAED,2BAA2B;YAC3B,MAAM,KAAK,GAAG,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YAE9E,MAAM,gBAAgB,GAAqB;gBACzC,EAAE,EAAE,KAAK;gBACT,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,MAAM,CAAC,MAAM;gBACvB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE;oBACR,KAAK,EAAE,WAAW,EAAE,oDAAoD;oBACxE,QAAQ,EAAE,8BAA8B;oBACxC,uBAAuB,EAAE,IAAI;iBAC9B;aACF,CAAC;YAEF,kCAAkC;YAClC,iDAAiD;YACjD,iDAAiD;YAEjD,OAAO,CAAC,GAAG,CAAC,+BAA+B,KAAK,cAAc,QAAQ,EAAE,CAAC,CAAC;YAC1E,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAE5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,uCAAuC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAEvE,wEAAwE;YACxE,sCAAsC;QACxC,CAAC;IACH,CAAC;CAAA;AAAA,CAAC;AAEF,gDAAgD;AAChD,eAAG,CAAC,WAAW,CAAC,sBAAsB,EAAE;IACtC,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,qBAAqB;IACjC,OAAO,EAAE,oBAAoB;CAC9B,CAAC,CAAC"}