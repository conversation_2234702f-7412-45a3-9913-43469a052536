#!/usr/bin/env python3
"""
Test script to find the best way to fetch procedure codes from Sikka API.
This script tries different endpoints and parameters to find what works best.
"""

import json
import requests
import sys
from datetime import datetime

# API configuration
API_BASE = "https://api.sikkasoft.com/v1"
API_BASE_V2 = "https://api.sikkasoft.com/v2"
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_TIMEOUT = 30  # seconds

def load_credentials():
    """Load API credentials from credentials.json file."""
    try:
        with open("credentials.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: credentials.json file not found.")
        sys.exit(1)
    except json.JSONDecodeError:
        print("Error: Invalid JSON in credentials.json file.")
        sys.exit(1)

def authenticate(app_id, app_key, office_id, secret_key):
    """Authenticate with Sikka API and get request key."""
    print("Authenticating with Sikka API...")
    
    try:
        auth_resp = requests.post(
            f"{API_BASE_V4}/request_key",
            headers={"Content-Type": "application/json"},
            json={
                "grant_type": "request_key",
                "office_id": office_id,
                "secret_key": secret_key,
                "app_id": app_id,
                "app_key": app_key
            },
            timeout=API_TIMEOUT
        )
        
        if auth_resp.status_code == 200:
            data = auth_resp.json()
            request_key = data.get("request_key")
            if request_key:
                print("Authentication successful!")
                return request_key
            else:
                print("Error: No request key in response.")
                print(data)
        else:
            print(f"Authentication failed with status code: {auth_resp.status_code}")
            print(auth_resp.text)
    except Exception as e:
        print(f"Error during authentication: {e}")
    
    sys.exit(1)

def test_procedure_endpoints(request_key, target_date):
    """Test different endpoints and parameters to find procedure codes."""
    headers = {"Request-Key": request_key}
    
    # Test different endpoints that might contain procedure codes
    endpoints = [
        (f"{API_BASE_V4}/transactions", "v4 transactions"),
        (f"{API_BASE_V2}/transactions", "v2 transactions"),
        (f"{API_BASE_V4}/procedures", "v4 procedures"),
        (f"{API_BASE_V2}/procedures", "v2 procedures")
    ]
    
    # Test different parameter combinations
    param_sets = [
        {"date": target_date},
        {"startdate": target_date, "enddate": target_date},
        {"date": target_date, "transaction_type": "Procedure"},
        {"date": target_date, "type": "Procedure"}
    ]
    
    for endpoint, name in endpoints:
        print(f"\nTesting {name} endpoint: {endpoint}")
        
        for params in param_sets:
            print(f"  With parameters: {params}")
            
            try:
                resp = requests.get(
                    endpoint,
                    headers=headers,
                    params=params,
                    timeout=API_TIMEOUT
                )
                
                print(f"  Status code: {resp.status_code}")
                
                if resp.status_code == 200:
                    try:
                        data = resp.json()
                        
                        # Check if response is a list or has 'items' key
                        if isinstance(data, list):
                            items = data
                            if len(data) > 0 and isinstance(data[0], dict) and "items" in data[0]:
                                items = data[0].get("items", [])
                        else:
                            items = data.get("items", [])
                        
                        print(f"  Found {len(items)} items")
                        
                        # Check for procedure codes in the first few items
                        if items:
                            print("  Sample items:")
                            for i, item in enumerate(items[:3]):
                                print(f"    Item {i+1}:")
                                
                                # Look for procedure code fields
                                procedure_code = None
                                for key in ["procedure_code", "code", "cdt_code"]:
                                    if key in item:
                                        procedure_code = item[key]
                                        break
                                
                                if procedure_code:
                                    print(f"      Procedure code: {procedure_code}")
                                
                                # Look for description fields
                                description = None
                                for key in ["procedure_description", "description", "name"]:
                                    if key in item:
                                        description = item[key]
                                        break
                                
                                if description:
                                    print(f"      Description: {description}")
                                
                                # Look for amount fields
                                amount = None
                                for key in ["amount", "fee", "price"]:
                                    if key in item:
                                        amount = item[key]
                                        break
                                
                                if amount:
                                    print(f"      Amount: {amount}")
                                
                                # Print all keys for debugging
                                print(f"      All keys: {list(item.keys())}")
                    except json.JSONDecodeError:
                        print("  Response is not valid JSON")
                        print(f"  Raw response: {resp.text[:200]}...")
                else:
                    print(f"  Error response: {resp.text[:200]}...")
            except Exception as e:
                print(f"  Error: {e}")

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python test_procedure_codes.py YYYY-MM-DD")
        sys.exit(1)
    
    target_date = sys.argv[1]
    
    # Validate date format
    try:
        datetime.strptime(target_date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        sys.exit(1)
    
    # Load credentials
    credentials = load_credentials()
    app_id = credentials.get("app_id")
    app_key = credentials.get("app_key")
    office_id = credentials.get("office_id")
    secret_key = credentials.get("secret_key")
    
    # Authenticate and get request key
    request_key = authenticate(app_id, app_key, office_id, secret_key)
    
    # Test procedure endpoints
    test_procedure_endpoints(request_key, target_date)

if __name__ == "__main__":
    main()
