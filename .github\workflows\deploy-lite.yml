name: Deploy <PERSON><PERSON> (Key Vault Enabled)
on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging

jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 8
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Azure CLI Login
      uses: azure/login@v2
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS_JSON }}

    - name: Test Key Vault connectivity
      run: |
        echo "🔐 Testing Azure Key Vault connectivity..."
        npm run keyvault:test || echo "⚠️  Key Vault connectivity issues detected"

    - name: Create deployment package with Key Vault integration
      run: |
        echo "📦 Creating deployment package with Key Vault integration..."
        node scripts/package-for-zipdeploy.mjs

    - name: Deploy to Azure with Key Vault monitoring
      run: |
        echo "🚀 Deploying to Azure with Key Vault monitoring..."
        npx tsx scripts/await-zipdeploy.ts

    - name: Validate Key Vault integration
      run: |
        echo "🔍 Validating Key Vault integration..."
        # Wait for application to start
        sleep 10
        
        # Test Key Vault status
        RESPONSE=$(curl -s "https://dentalapp.azurewebsites.net/api/deployment-info" || echo '{"keyVault":{"status":"failed"}}')
        
        # Parse and display Key Vault status
        if echo "$RESPONSE" | grep -q '"status":"connected"'; then
          echo "✅ Key Vault integration: Connected"
        elif echo "$RESPONSE" | grep -q '"status":"degraded"'; then
          echo "⚠️  Key Vault integration: Degraded"
        else
          echo "❌ Key Vault integration: Failed"
        fi
        
        # Show managed identity status
        if echo "$RESPONSE" | grep -q '"managedIdentity":"authenticated"'; then
          echo "✅ Managed Identity: Authenticated"
        else
          echo "⚠️  Managed Identity: Check configuration"
        fi

    - name: Deployment Summary
      run: |
        echo "🎉 Deployment completed with Key Vault integration!"
        echo "🌐 Application URL: https://dentalapp.azurewebsites.net"
        echo "🔐 Key Vault: key1000"
        echo "📊 Health check: https://dentalapp.azurewebsites.net/api/deployment-info"

    - name: Deployment Status
      if: always()
      run: |
        if [ ${{ job.status }} == 'success' ]; then
          echo "✅ Deployment succeeded with Key Vault integration"
        else
          echo "❌ Deployment failed - check logs and Key Vault configuration"
          echo "🔧 Troubleshooting:"
          echo "   - Check Azure CLI authentication"
          echo "   - Verify Key Vault permissions"
          echo "   - Check managed identity configuration"
          echo "   - Review App Service logs"
        fi

env:
  # Azure configuration
  AZURE_WEBAPP_NAME: dentalapp
  AZURE_RESOURCE_GROUP: dentalapp
  
  # Key Vault configuration
  AZURE_KEYVAULT_NAME: key1000
  
  # Node.js configuration
  NODE_ENV: production
  
  # Deployment settings
  SCM_DO_BUILD_DURING_DEPLOYMENT: false
  ENABLE_ORYX_BUILD: false
  
  # Security settings
  SCM_BASIC_AUTH: false
  
  # Key Vault references (examples - actual values come from App Service settings)
  OPENAI_API_KEY: "@Microsoft.KeyVault(SecretUri=https://key1000.vault.azure.net/secrets/OpenAIApiKey/)"
  AZURE_STORAGE_CONNECTION_STRING: "@Microsoft.KeyVault(SecretUri=https://key1000.vault.azure.net/secrets/AzureStorageConnectionString/)"
  SIKKA_API_KEY: "@Microsoft.KeyVault(SecretUri=https://key1000.vault.azure.net/secrets/SikkaApiKey/)"