<#
.SYNOPSIS
    Uploads all .mp3 files from USB drive to Azure Blob Storage and archives them locally.

.DESCRIPTION
    This script automatically detects USB drives, uploads all .mp3 files to Azure Blob Storage,
    and moves successfully uploaded files to an /archive folder on the USB drive.
    Supports dry-run mode with -WhatIf parameter.

.PARAMETER USBDriveLetter
    Specific USB drive letter to process (e.g., "E:"). If not specified, auto-detects USB drives.

.PARAMETER WhatIf
    Performs a dry-run showing what would be uploaded without actually doing it.

.PARAMETER Force
    Overwrites existing files in Azure Storage if they already exist.

.EXAMPLE
    .\Upload-USBRecordings.ps1
    Auto-detects USB drives and uploads all .mp3 files

.EXAMPLE
    .\Upload-USBRecordings.ps1 -USBDriveLetter "E:" -WhatIf
    Shows what would be uploaded from E: drive without actually uploading

.EXAMPLE
    .\Upload-USBRecordings.ps1 -Force
    Uploads files and overwrites any existing files in Azure Storage
#>

[CmdletBinding(SupportsShouldProcess)]
param(
    [Parameter(Mandatory=$false)]
    [string]$USBDriveLetter,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

# Azure Storage Configuration
$StorageAccountName = "dentalrecordings"
$ContainerName = "recordings"
$ClientId = "f830136a-8621-438b-a3cb-a260f8f3a089"
$ClientSecret = "****************************************"
$TenantId = "********-eb4a-4bac-ac7c-21c8ce299844"

# Global counters
$script:TotalFiles = 0
$script:SuccessfulUploads = 0
$script:FailedUploads = 0
$script:SkippedFiles = 0

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Get-USBDrives {
    Write-ColorOutput "🔍 Detecting USB drives..." "Cyan"
    
    $usbDrives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {
        $_.DriveType -eq 2 -and $_.Size -gt 0
    }
    
    if ($usbDrives) {
        foreach ($drive in $usbDrives) {
            $sizeGB = [math]::Round($drive.Size / 1GB, 2)
            $freeGB = [math]::Round($drive.FreeSpace / 1GB, 2)
            Write-ColorOutput "  📱 Found USB: $($drive.DeviceID) ($sizeGB GB, $freeGB GB free)" "Green"
        }
        return $usbDrives
    } else {
        Write-ColorOutput "❌ No USB drives detected" "Red"
        return $null
    }
}

function Get-AccessToken {
    Write-ColorOutput "🔐 Authenticating with Azure..." "Cyan"
    
    $body = @{
        grant_type    = "client_credentials"
        client_id     = $ClientId
        client_secret = $ClientSecret
        scope         = "https://storage.azure.com/.default"
    }
    
    try {
        $response = Invoke-RestMethod -Uri "https://login.microsoftonline.com/$TenantId/oauth2/v2.0/token" -Method Post -Body $body
        Write-ColorOutput "✅ Azure authentication successful" "Green"
        return $response.access_token
    }
    catch {
        Write-ColorOutput "❌ Azure authentication failed: $($_.Exception.Message)" "Red"
        throw
    }
}

function Get-MP3Files {
    param([string]$DrivePath)
    
    Write-ColorOutput "🎵 Scanning for .mp3 files in $DrivePath..." "Cyan"
    
    $mp3Files = Get-ChildItem -Path $DrivePath -Filter "*.mp3" -Recurse | Where-Object {
        $_.FullName -notlike "*\archive\*"
    }
    
    if ($mp3Files) {
        Write-ColorOutput "  📁 Found $($mp3Files.Count) .mp3 files" "Green"
        foreach ($file in $mp3Files) {
            $sizeMB = [math]::Round($file.Length / 1MB, 2)
            Write-ColorOutput "    🎵 $($file.Name) ($sizeMB MB)" "Gray"
        }
    } else {
        Write-ColorOutput "  📁 No .mp3 files found" "Yellow"
    }
    
    return $mp3Files
}

function Get-AzureBlobPath {
    param([System.IO.FileInfo]$File)
    
    # Extract date from filename (format: YYMMDD_HHMM.mp3)
    if ($File.BaseName -match '^(\d{6})_\d{4}') {
        $dateStr = $Matches[1]
        $year = "20" + $dateStr.Substring(0, 2)
        $month = $dateStr.Substring(2, 2)
        $day = $dateStr.Substring(4, 2)
        $dateFolder = "$year-$month-$day"
    } else {
        # Fallback to file creation date
        $dateFolder = $File.CreationTime.ToString("yyyy-MM-dd")
    }
    
    return "$dateFolder/device/$($File.Name)"
}

function Test-BlobExists {
    param(
        [string]$BlobPath,
        [string]$AccessToken
    )
    
    $uri = "https://$StorageAccountName.blob.core.windows.net/$ContainerName/$BlobPath"
    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "x-ms-version" = "2020-04-08"
    }
    
    try {
        Invoke-RestMethod -Uri $uri -Method Head -Headers $headers
        return $true
    }
    catch {
        return $false
    }
}

function Upload-FileToAzure {
    param(
        [System.IO.FileInfo]$File,
        [string]$BlobPath,
        [string]$AccessToken
    )
    
    $uri = "https://$StorageAccountName.blob.core.windows.net/$ContainerName/$BlobPath"
    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "x-ms-blob-type" = "BlockBlob"
        "x-ms-version" = "2020-04-08"
        "Content-Type" = "audio/mpeg"
    }
    
    try {
        $fileBytes = [System.IO.File]::ReadAllBytes($File.FullName)
        Invoke-RestMethod -Uri $uri -Method Put -Headers $headers -Body $fileBytes
        return $true
    }
    catch {
        Write-ColorOutput "    ❌ Upload failed: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Move-ToArchive {
    param([System.IO.FileInfo]$File)
    
    $archivePath = Join-Path $File.Directory.Root "archive"
    
    if (-not (Test-Path $archivePath)) {
        if ($PSCmdlet.ShouldProcess($archivePath, "Create archive directory")) {
            New-Item -Path $archivePath -ItemType Directory -Force | Out-Null
            Write-ColorOutput "    📁 Created archive directory: $archivePath" "Cyan"
        }
    }
    
    $destinationPath = Join-Path $archivePath $File.Name
    
    if ($PSCmdlet.ShouldProcess($File.FullName, "Move to archive")) {
        try {
            Move-Item -Path $File.FullName -Destination $destinationPath -Force
            Write-ColorOutput "    📦 Moved to archive: $($File.Name)" "Green"
            return $true
        }
        catch {
            Write-ColorOutput "    ❌ Failed to move to archive: $($_.Exception.Message)" "Red"
            return $false
        }
    }
    
    return $true
}

function Process-USBDrive {
    param(
        [string]$DrivePath,
        [string]$AccessToken
    )
    
    Write-ColorOutput "`n🚀 Processing USB drive: $DrivePath" "Magenta"
    
    $mp3Files = Get-MP3Files -DrivePath $DrivePath
    
    if (-not $mp3Files) {
        Write-ColorOutput "⏭️  No .mp3 files to process" "Yellow"
        return
    }
    
    $script:TotalFiles += $mp3Files.Count
    
    foreach ($file in $mp3Files) {
        $blobPath = Get-AzureBlobPath -File $file
        $sizeMB = [math]::Round($file.Length / 1MB, 2)
        
        Write-ColorOutput "`n  🎵 Processing: $($file.Name) ($sizeMB MB)" "White"
        Write-ColorOutput "    📍 Target: $blobPath" "Gray"
        
        # Check if file already exists in Azure
        if (-not $Force -and (Test-BlobExists -BlobPath $blobPath -AccessToken $AccessToken)) {
            Write-ColorOutput "    ⏭️  File already exists in Azure (use -Force to overwrite)" "Yellow"
            $script:SkippedFiles++
            continue
        }
        
        if ($PSCmdlet.ShouldProcess($blobPath, "Upload to Azure Storage")) {
            # Upload to Azure
            $uploadSuccess = Upload-FileToAzure -File $file -BlobPath $blobPath -AccessToken $AccessToken
            
            if ($uploadSuccess) {
                Write-ColorOutput "    ✅ Upload successful" "Green"
                $script:SuccessfulUploads++
                
                # Move to archive
                $archiveSuccess = Move-ToArchive -File $file
                if (-not $archiveSuccess) {
                    Write-ColorOutput "    ⚠️  Upload succeeded but archiving failed" "Yellow"
                }
            } else {
                $script:FailedUploads++
            }
        } else {
            Write-ColorOutput "    🔍 [DRY RUN] Would upload to: $blobPath" "Cyan"
            Write-ColorOutput "    🔍 [DRY RUN] Would move to archive after success" "Cyan"
        }
    }
}

function Show-Summary {
    Write-ColorOutput "`n" "White"
    Write-ColorOutput "📊 UPLOAD SUMMARY" "Magenta"
    Write-ColorOutput "=================" "Magenta"
    Write-ColorOutput "📁 Total files found: $script:TotalFiles" "White"
    Write-ColorOutput "✅ Successful uploads: $script:SuccessfulUploads" "Green"
    Write-ColorOutput "❌ Failed uploads: $script:FailedUploads" "Red"
    Write-ColorOutput "⏭️  Skipped files: $script:SkippedFiles" "Yellow"
    
    if ($WhatIfPreference) {
        Write-ColorOutput "`n🔍 This was a DRY RUN - no files were actually uploaded" "Cyan"
    }
}

# Main execution
try {
    Write-ColorOutput "🎵 USB Dental Recordings Upload Script" "Magenta"
    Write-ColorOutput "=====================================" "Magenta"
    
    if ($WhatIfPreference) {
        Write-ColorOutput "🔍 DRY RUN MODE - No files will be uploaded" "Cyan"
    }
    
    # Get USB drives
    if ($USBDriveLetter) {
        if (Test-Path $USBDriveLetter) {
            $usbDrives = @(@{DeviceID = $USBDriveLetter.TrimEnd(':') + ':'})
        } else {
            throw "Specified USB drive $USBDriveLetter not found"
        }
    } else {
        $usbDrives = Get-USBDrives
    }
    
    if (-not $usbDrives) {
        Write-ColorOutput "❌ No USB drives to process" "Red"
        exit 1
    }
    
    # Get Azure access token
    $accessToken = Get-AccessToken
    
    # Process each USB drive
    foreach ($drive in $usbDrives) {
        Process-USBDrive -DrivePath $drive.DeviceID -AccessToken $accessToken
    }
    
    # Show summary
    Show-Summary
    
    if ($script:FailedUploads -gt 0) {
        Write-ColorOutput "`n⚠️  Some uploads failed. Check the output above for details." "Yellow"
        exit 1
    } else {
        Write-ColorOutput "`n🎉 All operations completed successfully!" "Green"
        exit 0
    }
}
catch {
    Write-ColorOutput "`n❌ Script failed: $($_.Exception.Message)" "Red"
    exit 1
}
