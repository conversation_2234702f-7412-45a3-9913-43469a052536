/**
 * @deprecated This legacy API route is deprecated. Use /api/voice/recordings-sql instead.
 */

import { NextRequest, NextResponse } from 'next/server';

export async function GET(_request: NextRequest) {
  // This endpoint is deprecated - return 410 Gone with migration instructions
  return NextResponse.json({
    error: 'Endpoint deprecated',
    message: 'This legacy recordings endpoint is deprecated and has been removed.',
    migration: {
      newEndpoint: '/api/voice/recordings-sql',
      instructions: 'Please update your application to use the new SQL-based recordings endpoint.',
      documentation: 'See CLAUDE.md for migration guidance'
    }
  }, { 
    status: 410,
    headers: {
      'X-Deprecated-Endpoint': 'true',
      'X-Replacement-Endpoint': '/api/voice/recordings-sql'
    }
  });
}
