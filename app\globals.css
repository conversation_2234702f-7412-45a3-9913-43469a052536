@tailwind base;
@tailwind components;
@tailwind utilities;

/* Hide scrollbars */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Enhanced light/dark mode with CSS variables */
:root {
  --bg-color: #f8fafc;
  --text-color: #1e293b;
  --header-bg: #ffffff;
  --card-bg: #ffffff;
  --border-color: #e2e8f0;
  --button-bg: #f1f5f9;
  --button-text: #334155;
  --button-hover-bg: #e2e8f0;
  --input-bg: #ffffff;
  --input-border: #cbd5e1;
  --shadow-color: rgba(15, 23, 42, 0.08);
}

.dark {
  --bg-color: #0f172a;
  --text-color: #f1f5f9;
  --header-bg: #1e293b;
  --card-bg: #1e293b;
  --border-color: #475569;
  --button-bg: #334155;
  --button-text: #e2e8f0;
  --button-hover-bg: #475569;
  --input-bg: #1e293b;
  --input-border: #475569;
  --shadow-color: rgba(0, 0, 0, 0.25);
}

body {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Enhanced theming for common elements */
.min-h-screen {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
}

header {
  background-color: var(--header-bg) !important;
  border-bottom: 1px solid var(--border-color) !important;
  box-shadow: 0 1px 3px var(--shadow-color) !important;
}

/* Improved button and background styling */
.bg-gray-50 {
  background-color: var(--bg-color) !important;
}

.bg-gray-100, .bg-gray-200 {
  background-color: var(--button-bg) !important;
  color: var(--button-text) !important;
}

/* Removed hover effects for schedule page compatibility */

/* Enhanced text color hierarchy */
.text-gray-900 {
  color: var(--text-color) !important;
}

.text-gray-800 {
  color: var(--text-color) !important;
  opacity: 0.95;
}

.text-gray-700 {
  color: var(--text-color) !important;
  opacity: 0.9;
}

.text-gray-600 {
  color: var(--text-color) !important;
  opacity: 0.75;
}

.text-gray-500 {
  color: var(--text-color) !important;
  opacity: 0.65;
}

.text-gray-400 {
  color: var(--text-color) !important;
  opacity: 0.55;
}

/* Enhanced card and form styling */
.bg-white {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--border-color) !important;
}

.bg-gray-800 {
  background-color: var(--card-bg) !important;
}

/* Form input improvements */
input[type="text"], input[type="search"], input[type="email"], input[type="tel"],
textarea, select {
  background-color: var(--input-bg) !important;
  border-color: var(--input-border) !important;
  color: var(--text-color) !important;
}

input[type="text"]:focus, input[type="search"]:focus, input[type="email"]:focus,
input[type="tel"]:focus, textarea:focus, select:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Enhanced border styling */
.border-gray-200, .border-gray-300 {
  border-color: var(--border-color) !important;
}

.border-gray-600, .border-gray-700 {
  border-color: var(--border-color) !important;
}

/* Enhanced shadow system */
.shadow-sm {
  box-shadow: 0 1px 2px var(--shadow-color) !important;
}

.shadow {
  box-shadow: 0 1px 3px var(--shadow-color), 0 1px 2px var(--shadow-color) !important;
}

.shadow-md {
  box-shadow: 0 4px 6px -1px var(--shadow-color), 0 2px 4px -1px var(--shadow-color) !important;
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px var(--shadow-color), 0 4px 6px -2px var(--shadow-color) !important;
}

.shadow-xl {
  box-shadow: 0 20px 25px -5px var(--shadow-color), 0 10px 10px -5px var(--shadow-color) !important;
}

/* iPad and iPhone UI Improvements */
@media screen and (max-width: 1024px) {
  /* Touch-friendly minimum sizes */
  button, .button, input[type="button"], input[type="submit"] {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: 12px 16px !important;
  }
  
  /* Improved tap targets */
  .touch-manipulation {
    touch-action: manipulation !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
  }
  
  /* Better text sizing for mobile */
  .text-xs {
    font-size: 0.875rem !important; /* 14px instead of 12px */
  }
  
  .text-sm {
    font-size: 1rem !important; /* 16px instead of 14px */
  }
  
  /* Improved spacing */
  .space-y-2 > * + * {
    margin-top: 0.75rem !important;
  }
  
  .space-y-3 > * + * {
    margin-top: 1rem !important;
  }
  
  /* Better focus states for touch devices */
  button:focus, .button:focus, input:focus, textarea:focus, select:focus {
    outline: 2px solid #3b82f6 !important;
    outline-offset: 2px !important;
  }
}

/* iPad-specific improvements */
@media screen and (min-width: 768px) and (max-width: 1024px) {
  .container {
    max-width: 100% !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
  
  /* Two-column layouts work better on iPad */
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
  
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
}

/* Navigation Bar Improvements */
.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

nav {
  width: 100%;
  background-color: var(--header-bg);
  padding: 0.5rem 0;
  position: relative;
  box-sizing: border-box;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

nav::-webkit-scrollbar {
  display: none;
}

/* Navigation container */
.nav-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: max-content;
  min-width: 100%;
  padding: 0 1rem;
  margin: 0 auto;
  gap: 0.5rem;
  box-sizing: border-box;
}

/* Add padding to first and last items to prevent edge cutoff */
.nav-container::before,
.nav-container::after {
  content: '';
  min-width: 0.5rem;
  height: 1px;
  flex-shrink: 0;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.nav-container::-webkit-scrollbar {
  display: none;
}

/* Navigation items */
nav button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.75rem;
  margin: 0;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  min-width: 4rem;
  max-width: 8rem;
  height: 100%;
  color: var(--text-color);
  opacity: 0.7;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 0;
  line-height: 1.2;
  box-sizing: border-box;
  position: relative;
}

/* Active tab */
nav button[aria-current="page"] {
  background-color: rgba(59, 130, 246, 0.1);
  color: #1d4ed8;
  font-weight: 500;
  opacity: 1;
}

.dark nav button[aria-current="page"] {
  background-color: rgba(30, 58, 138, 0.5);
  color: #93c5fd;
}

/* Hover state */
nav button:hover {
  background-color: rgba(243, 244, 246, 0.5);
  opacity: 1;
}

.dark nav button:hover {
  background-color: rgba(55, 65, 81, 0.5);
}

/* Tab icons */
nav button svg {
  width: 1.25rem;
  height: 1.25rem;
  margin-bottom: 0.25rem;
  flex-shrink: 0;
}

/* Tab labels */
nav button span {
  font-size: 0.7rem;
  line-height: 1.1;
  padding: 0 0.125rem;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Tablet styles */
@media (min-width: 640px) {
  .nav-container {
    justify-content: flex-start;
    padding: 0 1.5rem;
  }

  nav button {
    flex-direction: row;
    padding: 0.5rem 0.75rem;
    min-width: auto;
    white-space: nowrap;
    flex-shrink: 0;
  }
  
  nav button svg {
    margin-bottom: 0;
    margin-right: 0.5rem;
  }
  
  nav button span {
    font-size: 0.875rem;
    white-space: nowrap;
    -webkit-line-clamp: 1;
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  .nav-container {
    justify-content: center;
    padding: 0 2rem;
  }
  
  nav button {
    padding: 0.5rem 1rem;
  }
  
  /* Add padding to the first and last items */
  .nav-container::before,
  .nav-container::after {
    content: '';
    min-width: 1rem;
    height: 1px;
    flex-shrink: 0;
  }
}

/* Ensure enough space for the navigation */
.min-h-\[48px\] {
  min-height: 3.5rem !important;
}

/* Date Picker Improvements */
.date-picker {
  gap: 4px;
}

/* Current day highlight */
.bg-blue-600 {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  position: relative;
  z-index: 1;
}

/* Date cells */
.date-cell {
  min-width: 40px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

/* Action Buttons */
.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 24px;
}

/* Individual buttons */
.action-button {
  padding: 12px 16px;
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

/* Visual Hierarchy */
.current-date {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e40af;
}

.today-button {
  background-color: #1e40af;
  color: white;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s;
}

.today-button:hover {
  background-color: #1e3a8a;
  transform: translateY(-1px);
}

/* Accessibility Improvements */
button:focus-visible {
  outline: 3px solid #3b82f6;
  outline-offset: 2px;
  z-index: 10;
}

/* Better contrast for text */
.text-slate-700 {
  color: #334155;
}

.dark .text-slate-300 {
  color: #e2e8f0;
}

/* Responsive improvements */
@media (max-width: 1024px) {
  .container {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .date-cell {
    min-width: 36px;
    min-height: 36px;
    font-size: 0.9rem;
  }
}

/* For mobile */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 12px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .action-buttons {
    grid-template-columns: 1fr;
  }
  
  /* Make navigation items more touch-friendly */
  nav button {
    min-width: 70px;
    padding: 0.5rem 0.25rem;
    font-size: 0.8rem;
  }
  
  /* Adjust date picker for mobile */
  .date-picker {
    gap: 2px;
  }
  
  .date-cell {
    min-width: 32px;
    min-height: 32px;
    font-size: 0.8rem;
  }
}

/* iPhone-specific improvements */
@media screen and (max-width: 480px) {
  /* Force single column on phone */
  .grid-cols-1, .md\:grid-cols-2, .lg\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }
  
  /* Larger text on small screens */
  .text-base {
    font-size: 1.125rem !important; /* 18px */
  }
  
  /* Better card spacing */
  .space-y-4 > * + * {
    margin-top: 1.5rem !important;
  }
  
  /* Hide scrollbars on mobile */
  .scrollbar-hide {
    -ms-overflow-style: none !important;
    scrollbar-width: none !important;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none !important;
  }
  
  /* Improved horizontal scrolling */
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch !important;
  }
}

/* Smooth scrolling for all devices */
html {
  scroll-behavior: smooth !important;
}

/* Better button hover states on touch devices */
@media (hover: hover) and (pointer: fine) {
  /* Normal hover styles only for mouse users */
  button:hover, .button:hover {
    transform: translateY(-1px) !important;
    transition: transform 0.1s ease !important;
  }
}

/* Remove hover effects on touch devices */
@media (hover: none) and (pointer: coarse) {
  button:hover, .button:hover {
    transform: none !important;
    transition: none !important;
  }
  
  /* Better active states for touch */
  button:active, .button:active {
    transform: translateY(1px) !important;
    transition: transform 0.1s ease !important;
  }
}

/* Schedule time labels - override global styles with stronger specificity */
.schedule-time-label,
span.schedule-time-label,
.schedule-time-label.text-xs,
.text-xs.font-medium.schedule-time-label {
  color: #374151 !important; /* Gray-700 for light mode */
  opacity: 1 !important; /* Override any opacity rules */
}

.dark .schedule-time-label,
.dark span.schedule-time-label,
.dark .schedule-time-label.text-xs,
.dark .text-xs.font-medium.schedule-time-label {
  color: #e5e7eb !important; /* Gray-200 for dark mode */
  opacity: 1 !important; /* Override any opacity rules */
}

/* Additional override for any gray text classes that might interfere */
.schedule-time-label.text-gray-500,
.schedule-time-label.text-gray-400,
.schedule-time-label.text-gray-600 {
  color: #374151 !important; /* Force light mode color */
}

.dark .schedule-time-label.text-gray-500,
.dark .schedule-time-label.text-gray-400,
.dark .schedule-time-label.text-gray-600 {
  color: #e5e7eb !important; /* Force dark mode color */
}

/* Enhanced time label override with maximum specificity */
.schedule-time-label-override {
  color: #374151 !important; /* Light mode color */
  opacity: 1 !important;
  font-weight: bold !important;
  font-size: 0.75rem !important;
}

.dark .schedule-time-label-override {
  color: #e5e7eb !important; /* Dark mode color */
  opacity: 1 !important;
  font-weight: bold !important;
  font-size: 0.75rem !important;
}

/* Disable hover effects on grid background cells only */
.schedule-grid-line {
  pointer-events: none !important;
}

.schedule-grid-line:hover {
  background: transparent !important;
  background-color: transparent !important;
  border-color: inherit !important;
}

/* Disable hover on operatory column backgrounds - but not time column */
.schedule-timeline > div:not(:first-child):hover {
  background-color: inherit !important;
}

/* Disable hover on the main timeline container background */
.schedule-timeline:hover {
  background-color: #d1d5db !important; /* gray-300 - darker, easier on eyes */
}

.dark .schedule-timeline:hover {
  background-color: #374151 !important; /* gray-700 */
}

/* Disable hover on time column - force no change */
.schedule-timeline > div:first-child:hover {
  background-color: #d1d5db !important; /* gray-300 - same as normal */
  transition: none !important;
  transform: none !important;
}

.dark .schedule-timeline > div:first-child:hover {
  background-color: #374151 !important; /* gray-700 - same as normal */
  transition: none !important;
  transform: none !important;
}

/* Appointment card borders - very thin top and bottom */
.schedule-appointment-card {
  border-top: 1px solid #1f2937 !important; /* Very dark gray for light mode */
  border-bottom: 1px solid #1f2937 !important;
  position: relative !important;
  z-index: 5 !important; /* Much higher than grid lines */
  margin-top: -1px !important; /* Overlap grid line to prevent double borders */
  margin-bottom: -1px !important;
}

.dark .schedule-appointment-card {
  border-top: 1px solid #6b7280 !important; /* Medium gray for dark mode */
  border-bottom: 1px solid #6b7280 !important;
}

/* Light mode appointment cards - subtle hover effect */
.schedule-appointment-card:hover {
  background-color: #e2e8f0 !important; /* Slightly darker gray on hover */
  opacity: 1 !important;
  z-index: 10 !important;
  transition: background-color 0.15s ease !important;
}

/* Dark mode appointment cards - no hover effect */
.dark .schedule-appointment-card:hover {
  background-color: #4b5563 !important; /* Keep same dark gray background */
  opacity: 1 !important;
  z-index: 10 !important;
}

.schedule-note-card:hover {
  background-color: #fde047 !important; /* Keep yellow background */
  opacity: 1 !important;
  z-index: 10 !important;
}

.dark .schedule-note-card:hover {
  background-color: #0c4a6e !important; /* Keep dark blue background */
  opacity: 1 !important;
  z-index: 10 !important;
}


