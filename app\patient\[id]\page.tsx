'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useTheme } from 'next-themes';
import { PageHeader } from '@/components/ui/page-header';
import { ClinicalNotesDisplay } from '@/components/clinical-notes/clinical-notes-display';
import { User, Calendar, FileText, ExternalLink } from 'lucide-react';

interface Patient {
  id: string;
  name: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  middleInitial?: string;
  preferredName?: string;
  dateOfBirth?: string;
  age?: string;
  gender?: string;
  genderInitial?: string;
  phone?: string;
  email?: string;
  lastVisit?: string;
  firstVisit?: string;
  recentVisitCount?: number;
  chartNumber?: string;
}

interface Visit {
  id: string;
  date: string;
  provider: string;
  appointmentType: string;
  status: string;
  operatory?: string;
  notes?: string;
  description?: string;
  procedures?: string[];
}

function PatientPageContent() {
  const router = useRouter();
  const params = useParams();
  const { theme } = useTheme();
  const id = params.id as string;

  const [patient, setPatient] = useState<Patient | null>(null);
  const [visits, setVisits] = useState<Visit[]>([]);
  const [selectedVisit, setSelectedVisit] = useState<Visit | null>(null);
  const [isLoadingPatient, setIsLoadingPatient] = useState(true);
  const [isLoadingVisits, setIsLoadingVisits] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load patient data
  useEffect(() => {
    if (id) {
      loadPatientData();
    }
  }, [id]);

  const loadPatientData = async () => {
    setIsLoadingPatient(true);
    setError(null);

    try {
      // Try to get patient by ID directly first
      const directResponse = await fetch(`/api/patients/${id}`);

      if (directResponse.ok) {
        const directData = await directResponse.json();
        if (directData.patient) {
          setPatient(directData.patient);
          loadVisits(id);
          return;
        }
      }

      // Fallback: Search by patient ID (some APIs might return results)
      const searchResponse = await fetch(`/api/patients/search?q=${id}`);

      if (searchResponse.ok) {
        const searchData = await searchResponse.json();
        const foundPatient = searchData.patients?.find((p: Patient) => p.id === id);

        if (foundPatient) {
          setPatient(foundPatient);
          loadVisits(id);
          return;
        }
      }

      // If still not found, try searching by partial ID
      if (id.length > 4) {
        const partialSearchResponse = await fetch(`/api/patients/search?q=${id.slice(-4)}`);
        if (partialSearchResponse.ok) {
          const partialData = await partialSearchResponse.json();
          const patientMatch = partialData.patients?.find((p: Patient) => p.id === id);
          if (patientMatch) {
            setPatient(patientMatch);
            loadVisits(id);
            return;
          }
        }
      }

      throw new Error('Patient not found');
    } catch (err) {
      console.error('Error loading patient:', err);
      setError(`Failed to load patient data for ID: ${id}. The patient may not exist or the search API may be unavailable.`);
    } finally {
      setIsLoadingPatient(false);
    }
  };

  const loadVisits = async (id: string) => {
    setIsLoadingVisits(true);
    try {
      const response = await fetch(`/api/patients/${id}/visits`);
      if (response.ok) {
        const data = await response.json();
        setVisits(data.visits || []);
      }
    } catch (err) {
      console.error('Error loading visits:', err);
    } finally {
      setIsLoadingVisits(false);
    }
  };

  const handleVisitSelect = (visit: Visit) => {
    setSelectedVisit(visit);
  };

  const handleBack = () => {
    if (selectedVisit) {
      setSelectedVisit(null);
    } else {
      router.back();
    }
  };

  const formatPatientName = (patient: Patient) => {
    const parts = [patient.firstName, patient.middleName, patient.lastName].filter(Boolean);
    return parts.join(' ');
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const handleOverjetClick = (chartNumber: string) => {
    window.open(`https://clinic.overjet.ai/app/fmx/dailypatients/${chartNumber}`, '_blank');
  };

  if (isLoadingPatient) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
        <PageHeader
          title="Dentalapp"
          showBackButton={true}
          onBackClick={() => router.back()}
        />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-300">Loading patient data...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error || !patient) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
        <PageHeader
          title="Dentalapp"
          showBackButton={true}
          onBackClick={() => router.back()}
        />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-6">
              <h3 className="text-lg font-semibold text-red-900 dark:text-red-100 mb-2">
                Error Loading Patient
              </h3>
              <p className="text-red-800 dark:text-red-200">
                {error || 'Patient not found'}
              </p>
              <button
                onClick={() => router.back()}
                className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Go Back
              </button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      <PageHeader
        title="Dentalapp"
        showBackButton={true}
        onBackClick={handleBack}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!selectedVisit ? (
          // Patient Details View
          <div className="max-w-6xl mx-auto">
            {/* Patient Information Header */}
            <div className="mb-6 p-6 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-sm">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                    <User className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                      {formatPatientName(patient)}
                    </h1>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-slate-600 dark:text-slate-400">
                      {patient.age && patient.genderInitial && (
                        <span>{patient.age}{patient.genderInitial}</span>
                      )}
                      {patient.dateOfBirth && (
                        <span>DOB: {formatDate(patient.dateOfBirth)}</span>
                      )}
                      {patient.phone && (
                        <span>{patient.phone}</span>
                      )}
                    </div>
                  </div>
                </div>
                
                {patient.chartNumber && (
                  <button
                    onClick={() => handleOverjetClick(patient.chartNumber!)}
                    className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    <ExternalLink className="h-4 w-4" />
                    <span>View in Overjet</span>
                  </button>
                )}
              </div>
            </div>

            {/* Recent Visits */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-sm p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  <h2 className="text-lg font-semibold text-slate-900 dark:text-white">
                    Recent Visits
                  </h2>
                </div>

                {isLoadingVisits ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-sm text-slate-600 dark:text-slate-400">Loading visits...</p>
                  </div>
                ) : visits.length > 0 ? (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {visits.slice(0, 10).map((visit) => (
                      <button
                        key={visit.id}
                        onClick={() => handleVisitSelect(visit)}
                        className="w-full text-left p-3 bg-slate-50 dark:bg-slate-700 hover:bg-slate-100 dark:hover:bg-slate-600 rounded-md transition-colors"
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium text-slate-900 dark:text-white">
                              {formatDate(visit.date)}
                            </p>
                            <p className="text-sm text-slate-600 dark:text-slate-400">
                              {visit.appointmentType}
                              {visit.provider && ` - ${visit.provider}`}
                              {visit.procedures && visit.procedures.length > 0 && (
                                <span className="block text-xs text-slate-500 dark:text-slate-500 mt-1">
                                  Procedures: {visit.procedures.join(', ')}
                                </span>
                              )}
                            </p>
                          </div>
                          <span className={`text-xs px-2 py-1 rounded ${
                            visit.status === 'Completed'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                              : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                          }`}>
                            {visit.status}
                          </span>
                        </div>
                      </button>
                    ))}
                  </div>
                ) : (
                  <p className="text-slate-600 dark:text-slate-400">No visits found</p>
                )}
              </div>

              {/* Clinical Notes Summary */}
              <div className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-sm p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <FileText className="h-5 w-5 text-green-600" />
                  <h2 className="text-lg font-semibold text-slate-900 dark:text-white">
                    Clinical Notes (Past 2 Years)
                  </h2>
                </div>

                <ClinicalNotesDisplay
                  patientId={patient.id}
                  title=""
                  showPagination={true}
                  showFilters={true}
                  isDarkMode={theme === 'dark'}
                  maxHeight="400px"
                />
              </div>
            </div>
          </div>
        ) : (
          // Visit Details View
          <div className="max-w-4xl mx-auto">
            <div className="mb-6 p-6 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                Visit Details - {formatDate(selectedVisit.date)}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-slate-600 dark:text-slate-400">
                    <strong>Type:</strong> {selectedVisit.appointmentType}
                  </p>
                  {selectedVisit.provider && (
                    <p className="text-slate-600 dark:text-slate-400">
                      <strong>Provider:</strong> {selectedVisit.provider}
                    </p>
                  )}
                  {selectedVisit.procedures && selectedVisit.procedures.length > 0 && (
                    <p className="text-slate-600 dark:text-slate-400">
                      <strong>Procedures:</strong> {selectedVisit.procedures.join(', ')}
                    </p>
                  )}
                </div>
                <div>
                  <p className="text-slate-600 dark:text-slate-400">
                    <strong>Status:</strong> {selectedVisit.status}
                  </p>
                  {selectedVisit.operatory && (
                    <p className="text-slate-600 dark:text-slate-400">
                      <strong>Operatory:</strong> {selectedVisit.operatory}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <ClinicalNotesDisplay
              patientId={patient.id}
              appointmentDate={selectedVisit.date}
              appointmentId={selectedVisit.id}
              title="Clinical Notes for this Visit"
              showPagination={false}
              showFilters={false}
              isDarkMode={theme === 'dark'}
            />
          </div>
        )}
      </main>
    </div>
  );
}

export default function PatientPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-300">Loading patient...</p>
        </div>
      </div>
    }>
      <PatientPageContent />
    </Suspense>
  );
}
