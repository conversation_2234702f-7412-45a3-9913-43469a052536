"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __asyncValues = (this && this.__asyncValues) || function (o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.scheduledProcessor = scheduledProcessor;
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING;
const CONTAINER_NAME = 'recordings';
/**
 * Scheduled Azure Function that runs every 10 minutes to:
 * 1. Process any missed files (in case blob trigger failed)
 * 2. Retry failed transcriptions (non-permanent failures)
 * 3. Update processing statistics
 * 4. Clean up old temporary files
 */
function scheduledProcessor(myTimer, context) {
    return __awaiter(this, void 0, void 0, function* () {
        const timestamp = new Date().toISOString();
        context.log(`🔄 Scheduled processor started at: ${timestamp}`);
        try {
            const blobServiceClient = storage_blob_1.BlobServiceClient.fromConnectionString(AZURE_STORAGE_CONNECTION_STRING);
            const containerClient = blobServiceClient.getContainerClient(CONTAINER_NAME);
            // 1. Scan for unprocessed audio files
            yield scanForUnprocessedFiles(context, containerClient);
            // 2. Update processing statistics for the UI
            yield updateProcessingStatistics(context, containerClient);
            // 3. Retry failed transcriptions (if they're retryable)
            yield retryFailedTranscriptions(context, containerClient);
            // 4. Clean up old temporary files (optional)
            yield cleanupOldFiles(context, containerClient);
            context.log(`✅ Scheduled processor completed successfully`);
        }
        catch (error) {
            context.log(`❌ Scheduled processor failed:`, error);
        }
    });
}
;
/**
 * Scan for audio files that don't have corresponding .json or .failure.json files
 */
function scanForUnprocessedFiles(context, containerClient) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, e_1, _b, _c;
        context.log(`🔍 Scanning for unprocessed files...`);
        const audioFiles = [];
        const processedFiles = new Set();
        const failedFiles = new Set();
        try {
            // Get all blobs
            for (var _d = true, _e = __asyncValues(containerClient.listBlobsFlat()), _f; _f = yield _e.next(), _a = _f.done, !_a; _d = true) {
                _c = _f.value;
                _d = false;
                const blob = _c;
                const fileName = blob.name;
                if (isAudioFile(fileName)) {
                    audioFiles.push(fileName);
                }
                else if (fileName.endsWith('.json') && !fileName.endsWith('.failure.json')) {
                    const baseName = fileName.replace(/\.json$/, '');
                    processedFiles.add(baseName);
                }
                else if (fileName.endsWith('.failure.json')) {
                    const baseName = fileName.replace(/\.failure\.json$/, '');
                    failedFiles.add(baseName);
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (!_d && !_a && (_b = _e.return)) yield _b.call(_e);
            }
            finally { if (e_1) throw e_1.error; }
        }
        // Find unprocessed files
        const unprocessedFiles = audioFiles.filter(fileName => {
            const baseName = fileName.replace(/\.[^/.]+$/, '');
            return !processedFiles.has(baseName) && !failedFiles.has(baseName);
        });
        context.log(`📊 Processing status:`);
        context.log(`  - Total audio files: ${audioFiles.length}`);
        context.log(`  - Processed: ${processedFiles.size}`);
        context.log(`  - Failed (permanent): ${failedFiles.size}`);
        context.log(`  - Unprocessed: ${unprocessedFiles.length}`);
        // Trigger processing for unprocessed files (up to 5 at a time to avoid overwhelming)
        const filesToProcess = unprocessedFiles.slice(0, 5);
        for (const fileName of filesToProcess) {
            context.log(`🎵 Triggering processing for missed file: ${fileName}`);
            yield triggerFileProcessing(context, fileName);
        }
    });
}
/**
 * Update processing statistics for the UI dashboard
 */
function updateProcessingStatistics(context, containerClient) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, e_2, _b, _c;
        const stats = {
            lastUpdated: new Date().toISOString(),
            totalFiles: 0,
            processed: 0,
            permanentlyFailed: 0,
            unprocessed: 0,
            processingQueue: 0,
            systemStatus: 'active',
            breakdown: {
                transcribed: 0,
                summarized: 0,
                complete: 0,
                validationFailed: 0,
                processingFailed: 0
            }
        };
        let audioFiles = 0;
        const processedFiles = new Set();
        const failedFiles = new Set();
        let completeFiles = 0;
        try {
            // Analyze all files
            for (var _d = true, _e = __asyncValues(containerClient.listBlobsFlat()), _f; _f = yield _e.next(), _a = _f.done, !_a; _d = true) {
                _c = _f.value;
                _d = false;
                const blob = _c;
                const fileName = blob.name;
                if (isAudioFile(fileName)) {
                    audioFiles++;
                    const baseName = fileName.replace(/\.[^/.]+$/, '');
                    // Check for completion status
                    const transcriptionFile = `${baseName}.json`;
                    const failureFile = `${baseName}.failure.json`;
                    try {
                        const transcriptionExists = yield checkFileExists(containerClient, transcriptionFile);
                        const failureExists = yield checkFileExists(containerClient, failureFile);
                        if (transcriptionExists) {
                            processedFiles.add(baseName);
                            // Check if it has both transcription and summary
                            const transcriptionBlob = containerClient.getBlobClient(transcriptionFile);
                            const download = yield transcriptionBlob.download(0);
                            const content = yield streamToBuffer(download.readableStreamBody);
                            const data = JSON.parse(content.toString());
                            if (data.transcription)
                                stats.breakdown.transcribed++;
                            if (data.summary)
                                stats.breakdown.summarized++;
                            if (data.transcription && data.summary) {
                                stats.breakdown.complete++;
                                completeFiles++;
                            }
                        }
                        else if (failureExists) {
                            failedFiles.add(baseName);
                            // Categorize failure type
                            const failureBlob = containerClient.getBlobClient(failureFile);
                            const download = yield failureBlob.download(0);
                            const content = yield streamToBuffer(download.readableStreamBody);
                            const failureData = JSON.parse(content.toString());
                            if (failureData.failureType === 'validation_failed') {
                                stats.breakdown.validationFailed++;
                            }
                            else {
                                stats.breakdown.processingFailed++;
                            }
                        }
                    }
                    catch (error) {
                        // File might be currently being processed or there was an error
                    }
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (!_d && !_a && (_b = _e.return)) yield _b.call(_e);
            }
            finally { if (e_2) throw e_2.error; }
        }
        stats.totalFiles = audioFiles;
        stats.processed = processedFiles.size;
        stats.permanentlyFailed = failedFiles.size;
        stats.unprocessed = audioFiles - processedFiles.size - failedFiles.size;
        // Save statistics to blob storage for UI consumption
        const statsFileName = '_processing_stats.json';
        yield uploadJsonToBlob(containerClient, statsFileName, stats);
        context.log(`📊 Updated processing statistics: ${stats.processed}/${stats.totalFiles} processed, ${stats.unprocessed} pending`);
    });
}
/**
 * Retry failed transcriptions that are marked as retryable
 */
function retryFailedTranscriptions(context, containerClient) {
    return __awaiter(this, void 0, void 0, function* () {
        context.log(`🔄 Checking for retryable failures...`);
        // Implementation would check for temporary failures and retry them
        // For now, we focus on permanent failures which shouldn't be retried
    });
}
/**
 * Clean up old temporary files
 */
function cleanupOldFiles(context, containerClient) {
    return __awaiter(this, void 0, void 0, function* () {
        // Clean up any temporary files older than 24 hours
        // This is optional and can be implemented later
    });
}
/**
 * Helper functions (same as in blob trigger)
 */
function isAudioFile(fileName) {
    const audioExtensions = ['.mp3', '.wav', '.m4a', '.mp4', '.webm', '.ogg', '.aac', '.flac'];
    const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return audioExtensions.includes(ext);
}
function checkFileExists(containerClient, fileName) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const blobClient = containerClient.getBlobClient(fileName);
            return yield blobClient.exists();
        }
        catch (_a) {
            return false;
        }
    });
}
function streamToBuffer(readableStream) {
    return __awaiter(this, void 0, void 0, function* () {
        return new Promise((resolve, reject) => {
            const chunks = [];
            readableStream.on('data', (data) => {
                chunks.push(data instanceof Buffer ? data : Buffer.from(data));
            });
            readableStream.on('end', () => {
                resolve(Buffer.concat(chunks));
            });
            readableStream.on('error', reject);
        });
    });
}
function uploadJsonToBlob(containerClient, fileName, data) {
    return __awaiter(this, void 0, void 0, function* () {
        const blobClient = containerClient.getBlockBlobClient(fileName);
        const jsonContent = JSON.stringify(data, null, 2);
        yield blobClient.upload(jsonContent, jsonContent.length, {
            blobHTTPHeaders: { blobContentType: 'application/json' }
        });
    });
}
function triggerFileProcessing(context, fileName) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const transcriptionUrl = process.env.TRANSCRIPTION_API_URL || 'https://dentalapp-5va08j5gu-suncoastdcs-projects.vercel.app/api/voice/transcribe';
            const response = yield fetch(transcriptionUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Internal-Call': 'true'
                },
                body: JSON.stringify({
                    fileName: fileName,
                    forceRetranscribe: false
                })
            });
            if (response.ok) {
                context.log(`✅ Successfully triggered processing for: ${fileName}`);
            }
            else {
                context.log(`⚠️ Failed to trigger processing for: ${fileName} - ${response.status}`);
            }
        }
        catch (error) {
            context.log(`❌ Error triggering processing for ${fileName}:`, error);
        }
    });
}
// Register the function with Azure Functions v4
functions_1.app.timer('scheduledProcessor', {
    schedule: '0 */10 * * * *', // Every 10 minutes
    handler: scheduledProcessor
});
//# sourceMappingURL=index.js.map