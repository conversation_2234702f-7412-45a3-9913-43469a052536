import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '../../../../src/lib/api/sikka-client';
import { loadCredentials } from '../../../../src/lib/api/credentials';
import OpenAI from 'openai';

// Lazy initialization of OpenAI client
function getOpenAIClient() {
  return new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
}

interface CompletedNote {
  appointmentId: string;
  patientName: string;
  provider: string;
  operatory: string;
  appointmentDate: string;
  appointmentType: string;
  existingNote?: string;
  transcription?: string;
  professionalizedNote: string;
  procedures?: string[];
  toothNumbers?: string[];
}

interface DayCompletionStatus {
  date: string;
  isComplete: boolean;
  totalAppointments: number;
  matchedRecordings: number;
  transcribedRecordings: number;
  readyForCopyPaste: boolean;
  completedNotes: CompletedNote[];
  missingItems: string[];
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    const forceGenerate = searchParams.get('force') === 'true';

    if (!date) {
      return NextResponse.json(
        { error: 'Date parameter is required' },
        { status: 400 }
      );
    }

    console.log(`Copy-paste mode check for date: ${date}`);

    // Initialize Sikka client
    const credentials = loadCredentials();
    const sikkaClient = new SikkaApiClient(credentials);
    await sikkaClient.authenticate();

    // Get appointments for the date
    const appointments = await sikkaClient.getAppointments(date);
    console.log(`Found ${appointments.length} appointments for ${date}`);

    // Get voice recordings for the date
    const recordingsResponse = await fetch(`${request.nextUrl.origin}/api/voice/recordings`);
    const recordingsData = await recordingsResponse.json();
    const allRecordings = recordingsData.recordings || [];

    // Filter recordings for this date
    const dateRecordings = allRecordings.filter((recording: any) => {
      const recordingDate = new Date(recording.createdAt).toISOString().split('T')[0];
      return recordingDate === date;
    });

    console.log(`Found ${dateRecordings.length} recordings for ${date}`);

    // Check completion status
    const matchedRecordings = dateRecordings.filter((r: any) => r.matchedAppointmentId);
    const transcribedRecordings = matchedRecordings.filter((r: any) => r.transcription);

    const completionStatus: DayCompletionStatus = {
      date,
      isComplete: false,
      totalAppointments: appointments.length,
      matchedRecordings: matchedRecordings.length,
      transcribedRecordings: transcribedRecordings.length,
      readyForCopyPaste: false,
      completedNotes: [],
      missingItems: []
    };

    // Check what's missing
    if (matchedRecordings.length < dateRecordings.length) {
      completionStatus.missingItems.push(`${dateRecordings.length - matchedRecordings.length} unmatched recordings`);
    }

    if (transcribedRecordings.length < matchedRecordings.length) {
      completionStatus.missingItems.push(`${matchedRecordings.length - transcribedRecordings.length} untranscribed recordings`);
    }

    // Determine if ready for copy-paste mode
    const hasAllRecordingsMatched = matchedRecordings.length === dateRecordings.length;
    const hasAllRecordingsTranscribed = transcribedRecordings.length === matchedRecordings.length;
    
    completionStatus.isComplete = hasAllRecordingsMatched && hasAllRecordingsTranscribed;
    completionStatus.readyForCopyPaste = completionStatus.isComplete || forceGenerate;

    // If ready for copy-paste mode, generate professionalized notes
    if (completionStatus.readyForCopyPaste) {
      console.log('Generating professionalized notes...');
      
      for (const recording of transcribedRecordings) {
        try {
          // Get the appointment details
          const appointment = appointments.find(apt => apt.id === recording.matchedAppointmentId);
          if (!appointment) continue;

          // Get existing clinical notes for this appointment/patient
          let existingNote = '';
          try {
            const notesResponse = await fetch(
              `${request.nextUrl.origin}/api/clinical-notes?appointment_id=${appointment.id}&patient_id=${appointment.patient_id}`
            );
            const notesData = await notesResponse.json();
            
            // Find the most relevant note (longest text, likely clinical note)
            const clinicalNotes = notesData.notes?.filter((note: any) => 
              note.text && note.text.length > 50 // At least a few sentences
            ) || [];
            
            if (clinicalNotes.length > 0) {
              // Get the longest note (most comprehensive)
              existingNote = clinicalNotes.reduce((longest: any, current: any) => 
                current.text.length > longest.text.length ? current : longest
              ).text;
            }
          } catch (error) {
            console.log(`No existing notes found for appointment ${appointment.id}`);
          }

          // Generate professionalized note
          const professionalizedNote = await generateProfessionalNote(
            recording.transcription,
            existingNote,
            appointment,
            recording
          );

          completedNotes.push({
            appointmentId: appointment.id,
            patientName: appointment.patient_name || recording.patientName,
            provider: appointment.provider || recording.provider,
            operatory: appointment.operatory,
            appointmentDate: appointment.date,
            appointmentType: appointment.description || appointment.type,
            existingNote: existingNote || undefined,
            transcription: recording.transcription,
            professionalizedNote,
            procedures: appointment.procedure_codes || [],
            toothNumbers: []
          });

        } catch (error) {
          console.error(`Error processing recording ${recording.id}:`, error);
        }
      }

      completionStatus.completedNotes = completedNotes;
    }

    return NextResponse.json(completionStatus);

  } catch (error) {
    console.error('Error in copy-paste mode check:', error);
    return NextResponse.json(
      { error: 'Failed to check copy-paste mode status' },
      { status: 500 }
    );
  }
}

async function generateProfessionalNote(
  transcription: string,
  existingNote: string,
  appointment: any,
  recording: any
): Promise<string> {
  try {
    const prompt = `You are a dental professional assistant. Create a comprehensive, professional clinical note by combining the voice transcription with any existing clinical notes.

APPOINTMENT DETAILS:
- Patient: ${appointment.patient_name}
- Date: ${appointment.date}
- Provider: ${appointment.provider}
- Appointment Type: ${appointment.description || appointment.type}
- Procedures: ${appointment.procedure_codes?.join(', ') || 'Not specified'}

EXISTING CLINICAL NOTE:
${existingNote || 'No existing clinical note found.'}

VOICE TRANSCRIPTION:
${transcription}

INSTRUCTIONS:
1. Create a professional, comprehensive clinical note that combines both sources
2. If there's an existing note, enhance it with information from the transcription
3. If no existing note, create a complete note based on the transcription
4. Use proper dental terminology and professional language
5. Organize information logically: Chief Complaint, Clinical Findings, Treatment Provided, Plan
6. Include specific details about procedures, tooth numbers, and clinical observations
7. Maintain HIPAA compliance - focus on clinical facts
8. Keep the note concise but comprehensive (2-4 paragraphs)

Format the note as a single, well-structured clinical entry suitable for copying into Dentrix.`;

    const openai = getOpenAIClient();
    const response = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: 'You are a professional dental clinical documentation assistant. Create clear, accurate, and comprehensive clinical notes.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 1000,
      temperature: 0.3
    });

    return response.choices[0]?.message?.content || 'Unable to generate professional note.';

  } catch (error) {
    console.error('Error generating professional note:', error);
    return `Professional note generation failed. Original transcription: ${transcription}`;
  }
}
