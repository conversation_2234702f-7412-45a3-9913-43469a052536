"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __asyncValues = (this && this.__asyncValues) || function (o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.transcriptionProcessor = transcriptionProcessor;
const functions_1 = require("@azure/functions");
const openai_1 = __importDefault(require("openai"));
const storage_blob_1 = require("@azure/storage-blob");
/**
 * Enhanced Azure Function that processes transcription jobs from the queue
 * Downloads audio from blob storage, transcribes with OpenAI, stores results, and updates job status
 */
function transcriptionProcessor(queueItem, context) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, e_1, _b, _c;
        var _d, _e, _f;
        const startTime = Date.now();
        const correlationId = `azure-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        // Enhanced logging with correlation ID and job details
        context.log(`🔄 [${correlationId}] Processing transcription job: ${queueItem.jobId}`);
        context.log(`📁 [${correlationId}] File: ${queueItem.filename} | Type: ${queueItem.type} | Priority: ${queueItem.priority}`);
        context.log(`🔁 [${correlationId}] Retry count: ${queueItem.retryCount} | Source: ${((_d = queueItem.metadata) === null || _d === void 0 ? void 0 : _d.source) || 'unknown'}`);
        let jobStatus = 'processing';
        let errorDetails;
        let transcriptionResult = '';
        let processingMetadata;
        try {
            // Update job status to processing with structured logging
            yield updateJobStatus(queueItem.jobId, {
                status: 'processing',
                startedAt: new Date().toISOString(),
                retryCount: queueItem.retryCount
            }, context, correlationId);
            // Validate required environment variables with structured error handling
            const requiredVars = ['OPENAI_API_KEY', 'AzureWebJobsStorage'];
            const missingVars = requiredVars.filter(varName => !process.env[varName]);
            if (missingVars.length > 0) {
                throw createStructuredError('MISSING_CONFIG', `Missing required environment variables: ${missingVars.join(', ')}`, 'configuration_error', false);
            }
            // Initialize OpenAI client with enhanced error handling
            const openai = new openai_1.default({
                apiKey: process.env.OPENAI_API_KEY,
            });
            // Initialize Azure Blob Storage client with validation
            const blobServiceClient = storage_blob_1.BlobServiceClient.fromConnectionString(process.env.AzureWebJobsStorage || '');
            const containerClient = blobServiceClient.getContainerClient('dentalrecordings');
            context.log(`📊 [${correlationId}] Environment validated and clients initialized`);
            // Download audio file from blob storage with enhanced error handling
            context.log(`📥 [${correlationId}] Downloading audio file: ${queueItem.containerPath}`);
            const blobClient = containerClient.getBlobClient(queueItem.containerPath);
            // Check if blob exists before downloading
            const blobExists = yield blobClient.exists();
            if (!blobExists) {
                throw createStructuredError('FILE_NOT_FOUND', `Audio file not found: ${queueItem.containerPath}`, 'file_not_found', false);
            }
            const downloadResponse = yield blobClient.download();
            if (!downloadResponse.readableStreamBody) {
                throw createStructuredError('DOWNLOAD_FAILED', 'Failed to download audio file from blob storage', 'storage_error', true);
            }
            // Convert stream to buffer with size validation
            const chunks = [];
            let totalSize = 0;
            const maxSize = 25 * 1024 * 1024; // 25MB limit for Whisper API
            try {
                for (var _g = true, _h = __asyncValues(downloadResponse.readableStreamBody), _j; _j = yield _h.next(), _a = _j.done, !_a; _g = true) {
                    _c = _j.value;
                    _g = false;
                    const chunk = _c;
                    totalSize += chunk.length;
                    if (totalSize > maxSize) {
                        throw createStructuredError('FILE_TOO_LARGE', `File size exceeds 25MB limit: ${(totalSize / 1024 / 1024).toFixed(2)}MB`, 'validation_error', false);
                    }
                    chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (!_g && !_a && (_b = _h.return)) yield _b.call(_h);
                }
                finally { if (e_1) throw e_1.error; }
            }
            const audioBuffer = Buffer.concat(chunks);
            context.log(`📦 [${correlationId}] Downloaded ${(totalSize / 1024 / 1024).toFixed(2)}MB file`);
            // Create File object for OpenAI API with proper validation
            const audioFile = new File([audioBuffer], queueItem.filename, {
                type: getContentType(queueItem.filename)
            });
            // Get transcription settings with defaults
            const settings = Object.assign({ model: 'whisper-1', promptId: 'dental-transcription-context', enableSpeakerSeparation: false }, (_e = queueItem.metadata) === null || _e === void 0 ? void 0 : _e.settings);
            // Get transcription prompt
            const transcriptionPrompt = getTranscriptionPrompt(settings.promptId);
            // Configure response format for speaker separation
            const responseFormat = settings.enableSpeakerSeparation ? 'verbose_json' : 'text';
            context.log(`🎙️ [${correlationId}] Starting transcription with model: ${settings.model}`);
            context.log(`🔊 [${correlationId}] Speaker separation: ${settings.enableSpeakerSeparation}`);
            context.log(`📝 [${correlationId}] Using prompt: ${settings.promptId}`);
            // Call OpenAI Whisper API with enhanced error handling and retry logic
            const transcription = yield withRetry(() => __awaiter(this, void 0, void 0, function* () {
                return yield openai.audio.transcriptions.create({
                    file: audioFile,
                    model: settings.model,
                    prompt: transcriptionPrompt.substring(0, 224), // Whisper prompt limit
                    language: 'en',
                    response_format: responseFormat,
                    temperature: 0.2,
                    timestamp_granularities: responseFormat === 'verbose_json' ? ['word', 'segment'] : undefined
                });
            }), 3, // maxRetries
            context, correlationId, 'OpenAI Whisper API');
            let finalTranscription;
            let speakersDetected;
            if (responseFormat === 'verbose_json' && typeof transcription === 'object') {
                context.log(`👥 [${correlationId}] Processing speaker separation`);
                // Process with speaker separation
                const segments = transcription.segments || [];
                try {
                    // Use GPT-4o to identify speakers with retry logic
                    const speakerInfo = yield withRetry(() => processSpeakerRecognition(openai, segments, correlationId), 2, // fewer retries for speaker recognition
                    context, correlationId, 'Speaker Recognition');
                    speakersDetected = (speakerInfo === null || speakerInfo === void 0 ? void 0 : speakerInfo.speakers) ?
                        new Set(speakerInfo.speakers.map(s => s.speaker)).size : undefined;
                    // Format transcription with speaker labels
                    finalTranscription = formatTranscriptionWithSpeakers(segments, speakerInfo);
                    context.log(`👥 [${correlationId}] Speaker separation completed: ${speakersDetected || 0} speakers detected`);
                }
                catch (speakerError) {
                    context.log(`⚠️ [${correlationId}] Speaker recognition failed, using basic transcription:`, speakerError);
                    finalTranscription = segments.map(seg => seg.text).join(' ');
                }
            }
            else {
                finalTranscription = typeof transcription === 'string' ? transcription : transcription.text;
            }
            // Validate transcription result
            if (!finalTranscription || finalTranscription.trim().length === 0) {
                throw createStructuredError('EMPTY_TRANSCRIPTION', 'OpenAI returned empty transcription result', 'api_error', true);
            }
            transcriptionResult = finalTranscription;
            const processingTime = Date.now() - startTime;
            // Create processing metadata
            processingMetadata = {
                modelUsed: settings.model,
                processingTimeMs: processingTime,
                transcribedAt: new Date().toISOString(),
                speakerSeparationEnabled: settings.enableSpeakerSeparation,
                speakersDetected,
                audioSource: 'Azure Blob Storage',
                fileSizeMB: parseFloat((totalSize / 1024 / 1024).toFixed(2)),
                transcriptionLength: finalTranscription.length,
                retryCount: queueItem.retryCount,
                correlationId
            };
            // Store transcription in blob storage with enhanced error handling
            const transcriptionPath = `transcriptions/${queueItem.jobId}.json`;
            const transcriptionData = {
                jobId: queueItem.jobId,
                filename: queueItem.filename,
                containerPath: queueItem.containerPath,
                transcription: finalTranscription,
                metadata: processingMetadata,
                queueMessage: {
                    type: queueItem.type,
                    priority: queueItem.priority,
                    source: (_f = queueItem.metadata) === null || _f === void 0 ? void 0 : _f.source
                },
                processingHistory: {
                    startedAt: new Date(startTime).toISOString(),
                    completedAt: new Date().toISOString(),
                    duration: processingTime,
                    attempt: queueItem.retryCount + 1
                }
            };
            try {
                const transcriptionBlob = containerClient.getBlockBlobClient(transcriptionPath);
                const jsonContent = JSON.stringify(transcriptionData, null, 2);
                yield transcriptionBlob.upload(jsonContent, Buffer.byteLength(jsonContent), {
                    blobHTTPHeaders: {
                        blobContentType: 'application/json',
                        blobCacheControl: 'no-cache'
                    },
                    metadata: {
                        jobId: queueItem.jobId,
                        correlationId,
                        processingTimeMs: processingTime.toString()
                    }
                });
                context.log(`💾 [${correlationId}] Transcription saved to: ${transcriptionPath}`);
                // Update main transcriptions.json file with error handling
                yield withRetry(() => updateTranscriptionsIndex(containerClient, queueItem.jobId, finalTranscription, correlationId), 2, context, correlationId, 'Transcriptions Index Update');
            }
            catch (storageError) {
                context.log(`⚠️ [${correlationId}] Failed to save transcription to storage:`, storageError);
                // Don't fail the job if storage fails - we have the transcription
            }
            // Update job status to completed with results
            jobStatus = 'completed';
            yield updateJobStatus(queueItem.jobId, {
                status: 'completed',
                completedAt: new Date().toISOString(),
                results: {
                    transcription: {
                        text: finalTranscription,
                        confidence: 0.9, // Could be enhanced with actual confidence from Whisper
                        processingTimeMs: processingTime,
                        modelUsed: settings.model,
                        speakersDetected
                    }
                }
            }, context, correlationId);
            // Create completion result for further processing
            const completionResult = {
                jobId: queueItem.jobId,
                filename: queueItem.filename,
                containerPath: queueItem.containerPath,
                transcription: finalTranscription,
                metadata: processingMetadata,
                success: true,
                correlationId,
                processingTimeMs: processingTime
            };
            // Send to completed queue for further processing (summarization, etc.)
            context.bindings.completedJob = completionResult;
            context.log(`✅ [${correlationId}] Transcription completed successfully!`);
            context.log(`📊 [${correlationId}] Processing time: ${processingTime}ms`);
            context.log(`📝 [${correlationId}] Transcription length: ${finalTranscription.length} characters`);
            context.log(`👥 [${correlationId}] Speakers detected: ${speakersDetected || 'N/A'}`);
            context.log(`💼 [${correlationId}] Job status updated to completed`);
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            // Classify error for structured handling
            errorDetails = {
                code: error.code || 'TRANSCRIPTION_FAILED',
                message: error.message || 'Unknown transcription error',
                category: error.category || classifyError(error),
                retryable: error.retryable !== undefined ? error.retryable : isRetryableError(error),
                stackTrace: error.stack,
                context: {
                    operation: 'azure_function_transcription',
                    correlationId,
                    jobId: queueItem.jobId,
                    filename: queueItem.filename,
                    retryCount: queueItem.retryCount,
                    processingTimeMs: processingTime
                }
            };
            jobStatus = 'failed';
            context.log(`❌ [${correlationId}] Transcription failed for job ${queueItem.jobId}:`, {
                error: errorDetails,
                jobDetails: {
                    filename: queueItem.filename,
                    type: queueItem.type,
                    retryCount: queueItem.retryCount
                }
            });
            // Update job status to failed with error details
            try {
                yield updateJobStatus(queueItem.jobId, {
                    status: 'failed',
                    completedAt: new Date().toISOString(),
                    errorDetails
                }, context, correlationId);
            }
            catch (statusError) {
                context.log(`❌ [${correlationId}] Failed to update job status:`, statusError);
            }
            // Create error result for monitoring and potential retry
            const errorResult = {
                jobId: queueItem.jobId,
                filename: queueItem.filename,
                containerPath: queueItem.containerPath,
                transcription: '',
                metadata: {
                    modelUsed: 'whisper-1',
                    processingTimeMs: processingTime,
                    transcribedAt: new Date().toISOString(),
                    speakerSeparationEnabled: false,
                    audioSource: 'Azure Blob Storage',
                    fileSizeMB: 0,
                    transcriptionLength: 0,
                    retryCount: queueItem.retryCount,
                    correlationId
                },
                success: false,
                error: errorDetails,
                correlationId
            };
            // Send error result to completed queue for logging and potential retry handling
            context.bindings.completedJob = errorResult;
            // Log final status
            context.log(`💼 [${correlationId}] Job ${queueItem.jobId} marked as failed after ${queueItem.retryCount + 1} attempts`);
            // Don't throw - let the function complete so the queue item is removed
            // The error will be logged in the result and job status
        }
    });
}
;
// Helper functions
function getContentType(filename) {
    const ext = filename.toLowerCase().split('.').pop();
    const contentTypes = {
        'mp3': 'audio/mpeg',
        'wav': 'audio/wav',
        'm4a': 'audio/mp4',
        'webm': 'audio/webm',
        'ogg': 'audio/ogg',
        'flac': 'audio/flac',
        'aac': 'audio/aac'
    };
    return contentTypes[ext || ''] || 'audio/mpeg';
}
function getTranscriptionPrompt(promptId) {
    // Simplified prompt selection - in production, load from settings
    const prompts = {
        'dental-transcription-context': 'This is a dental appointment recording. Please transcribe accurately, including medical terminology, patient responses, and clinical observations.',
        'default': 'Please transcribe this audio accurately.'
    };
    return prompts[promptId] || prompts['default'];
}
function processSpeakerRecognition(openai, segments, correlationId) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const speakerPrompt = `Analyze this dental appointment transcription and identify speakers.

Context: This is a dental visit recording. Typical speakers are:
- Doctor/Dentist (clinical language, giving instructions, asking about symptoms)
- Dental Assistant/Hygienist (procedural language, taking measurements, assisting)
- Patient (responding to questions, expressing concerns, asking questions)

Please identify speaker changes and label each segment. Return JSON format:
{
  "speakers": [
    {"start": 0.0, "end": 15.2, "speaker": "Doctor", "confidence": 0.9},
    {"start": 15.2, "end": 23.1, "speaker": "Patient", "confidence": 0.8}
  ]
}

Transcription segments:
${segments.map((seg, i) => `[${seg.start}s-${seg.end}s]: ${seg.text}`).join('\n')}`;
            const response = yield openai.chat.completions.create({
                model: 'gpt-4o',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a dental transcription specialist. Analyze audio segments and identify speakers in dental appointments.'
                    },
                    {
                        role: 'user',
                        content: speakerPrompt
                    }
                ],
                temperature: 0.1,
                response_format: { type: 'json_object' }
            });
            return JSON.parse(response.choices[0].message.content || '{}');
        }
        catch (error) {
            console.error('Speaker recognition failed:', error);
            return null;
        }
    });
}
function formatTranscriptionWithSpeakers(segments, speakerInfo) {
    if (!(speakerInfo === null || speakerInfo === void 0 ? void 0 : speakerInfo.speakers)) {
        return segments.map(seg => seg.text).join(' ');
    }
    let formattedText = '';
    let currentSpeaker = '';
    for (const segment of segments) {
        const speaker = speakerInfo.speakers.find((s) => segment.start >= s.start && segment.end <= s.end);
        const segmentSpeaker = (speaker === null || speaker === void 0 ? void 0 : speaker.speaker) || 'Unknown';
        if (segmentSpeaker !== currentSpeaker) {
            if (formattedText)
                formattedText += '\n\n';
            formattedText += `**${segmentSpeaker}:** `;
            currentSpeaker = segmentSpeaker;
        }
        formattedText += segment.text + ' ';
    }
    return formattedText.trim();
}
function updateTranscriptionsIndex(containerClient, jobId, transcription, correlationId) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, e_2, _b, _c;
        try {
            // Download existing transcriptions.json
            const transcriptionsBlob = containerClient.getBlockBlobClient('metadata/transcriptions.json');
            let transcriptions = {};
            try {
                const downloadResponse = yield transcriptionsBlob.download();
                if (downloadResponse.readableStreamBody) {
                    const chunks = [];
                    try {
                        for (var _d = true, _e = __asyncValues(downloadResponse.readableStreamBody), _f; _f = yield _e.next(), _a = _f.done, !_a; _d = true) {
                            _c = _f.value;
                            _d = false;
                            const chunk = _c;
                            chunks.push(chunk);
                        }
                    }
                    catch (e_2_1) { e_2 = { error: e_2_1 }; }
                    finally {
                        try {
                            if (!_d && !_a && (_b = _e.return)) yield _b.call(_e);
                        }
                        finally { if (e_2) throw e_2.error; }
                    }
                    const content = Buffer.concat(chunks).toString();
                    transcriptions = JSON.parse(content);
                }
            }
            catch (error) {
                // File doesn't exist yet, start with empty object
            }
            // Add new transcription
            transcriptions[jobId] = transcription;
            // Upload updated transcriptions.json
            const updatedContent = JSON.stringify(transcriptions, null, 2);
            yield transcriptionsBlob.upload(updatedContent, Buffer.byteLength(updatedContent), { blobHTTPHeaders: { blobContentType: 'application/json' } });
        }
        catch (error) {
            console.error('Failed to update transcriptions index:', error);
            // Don't throw - this is not critical for the transcription to succeed
        }
    });
}
/**
 * Helper function to update job status in persistent storage
 */
function updateJobStatus(jobId, updates, context, correlationId) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Import and use job persistence system
            // const { createJobStore } = await import('../../src/lib/job-persistence');
            // const jobStore = createJobStore();
            // await jobStore.updateJob(jobId, updates);
            context.log(`📋 [${correlationId}] Job status updated: ${jobId} -> ${updates.status}`);
        }
        catch (error) {
            context.log(`❌ [${correlationId}] Failed to update job status:`, error);
            // Don't throw - this shouldn't fail the transcription
        }
    });
}
/**
 * Create structured error with consistent format
 */
function createStructuredError(code, message, category, retryable) {
    const error = new Error(message);
    error.code = code;
    error.category = category;
    error.retryable = retryable;
    return error;
}
/**
 * Classify error type for structured error handling
 */
function classifyError(error) {
    var _a, _b, _c, _d;
    if (error.status === 429 || ((_a = error.message) === null || _a === void 0 ? void 0 : _a.includes('rate limit'))) {
        return 'quota_exceeded';
    }
    if (error.status >= 400 && error.status < 500) {
        return 'api_error';
    }
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
        return 'network_error';
    }
    if (error.code === 'ENOENT' || ((_b = error.message) === null || _b === void 0 ? void 0 : _b.includes('not found'))) {
        return 'file_not_found';
    }
    if (((_c = error.message) === null || _c === void 0 ? void 0 : _c.includes('validation')) || ((_d = error.message) === null || _d === void 0 ? void 0 : _d.includes('invalid'))) {
        return 'validation_error';
    }
    return 'system_error';
}
/**
 * Determine if error should trigger retry
 */
function isRetryableError(error) {
    // Rate limiting is retryable
    if (error.status === 429)
        return true;
    // Server errors are typically retryable
    if (error.status >= 500)
        return true;
    // Network errors are retryable
    if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT')
        return true;
    // File not found is not retryable
    if (error.code === 'ENOENT' || error.code === 'FILE_NOT_FOUND')
        return false;
    // Configuration errors are not retryable
    if (error.code === 'MISSING_CONFIG')
        return false;
    // Default to retryable for transient issues
    return true;
}
/**
 * Retry wrapper with exponential backoff
 */
function withRetry(operation, maxRetries, context, correlationId, operationName) {
    return __awaiter(this, void 0, void 0, function* () {
        let lastError;
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Max 10s
                    context.log(`🔄 [${correlationId}] Retrying ${operationName} (attempt ${attempt + 1}/${maxRetries + 1}) after ${delay}ms`);
                    yield new Promise(resolve => setTimeout(resolve, delay));
                }
                return yield operation();
            }
            catch (error) {
                lastError = error;
                if (attempt === maxRetries || !isRetryableError(error)) {
                    context.log(`❌ [${correlationId}] ${operationName} failed after ${attempt + 1} attempts`);
                    break;
                }
                context.log(`⚠️ [${correlationId}] ${operationName} attempt ${attempt + 1} failed:`, error.message);
            }
        }
        throw lastError;
    });
}
// Register the function with Azure Functions v4
functions_1.app.storageQueue('transcriptionProcessor', {
    queueName: 'transcription-jobs',
    connection: 'AzureWebJobsStorage',
    handler: transcriptionProcessor
});
//# sourceMappingURL=index.js.map