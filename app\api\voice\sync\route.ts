import { NextRequest, NextResponse } from 'next/server';
import { readdir, stat, copyFile, unlink, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import crypto from 'crypto';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Sync configuration
const NETWORK_SHARE_PATH = process.env.NETWORK_SHARE_PATH || '\\\\***********\\share\\RECORDINGS';
const LOCAL_BACKUP_PATH = path.join(process.cwd(), 'voice-recordings');

interface SyncResult {
  status: 'success' | 'partial' | 'failed';
  networkToLocal: {
    transferred: number;
    skipped: number;
    errors: string[];
  };
  localToNetwork: {
    transferred: number;
    skipped: number;
    errors: string[];
  };
  summary: {
    totalFiles: number;
    networkFiles: number;
    localFiles: number;
    duplicates: number;
    syncedFiles: number;
  };
  debug: {
    networkAvailable: boolean;
    localAvailable: boolean;
    syncDirection: 'bidirectional' | 'network-to-local' | 'local-to-network' | 'none';
    lastSyncTime: string;
  };
}

// Function to ensure network authentication
async function ensureNetworkAccess(uncPath: string): Promise<boolean> {
  try {
    await readdir(uncPath);
    return true;
  } catch (error) {
    try {
      const username = process.env.NETWORK_SHARE_USERNAME;
      const password = process.env.NETWORK_SHARE_PASSWORD;
      const domain = process.env.NETWORK_SHARE_DOMAIN || '';

      if (!username || !password) {
        return false;
      }

      const userParam = domain ? `${domain}\\${username}` : username;
      const netUseCommand = `net use "${uncPath}" /user:"${userParam}" "${password}" /persistent:no`;

      await execAsync(netUseCommand);
      await readdir(uncPath);
      return true;
    } catch (authError) {
      return false;
    }
  }
}

// Function to get file hash for comparison
async function getFileHash(filePath: string): Promise<string> {
  const stats = await stat(filePath);
  return crypto.createHash('md5').update(`${stats.size}-${stats.mtime.getTime()}`).digest('hex');
}

// Function to scan recordings from a directory
async function scanRecordings(dirPath: string): Promise<Map<string, any>> {
  const recordings = new Map();

  async function scanDirectory(currentPath: string, depth: number = 0) {
    if (depth > 3) return;

    try {
      const files = await readdir(currentPath);
      
      for (const file of files) {
        try {
          const filePath = path.join(currentPath, file);
          const stats = await stat(filePath);

          if (stats.isDirectory()) {
            await scanDirectory(filePath, depth + 1);
          } else if (stats.isFile()) {
            const isAudioFile = file.toLowerCase().match(/\.(webm|mp3|wav|m4a|ogg|wma)$/i);
            
            if (isAudioFile && stats.size > 0) {
              const hash = await getFileHash(filePath);
              const relativePath = path.relative(dirPath, filePath);
              
              recordings.set(file, {
                name: file,
                path: filePath,
                relativePath,
                size: stats.size,
                mtime: stats.mtime,
                hash
              });
            }
          }
        } catch (error) {
          console.error(`Error processing ${file}:`, error.message);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${currentPath}:`, error.message);
    }
  }

  await scanDirectory(dirPath);
  return recordings;
}

// Function to perform bidirectional sync
async function performSync(): Promise<SyncResult> {
  const result: SyncResult = {
    status: 'failed',
    networkToLocal: { transferred: 0, skipped: 0, errors: [] },
    localToNetwork: { transferred: 0, skipped: 0, errors: [] },
    summary: { totalFiles: 0, networkFiles: 0, localFiles: 0, duplicates: 0, syncedFiles: 0 },
    debug: {
      networkAvailable: false,
      localAvailable: false,
      syncDirection: 'none',
      lastSyncTime: new Date().toISOString()
    }
  };

  // Check network availability
  if (NETWORK_SHARE_PATH.startsWith('\\\\')) {
    result.debug.networkAvailable = await ensureNetworkAccess(NETWORK_SHARE_PATH);
  } else {
    result.debug.networkAvailable = existsSync(NETWORK_SHARE_PATH);
  }

  // Check local availability
  result.debug.localAvailable = existsSync(LOCAL_BACKUP_PATH);
  if (!result.debug.localAvailable) {
    try {
      await mkdir(LOCAL_BACKUP_PATH, { recursive: true });
      result.debug.localAvailable = true;
    } catch (error) {
      result.localToNetwork.errors.push(`Failed to create local backup directory: ${error.message}`);
    }
  }

  let networkRecordings = new Map();
  let localRecordings = new Map();

  // Scan network recordings
  if (result.debug.networkAvailable) {
    try {
      networkRecordings = await scanRecordings(NETWORK_SHARE_PATH);
      result.summary.networkFiles = networkRecordings.size;
      console.log(`Found ${networkRecordings.size} recordings on network share`);
    } catch (error) {
      result.networkToLocal.errors.push(`Failed to scan network recordings: ${error.message}`);
      result.debug.networkAvailable = false;
    }
  }

  // Scan local recordings
  if (result.debug.localAvailable) {
    try {
      localRecordings = await scanRecordings(LOCAL_BACKUP_PATH);
      result.summary.localFiles = localRecordings.size;
      console.log(`Found ${localRecordings.size} recordings in local backup`);
    } catch (error) {
      result.localToNetwork.errors.push(`Failed to scan local recordings: ${error.message}`);
      result.debug.localAvailable = false;
    }
  }

  // Determine sync direction
  if (result.debug.networkAvailable && result.debug.localAvailable) {
    result.debug.syncDirection = 'bidirectional';
  } else if (result.debug.networkAvailable) {
    result.debug.syncDirection = 'network-to-local';
  } else if (result.debug.localAvailable) {
    result.debug.syncDirection = 'local-to-network';
  } else {
    result.debug.syncDirection = 'none';
    result.status = 'failed';
    return result;
  }

  // Sync network to local (download missing files)
  if (result.debug.networkAvailable && result.debug.localAvailable) {
    for (const [fileName, networkFile] of networkRecordings) {
      try {
        const localFile = localRecordings.get(fileName);
        
        if (!localFile) {
          // File doesn't exist locally, copy it
          const localPath = path.join(LOCAL_BACKUP_PATH, networkFile.relativePath);
          const localDir = path.dirname(localPath);
          
          await mkdir(localDir, { recursive: true });
          await copyFile(networkFile.path, localPath);
          
          // Verify copy
          const localHash = await getFileHash(localPath);
          if (localHash === networkFile.hash) {
            result.networkToLocal.transferred++;
            console.log(`Downloaded: ${fileName}`);
          } else {
            result.networkToLocal.errors.push(`Verification failed for ${fileName}`);
            await unlink(localPath);
          }
        } else if (localFile.hash !== networkFile.hash) {
          // File exists but is different, use newer version
          if (networkFile.mtime > localFile.mtime) {
            await copyFile(networkFile.path, localFile.path);
            result.networkToLocal.transferred++;
            console.log(`Updated local file: ${fileName}`);
          }
        } else {
          result.networkToLocal.skipped++;
          result.summary.duplicates++;
        }
      } catch (error) {
        result.networkToLocal.errors.push(`Error syncing ${fileName}: ${error.message}`);
      }
    }
  }

  // Sync local to network (upload missing files)
  if (result.debug.networkAvailable && result.debug.localAvailable) {
    for (const [fileName, localFile] of localRecordings) {
      try {
        const networkFile = networkRecordings.get(fileName);
        
        if (!networkFile) {
          // File doesn't exist on network, copy it
          const networkPath = path.join(NETWORK_SHARE_PATH, localFile.relativePath);
          const networkDir = path.dirname(networkPath);
          
          await mkdir(networkDir, { recursive: true });
          await copyFile(localFile.path, networkPath);
          
          // Verify copy
          const networkHash = await getFileHash(networkPath);
          if (networkHash === localFile.hash) {
            result.localToNetwork.transferred++;
            console.log(`Uploaded: ${fileName}`);
          } else {
            result.localToNetwork.errors.push(`Verification failed for ${fileName}`);
            await unlink(networkPath);
          }
        } else if (localFile.hash !== networkFile.hash) {
          // File exists but is different, use newer version
          if (localFile.mtime > networkFile.mtime) {
            await copyFile(localFile.path, networkFile.path);
            result.localToNetwork.transferred++;
            console.log(`Updated network file: ${fileName}`);
          }
        } else {
          result.localToNetwork.skipped++;
        }
      } catch (error) {
        result.localToNetwork.errors.push(`Error syncing ${fileName}: ${error.message}`);
      }
    }
  }

  // Calculate summary
  result.summary.totalFiles = Math.max(result.summary.networkFiles, result.summary.localFiles);
  result.summary.syncedFiles = result.networkToLocal.transferred + result.localToNetwork.transferred;

  // Determine overall status
  const totalErrors = result.networkToLocal.errors.length + result.localToNetwork.errors.length;
  if (totalErrors === 0) {
    result.status = 'success';
  } else if (result.summary.syncedFiles > 0) {
    result.status = 'partial';
  } else {
    result.status = 'failed';
  }

  return result;
}

export async function POST(request: NextRequest) {
  try {
    const { force = false } = await request.json();
    
    console.log('Starting voice recordings sync...', { force });
    
    const syncResult = await performSync();
    
    console.log('Sync completed:', {
      status: syncResult.status,
      networkToLocal: syncResult.networkToLocal.transferred,
      localToNetwork: syncResult.localToNetwork.transferred,
      errors: syncResult.networkToLocal.errors.length + syncResult.localToNetwork.errors.length
    });

    return NextResponse.json(syncResult);

  } catch (error: any) {
    console.error('Sync error:', error);
    return NextResponse.json({
      status: 'failed',
      error: 'Sync operation failed',
      details: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    // Return sync status without performing sync
    const result: SyncResult = {
      status: 'success',
      networkToLocal: { transferred: 0, skipped: 0, errors: [] },
      localToNetwork: { transferred: 0, skipped: 0, errors: [] },
      summary: { totalFiles: 0, networkFiles: 0, localFiles: 0, duplicates: 0, syncedFiles: 0 },
      debug: {
        networkAvailable: false,
        localAvailable: existsSync(LOCAL_BACKUP_PATH),
        syncDirection: 'none',
        lastSyncTime: new Date().toISOString()
      }
    };

    // Check network availability
    if (NETWORK_SHARE_PATH.startsWith('\\\\')) {
      result.debug.networkAvailable = await ensureNetworkAccess(NETWORK_SHARE_PATH);
    } else {
      result.debug.networkAvailable = existsSync(NETWORK_SHARE_PATH);
    }

    // Get file counts for summary
    if (result.debug.networkAvailable) {
      try {
        const networkRecordings = await scanRecordings(NETWORK_SHARE_PATH);
        result.summary.networkFiles = networkRecordings.size;
      } catch (error) {
        result.debug.networkAvailable = false;
      }
    }

    if (result.debug.localAvailable) {
      try {
        const localRecordings = await scanRecordings(LOCAL_BACKUP_PATH);
        result.summary.localFiles = localRecordings.size;
      } catch (error) {
        result.debug.localAvailable = false;
      }
    }

    // Determine sync direction
    if (result.debug.networkAvailable && result.debug.localAvailable) {
      result.debug.syncDirection = 'bidirectional';
    } else if (result.debug.networkAvailable) {
      result.debug.syncDirection = 'network-to-local';
    } else if (result.debug.localAvailable) {
      result.debug.syncDirection = 'local-to-network';
    }

    result.summary.totalFiles = Math.max(result.summary.networkFiles, result.summary.localFiles);

    return NextResponse.json(result);

  } catch (error: any) {
    console.error('Sync status check error:', error);
    return NextResponse.json({
      error: 'Failed to check sync status',
      details: error.message
    }, { status: 500 });
  }
}
