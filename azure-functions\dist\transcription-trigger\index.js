"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.transcriptionTrigger = transcriptionTrigger;
const functions_1 = require("@azure/functions");
/**
 * Azure Function that triggers when new audio files are uploaded to Blob Storage
 * Automatically queues them for transcription processing
 */
function transcriptionTrigger(myBlob, context) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, _b;
        const blobName = (_a = context.triggerMetadata) === null || _a === void 0 ? void 0 : _a.name;
        const blobPath = (_b = context.triggerMetadata) === null || _b === void 0 ? void 0 : _b.uri;
        context.log(`🎵 New audio file detected: ${blobName}`);
        context.log(`📁 Blob path: ${blobPath}`);
        context.log(`📊 File size: ${myBlob.length} bytes`);
        try {
            // Check if this is an audio file
            const audioExtensions = ['.mp3', '.wav', '.m4a', '.webm', '.ogg', '.flac', '.aac'];
            const fileExtension = blobName.toLowerCase().substring(blobName.lastIndexOf('.'));
            if (!audioExtensions.includes(fileExtension)) {
                context.log(`⏭️ Skipping non-audio file: ${blobName}`);
                return;
            }
            // Skip metadata files
            if (blobName.includes('metadata/') ||
                blobName.includes('transcriptions.json') ||
                blobName.includes('summaries.json')) {
                context.log(`⏭️ Skipping metadata file: ${blobName}`);
                return;
            }
            // Create transcription job
            const jobId = `auto-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            const transcriptionJob = {
                id: jobId,
                filename: blobName,
                blobPath: blobPath,
                fileSize: myBlob.length,
                uploadedAt: new Date().toISOString(),
                priority: 'normal',
                settings: {
                    model: 'whisper-1', // Default model, can be overridden by user settings
                    promptId: 'dental-transcription-context',
                    enableSpeakerSeparation: true
                }
            };
            // Add job to queue for processing
            // TODO: Implement proper v4 queue output binding
            // context.bindings.queueItem = transcriptionJob;
            context.log(`✅ Queued transcription job: ${jobId} for file: ${blobName}`);
            context.log(`📋 Job details:`, JSON.stringify(transcriptionJob, null, 2));
        }
        catch (error) {
            context.log(`❌ Error processing blob trigger for ${blobName}:`, error);
            // Don't throw error - we don't want to retry blob triggers indefinitely
            // Instead, log the error and continue
        }
    });
}
;
// Register the function with Azure Functions v4
functions_1.app.storageBlob('transcriptionTrigger', {
    path: 'recordings/audio/{name}',
    connection: 'AzureWebJobsStorage',
    handler: transcriptionTrigger
});
//# sourceMappingURL=index.js.map