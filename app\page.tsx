"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo, lazy, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Calendar, FileText, Upload, Bo<PERSON>, Wrench } from "lucide-react";
import { PageHeader } from "../src/components/ui/page-header";
import { PullToRefresh } from "../src/components/ui/pull-to-refresh";
import { CalendarWidget } from "../src/components/ui/calendar-widget";
import { QuickActionsPanel } from "../src/components/ui/quick-actions-panel";
import { useTheme } from 'next-themes';
import { VERSION_INFO } from "../src/lib/version";

// Lazy load heavy components to improve performance
const VoiceUploadWrapper = lazy(() => import("../src/components/voice/voice-upload-wrapper").then(module => ({ default: module.VoiceUploadWrapper })));
const VoiceRecordingsTab = lazy(() => import("../src/components/voice/voice-recordings-tab").then(module => ({ default: module.VoiceRecordingsTab })));
const SimpleClinicalNotes = lazy(() => import("../src/components/clinical-notes/simple-clinical-notes"));
const AgentChat = lazy(() => import("../src/components/ai/agent-chat").then(module => ({ default: module.AgentChat })));
const ToolsAndSettings = lazy(() => import("../src/components/tools/tools-and-settings").then(module => ({ default: module.ToolsAndSettings })));

type TabType = 'schedule' | 'voice-upload' | 'voice-recordings' | 'clinical-notes' | 'ai-assistant' | 'tools-settings';

// Loading component for lazy-loaded tabs
const TabLoadingSpinner = () => (
  <div className="flex justify-center items-center h-64">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    <span className="ml-3 text-slate-600 dark:text-slate-400">Loading...</span>
  </div>
);

function HomeContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Initialize activeTab from URL parameter to prevent flash
  const getInitialTab = (): TabType => {
    const tabParam = searchParams.get('tab');
    if (tabParam && ['schedule', 'voice-upload', 'voice-recordings', 'clinical-notes', 'ai-assistant', 'tools-settings'].includes(tabParam)) {
      return tabParam as TabType;
    } else if (tabParam === 'tools') {
      return 'tools-settings';
    }
    return 'schedule';
  };

  const [activeTab, setActiveTab] = useState<TabType>(getInitialTab);
  const [isMounted, setIsMounted] = useState(false);
  const [tabSwitchDebounce, setTabSwitchDebounce] = useState<NodeJS.Timeout | null>(null);
  const [calendarView, setCalendarView] = useState<'week' | 'month'>('week');

  // Get today's date in local timezone
  const today = new Date();
  // Format as YYYY-MM-DD without timezone conversion
  const todayString = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
  const [selectedDate, setSelectedDate] = useState<string>(todayString);

  // Debug log
  console.log('Home page - Current date:', {
    today: today.toString(),
    todayString,
    selectedDate
  });

  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Update tab when URL parameters change (for programmatic navigation)
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    const newTab = tabParam && ['schedule', 'voice-upload', 'voice-recordings', 'clinical-notes', 'ai-assistant', 'tools-settings'].includes(tabParam)
      ? tabParam as TabType
      : tabParam === 'tools'
        ? 'tools-settings'
        : 'schedule';

    if (newTab !== activeTab) {
      setActiveTab(newTab);
    }
  }, [searchParams, activeTab]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (tabSwitchDebounce) {
        clearTimeout(tabSwitchDebounce);
      }
    };
  }, [tabSwitchDebounce]);

  const tabs = useMemo(() => [
    { 
      id: 'schedule' as TabType, 
      label: 'Schedule', 
      shortLabel: 'Sched',
      icon: Calendar 
    },
    { 
      id: 'voice-upload' as TabType, 
      label: 'Voice Upload', 
      shortLabel: 'Upload',
      icon: Upload 
    },
    { 
      id: 'clinical-notes' as TabType, 
      label: 'Clinical Notes', 
      shortLabel: 'Notes',
      icon: FileText 
    },
    { 
      id: 'ai-assistant' as TabType, 
      label: 'AI Office', 
      shortLabel: 'AI',
      icon: Bot 
    },
    { 
      id: 'tools-settings' as TabType, 
      label: 'Settings', 
      shortLabel: 'Tools',
      icon: Wrench 
    },
  ], []);

  // Debounced tab switching to prevent rapid API calls and update URL
  const handleTabSwitch = useCallback((newTab: TabType) => {
    if (tabSwitchDebounce) {
      clearTimeout(tabSwitchDebounce);
    }

    const timeout = setTimeout(() => {
      setActiveTab(newTab);

      // Update URL with the new tab parameter
      const params = new URLSearchParams(searchParams);
      params.set('tab', newTab);

      // Use replace to avoid adding to browser history for every tab switch
      router.replace(`/?${params.toString()}`);
    }, 150); // 150ms debounce

    setTabSwitchDebounce(timeout);
  }, [tabSwitchDebounce, router, searchParams]);

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    // Refresh the current page
    window.location.reload();
  }, []);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'schedule':
        return (
          <div className="space-y-6">
            <CalendarWidget
              selectedDate={selectedDate}
              onDateChange={setSelectedDate}
              calendarView={calendarView}
              onViewChange={setCalendarView}
              isDarkMode={isDarkMode}
              isMounted={isMounted}
            />
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 border border-slate-200 dark:border-slate-700">
              <QuickActionsPanel
                selectedDate={selectedDate}
                onNavigate={(path) => router.push(path)}
              />
            </div>
          </div>
        );

      case 'voice-upload':
        return (
          <div className="space-y-6">
            <Suspense fallback={<TabLoadingSpinner />}>
              <VoiceUploadWrapper isDarkMode={isDarkMode} />
            </Suspense>
          </div>
        );

      case 'voice-recordings':
        return (
          <div className="space-y-6">
            <Suspense fallback={<TabLoadingSpinner />}>
              <VoiceRecordingsTab isDarkMode={isDarkMode} />
            </Suspense>
          </div>
        );

      case 'clinical-notes':
        return (
          <div className="space-y-6">
            <Suspense fallback={<TabLoadingSpinner />}>
              <SimpleClinicalNotes isDarkMode={isDarkMode} />
            </Suspense>
          </div>
        );

      case 'ai-assistant':
        return (
          <div className="space-y-6">
            <Suspense fallback={<TabLoadingSpinner />}>
              <AgentChat isDarkMode={isDarkMode} />
            </Suspense>
          </div>
        );

      case 'tools-settings':
        return (
          <div className="space-y-6">
            <Suspense fallback={<TabLoadingSpinner />}>
              <ToolsAndSettings isDarkMode={isDarkMode} />
            </Suspense>
          </div>
        );

      case 'match-recordings':
        // Redirect to voice-workflow page for match recordings functionality
        router.push('/voice-workflow');
        return (
          <div className="flex items-center justify-center py-8">
            <p className="text-slate-600 dark:text-slate-400">
              Redirecting to Voice Workflow...
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <PullToRefresh onRefresh={handleRefresh}>
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
        <PageHeader
          title="Dentalapp"
          isHomePage={true}
          activeTab={activeTab}
          onTabChange={(tabId: string) => handleTabSwitch(tabId as TabType)}
        />

        <main className="max-w-4xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6">
          {/* Tab Content */}
          {renderTabContent()}

          {/* Footer */}
          <div className="text-center mt-12 text-slate-500 dark:text-slate-400">
            <p>Dentalapp - Powered by Sikka API</p>
            <p className="text-xs mt-1 opacity-75">
              {VERSION_INFO.version} • #{VERSION_INFO.buildNumber} • {VERSION_INFO.buildDate} • {VERSION_INFO.buildTime} • {VERSION_INFO.commitHash.substring(0, 7)}
            </p>
          </div>
        </main>
      </div>
    </PullToRefresh>
  );
}

export default function Home() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <HomeContent />
    </Suspense>
  );
}
