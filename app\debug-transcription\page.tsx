/**
 * TRANSCRIPTION DEBUG PAGE
 * 
 * This page shows real-time transcription status and errors
 * directly in the web app - no more console hunting!
 * 
 * Access at: /debug-transcription
 */

'use client';

import { useState, useEffect } from 'react';

interface DebugInfo {
  timestamp: string;
  system: {
    azureConfigured: boolean;
    openaiConfigured: boolean;
  };
  results: {
    azureStorage?: any;
    openai?: any;
    allFiles?: {
      count: number;
      files: Array<{
        name: string;
        path: string;
        size: string;
      }>;
      truncated: boolean;
    };
    specificFile?: any;
  };
  error?: any;
}

export default function DebugTranscriptionPage() {
  // Restrict access in production
  if (process.env.NODE_ENV === 'production') {
    return <div className="p-8 text-center text-gray-500">Access denied in production</div>;
  }
  
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [testFile, setTestFile] = useState('');
  const [showFiles, setShowFiles] = useState(false);

  const fetchDebugInfo = async (includeFiles = false, specificFile = '') => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (includeFiles) params.set('listFiles', 'true');
      if (specificFile) params.set('file', specificFile);
      
      const response = await fetch(`/api/voice/debug-transcription?${params}`);
      const data = await response.json();
      setDebugInfo(data);
    } catch (error) {
      console.error('Failed to fetch debug info:', error);
    } finally {
      setLoading(false);
    }
  };

  const testFileResolution = async () => {
    if (!testFile.trim()) return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/voice/debug-transcription', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'testFileResolution',
          fileName: testFile
        })
      });
      const data = await response.json();
      
      // Update debug info with the test result
      setDebugInfo(prev => ({
        ...prev!,
        results: {
          ...prev!.results,
          specificFile: data.result
        }
      }));
    } catch (error) {
      console.error('Failed to test file resolution:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDebugInfo();
  }, []);

  if (!debugInfo) {
    return (
      <div className="container mx-auto p-6">
        <h1 className="text-2xl font-bold mb-4">Loading Debug Info...</h1>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-3xl font-bold mb-6 text-gray-800">
          🔧 Transcription Debug Console v2.0
        </h1>
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <h2 className="text-lg font-semibold text-green-800 mb-2">✅ NEW: Advanced File Resolution System</h2>
          <ul className="text-sm text-green-700 space-y-1">
            <li>• FileResolver handles naming mismatches (e.g., file.mp3-12345-67890)</li>
            <li>• Direct OpenAI calls eliminate HTTP timeouts</li>
            <li>• Web-based debugging - no more console hunting!</li>
            <li>• 4 intelligent file search strategies</li>
          </ul>
        </div>
        
        <div className="text-sm text-gray-500 mb-6">
          Last updated: {new Date(debugInfo.timestamp).toLocaleString()}
        </div>

        {/* System Status */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">System Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className={`p-4 rounded-lg ${debugInfo.system.azureConfigured ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'} border`}>
              <div className="flex items-center">
                <span className={`text-2xl mr-2 ${debugInfo.system.azureConfigured ? 'text-green-600' : 'text-red-600'}`}>
                  {debugInfo.system.azureConfigured ? '✅' : '❌'}
                </span>
                <div>
                  <h3 className="font-semibold">Azure Storage</h3>
                  <p className="text-sm text-gray-600">
                    {debugInfo.system.azureConfigured ? 'Configured' : 'Not Configured'}
                  </p>
                </div>
              </div>
            </div>
            
            <div className={`p-4 rounded-lg ${debugInfo.system.openaiConfigured ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'} border`}>
              <div className="flex items-center">
                <span className={`text-2xl mr-2 ${debugInfo.system.openaiConfigured ? 'text-green-600' : 'text-red-600'}`}>
                  {debugInfo.system.openaiConfigured ? '✅' : '❌'}
                </span>
                <div>
                  <h3 className="font-semibold">OpenAI API</h3>
                  <p className="text-sm text-gray-600">
                    {debugInfo.system.openaiConfigured ? 'Configured' : 'Not Configured'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* File Testing */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Test File Resolution</h2>
          <div className="flex gap-2 mb-4">
            <input
              type="text"
              placeholder="Enter filename (e.g., azure-250626_1630.mp3)"
              value={testFile}
              onChange={(e) => setTestFile(e.target.value)}
              className="flex-1 px-3 py-2 border rounded-md"
            />
            <button
              onClick={testFileResolution}
              disabled={loading || !testFile.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md disabled:opacity-50"
            >
              Test File
            </button>
          </div>
          
          {debugInfo.results.specificFile && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">File Resolution Result:</h3>
              <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
                {JSON.stringify(debugInfo.results.specificFile, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Available Files */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Available Files</h2>
            <button
              onClick={() => {
                setShowFiles(!showFiles);
                if (!showFiles) fetchDebugInfo(true);
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded-md"
            >
              {showFiles ? 'Hide Files' : 'Show Files'}
            </button>
          </div>
          
          {showFiles && debugInfo.results.allFiles && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="mb-2">
                <strong>{debugInfo.results.allFiles.count}</strong> files found
                {debugInfo.results.allFiles.truncated && ' (showing first 20)'}
              </p>
              <div className="max-h-64 overflow-y-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Name</th>
                      <th className="text-left p-2">Path</th>
                      <th className="text-left p-2">Size</th>
                    </tr>
                  </thead>
                  <tbody>
                    {debugInfo.results.allFiles.files.map((file, index) => (
                      <tr key={index} className="border-b">
                        <td className="p-2 font-mono">{file.name}</td>
                        <td className="p-2 text-gray-600">{file.path}</td>
                        <td className="p-2">{file.size}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>

        {/* Error Information */}
        {debugInfo.error && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4 text-red-600">Error Details</h2>
            <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
              <pre className="text-sm text-red-800 overflow-x-auto">
                {JSON.stringify(debugInfo.error, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Refresh Button */}
        <div className="text-center">
          <button
            onClick={() => fetchDebugInfo()}
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-md disabled:opacity-50"
          >
            {loading ? 'Refreshing...' : 'Refresh Debug Info'}
          </button>
        </div>
      </div>
    </div>
  );
}