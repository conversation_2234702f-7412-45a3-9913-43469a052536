import requests
import json
from datetime import datetime
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 60

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None

    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def fetch_appointments_for_date(request_key, target_date):
    """Fetch appointments scheduled for a specific date."""
    headers = {"Request-Key": request_key}

    print(f"Fetching appointments for {target_date}...")

    # Use the v2 endpoint with pagination
    all_appointments = []
    offset = 0
    limit = 100  # Maximum number of appointments per page
    max_pages = 20  # Maximum number of pages to fetch

    # Set up parameters with the correct date filtering parameters
    params = {
        "startdate": target_date,
        "enddate": target_date,
        "date_filter_on": "appointment_date",
        "limit": limit,
        "offset": offset
    }

    for page in range(max_pages):
        print(f"  Fetching page {page+1} (offset: {offset}, limit: {limit})...")

        # Update offset for pagination
        params["offset"] = offset

        try:
            resp = requests.get(
                f"{API_BASE_V2}/appointments",
                headers=headers,
                params=params,
                timeout=API_TIMEOUT
            )

            if resp.status_code == 200:
                data = resp.json()

                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    print(f"    Found {len(items)} appointments on page {page+1}")

                    # Filter for appointments scheduled on the target date
                    target_appointments = [a for a in items if a.get("date") == target_date]
                    print(f"    Of these, {len(target_appointments)} are scheduled for {target_date}")

                    all_appointments.extend(target_appointments)

                    # If we got fewer items than the limit, we've reached the end
                    if len(items) < limit:
                        print("    Reached the end of appointments")
                        break

                    # Move to the next page
                    offset += limit
                else:
                    print("    No appointments found on this page")
                    break
            else:
                print(f"    Error: {resp.status_code}")
                print(f"    {resp.text}")
                break
        except Exception as e:
            print(f"    Error: {e}")
            break

    print(f"\nTotal appointments found for {target_date}: {len(all_appointments)}")

    # Remove duplicates if any
    appointment_map = {}
    for appt in all_appointments:
        appt_id = appt.get("appointment_sr_no")
        if appt_id:
            appointment_map[appt_id] = appt

    unique_appointments = list(appointment_map.values())

    if len(unique_appointments) < len(all_appointments):
        print(f"Removed {len(all_appointments) - len(unique_appointments)} duplicate appointments")

    return unique_appointments

def display_appointments_list(appointments, target_date, selected_operatories=None, show_notes=True):
    """Display a simple list of appointments in chronological order.

    Args:
        appointments: List of appointment dictionaries
        target_date: The target date in YYYY-MM-DD format
        selected_operatories: Optional list of operatories to filter by
        show_notes: Whether to show schedule notes (entries without patient names)
    """
    if not appointments:
        print(f"No appointments found for {target_date}")
        return

    # Filter by operatory if specified
    if selected_operatories:
        filtered_appointments = [a for a in appointments if a.get("operatory") in selected_operatories]
        print(f"Filtering by operatories: {', '.join(selected_operatories)}")
        print(f"Showing {len(filtered_appointments)} of {len(appointments)} appointments")
    else:
        filtered_appointments = appointments

    # Filter out notes if requested
    if not show_notes:
        filtered_appointments = [a for a in filtered_appointments if a.get("patient_name")]
        print(f"Filtered out {len(appointments) - len(filtered_appointments)} schedule notes")

    # Sort appointments by time
    sorted_appointments = sorted(filtered_appointments, key=lambda a: a.get("time", ""))

    # Display the header
    date_obj = datetime.strptime(target_date, "%Y-%m-%d")
    formatted_date = date_obj.strftime("%A, %B %d, %Y")

    print(f"\n{'=' * 100}")
    print(f"{'APPOINTMENTS FOR ' + formatted_date:^100}")
    print(f"{'=' * 100}")
    print(f"{'TIME':<8}{'PATIENT':<30}{'OPERATORY':<10}{'PROVIDER':<15}{'DESCRIPTION':<30}")
    print(f"{'-' * 8}{'-' * 30}{'-' * 10}{'-' * 15}{'-' * 30}")

    # Display each appointment
    for appt in sorted_appointments:
        time = appt.get("time", "")
        patient_name = appt.get("patient_name", "Unknown Patient")
        operatory = appt.get("operatory", "N/A")
        provider_id = appt.get("provider_id", "N/A")
        description = appt.get("description", "")

        # Truncate long fields
        if len(patient_name) > 27:
            patient_name = patient_name[:24] + "..."

        if len(description) > 27:
            description = description[:24] + "..."

        print(f"{time:<8}{patient_name:<30}{operatory:<10}{provider_id:<15}{description:<30}")

def main():
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="List appointments for a specific date")
    parser.add_argument("date", help="Date in YYYY-MM-DD format")
    parser.add_argument("--operatories", help="Comma-separated list of operatories to display (e.g., DL01,DL02)")
    parser.add_argument("--hide-notes", action="store_true", help="Hide schedule notes (entries without patient names)")

    args = parser.parse_args()
    target_date = args.date

    # Validate date format
    try:
        datetime.strptime(target_date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        return

    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return

    # Fetch appointments for the specified date
    appointments = fetch_appointments_for_date(request_key, target_date)

    # Get all operatories from appointments
    all_operatories = set()
    for appt in appointments:
        operatory = appt.get("operatory")
        if operatory and operatory not in ["", "N/A"]:
            all_operatories.add(operatory)

    print(f"\nOperatories found in appointments: {', '.join(sorted(all_operatories))}")

    # Parse operatories if provided
    selected_operatories = None
    if args.operatories:
        selected_operatories = [op.strip() for op in args.operatories.split(",")]

    # Display the appointments list
    display_appointments_list(
        appointments,
        target_date,
        selected_operatories=selected_operatories,
        show_notes=not args.hide_notes
    )

if __name__ == "__main__":
    main()
