import requests
import json
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Target date and provider
TARGET_DATE = "2025-05-16"
TARGET_PROVIDER = "LL01"

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        }
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def test_v2_appointments(request_key):
    """Test v2 appointments endpoint to check for operatory information."""
    print("\nTesting v2 appointments endpoint...")
    headers = {"Request-Key": request_key}
    params = {"date": TARGET_DATE}
    
    # Try different v2 endpoints that might contain appointment data
    endpoints = [
        "appointments",
        "appointment",
        "schedule"
    ]
    
    for endpoint in endpoints:
        print(f"\nTrying endpoint: {endpoint}")
        resp = requests.get(f"{API_BASE_V2}/{endpoint}", headers=headers, params=params)
        
        print(f"Response status: {resp.status_code}")
        if resp.status_code == 200:
            try:
                data = resp.json()
                print(f"Success! Got data")
                
                # Analyze the structure of the response
                if isinstance(data, list):
                    print(f"Response is a list with {len(data)} items")
                    if len(data) > 0:
                        print(f"First item keys: {list(data[0].keys())}")
                        
                        # Check for operatory-related fields
                        operatory_fields = [key for key in data[0].keys() if "operatory" in key.lower() or "chair" in key.lower() or "room" in key.lower()]
                        print(f"Operatory-related fields: {operatory_fields}")
                        
                        if operatory_fields:
                            print("\nSample operatory values:")
                            for i, appt in enumerate(data[:3]):  # Check first 3 appointments
                                print(f"Appointment {i+1}:")
                                for field in operatory_fields:
                                    print(f"  {field}: {appt.get(field)}")
                elif isinstance(data, dict):
                    if "items" in data:
                        items = data.get("items", [])
                        print(f"Response contains {len(items)} items")
                        if len(items) > 0:
                            print(f"Item keys: {list(items[0].keys())}")
                            
                            # Check for operatory-related fields
                            operatory_fields = [key for key in items[0].keys() if "operatory" in key.lower() or "chair" in key.lower() or "room" in key.lower()]
                            print(f"Operatory-related fields: {operatory_fields}")
                            
                            if operatory_fields:
                                print("\nSample operatory values:")
                                for i, appt in enumerate(items[:3]):  # Check first 3 appointments
                                    print(f"Appointment {i+1}:")
                                    for field in operatory_fields:
                                        print(f"  {field}: {appt.get(field)}")
                    else:
                        print(f"Response keys: {list(data.keys())}")
            except Exception as e:
                print(f"Error parsing response: {e}")
                print(f"Response text: {resp.text[:200]}...")
        elif resp.status_code != 204:  # No content is a valid response
            print("Error response:")
            print(resp.text[:500])

def test_dentrix_specific_endpoints(request_key):
    """Test Dentrix-specific endpoints that might contain operatory information."""
    print("\nTesting Dentrix-specific endpoints...")
    headers = {"Request-Key": request_key}
    params = {"date": TARGET_DATE}
    
    # Try endpoints that might be specific to Dentrix
    endpoints = [
        "dentrix/appointments",
        "dentrix/operatories",
        "dentrix/schedule",
        "operatories",
        "rooms",
        "chairs"
    ]
    
    for endpoint in endpoints:
        print(f"\nTrying endpoint: {endpoint}")
        try:
            resp = requests.get(f"{API_BASE_V2}/{endpoint}", headers=headers, params=params)
            
            print(f"Response status: {resp.status_code}")
            if resp.status_code == 200:
                try:
                    data = resp.json()
                    print(f"Success! Got data")
                    print(f"Response structure: {type(data)}")
                    if isinstance(data, list) and len(data) > 0:
                        print(f"First item keys: {list(data[0].keys())}")
                    elif isinstance(data, dict):
                        print(f"Response keys: {list(data.keys())}")
                except Exception as e:
                    print(f"Error parsing response: {e}")
                    print(f"Response text: {resp.text[:200]}...")
            elif resp.status_code != 204:  # No content is a valid response
                print("Error response:")
                print(resp.text[:500])
        except Exception as e:
            print(f"Error making request: {e}")

def main():
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Test v2 appointments endpoint
    test_v2_appointments(request_key)
    
    # Test Dentrix-specific endpoints
    test_dentrix_specific_endpoints(request_key)
    
    print("\nTesting complete.")

if __name__ == "__main__":
    main()
