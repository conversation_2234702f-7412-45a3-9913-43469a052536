import { NextRequest, NextResponse } from 'next/server';
import { TranscriptionService } from '@/lib/transcription-service';
import { transcriptionQueue } from '@/lib/transcription-queue';
import { featureFlags } from '@/lib/feature-flags';

interface UploadProgress {
  fileName: string;
  progress: number;
  status: 'pending' | 'queued' | 'processing' | 'completed' | 'error' | 'skipped';
  error?: string;
  jobId?: string;
  transcriptionId?: string;
  transcriptionText?: string;
  summaryText?: string;
  confidenceScore?: number;
  skipReason?: string;
}

interface BulkTranscriptionResult {
  success: boolean;
  totalFiles: number;
  successfulUploads: number;
  failedUploads: number;
  skippedUploads: number;
  results: UploadProgress[];
  processingTimeMs: number;
  queueInfo: {
    totalJobsQueued: number;
    estimatedProcessingTime: number;
  };
}

/**
 * TRANSCRIPTION-ONLY UPLOAD ENDPOINT
 * Upload → Validate → Queue for Transcription → Return Job IDs
 * No file storage - immediate transcription workflow
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Check if transcription-only workflow is enabled
    if (!featureFlags.enableTranscriptionOnlyWorkflow) {
      return NextResponse.json({
        error: 'Transcription-only workflow not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const deviceId = (formData.get('deviceId') as string) || 'direct-upload';
    const patientId = formData.get('patientId') as string | undefined;
    const userId = (formData.get('userId') as string) || 'system';
    const priority = (formData.get('priority') as 'low' | 'normal' | 'high') || 'normal';

    if (!files || files.length === 0) {
      return NextResponse.json({
        error: 'No audio files provided for transcription',
        code: 'NO_FILES'
      }, { status: 400 });
    }

    console.log(`🎤 Processing ${files.length} files for transcription upload`);

    // Get client IP for audit logging
    const clientIp = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';

    const results: UploadProgress[] = [];
    let successfulUploads = 0;
    let failedUploads = 0;
    let skippedUploads = 0;
    const jobIds: string[] = [];

    // Process each file
    for (const file of files) {
      const fileName = file.name;
      const result: UploadProgress = {
        fileName,
        progress: 0,
        status: 'pending'
      };

      try {
        // Convert File to Buffer
        const audioBuffer = Buffer.from(await file.arrayBuffer());
        
        // Validate file size
        if (audioBuffer.length === 0) {
          result.status = 'skipped';
          result.skipReason = 'Empty file';
          result.progress = 100;
          skippedUploads++;
          results.push(result);
          continue;
        }

        if (audioBuffer.length > 25 * 1024 * 1024) { // 25MB OpenAI limit
          result.status = 'skipped';
          result.skipReason = 'File too large (>25MB)';
          result.progress = 100;
          skippedUploads++;
          results.push(result);
          continue;
        }

        // Validate audio format
        const isValidFormat = await TranscriptionService.validateAudioFormat(audioBuffer, fileName);
        if (!isValidFormat) {
          result.status = 'skipped';
          result.skipReason = `Invalid format. Supported: ${TranscriptionService.getSupportedFormats().join(', ')}`;
          result.progress = 100;
          skippedUploads++;
          results.push(result);
          continue;
        }

        result.progress = 25;
        result.status = 'queued';

        // Queue the transcription job
        const jobId = await transcriptionQueue.enqueueJob(audioBuffer, {
          filename: fileName,
          deviceId,
          patientId,
          userId,
          ipAddress: clientIp
        }, priority);

        result.jobId = jobId;
        result.progress = 50;

        // Check if job completed immediately (for very small files)
        const jobStatus = transcriptionQueue.getJobStatus(jobId);
        if (jobStatus?.status === 'completed' && jobStatus.result) {
          result.status = 'completed';
          result.transcriptionId = jobStatus.result.transcriptionId;
          result.transcriptionText = jobStatus.result.transcriptionText;
          result.summaryText = jobStatus.result.summaryText;
          result.confidenceScore = jobStatus.result.confidenceScore;
          result.progress = 100;
          successfulUploads++;
        } else {
          // Job is queued for processing
          result.status = 'queued';
          result.progress = 75;
          successfulUploads++;
        }

        jobIds.push(jobId);
        results.push(result);

        console.log(`✅ Queued transcription for: ${fileName} (Job: ${jobId})`);

      } catch (error) {
        console.error(`❌ Failed to process ${fileName}:`, error);
        
        result.status = 'error';
        result.error = error instanceof Error ? error.message : 'Unknown error';
        result.progress = 100;
        failedUploads++;
        results.push(result);
      }
    }

    const processingTimeMs = Date.now() - startTime;

    // Get queue statistics for response
    const queueStats = transcriptionQueue.getStats();
    const estimatedProcessingTime = Math.max(queueStats.avgProcessingTimeMs * jobIds.length, 30000); // At least 30s

    const response: BulkTranscriptionResult = {
      success: failedUploads === 0,
      totalFiles: files.length,
      successfulUploads,
      failedUploads,
      skippedUploads,
      results,
      processingTimeMs,
      queueInfo: {
        totalJobsQueued: jobIds.length,
        estimatedProcessingTime
      }
    };

    console.log(`🎉 Upload completed: ${successfulUploads} queued, ${failedUploads} failed, ${skippedUploads} skipped`);

    return NextResponse.json(response, { 
      status: response.success ? 200 : 207 // 207 Multi-Status for partial success
    });

  } catch (error) {
    console.error('❌ Transcribe upload error:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Upload processing failed',
      code: 'UPLOAD_ERROR',
      processingTimeMs: Date.now() - startTime
    }, { status: 500 });
  }
}

/**
 * GET endpoint to check status of multiple jobs
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobIds = searchParams.get('jobIds')?.split(',') || [];

    if (jobIds.length === 0) {
      return NextResponse.json({
        error: 'Job IDs are required',
        code: 'MISSING_JOB_IDS'
      }, { status: 400 });
    }

    const jobStatuses = jobIds.map(jobId => {
      const status = transcriptionQueue.getJobStatus(jobId);
      return {
        jobId,
        found: !!status,
        status: status?.status || 'not_found',
        progress: getProgressFromStatus(status?.status),
        result: status?.result,
        error: status?.error,
        createdAt: status?.createdAt,
        completedAt: status?.completedAt
      };
    });

    const completed = jobStatuses.filter(job => job.status === 'completed').length;
    const failed = jobStatuses.filter(job => job.status === 'failed').length;
    const processing = jobStatuses.filter(job => job.status === 'processing').length;
    const queued = jobStatuses.filter(job => job.status === 'queued').length;

    return NextResponse.json({
      success: true,
      jobs: jobStatuses,
      summary: {
        total: jobIds.length,
        completed,
        failed,
        processing,
        queued,
        allCompleted: completed + failed === jobIds.length
      }
    });

  } catch (error) {
    console.error('❌ Job status check error:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Status check failed',
      code: 'STATUS_ERROR'
    }, { status: 500 });
  }
}

function getProgressFromStatus(status?: string): number {
  switch (status) {
    case 'queued': return 25;
    case 'processing': return 50;
    case 'completed': return 100;
    case 'failed': return 100;
    default: return 0;
  }
}