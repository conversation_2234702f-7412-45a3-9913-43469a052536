import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '../../../src/lib/api/sikka-client';
import { loadCredentials } from '../../../src/lib/api/credentials';

// Initialize Sikka client with credentials
const credentials = loadCredentials();
const sikkaClient = new SikkaApiClient(credentials);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const patientId = searchParams.get('patient_id');
    const appointmentId = searchParams.get('appointment_id');
    const date = searchParams.get('date');
    const dateRange = searchParams.get('date_range'); // e.g., "7" for 7 days
    const limit = searchParams.get('limit') || '50';
    const page = searchParams.get('page') || '1';

    console.log('Clinical Notes API called with params:', {
      patientId,
      appointmentId,
      date,
      dateRange,
      limit,
      page
    });

    let clinicalNotes: any[] = [];
    let totalCount = 0;
    const debugInfo: any = {
      source: '',
      searchCriteria: {},
      apiCalls: [],
      errors: []
    };

    // Priority 1: Get notes by appointment ID (most specific)
    if (appointmentId) {
      try {
        debugInfo.source = 'appointment';
        debugInfo.searchCriteria.appointmentId = appointmentId;
        debugInfo.apiCalls.push(`getMedicalNotesByAppointment(${appointmentId})`);
        
        clinicalNotes = await sikkaClient.getMedicalNotesByAppointment(appointmentId);
        console.log(`Found ${clinicalNotes.length} notes for appointment ${appointmentId}`);
      } catch (error) {
        console.error('Error fetching notes by appointment:', error);
        debugInfo.errors.push(`Appointment notes error: ${error.message}`);
      }
    }

    // Priority 2: Get notes by patient ID and date range
    if (clinicalNotes.length === 0 && patientId) {
      try {
        debugInfo.source = 'patient';
        debugInfo.searchCriteria.patientId = patientId;
        
        if (date) {
          // Get notes for specific date
          debugInfo.searchCriteria.date = date;
          debugInfo.apiCalls.push(`getMedicalNotes(${patientId}, ${date})`);
          clinicalNotes = await sikkaClient.getMedicalNotes(patientId, date);
        } else if (dateRange) {
          // Get notes for date range around today
          const today = new Date();
          const daysRange = parseInt(dateRange);
          const startDate = new Date(today);
          startDate.setDate(today.getDate() - daysRange);
          const endDate = new Date(today);
          endDate.setDate(today.getDate() + daysRange);

          debugInfo.searchCriteria.dateRange = `${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`;
          debugInfo.apiCalls.push(`getMedicalNotes(${patientId}) with date filtering`);
          
          // Get all notes for patient and filter by date range
          const allNotes = await sikkaClient.getMedicalNotes(patientId);
          clinicalNotes = allNotes.filter(note => {
            if (!note.date) return false;
            const noteDate = new Date(note.date);
            return noteDate >= startDate && noteDate <= endDate;
          });
        } else {
          // Get clinical notes for patient using the specific Clinical note endpoint
          debugInfo.apiCalls.push(`getPatientClinicalNotes(${patientId})`);
          clinicalNotes = await sikkaClient.getPatientClinicalNotes(patientId);
        }
        
        console.log(`Found ${clinicalNotes.length} notes for patient ${patientId}`);
      } catch (error) {
        console.error('Error fetching notes by patient:', error);
        debugInfo.errors.push(`Patient notes error: ${error.message}`);
      }
    }

    // Priority 3: Get notes by date only
    if (clinicalNotes.length === 0 && date) {
      try {
        debugInfo.source = 'date';
        debugInfo.searchCriteria.date = date;
        debugInfo.apiCalls.push(`getClinicalNotesByDate(${date})`);

        clinicalNotes = await sikkaClient.getClinicalNotesByDate(date);
        console.log(`Found ${clinicalNotes.length} clinical notes for date ${date}`);
      } catch (error) {
        console.error('Error fetching clinical notes by date:', error);
        debugInfo.errors.push(`Date notes error: ${error.message}`);
      }
    }

    // Debug: Log what we're getting from Sikka
    console.log(`Raw clinical notes from Sikka (first 3):`, clinicalNotes.slice(0, 3));

    // Filter out empty or invalid notes - be more permissive for now
    const validNotes = clinicalNotes.filter(note => {
      const hasNote = note && (note.notes || note.text || note.note_text);
      const noteText = note.notes || note.text || note.note_text || '';
      const hasContent = noteText && noteText.trim().length > 0;
      const isNotPurged = !noteText.toLowerCase().includes('purged') && !noteText.toLowerCase().includes('cancelled');

      console.log(`Note filter: hasNote=${hasNote}, hasContent=${hasContent}, isNotPurged=${isNotPurged}, text="${noteText?.substring(0, 50)}..."`);

      return hasNote && hasContent && isNotPurged;
    });

    // Apply pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    
    totalCount = validNotes.length;
    const paginatedNotes = validNotes.slice(startIndex, endIndex);

    // Function to format Dentrix codes for display
    const formatForDisplay = (text: string): string => {
      if (!text) return '';
      return text
        .replace(/\{LF\}/g, '\n')           // Line Feed -> newline
        .replace(/\{cm\}/g, ', ')           // Comma -> comma + space
        .replace(/\{CR\}/g, '\n')           // Carriage Return -> newline
        .replace(/\{TAB\}/g, '\t')          // Tab -> tab
        .trim();
    };

    // Function to format for copy-paste to Dentrix (preserve original codes)
    const formatForDentrix = (text: string): string => {
      if (!text) return '';
      // Keep original Dentrix formatting codes intact
      return text.trim();
    };

    // Enhance notes with additional metadata and formatting
    const enhancedNotes = paginatedNotes.map(note => {
      const rawNotes = note.notes || note.text || note.note_text || '';
      const formattedForDisplay = formatForDisplay(rawNotes);
      const formattedForDentrix = formatForDentrix(rawNotes);

      return {
        ...note,
        // Ensure consistent date format
        date: note.date ? new Date(note.date).toISOString().split('T')[0] : null,
        // Original raw notes with Dentrix formatting codes
        notesRaw: rawNotes,
        // Formatted for UI display (human readable)
        notes: formattedForDisplay,
        // Formatted for copy-paste back to Dentrix
        notesDentrix: formattedForDentrix,
        // Add note length for UI purposes
        noteLength: formattedForDisplay.length,
        // Add word count
        wordCount: formattedForDisplay ? formattedForDisplay.split(/\s+/).filter(w => w.length > 0).length : 0,
        // Add preview (first 100 characters of formatted text)
        preview: formattedForDisplay ? formattedForDisplay.substring(0, 100) + (formattedForDisplay.length > 100 ? '...' : '') : '',
        // Add timestamp for sorting
        timestamp: note.date ? new Date(note.date).getTime() : 0
      };
    });

    // Sort by date descending (most recent first)
    enhancedNotes.sort((a, b) => b.timestamp - a.timestamp);

    const response = {
      notes: enhancedNotes,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limitNum),
        hasNext: endIndex < totalCount,
        hasPrev: pageNum > 1
      },
      debug: debugInfo,
      summary: {
        totalNotes: totalCount,
        notesReturned: enhancedNotes.length,
        searchCriteria: debugInfo.searchCriteria,
        source: debugInfo.source
      }
    };

    console.log(`Clinical Notes API returning ${enhancedNotes.length} of ${totalCount} notes`);
    return NextResponse.json(response);

  } catch (error: any) {
    console.error('Clinical Notes API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch clinical notes',
        details: error.message,
        notes: [],
        pagination: { page: 1, limit: 50, total: 0, totalPages: 0, hasNext: false, hasPrev: false }
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { patient_id, appointment_id, note_text, note_type, created_date } = body;

    if (!patient_id || !note_text) {
      return NextResponse.json(
        { error: 'Patient ID and note text are required' },
        { status: 400 }
      );
    }

    const noteData = {
      patient_id,
      appointment_id,
      note_text,
      note_type: note_type || 'Clinical Note',
      created_date: created_date || new Date().toISOString().split('T')[0]
    };

    console.log('Creating clinical note:', noteData);

    const result = await sikkaClient.createMedicalNote(noteData);
    
    return NextResponse.json({
      success: true,
      note: result,
      message: 'Clinical note created successfully'
    });

  } catch (error: any) {
    console.error('Error creating clinical note:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create clinical note',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
