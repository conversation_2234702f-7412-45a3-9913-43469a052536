import { NextResponse } from 'next/server';
import { readdir, stat } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Function to ensure network authentication using environment variables
async function ensureNetworkAccess(uncPath: string): Promise<boolean> {
  try {
    // Try to access the path directly first (might work with stored credentials)
    await readdir(uncPath);
    console.log(`Direct access to ${uncPath} successful`);
    return true;
  } catch (error) {
    console.log(`Direct access to ${uncPath} failed, trying credential authentication...`);

    try {
      // Get credentials from environment variables
      const username = process.env.NETWORK_SHARE_USERNAME;
      const password = process.env.NETWORK_SHARE_PASSWORD;
      const domain = process.env.NETWORK_SHARE_DOMAIN || '';

      if (!username || !password) {
        console.log('Network credentials not configured in environment variables');
        return false;
      }

      // Build net use command with credentials
      const userParam = domain ? `${domain}\\${username}` : username;
      const netUseCommand = `net use "${uncPath}" /user:"${userParam}" "${password}" /persistent:no`;

      console.log(`Attempting authentication for ${uncPath} with user: ${userParam}`);
      const { stdout, stderr } = await execAsync(netUseCommand);

      if (stderr && stderr.includes('error')) {
        throw new Error(`Authentication failed: ${stderr}`);
      }

      console.log('Network authentication successful:', stdout);

      // Test access again
      await readdir(uncPath);
      console.log(`Authenticated access to ${uncPath} successful`);
      return true;
    } catch (authError) {
      console.log('Credential authentication failed:', authError.message);
      return false;
    }
  }
}

async function getRecordingDatesFromPath(dirPath: string, isNetwork: boolean = false): Promise<Record<string, number>> {
  const dateMap: Record<string, number> = {};

  // If this is a network path, ensure authentication first
  if (isNetwork && dirPath.startsWith('\\\\')) {
    const authSuccess = await ensureNetworkAccess(dirPath);
    if (!authSuccess) {
      console.log(`Authentication failed for ${dirPath}, skipping...`);
      return dateMap;
    }
  }

  async function scanDirectory(currentPath: string, depth: number = 0) {
    // Prevent infinite recursion
    if (depth > 3) return;

    try {
      const files = await Promise.race([
        readdir(currentPath),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Directory scan timeout')), 10000))
      ]) as string[];

      console.log(`Scanning directory ${currentPath}, found ${files.length} items`);

      for (const file of files) {
        try {
          const filePath = path.join(currentPath, file);
          const stats = await Promise.race([
            stat(filePath),
            new Promise((_, reject) => setTimeout(() => reject(new Error('File stat timeout')), 5000))
          ]) as any;

          if (stats.isDirectory()) {
            // Recursively scan subdirectories (for network share organization)
            await scanDirectory(filePath, depth + 1);
          } else if (stats.isFile()) {
            // Check if it's an audio file
            const isAudioFile = file.toLowerCase().match(/\.(webm|mp3|wav|m4a|ogg|wma)$/i);

            if (isAudioFile && stats.size > 0) { // Only count non-empty files
              const fileDate = stats.birthtime || stats.mtime;
              const dateKey = fileDate.toISOString().split('T')[0];
              console.log(`Found audio file: ${file}, date: ${dateKey}, size: ${stats.size}`);

              if (!dateMap[dateKey]) {
                dateMap[dateKey] = 0;
              }
              dateMap[dateKey]++;
            }
          }
        } catch (error) {
          console.error(`Error processing ${file}:`, error.message);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${currentPath}:`, error.message);
    }
  }

  await scanDirectory(dirPath);
  return dateMap;
}

export async function GET() {
  try {
    const allRecordingDates: Record<string, number> = {};
    const debugInfo = {
      networkPath: process.env.NETWORK_SHARE_PATH || '\\\\***********\\share\\RECORDINGS',
      localPath: path.join(process.cwd(), 'voice-recordings'),
      networkAvailable: false,
      localAvailable: false,
      networkDates: 0,
      localDates: 0,
      errors: [] as string[],
      lastCheckTime: new Date().toISOString()
    };

    // Check network share first with multiple path attempts
    const networkPaths = [
      process.env.NETWORK_SHARE_PATH || '\\\\***********\\share\\RECORDINGS'
    ];

    let networkSuccess = false;
    for (const networkPath of networkPaths) {
      try {
        console.log(`Scanning network path for dates: ${networkPath}`);

        const networkDates = await Promise.race([
          getRecordingDatesFromPath(networkPath, true),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Network scan timeout')), 15000))
        ]) as Record<string, number>;

        Object.entries(networkDates).forEach(([date, count]) => {
          allRecordingDates[date] = (allRecordingDates[date] || 0) + count;
        });

        debugInfo.networkAvailable = true;
        debugInfo.networkDates = Object.keys(networkDates).length;
        console.log(`Found recordings on ${Object.keys(networkDates).length} dates from network share:`, Object.keys(networkDates));
        networkSuccess = true;
        break;
      } catch (error) {
        const errorMsg = `Network path ${networkPath} failed: ${error.message}`;
        console.log(errorMsg);
        debugInfo.errors.push(errorMsg);
      }
    }

    if (!networkSuccess) {
      debugInfo.errors.push('All network paths failed - check credentials and network connectivity');
    }

    // Check local backup
    const localBackupPath = path.join(process.cwd(), 'voice-recordings');
    if (existsSync(localBackupPath)) {
      try {
        const localDates = await Promise.race([
          getRecordingDatesFromPath(localBackupPath, false),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Local scan timeout')), 10000))
        ]) as Record<string, number>;

        Object.entries(localDates).forEach(([date, count]) => {
          allRecordingDates[date] = (allRecordingDates[date] || 0) + count;
        });

        debugInfo.localAvailable = true;
        debugInfo.localDates = Object.keys(localDates).length;
        console.log(`Found recordings on ${Object.keys(localDates).length} dates from local backup`);
      } catch (error) {
        const errorMsg = `Failed to scan local recordings for dates: ${error.message}`;
        console.error(errorMsg);
        debugInfo.errors.push(errorMsg);
      }
    } else {
      debugInfo.errors.push(`Local backup directory does not exist: ${localBackupPath}`);
    }

    // Sort dates and get recent ones (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentDates = Object.entries(allRecordingDates)
      .filter(([date]) => new Date(date) >= thirtyDaysAgo)
      .sort(([a], [b]) => b.localeCompare(a)) // Most recent first
      .reduce((acc, [date, count]) => {
        acc[date] = count;
        return acc;
      }, {} as Record<string, number>);

    console.log(`Total recording dates found: ${Object.keys(allRecordingDates).length}, Recent: ${Object.keys(recentDates).length}`);

    return NextResponse.json({
      allDates: allRecordingDates,
      recentDates,
      totalDates: Object.keys(allRecordingDates).length,
      totalRecordings: Object.values(allRecordingDates).reduce((sum, count) => sum + count, 0),
      debug: debugInfo
    });

  } catch (error: any) {
    console.error('Error scanning recording dates:', error);
    return NextResponse.json({
      error: 'Failed to scan recording dates',
      allDates: {},
      recentDates: {},
      totalDates: 0,
      totalRecordings: 0,
      debug: {
        errors: [error.message],
        lastCheckTime: new Date().toISOString()
      }
    });
  }
}
