import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const openaiKey = process.env.OPENAI_API_KEY;
  
  return NextResponse.json({
    hasOpenAIKey: !!openaiKey,
    keyLength: openaiKey?.length || 0,
    keyPrefix: openaiKey?.substring(0, 10) || 'not set',
    isPlaceholder: openaiKey === 'your-openai-api-key-here' || openaiKey === 'sk-PASTE-YOUR-ACTUAL-API-KEY-HERE',
    allEnvKeys: Object.keys(process.env).filter(key => key.includes('OPENAI')),
    timestamp: new Date().toISOString()
  });
}
