// PM2 Configuration for Dental App Production
module.exports = {
  apps: [{
    name: 'dental-app',
    script: 'npm',
    args: 'start',
    cwd: './app',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      // Network share for voice recordings
      NETWORK_SHARE_PATH: '\\\\***********\\share\\RECORDINGS',
      // Local backup path
      LOCAL_BACKUP_PATH: 'C:\\DentalApp\\data\\voice-recordings',
      // Database path
      DATABASE_PATH: 'C:\\DentalApp\\data\\database.sqlite',
      // Logs path
      LOGS_PATH: 'C:\\DentalApp\\logs'
    },
    error_file: 'C:\\DentalApp\\logs\\error.log',
    out_file: 'C:\\DentalApp\\logs\\output.log',
    log_file: 'C:\\DentalApp\\logs\\combined.log',
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss',
    max_restarts: 10,
    min_uptime: '10s',
    restart_delay: 4000
  }]
};
