import json
import os
import sys
import subprocess
from datetime import datetime

def run_detailed_appointments(date):
    """Run the detailed_appointments.py script and capture its output."""
    print(f"Running detailed_appointments.py for date {date}...")
    result = subprocess.run(
        ["python", "detailed_appointments.py", "--date", date],
        capture_output=True,
        text=True
    )
    return result.stdout

def parse_appointments(output):
    """Parse the output of detailed_appointments.py and extract appointment data."""
    appointments = []

    # Find the appointment sections
    sections = output.split("--------------------------------------------------------------")

    for section in sections:
        if "Time:" in section and "Length:" in section:
            # This is an appointment section
            appointment = {}

            # Extract time
            time_match = section.split("Time:")[1].split("|")[0].strip()
            appointment["time"] = time_match

            # Extract length
            length_match = section.split("Length:")[1].split("|")[0].strip()
            appointment["length"] = length_match.split()[0]  # Just get the number

            # Extract provider
            provider_match = section.split("Provider:")[1].split("\n")[0].strip()
            appointment["provider"] = provider_match

            # Extract patient name
            patient_line = section.split("Patient:")[1].split("\n")[0].strip()
            appointment["patientName"] = patient_line

            # Extract description
            if "Description:" in section:
                desc_match = section.split("Description:")[1].split("\n")[0].strip()
                appointment["description"] = desc_match
            else:
                appointment["description"] = ""

            # Extract operatory
            if "OPERATORY:" in section:
                operatory_match = section.split("OPERATORY:")[1].split("\n")[0].strip()
                appointment["operatory"] = operatory_match
            else:
                # Try to find the operatory from the context
                for line in section.split("\n"):
                    if "OPERATORY:" in line:
                        operatory_match = line.split("OPERATORY:")[1].strip()
                        appointment["operatory"] = operatory_match
                        break
                else:
                    appointment["operatory"] = "Unknown"

            # Check if this is a blocked time
            appointment["isBlocked"] = "Blocked" in section or not appointment["patientName"]

            # Add a unique ID
            appointment["id"] = f"{appointment['operatory']}-{appointment['time']}"

            appointments.append(appointment)

    return appointments

def create_json_file(appointments, date):
    """Create a JSON file with the appointment data."""
    data = {
        "date": date,
        "appointments": appointments
    }

    # Create a date-specific JSON file
    filename = f"appointments_{date}.json"
    with open(filename, "w") as f:
        json.dump(data, f, indent=2)

    # Also create a generic appointments.json file for backward compatibility
    with open("appointments.json", "w") as f:
        json.dump(data, f, indent=2)

    print(f"Created {filename} with {len(appointments)} appointments")

def main():
    # Use today's date if no date is provided
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        date = datetime.now().strftime("%Y-%m-%d")

    # Run the detailed_appointments.py script
    output = run_detailed_appointments(date)

    # Parse the output
    appointments = parse_appointments(output)

    # Create the JSON file
    create_json_file(appointments, date)

if __name__ == "__main__":
    main()
