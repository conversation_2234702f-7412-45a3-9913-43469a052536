import { NextResponse } from 'next/server';
import { healthService } from '@/lib/monitoring/health-service';
import { alertService } from '@/lib/monitoring/alert-service';

export async function GET() {
  try {
    const healthStatus = await healthService.checkHealth();
    
    // Process health status for alerts
    await alertService.processHealthStatus(healthStatus);
    
    const httpStatus = healthStatus.status === 'healthy' ? 200 : 
                      healthStatus.status === 'degraded' ? 200 : 503;
    
    return NextResponse.json(healthStatus, { status: httpStatus });
  } catch (error) {
    console.error('❌ Health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Health check failed',
      components: {
        database: { status: 'unhealthy', error: 'Health check exception' },
        openai: { status: 'unhealthy', error: 'Health check exception' },
        queue: { status: 'unhealthy', error: 'Health check exception' },
        memory: { status: 'unhealthy', error: 'Health check exception' },
        disk: { status: 'unhealthy', error: 'Health check exception' }
      }
    }, { status: 503 });
  }
}