"use strict";exports.id=2624,exports.ids=[2624],exports.modules={4780:(e,r,s)=>{s.d(r,{cn:()=>l});var t=s(49384),a=s(82348);function l(...e){return(0,a.QP)((0,t.$)(e))}},23562:(e,r,s)=>{s.d(r,{k:()=>i});var t=s(60687),a=s(43210),l=s(31228),n=s(4780);let i=a.forwardRef(({className:e,value:r,...s},a)=>(0,t.jsx)(l.bL,{ref:a,className:(0,n.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...s,children:(0,t.jsx)(l.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})}));i.displayName=l.bL.displayName},29523:(e,r,s)=>{s.d(r,{$:()=>o});var t=s(60687),a=s(43210),l=s(81391),n=s(24224),i=s(4780);let d=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef(({className:e,variant:r,size:s,asChild:a=!1,...n},o)=>{let c=a?l.DX:"button";return(0,t.jsx)(c,{className:(0,i.cn)(d({variant:r,size:s,className:e})),ref:o,...n})});o.displayName="Button"},42624:(e,r,s)=>{s.d(r,{o:()=>o});var t=s(60687),a=s(43210),l=s(29523),n=s(44493),i=s(23562),d=s(10218);function o(){let{theme:e,resolvedTheme:r}=(0,d.D)(),s="dark"===r,[o,c]=(0,a.useState)(!1),[m,x]=(0,a.useState)(!0),[u,g]=(0,a.useState)([]),[p,y]=(0,a.useState)(0),[b,f]=(0,a.useState)({total:0,completed:0,failed:0,current:0,needsTranscription:0,needsSummary:0,alreadyCompleted:0,skippedInvalid:0,currentlyProcessing:0}),[h,v]=(0,a.useState)(""),[N,j]=(0,a.useState)(!1),[$,k]=(0,a.useState)(!1),w=(0,a.useRef)(null),S=(0,a.useRef)(null),C=e=>{let r=new Date().toLocaleTimeString();g(s=>[...s.slice(-50),`[${r}] ${e}`])},T=async()=>{x(!0),C("\uD83D\uDD0D Analyzing current file status...");try{let e=await fetch("/api/voice/analyze-files"),r=await e.json();r.success?(f(e=>({...e,total:r.analysis.totalFiles,needsTranscription:r.analysis.needsTranscription,needsSummary:r.analysis.needsSummary,alreadyCompleted:r.analysis.alreadyCompleted,skippedInvalid:r.analysis.skippedInvalid})),k(r.analysis.hasWorkToDo),j(!0),C(`📊 Analysis complete: ${r.analysis.needsTranscription} need transcription, ${r.analysis.needsSummary} need summary, ${r.analysis.alreadyCompleted} complete, ${r.analysis.skippedInvalid} invalid files skipped`),r.analysis.hasWorkToDo||C("✨ All files are already processed! No work needed.")):C(`❌ Analysis failed: ${r.error}`)}catch(e){C(`❌ Analysis error: ${e instanceof Error?e.message:"Unknown error"}`)}finally{x(!1)}};return(0,t.jsxs)(n.Zp,{className:`w-full ${s?"bg-gray-800 border-gray-700":""}`,children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:`flex items-center gap-2 ${s?"text-white":""}`,children:["\uD83C\uDFAF Real-Time Transcription Control",o&&(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-6",children:[N&&!o&&(0,t.jsxs)("div",{className:`p-4 rounded-lg border ${$?s?"bg-blue-900/20 border-blue-700":"bg-blue-50 border-blue-200":s?"bg-green-900/20 border-green-700":"bg-green-50 border-green-200"}`,children:[(0,t.jsx)("h4",{className:`font-semibold mb-2 ${$?s?"text-blue-300":"text-blue-800":s?"text-green-300":"text-green-800"}`,children:$?"\uD83D\uDCCB Files Ready for Processing":"✨ All Files Complete"}),(0,t.jsx)("p",{className:`text-sm ${$?s?"text-blue-200":"text-blue-700":s?"text-green-200":"text-green-700"}`,children:$?`Found ${b.needsTranscription+b.needsSummary} files that need processing. Click the button below to start.`:"All your files already have transcriptions and summaries. No processing needed!"})]}),(0,t.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,t.jsx)(l.$,{onClick:()=>{o||(c(!0),g([]),y(0),f({total:0,completed:0,failed:0,current:0,needsTranscription:0,needsSummary:0,alreadyCompleted:0,skippedInvalid:0,currentlyProcessing:0}),v(""),C("\uD83D\uDE80 Starting real-time transcription..."),w.current=new EventSource("/api/voice/transcribe-stream"),w.current.onmessage=e=>{try{let r=JSON.parse(e.data);switch(C(r.message),r.type){case"connected":C("\uD83D\uDD17 Connected to transcription stream");break;case"recordings_loaded":f(e=>({...e,total:r.count||0}));break;case"analysis_complete":f(e=>({...e,total:r.totalFiles||0,needsTranscription:r.needsTranscription||0,needsSummary:r.needsSummary||0,alreadyCompleted:r.alreadyCompleted||0,skippedInvalid:r.skippedInvalid||0}));break;case"progress":v(r.fileName||""),f(e=>({...e,current:r.current||0,total:r.total||e.total,completed:r.completed||e.completed,failed:r.failed||e.failed,currentlyProcessing:r.currentlyProcessing||e.currentlyProcessing})),r.total&&r.current&&y(r.current/r.total*100);break;case"file_chunking":case"file_chunked":case"file_summarizing":break;case"file_completed":f(e=>({...e,completed:r.completed||e.completed+1,currentlyProcessing:r.currentlyProcessing||e.currentlyProcessing,needsTranscription:r.remainingNeedsTranscription??e.needsTranscription,needsSummary:r.remainingNeedsSummary??e.needsSummary}));break;case"file_failed":f(e=>({...e,failed:r.failed||e.failed+1,currentlyProcessing:r.currentlyProcessing||e.currentlyProcessing}));break;case"completed":c(!1),v(""),y(100),C("\uD83C\uDF89 All processing complete!");break;case"error":c(!1),v(""),C(`💥 Error: ${r.message}`)}}catch(e){C(`⚠️ Failed to parse update: ${e}`)}},w.current.onerror=e=>{C("❌ Connection error occurred"),c(!1),w.current?.close()})},disabled:o||m||!$,className:$?"bg-green-600 hover:bg-green-700":"bg-gray-400",children:o?"⚡ Processing...":m?"\uD83D\uDD0D Analyzing...":$?"\uD83D\uDE80 Start Real-Time Processing":"✅ All Files Complete"}),o&&(0,t.jsx)(l.$,{onClick:()=>{w.current&&(w.current.close(),w.current=null),c(!1),v(""),C("⏹️ Transcription stopped by user")},variant:"destructive",children:"⏹️ Stop"}),N&&!o&&(0,t.jsx)(l.$,{onClick:T,variant:"outline",disabled:m,children:"\uD83D\uDD04 Refresh Analysis"})]}),o&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("span",{className:s?"text-gray-300":"text-gray-700",children:["Progress: ",b.current,"/",b.total]}),(0,t.jsxs)("span",{className:s?"text-gray-300":"text-gray-700",children:[Math.round(p),"%"]})]}),(0,t.jsx)(i.k,{value:p,className:"h-3"}),h&&(0,t.jsxs)("p",{className:`text-sm font-mono ${s?"text-gray-400":"text-gray-600"}`,children:["\uD83C\uDFB5 Currently processing: ",h]}),b.currentlyProcessing>0&&(0,t.jsxs)("p",{className:"text-sm text-blue-600 dark:text-blue-400 font-medium",children:["⚡ Parallel processing: ",b.currentlyProcessing," files running simultaneously"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:`text-center p-3 rounded border-2 ${s?"bg-orange-900/20 border-orange-700":"bg-orange-50 border-orange-200"}`,children:[(0,t.jsx)("div",{className:`text-3xl font-bold ${s?"text-orange-400":"text-orange-600"}`,children:b.needsTranscription+b.needsSummary}),(0,t.jsx)("div",{className:`text-sm font-medium ${s?"text-orange-300":"text-orange-700"}`,children:"Unprocessed Files"})]}),(0,t.jsxs)("div",{className:`text-center p-3 rounded ${s?"bg-green-900/20":"bg-green-50"}`,children:[(0,t.jsx)("div",{className:`text-2xl font-bold ${s?"text-green-400":"text-green-600"}`,children:b.completed}),(0,t.jsx)("div",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"Processed"})]}),(0,t.jsxs)("div",{className:`text-center p-3 rounded ${s?"bg-yellow-900/20":"bg-yellow-50"}`,children:[(0,t.jsx)("div",{className:`text-2xl font-bold ${s?"text-yellow-400":"text-yellow-600"}`,children:b.current}),(0,t.jsx)("div",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"Current"})]}),(0,t.jsxs)("div",{className:`text-center p-3 rounded ${s?"bg-red-900/20":"bg-red-50"}`,children:[(0,t.jsx)("div",{className:`text-2xl font-bold ${s?"text-red-400":"text-red-600"}`,children:b.failed}),(0,t.jsx)("div",{className:`text-sm ${s?"text-gray-400":"text-gray-600"}`,children:"Failed"})]})]}),N&&(b.needsTranscription>0||b.needsSummary>0||b.alreadyCompleted>0)&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h4",{className:`text-sm font-medium border-b pb-1 ${s?"text-gray-300 border-gray-600":"text-gray-700 border-gray-300"}`,children:"Detailed Breakdown"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-3",children:[(0,t.jsxs)("div",{className:`text-center p-2 rounded ${s?"bg-orange-900/20":"bg-orange-50"}`,children:[(0,t.jsx)("div",{className:`text-lg font-bold ${s?"text-orange-400":"text-orange-600"}`,children:b.needsTranscription}),(0,t.jsx)("div",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Need Transcription"})]}),(0,t.jsxs)("div",{className:`text-center p-2 rounded ${s?"bg-purple-900/20":"bg-purple-50"}`,children:[(0,t.jsx)("div",{className:`text-lg font-bold ${s?"text-purple-400":"text-purple-600"}`,children:b.needsSummary}),(0,t.jsx)("div",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Need Summary"})]}),(0,t.jsxs)("div",{className:`text-center p-2 rounded ${s?"bg-gray-700":"bg-gray-50"}`,children:[(0,t.jsx)("div",{className:`text-lg font-bold ${s?"text-gray-400":"text-gray-600"}`,children:b.alreadyCompleted}),(0,t.jsx)("div",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Already Complete"})]}),(0,t.jsxs)("div",{className:`text-center p-2 rounded ${s?"bg-slate-700":"bg-slate-50"}`,children:[(0,t.jsx)("div",{className:`text-lg font-bold ${s?"text-slate-400":"text-slate-600"}`,children:b.skippedInvalid}),(0,t.jsx)("div",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Invalid Files"})]}),(0,t.jsxs)("div",{className:`text-center p-2 rounded ${s?"bg-blue-900/20":"bg-blue-50"}`,children:[(0,t.jsx)("div",{className:`text-lg font-bold ${s?"text-blue-400":"text-blue-600"}`,children:b.total}),(0,t.jsx)("div",{className:`text-xs ${s?"text-gray-400":"text-gray-600"}`,children:"Total Files"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:`font-semibold mb-2 ${s?"text-white":"text-gray-900"}`,children:"\uD83D\uDD34 Live Activity Stream"}),(0,t.jsx)("div",{ref:S,className:"bg-black text-green-400 p-4 rounded h-64 overflow-y-auto font-mono text-sm",children:0===u.length?(0,t.jsx)("div",{className:"text-gray-500",children:"Ready to start processing. Click the button above to begin."}):u.map((e,r)=>(0,t.jsx)("div",{className:"mb-1",children:e},r))})]}),(0,t.jsxs)("div",{className:`border rounded p-4 ${s?"bg-blue-900/20 border-blue-700":"bg-blue-50 border-blue-200"}`,children:[(0,t.jsx)("h4",{className:`font-semibold mb-2 ${s?"text-blue-300":"text-blue-800"}`,children:"How This Works:"}),(0,t.jsxs)("ul",{className:`text-sm space-y-1 ${s?"text-blue-200":"text-blue-700"}`,children:[(0,t.jsx)("li",{children:"• Real-time streaming updates using Server-Sent Events"}),(0,t.jsx)("li",{children:"• Processes files directly from Azure Storage"}),(0,t.jsx)("li",{children:"• ⚡ High-speed parallel processing - up to 5 transcriptions + 8 summaries simultaneously"}),(0,t.jsx)("li",{children:"• \uD83D\uDD04 Two-phase workflow: transcriptions first, then summaries"}),(0,t.jsx)("li",{children:"• Intelligent analysis - only processes files that need work"}),(0,t.jsx)("li",{children:"• Shows live progress for each file being processed"}),(0,t.jsx)("li",{children:'• No more "nothing happens" - you see everything in real-time!'})]})]})]})]})}},44493:(e,r,s)=>{s.d(r,{Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i});var t=s(60687),a=s(43210),l=s(4780);let n=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));n.displayName="Card";let i=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...r}));i.displayName="CardHeader";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",e),...r}));o.displayName="CardContent",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"}};