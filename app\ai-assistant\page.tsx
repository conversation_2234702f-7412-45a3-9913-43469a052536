"use client";

import { AgentChat } from '@/components/ai/agent-chat';
import { PageHeader } from '@/components/ui/page-header';
import { useTheme } from 'next-themes';

export default function AIAssistantPage() {
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      <PageHeader
        title="Dentalapp"
        isHomePage={false}
        activeTab="ai-assistant"
      />

      <main className="max-w-4xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold">AI Office Manager</h1>
          </div>

          <div className="rounded-lg shadow">
            <AgentChat isDarkMode={isDarkMode} />
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">About AI Office Manager</h2>
            <p className="mb-3">
              The AI Office Manager helps you analyze practice data while keeping all patient information secure and private.
            </p>
            <h3 className="font-medium mb-2">Example questions you can ask:</h3>
            <ul className="list-disc pl-5 space-y-1">
              <li>How many appointments do we have scheduled next week?</li>
              <li>What's our busiest day for the current month?</li>
              <li>Which operatories are most frequently used?</li>
              <li>What procedures are most common in our practice?</li>
              <li>How many new patients did we see last month?</li>
            </ul>
          </div>
        </div>
      </main>
    </div>
  );
}