import requests
import json
from datetime import datetime, timedelta
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 60

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def check_appointment_dates(request_key, date):
    """Check if appointments are actually for the requested date."""
    headers = {"Request-Key": request_key}
    
    print(f"Checking appointments for {date}...")
    
    # Try v2 endpoint
    try:
        resp = requests.get(
            f"{API_BASE_V2}/appointments", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            
            # Extract items from v2 response
            if isinstance(data, list) and len(data) > 0:
                items = data[0].get("items", [])
                print(f"Found {len(items)} appointments from v2 endpoint")
                
                # Check appointment dates
                date_counts = {}
                for appt in items:
                    appt_date = appt.get("date", "Unknown")
                    if appt_date not in date_counts:
                        date_counts[appt_date] = 0
                    date_counts[appt_date] += 1
                
                print("\nAppointments by date:")
                for d, count in sorted(date_counts.items()):
                    print(f"  {d}: {count} appointments")
                
                # Check if any appointments are for the requested date
                if date in date_counts:
                    print(f"\nFound {date_counts[date]} appointments for the requested date: {date}")
                else:
                    print(f"\nNo appointments found for the requested date: {date}")
                
                # Print sample appointments
                print("\nSample appointments:")
                for i, appt in enumerate(items[:10]):
                    print(f"  {i+1}. Date: {appt.get('date', 'Unknown')} - Time: {appt.get('time', '')} - Patient: {appt.get('patient_name', 'Unknown')} - Operatory: {appt.get('operatory', 'N/A')} - Provider: {appt.get('provider_id', 'N/A')} - Description: {appt.get('description', '')}")
            else:
                print("No appointments found")
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try different date formats
    print("\nTrying different date formats...")
    date_formats = [
        date,  # YYYY-MM-DD
        date.replace("-", "/"),  # YYYY/MM/DD
        f"{date.split('-')[1]}/{date.split('-')[2]}/{date.split('-')[0]}"  # MM/DD/YYYY
    ]
    
    for date_format in date_formats:
        print(f"\nTrying date format: {date_format}")
        try:
            resp = requests.get(
                f"{API_BASE_V2}/appointments", 
                headers=headers, 
                params={"date": date_format},
                timeout=API_TIMEOUT
            )
            
            if resp.status_code == 200:
                data = resp.json()
                
                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    print(f"Found {len(items)} appointments")
                    
                    # Check appointment dates
                    date_counts = {}
                    for appt in items:
                        appt_date = appt.get("date", "Unknown")
                        if appt_date not in date_counts:
                            date_counts[appt_date] = 0
                        date_counts[appt_date] += 1
                    
                    print("\nAppointments by date:")
                    for d, count in sorted(date_counts.items()):
                        print(f"  {d}: {count} appointments")
                    
                    # Print sample appointments
                    print("\nSample appointments:")
                    for i, appt in enumerate(items[:5]):
                        print(f"  {i+1}. Date: {appt.get('date', 'Unknown')} - Time: {appt.get('time', '')} - Patient: {appt.get('patient_name', 'Unknown')} - Operatory: {appt.get('operatory', 'N/A')} - Provider: {appt.get('provider_id', 'N/A')} - Description: {appt.get('description', '')}")
                else:
                    print("No appointments found")
            else:
                print(f"Error: {resp.status_code}")
                print(resp.text)
        except Exception as e:
            print(f"Error: {e}")
    
    # Try v4 endpoint
    print("\nTrying v4 endpoint...")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/appointments", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            items = data.get("items", [])
            print(f"Found {len(items)} appointments from v4 endpoint")
            
            # Check appointment dates
            date_counts = {}
            for appt in items:
                appt_date = appt.get("date", "Unknown")
                if appt_date not in date_counts:
                    date_counts[appt_date] = 0
                date_counts[appt_date] += 1
            
            print("\nAppointments by date:")
            for d, count in sorted(date_counts.items()):
                print(f"  {d}: {count} appointments")
            
            # Print sample appointments
            print("\nSample appointments:")
            for i, appt in enumerate(items[:5]):
                print(f"  {i+1}. Date: {appt.get('date', 'Unknown')} - Time: {appt.get('time', '')} - Patient: {appt.get('patient_name', 'Unknown')} - Provider: {appt.get('provider_id', 'N/A')} - Description: {appt.get('description', '')}")
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")

def main():
    # Get date from command line or use default
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        date = "2023-05-16"  # Default date
    
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Check appointment dates
    check_appointment_dates(request_key, date)
    
    print("\nCheck complete.")

if __name__ == "__main__":
    main()
