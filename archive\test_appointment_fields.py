import requests
import json
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Target date and provider
TARGET_DATE = "2025-05-16"
TARGET_PROVIDER = "LL01"

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        }
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def analyze_appointment_fields(request_key):
    """Analyze all fields in appointment data to look for operatory information."""
    print("\nAnalyzing appointment fields for operatory information...")
    headers = {"Request-Key": request_key}
    params = {"date": TARGET_DATE}
    
    # Try both v4 and v2 appointments endpoints
    endpoints = [
        (f"{API_BASE_V4}/appointments", "v4"),
        (f"{API_BASE_V2}/appointments", "v2")
    ]
    
    for endpoint, version in endpoints:
        print(f"\nFetching appointments from {version} endpoint...")
        resp = requests.get(endpoint, headers=headers, params=params)
        
        if resp.status_code == 200:
            try:
                data = resp.json()
                
                # Extract items based on response structure
                if version == "v4":
                    items = data.get("items", [])
                else:  # v2
                    if isinstance(data, list) and len(data) > 0:
                        items = data[0].get("items", [])
                    else:
                        items = []
                
                print(f"Found {len(items)} appointments")
                
                if items:
                    # Get all unique field names across all appointments
                    all_fields = set()
                    for item in items:
                        all_fields.update(item.keys())
                    
                    print(f"All fields in appointments: {sorted(all_fields)}")
                    
                    # Look for fields that might contain operatory information
                    potential_operatory_fields = [
                        field for field in all_fields 
                        if any(keyword in field.lower() 
                              for keyword in ["operatory", "chair", "room", "location", "unit", "station", "bay"])
                    ]
                    
                    print(f"Potential operatory-related fields: {potential_operatory_fields}")
                    
                    # Check if any appointments have values in the description field that might indicate operatory
                    print("\nChecking description fields for operatory information...")
                    operatory_indicators = ["op", "operatory", "chair", "room", "bay", "unit"]
                    
                    for i, appt in enumerate(items[:10]):  # Check first 10 appointments
                        description = appt.get("description", "").lower()
                        
                        # Check if description contains operatory indicators
                        matches = [indicator for indicator in operatory_indicators if indicator in description]
                        
                        if matches:
                            print(f"Appointment {i+1} description: {appt.get('description')}")
                            print(f"  Potential operatory indicators: {matches}")
                    
                    # Check if any appointments have note fields that might contain operatory information
                    if "note" in all_fields:
                        print("\nChecking note fields for operatory information...")
                        for i, appt in enumerate(items[:10]):  # Check first 10 appointments
                            note = appt.get("note", "").lower()
                            
                            # Check if note contains operatory indicators
                            matches = [indicator for indicator in operatory_indicators if indicator in note]
                            
                            if matches:
                                print(f"Appointment {i+1} note: {appt.get('note')}")
                                print(f"  Potential operatory indicators: {matches}")
                    
                    # Check for any fields with numeric values that might be operatory IDs
                    print("\nChecking for numeric fields that might be operatory IDs...")
                    numeric_fields = [
                        field for field in all_fields 
                        if any(appt.get(field) and str(appt.get(field)).isdigit() for appt in items[:10])
                    ]
                    
                    print(f"Fields with numeric values: {numeric_fields}")
                    
                    # Print sample values for these fields
                    for field in numeric_fields:
                        print(f"\nValues for field '{field}':")
                        values = [appt.get(field) for appt in items[:10] if appt.get(field)]
                        print(f"  Sample values: {values}")
            except Exception as e:
                print(f"Error parsing response: {e}")
                print(f"Response text: {resp.text[:200]}...")
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text[:500])

def main():
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Analyze appointment fields
    analyze_appointment_fields(request_key)
    
    print("\nAnalysis complete.")

if __name__ == "__main__":
    main()
