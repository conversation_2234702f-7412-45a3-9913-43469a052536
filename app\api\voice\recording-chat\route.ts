import { NextRequest, NextResponse } from 'next/server';
import { BlobServiceClient } from '@azure/storage-blob';
import OpenAI from 'openai';

const AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING;
const CONTAINER_NAME = 'recordings';

// Helper function to convert stream to buffer
async function streamToBuffer(readableStream: NodeJS.ReadableStream): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    readableStream.on('data', (data) => {
      chunks.push(data instanceof Buffer ? data : Buffer.from(data));
    });
    readableStream.on('end', () => {
      resolve(Buffer.concat(chunks));
    });
    readableStream.on('error', reject);
  });
}

export async function POST(request: NextRequest) {
  try {
    const { recordingId, question, conversationHistory = [] } = await request.json();

    if (!recordingId || !question) {
      return NextResponse.json(
        { error: 'Recording ID and question are required' },
        { status: 400 }
      );
    }

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json({
        error: 'AI functionality is not currently available. Please contact your system administrator to configure the OpenAI API key.'
      }, { status: 503 });
    }

    console.log('Recording Chat: Processing query for recording:', recordingId);

    // Fetch recording data from Azure Blob Storage
    let recordingData = null;
    let transcription = '';
    let summary = '';

    if (AZURE_STORAGE_CONNECTION_STRING) {
      try {
        const blobServiceClient = BlobServiceClient.fromConnectionString(AZURE_STORAGE_CONNECTION_STRING);
        const containerClient = blobServiceClient.getContainerClient(CONTAINER_NAME);

        // Get the JSON file with transcription and summary
        const jsonBlobName = recordingId.replace(/\.[^/.]+$/, '') + '.json';
        const jsonBlobClient = containerClient.getBlockBlobClient(jsonBlobName);
        
        const exists = await jsonBlobClient.exists();
        if (exists) {
          const downloadResponse = await jsonBlobClient.download();
          if (downloadResponse.readableStreamBody) {
            const jsonBuffer = await streamToBuffer(downloadResponse.readableStreamBody);
            recordingData = JSON.parse(jsonBuffer.toString());
            transcription = recordingData.transcription || '';
            summary = recordingData.summary || '';
          }
        }
      } catch (error) {
        console.error('Error fetching recording data:', error);
      }
    }

    if (!transcription && !summary) {
      return NextResponse.json({
        error: 'No transcription or summary available for this recording. Please transcribe the recording first.'
      }, { status: 404 });
    }

    // Create context for the AI
    const context = {
      recordingId,
      transcription,
      summary,
      hasTranscription: !!transcription,
      hasSummary: !!summary
    };

    // Generate AI response using OpenAI
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const systemPrompt = `You are a dental assistant AI helping to analyze voice recordings from dental appointments. 

You have access to the following recording information:
- Recording ID: ${recordingId}
- Has Transcription: ${context.hasTranscription}
- Has Summary: ${context.hasSummary}

IMPORTANT GUIDELINES:
- You are analyzing a voice recording from a dental appointment
- Answer questions based ONLY on the transcription and summary provided
- If the transcription/summary doesn't contain information to answer the question, clearly state that
- Be helpful and specific in your responses
- Focus on clinical details, patient information, procedures mentioned, and appointment details
- Maintain professional dental terminology when appropriate
- Do not make up information not present in the transcription/summary

The user will ask questions about this recording, and you should provide helpful, accurate responses based on the available data.`;

    const messages = [
      { role: "system", content: systemPrompt },
      // Add conversation history
      ...conversationHistory.map((msg: any) => ({
        role: msg.role,
        content: msg.content
      })),
      // Add current context
      {
        role: "user",
        content: `Recording Context:
Transcription: ${transcription || 'Not available'}
Summary: ${summary || 'Not available'}

User Question: ${question}`
      }
    ];

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: messages as any,
      max_tokens: 800,
      temperature: 0.3
    });

    const aiResponse = response.choices[0]?.message?.content || 'I apologize, but I was unable to generate a response for your question.';

    return NextResponse.json({
      response: aiResponse,
      recordingId,
      context: {
        hasTranscription: context.hasTranscription,
        hasSummary: context.hasSummary,
        transcriptionLength: transcription.length,
        summaryLength: summary.length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Recording Chat: Error processing request:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to process chat request' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}