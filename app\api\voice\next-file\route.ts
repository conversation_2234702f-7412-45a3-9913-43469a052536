import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get current recordings status
    const baseUrl = request.url.split('/api')[0];
    const recordingsResponse = await fetch(`${baseUrl}/api/voice/recordings`);
    
    if (!recordingsResponse.ok) {
      throw new Error('Failed to fetch recordings');
    }
    
    const recordingsData = await recordingsResponse.json();
    const recordings = recordingsData.recordings || [];
    
    // Find the next file that needs processing
    // Priority: 1) Needs transcription, 2) Needs summary
    const nextFile = recordings.find(recording => {
      const hasTranscription = !!(recording.transcription && recording.transcription.trim());
      const hasSummary = !!(recording.summary && recording.summary.trim());
      
      // Prioritize files that need transcription first
      if (!hasTranscription) return true;
      // Then files that need summary
      if (!hasSummary) return true;
      
      return false;
    });
    
    if (!nextFile) {
      return NextResponse.json({
        success: true,
        hasNextFile: false,
        message: 'All files are fully processed',
        totalFiles: recordings.length,
        allProcessed: true
      });
    }
    
    const hasTranscription = !!(nextFile.transcription && nextFile.transcription.trim());
    const hasSummary = !!(nextFile.summary && nextFile.summary.trim());
    
    return NextResponse.json({
      success: true,
      hasNextFile: true,
      nextFile: {
        id: nextFile.id,
        name: nextFile.name,
        fileName: nextFile.fileName,
        size: nextFile.size,
        status: nextFile.status,
        hasTranscription,
        hasSummary,
        needsTranscription: !hasTranscription,
        needsSummary: !hasSummary,
        transcriptionLength: nextFile.transcription?.length || 0,
        summaryLength: nextFile.summary?.length || 0
      },
      remainingFiles: recordings.filter(r => {
        const hasT = !!(r.transcription && r.transcription.trim());
        const hasS = !!(r.summary && r.summary.trim());
        return !hasT || !hasS;
      }).length
    });
    
  } catch (error) {
    console.error('Next file error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}