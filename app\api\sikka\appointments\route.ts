import { NextRequest, NextResponse } from 'next/server';

/**
 * Sikka API Integration for Appointments
 * 
 * This endpoint provides access to Sikka appointment data
 * Based on user's memory: Sikka API explorer is available at 
 * https://api.sikkasoft.com/v4/portal/user/api-explorer
 */

export async function GET(request: NextRequest) {
  try {
    // Get configuration from environment variables
    const sikkaApiUrl = process.env.SIKKA_API_URL || 'https://api.sikkasoft.com/v4';
    const sikkaApiKey = process.env.SIKKA_API_KEY;
    
    if (!sikkaApiKey) {
      return NextResponse.json({
        error: 'Sikka API not configured',
        message: 'SIKKA_API_KEY environment variable not set',
        configured: false
      }, { status: 503 });
    }
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    const provider = searchParams.get('provider');
    const patientId = searchParams.get('patientId');
    
    // Build Sikka API request
    let sikkaEndpoint = `${sikkaApiUrl}/appointments`;
    const queryParams = new URLSearchParams();
    
    if (date) {
      queryParams.append('date', date);
    }
    if (provider) {
      queryParams.append('provider', provider);
    }
    if (patientId) {
      queryParams.append('patient_id', patientId);
    }
    
    if (queryParams.toString()) {
      sikkaEndpoint += `?${queryParams.toString()}`;
    }
    
    console.log(`Calling Sikka API: ${sikkaEndpoint}`);
    
    // Make request to Sikka API
    const sikkaResponse = await fetch(sikkaEndpoint, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${sikkaApiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    if (!sikkaResponse.ok) {
      const errorText = await sikkaResponse.text();
      console.error('Sikka API error:', {
        status: sikkaResponse.status,
        statusText: sikkaResponse.statusText,
        body: errorText
      });
      
      return NextResponse.json({
        error: 'Sikka API request failed',
        status: sikkaResponse.status,
        message: sikkaResponse.statusText,
        details: errorText
      }, { status: sikkaResponse.status });
    }
    
    const appointments = await sikkaResponse.json();
    
    return NextResponse.json({
      success: true,
      appointments: appointments,
      source: 'sikka',
      timestamp: new Date().toISOString()
    });
    
  } catch (error: any) {
    console.error('Sikka API integration error:', error);
    
    return NextResponse.json({
      error: 'Internal server error',
      message: error.message,
      configured: !!process.env.SIKKA_API_KEY
    }, { status: 500 });
  }
}

/**
 * Test endpoint for Sikka API configuration
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { apiKey, apiUrl } = body;
    
    if (!apiKey) {
      return NextResponse.json({
        error: 'API key required for testing'
      }, { status: 400 });
    }
    
    const testUrl = apiUrl || 'https://api.sikkasoft.com/v4';
    const testEndpoint = `${testUrl}/appointments?limit=1`;
    
    console.log(`Testing Sikka API: ${testEndpoint}`);
    
    const testResponse = await fetch(testEndpoint, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    const responseText = await testResponse.text();
    
    return NextResponse.json({
      success: testResponse.ok,
      status: testResponse.status,
      statusText: testResponse.statusText,
      response: responseText.substring(0, 500) + (responseText.length > 500 ? '...' : ''),
      endpoint: testEndpoint
    });
    
  } catch (error: any) {
    return NextResponse.json({
      error: 'Test failed',
      message: error.message
    }, { status: 500 });
  }
}
