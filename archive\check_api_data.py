import requests
import json
from datetime import datetime, timedelta
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 30

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def check_api_endpoints(request_key, date):
    """Check various API endpoints to see what data is available."""
    headers = {"Request-Key": request_key}
    
    # Check v2 appointments endpoint
    print(f"\nChecking v2 appointments endpoint for {date}...")
    try:
        resp = requests.get(
            f"{API_BASE_V2}/appointments", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            
            # Extract items from v2 response
            if isinstance(data, list) and len(data) > 0:
                items = data[0].get("items", [])
                print(f"Found {len(items)} appointments")
                
                # Check if appointments have operatory information
                has_operatory = any(a.get("operatory") for a in items)
                if has_operatory:
                    print("Appointments have operatory information")
                    
                    # Count appointments by operatory
                    operatory_counts = {}
                    for appt in items:
                        operatory = appt.get("operatory", "N/A")
                        if operatory not in operatory_counts:
                            operatory_counts[operatory] = 0
                        operatory_counts[operatory] += 1
                    
                    print("\nAppointments by operatory:")
                    for op, count in sorted(operatory_counts.items()):
                        print(f"  {op}: {count} appointments")
                else:
                    print("Appointments do NOT have operatory information")
                
                # Check if appointments have provider information
                has_provider = any(a.get("provider_id") for a in items)
                if has_provider:
                    print("\nAppointments have provider information")
                    
                    # Count appointments by provider
                    provider_counts = {}
                    for appt in items:
                        provider = appt.get("provider_id", "N/A")
                        if provider not in provider_counts:
                            provider_counts[provider] = 0
                        provider_counts[provider] += 1
                    
                    print("\nAppointments by provider:")
                    for provider, count in sorted(provider_counts.items()):
                        print(f"  {provider}: {count} appointments")
                else:
                    print("Appointments do NOT have provider information")
                
                # Check if appointments have MODNO information
                modno_count = sum(1 for a in items if "MODNO" in a.get("description", "").upper())
                print(f"\nFound {modno_count} MODNO appointments")
                
                # Print sample appointments
                print("\nSample appointments:")
                for i, appt in enumerate(sorted(items, key=lambda a: a.get("time", ""))[:10]):
                    print(f"  {i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('operatory', 'N/A')} - {appt.get('provider_id', 'N/A')} - {appt.get('description', '')}")
            else:
                print("No appointments found")
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Check v4 appointments endpoint
    print(f"\nChecking v4 appointments endpoint for {date}...")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/appointments", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            items = data.get("items", [])
            print(f"Found {len(items)} appointments")
            
            # Check if appointments have operatory information
            has_operatory = any("operatory" in a for a in items)
            if has_operatory:
                print("Appointments have operatory information")
                
                # Count appointments by operatory
                operatory_counts = {}
                for appt in items:
                    operatory = appt.get("operatory", "N/A")
                    if operatory not in operatory_counts:
                        operatory_counts[operatory] = 0
                    operatory_counts[operatory] += 1
                
                print("\nAppointments by operatory:")
                for op, count in sorted(operatory_counts.items()):
                    print(f"  {op}: {count} appointments")
            else:
                print("Appointments do NOT have operatory information")
            
            # Check if appointments have provider information
            has_provider = any("provider_id" in a for a in items)
            if has_provider:
                print("\nAppointments have provider information")
                
                # Count appointments by provider
                provider_counts = {}
                for appt in items:
                    provider = appt.get("provider_id", "N/A")
                    if provider not in provider_counts:
                        provider_counts[provider] = 0
                    provider_counts[provider] += 1
                
                print("\nAppointments by provider:")
                for provider, count in sorted(provider_counts.items()):
                    print(f"  {provider}: {count} appointments")
            else:
                print("Appointments do NOT have provider information")
            
            # Print sample appointments
            print("\nSample appointments:")
            for i, appt in enumerate(sorted(items, key=lambda a: a.get("time", ""))[:10]):
                print(f"  {i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('provider_id', 'N/A')} - {appt.get('description', '')}")
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Check v4 schedule endpoint
    print(f"\nChecking v4 schedule endpoint for {date}...")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/schedule", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            items = data.get("items", [])
            print(f"Found {len(items)} schedule items")
            
            # Print sample schedule items
            print("\nSample schedule items:")
            for i, item in enumerate(items[:10]):
                print(f"  {i+1}. {item}")
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Check v4 providers endpoint
    print(f"\nChecking v4 providers endpoint...")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/providers", 
            headers=headers,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            items = data.get("items", [])
            print(f"Found {len(items)} providers")
            
            # Print providers
            print("\nProviders:")
            for i, provider in enumerate(items):
                print(f"  {i+1}. {provider.get('provider_id', 'N/A')} - {provider.get('name', 'Unknown')}")
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Check v4 operatories endpoint
    print(f"\nChecking v4 operatories endpoint...")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/operatories", 
            headers=headers,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            items = data.get("items", [])
            print(f"Found {len(items)} operatories")
            
            # Print operatories
            print("\nOperatories:")
            for i, operatory in enumerate(items):
                print(f"  {i+1}. {operatory}")
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")

def main():
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Get date from command line or use default
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        date = "2023-05-16"  # Default date
    
    # Check API endpoints
    check_api_endpoints(request_key, date)
    
    print("\nCheck complete.")

if __name__ == "__main__":
    main()
