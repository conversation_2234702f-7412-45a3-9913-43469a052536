import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '../../../../src/lib/api/sikka-client';
import { loadCredentials } from '../../../../src/lib/api/credentials';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: appointmentId } = await params;

  try {
    
    if (!appointmentId) {
      return NextResponse.json(
        { error: 'Appointment ID is required' },
        { status: 400 }
      );
    }

    // Load credentials
    const credentials = await loadCredentials();
    if (!credentials) {
      return NextResponse.json(
        { error: 'API credentials not configured' },
        { status: 500 }
      );
    }

    // Initialize Sikka client
    const sikkaClient = new SikkaApiClient(credentials);

    // Get appointment details
    const appointment = await sikkaClient.getAppointmentById(appointmentId);

    if (!appointment) {
      // Check if this looks like a generated ID (contains date and operatory)
      const isGeneratedId = appointmentId.includes('-') && appointmentId.match(/^\d{4}-\d{2}-\d{2}-/);

      return NextResponse.json(
        {
          error: 'Appointment not found',
          message: isGeneratedId
            ? 'This appointment may not have a valid Sikka ID. Please access it from the schedule view.'
            : 'The requested appointment could not be found in the system.',
          appointmentId,
          suggestion: isGeneratedId ? 'view_schedule' : 'check_id'
        },
        { status: 404 }
      );
    }

    return NextResponse.json(appointment);

  } catch (error) {
    console.error('Error fetching appointment:', error);

    // Check if this looks like a generated ID (contains date and operatory)
    const isGeneratedId = appointmentId.includes('-') && appointmentId.match(/^\d{4}-\d{2}-\d{2}-/);

    return NextResponse.json(
      {
        error: 'Appointment not found',
        message: isGeneratedId
          ? 'This appointment may not have a valid Sikka ID. Please access it from the schedule view.'
          : 'Failed to fetch appointment details. The appointment may not exist or there may be a system error.',
        appointmentId,
        suggestion: isGeneratedId ? 'view_schedule' : 'try_again'
      },
      { status: 404 }
    );
  }
}
