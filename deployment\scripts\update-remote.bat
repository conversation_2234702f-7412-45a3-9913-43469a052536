@echo off
echo ========================================
echo    Dental App Remote Update
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

:: Set working directory
cd /d "C:\DentalApp"

echo [1/7] Checking Git installation...
git --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Git not found. Please install Git first.
    echo Download from: https://git-scm.com/download/win
    pause
    exit /b 1
)

echo [2/7] Stopping Dental App service...
call pm2 stop dental-app
if %errorLevel% neq 0 (
    echo Warning: Could not stop service (may not be running)
)

echo [3/7] Creating backup of current version...
set BACKUP_DIR=backups\backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_DIR=%BACKUP_DIR: =0%
mkdir "%BACKUP_DIR%" 2>nul
xcopy /E /I /Y "app" "%BACKUP_DIR%\app\" >nul
echo Backup created: %BACKUP_DIR%

echo [4/7] Checking for updates...
cd app
git fetch origin main
for /f %%i in ('git rev-list HEAD...origin/main --count') do set COMMITS_BEHIND=%%i

if %COMMITS_BEHIND%==0 (
    echo No updates available. App is up to date.
    echo [7/7] Restarting service...
    cd /d "C:\DentalApp"
    call pm2 start dental-app
    echo Update check complete - no changes needed.
    pause
    exit /b 0
)

echo Found %COMMITS_BEHIND% new updates available.

echo [5/7] Downloading updates...
git pull origin main
if %errorLevel% neq 0 (
    echo ERROR: Failed to download updates
    echo Restoring from backup...
    cd /d "C:\DentalApp"
    rmdir /s /q "app"
    xcopy /E /I /Y "%BACKUP_DIR%\app" "app\"
    call pm2 start dental-app
    echo Restore complete. App is running previous version.
    pause
    exit /b 1
)

echo [6/7] Installing dependencies...
call npm install --production
if %errorLevel% neq 0 (
    echo ERROR: Failed to install dependencies
    echo Restoring from backup...
    cd /d "C:\DentalApp"
    rmdir /s /q "app"
    xcopy /E /I /Y "%BACKUP_DIR%\app" "app\"
    call pm2 start dental-app
    echo Restore complete. App is running previous version.
    pause
    exit /b 1
)

echo [7/7] Restarting Dental App service...
cd /d "C:\DentalApp"
call pm2 restart dental-app
if %errorLevel% neq 0 (
    echo ERROR: Failed to restart service
    echo Please check logs and try manual restart
    pause
    exit /b 1
)

:: Wait for service to start
echo Waiting for service to start...
timeout /t 5 /nobreak >nul

:: Check if service is running
call pm2 list | findstr "dental-app" | findstr "online" >nul
if %errorLevel%==0 (
    echo.
    echo ========================================
    echo    Update Successful!
    echo ========================================
    echo.
    echo Dental App has been updated and restarted.
    echo Access the app at: http://localhost:3000
    echo.
    echo Changes applied: %COMMITS_BEHIND% updates
    echo Backup location: %BACKUP_DIR%
    echo.
    echo Update completed at: %date% %time%
) else (
    echo.
    echo ========================================
    echo    Update Warning
    echo ========================================
    echo.
    echo Update completed but service may not be running properly.
    echo Please check the service status and logs.
    echo.
    echo Run: C:\DentalApp\scripts\status.bat
    echo Or:  C:\DentalApp\scripts\logs.bat
)

echo.
echo Press any key to continue...
pause >nul
