'use client';

import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { type ThemeProviderProps } from 'next-themes/dist/types';
import { SWRConfig } from 'swr';

// SWR configuration optimized for performance
const swrConfig = {
  revalidateOnFocus: false, // Disable revalidation on focus to reduce API calls
  revalidateOnReconnect: true, // Keep revalidation on reconnect for data consistency
  dedupingInterval: 300000, // 5 minutes deduping (increased from 1 minute)
  refreshInterval: 0, // No automatic refresh (manual refresh only)
  errorRetryCount: 1, // Reduced retry count to prevent excessive requests
  shouldRetryOnError: false,
  revalidateIfStale: false, // Don't revalidate stale data automatically
  revalidateOnMount: true, // Only revalidate on mount
  // Global fetcher function with AbortController support
  fetcher: async (url: string) => {
    const controller = new AbortController();

    // Set a timeout for the request
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'Cache-Control': 'max-age=300', // 5 minute cache
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`);
      }
      return response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      throw error;
    }
  }
};

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      suppressHydrationWarning
      {...props}
    >
      <SWRConfig value={swrConfig}>
        {children}
      </SWRConfig>
    </NextThemesProvider>
  );
}
