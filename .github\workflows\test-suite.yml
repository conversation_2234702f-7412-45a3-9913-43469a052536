name: Test Suite

on:
  # Manual trigger
  workflow_dispatch:
  # Run on pull requests
  pull_request:
    branches: [ main ]
  # Optional: Run on schedule (weekly)
  schedule:
    - cron: '0 6 * * 1'  # Every Monday at 6 AM

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run unit tests (safe tests only)
      run: npm run test:ci
      continue-on-error: true
    
    - name: Run component tests
      run: |
        echo "Running component tests..."
        # Add specific component test commands here
      continue-on-error: true
    
    - name: Test report
      run: |
        echo "✅ Test suite completed"
        echo "📊 Check individual test results above"
        echo "🎯 This pipeline validates code quality without blocking deployment"

  api-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'  # Only run API tests manually
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Start test server
      run: |
        npm run build
        npm start &
        sleep 10
      
    - name: Run API health tests
      run: npm test tests/api-health.test.js
      env:
        TEST_BASE_URL: http://localhost:3000
