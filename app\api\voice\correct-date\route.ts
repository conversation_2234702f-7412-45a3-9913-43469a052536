import { NextRequest, NextResponse } from 'next/server';
import { AzureStorageService } from '../../../../src/lib/azure-storage';

/**
 * API endpoint for manually correcting recording dates
 * This provides a fallback when automatic date parsing fails
 */

export async function POST(request: NextRequest) {
  try {
    const { recordingId, correctedDate, reason } = await request.json();

    if (!recordingId || !correctedDate) {
      return NextResponse.json(
        { error: 'Recording ID and corrected date are required' },
        { status: 400 }
      );
    }

    // Validate date format
    const dateObj = new Date(correctedDate);
    if (isNaN(dateObj.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format' },
        { status: 400 }
      );
    }

    // Load existing corrections
    let dateCorrections = {};
    try {
      if (AzureStorageService.isConfigured()) {
        dateCorrections = await AzureStorageService.downloadJson('metadata/date-corrections.json') || {};
      } else {
        // Fallback to local file
        const fs = await import('fs/promises');
        const path = await import('path');
        const correctionsPath = path.join(process.cwd(), 'data', 'date-corrections.json');
        try {
          const data = await fs.readFile(correctionsPath, 'utf-8');
          dateCorrections = JSON.parse(data);
        } catch (error) {
          // File doesn't exist yet, start with empty object
        }
      }
    } catch (error) {
      console.log('No existing date corrections found, starting fresh');
    }

    // Add the correction
    dateCorrections[recordingId] = {
      correctedDate: dateObj.toISOString(),
      reason: reason || 'Manual correction',
      correctedAt: new Date().toISOString(),
      correctedBy: 'user' // Could be enhanced with actual user info
    };

    // Save corrections
    try {
      if (AzureStorageService.isConfigured()) {
        await AzureStorageService.uploadJson('metadata/date-corrections.json', dateCorrections);
      } else {
        // Fallback to local file
        const fs = await import('fs/promises');
        const path = await import('path');
        const dataDir = path.join(process.cwd(), 'data');
        const correctionsPath = path.join(dataDir, 'date-corrections.json');
        
        // Ensure data directory exists
        try {
          await fs.mkdir(dataDir, { recursive: true });
        } catch (error) {
          // Directory might already exist
        }
        
        await fs.writeFile(correctionsPath, JSON.stringify(dateCorrections, null, 2));
      }
    } catch (error) {
      console.error('Failed to save date correction:', error);
      return NextResponse.json(
        { error: 'Failed to save date correction' },
        { status: 500 }
      );
    }

    console.log(`Date corrected for recording ${recordingId}: ${correctedDate} (${reason})`);

    return NextResponse.json({
      success: true,
      recordingId,
      correctedDate: dateObj.toISOString(),
      message: 'Date correction saved successfully'
    });

  } catch (error: any) {
    console.error('Date correction error:', error);
    return NextResponse.json(
      { error: `Failed to correct date: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const recordingId = searchParams.get('recordingId');

    // Load date corrections
    let dateCorrections = {};
    try {
      if (AzureStorageService.isConfigured()) {
        dateCorrections = await AzureStorageService.downloadJson('metadata/date-corrections.json') || {};
      } else {
        // Fallback to local file
        const fs = await import('fs/promises');
        const path = await import('path');
        const correctionsPath = path.join(process.cwd(), 'data', 'date-corrections.json');
        try {
          const data = await fs.readFile(correctionsPath, 'utf-8');
          dateCorrections = JSON.parse(data);
        } catch (error) {
          // File doesn't exist yet
        }
      }
    } catch (error) {
      console.log('No date corrections found');
    }

    if (recordingId) {
      // Get correction for specific recording
      const correction = dateCorrections[recordingId];
      if (!correction) {
        return NextResponse.json(
          { error: 'No correction found for this recording' },
          { status: 404 }
        );
      }
      return NextResponse.json({ recordingId, correction });
    } else {
      // Get all corrections
      return NextResponse.json({ 
        corrections: dateCorrections,
        count: Object.keys(dateCorrections).length
      });
    }

  } catch (error: any) {
    console.error('Failed to get date corrections:', error);
    return NextResponse.json(
      { error: `Failed to get date corrections: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const recordingId = searchParams.get('recordingId');

    if (!recordingId) {
      return NextResponse.json(
        { error: 'Recording ID is required' },
        { status: 400 }
      );
    }

    // Load existing corrections
    let dateCorrections = {};
    try {
      if (AzureStorageService.isConfigured()) {
        dateCorrections = await AzureStorageService.downloadJson('metadata/date-corrections.json') || {};
      } else {
        // Fallback to local file
        const fs = await import('fs/promises');
        const path = await import('path');
        const correctionsPath = path.join(process.cwd(), 'data', 'date-corrections.json');
        try {
          const data = await fs.readFile(correctionsPath, 'utf-8');
          dateCorrections = JSON.parse(data);
        } catch (error) {
          // File doesn't exist yet
        }
      }
    } catch (error) {
      console.log('No existing date corrections found');
    }

    // Check if correction exists
    if (!dateCorrections[recordingId]) {
      return NextResponse.json(
        { error: 'No correction found for this recording' },
        { status: 404 }
      );
    }

    // Remove the correction
    delete dateCorrections[recordingId];

    // Save updated corrections
    try {
      if (AzureStorageService.isConfigured()) {
        await AzureStorageService.uploadJson('metadata/date-corrections.json', dateCorrections);
      } else {
        // Fallback to local file
        const fs = await import('fs/promises');
        const path = await import('path');
        const correctionsPath = path.join(process.cwd(), 'data', 'date-corrections.json');
        await fs.writeFile(correctionsPath, JSON.stringify(dateCorrections, null, 2));
      }
    } catch (error) {
      console.error('Failed to save updated corrections:', error);
      return NextResponse.json(
        { error: 'Failed to remove date correction' },
        { status: 500 }
      );
    }

    console.log(`Date correction removed for recording ${recordingId}`);

    return NextResponse.json({
      success: true,
      recordingId,
      message: 'Date correction removed successfully'
    });

  } catch (error: any) {
    console.error('Failed to remove date correction:', error);
    return NextResponse.json(
      { error: `Failed to remove date correction: ${error.message}` },
      { status: 500 }
    );
  }
}
