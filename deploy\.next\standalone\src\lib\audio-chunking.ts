import { OpenAI } from 'openai';

// OpenAI client initialized at runtime to avoid build-time errors

interface ChunkResult {
  chunkIndex: number;
  transcription: string;
  duration?: number;
}

interface ChunkingResult {
  success: boolean;
  fullTranscription: string;
  chunks: ChunkResult[];
  totalDuration?: number;
  error?: string;
}

// Circuit breaker state
interface CircuitBreakerState {
  failures: number;
  lastFailureTime: number;
  state: 'closed' | 'open' | 'half-open';
}

const circuitBreaker: CircuitBreakerState = {
  failures: 0,
  lastFailureTime: 0,
  state: 'closed'
};

const MAX_FAILURES = 3;
const CIRCUIT_BREAKER_TIMEOUT = 30000; // 30 seconds
const API_TIMEOUT = 60000; // 60 seconds for API calls

/**
 * Circuit breaker logic to prevent cascading failures
 */
function shouldAllowRequest(): boolean {
  const now = Date.now();
  
  if (circuitBreaker.state === 'open') {
    if (now - circuitBreaker.lastFailureTime > CIRCUIT_BREAKER_TIMEOUT) {
      circuitBreaker.state = 'half-open';
      return true;
    }
    return false;
  }
  
  return true;
}

function recordSuccess(): void {
  circuitBreaker.failures = 0;
  circuitBreaker.state = 'closed';
}

function recordFailure(): void {
  circuitBreaker.failures++;
  circuitBreaker.lastFailureTime = Date.now();
  
  if (circuitBreaker.failures >= MAX_FAILURES) {
    circuitBreaker.state = 'open';
    console.warn(`🚨 Circuit breaker opened after ${circuitBreaker.failures} failures`);
  }
}

/**
 * Create a timeout wrapper for API calls
 */
function withTimeout<T>(promise: Promise<T>, timeoutMs: number, operation: string): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`${operation} timed out after ${timeoutMs}ms`));
      }, timeoutMs);
    })
  ]);
}

/**
 * Split audio buffer into chunks for processing
 * Note: This is a simplified implementation. In production, you'd use
 * actual audio processing libraries like ffmpeg or audio-specific tools
 */
function createAudioChunks(
  audioBuffer: Buffer, 
  maxChunkSize: number = 23 * 1024 * 1024, // 23MB to leave room for overlap
  overlapBytes: number = 1024 * 1024 // 1MB overlap to avoid cutting words
): Buffer[] {
  const chunks: Buffer[] = [];
  let offset = 0;
  
  while (offset < audioBuffer.length) {
    const chunkEnd = Math.min(offset + maxChunkSize, audioBuffer.length);
    const chunk = audioBuffer.slice(offset, chunkEnd);
    chunks.push(chunk);
    
    // Move offset, accounting for overlap (except for the last chunk)
    if (chunkEnd < audioBuffer.length) {
      offset = chunkEnd - overlapBytes;
    } else {
      break;
    }
  }
  
  return chunks;
}

/**
 * Transcribe a large audio file by chunking it
 */
export async function transcribeWithChunking(
  audioBuffer: Buffer,
  fileName: string,
  contentType: string = 'audio/mpeg',
  recordingId?: string // Optional recording ID for progress updates
): Promise<ChunkingResult> {
  try {
    console.log(`🔧 Starting chunked transcription for ${fileName} (${(audioBuffer.length / 1024 / 1024).toFixed(1)}MB)`);
    
    // Check circuit breaker
    if (!shouldAllowRequest()) {
      const error = 'Circuit breaker is open - too many recent failures';
      console.error(`❌ ${error}`);
      
      // Update progress on circuit breaker failure
      if (recordingId) {
        try {
          const { updateTranscriptionProgress } = await import('../../app/api/voice/transcribe/progress/route');
          updateTranscriptionProgress(recordingId, 'error', 0, 0, 0, error);
        } catch (progressError) {
          console.warn('Could not update progress on circuit breaker failure:', progressError);
        }
      }
      
      return {
        success: false,
        fullTranscription: '',
        chunks: [],
        error
      };
    }
    
    // Create audio chunks
    const chunks = createAudioChunks(audioBuffer);
    console.log(`📦 Split into ${chunks.length} chunks`);
    
    // Initialize progress if recordingId is provided
    if (recordingId) {
      try {
        const { updateTranscriptionProgress } = await import('../../app/api/voice/transcribe/progress/route');
        updateTranscriptionProgress(recordingId, 'processing', 0, 0, chunks.length, 'Starting chunked transcription...');
      } catch (error) {
        console.warn('Could not initialize progress tracking:', error);
      }
    }
    
    const chunkResults: ChunkResult[] = [];
    const totalDuration = 0;
    let consecutiveFailures = 0;
    const MAX_CONSECUTIVE_FAILURES = 2;
    
    // Process each chunk
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      const chunkFileName = `${fileName.replace(/\.[^/.]+$/, '')}_chunk_${i + 1}.${fileName.split('.').pop()}`;
      
      console.log(`🎵 Processing chunk ${i + 1}/${chunks.length} (${(chunk.length / 1024 / 1024).toFixed(1)}MB)`);
      
      // Update progress for this chunk
      if (recordingId) {
        try {
          const { updateTranscriptionProgress } = await import('../../app/api/voice/transcribe/progress/route');
          const progress = Math.round(((i) / chunks.length) * 100);
          updateTranscriptionProgress(recordingId, 'processing', progress, i, chunks.length, `Processing chunk ${i + 1}/${chunks.length}...`);
        } catch (error) {
          console.warn('Could not update progress for chunk:', error);
        }
      }
      
      // Check for too many consecutive failures
      if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
        const error = `Too many consecutive failures (${consecutiveFailures}) - stopping transcription`;
        console.error(`❌ ${error}`);
        
        if (recordingId) {
          try {
            const { updateTranscriptionProgress } = await import('../../app/api/voice/transcribe/progress/route');
            updateTranscriptionProgress(recordingId, 'error', 0, 0, 0, error);
          } catch (progressError) {
            console.warn('Could not update progress on consecutive failure:', progressError);
          }
        }
        
        return {
          success: false,
          fullTranscription: '',
          chunks: chunkResults,
          error
        };
      }
      
      try {
        console.log(`🎵 Starting transcription for chunk ${i + 1}/${chunks.length} (${(chunk.length / 1024 / 1024).toFixed(1)}MB)`);
        
        // Create a proper File object for the chunk
        const chunkFile = new File([chunk], chunkFileName, { type: contentType });
        
        // Initialize OpenAI client at runtime
        const openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
        });

        console.log(`📡 Sending chunk ${i + 1} to OpenAI API...`);
        
        // Transcribe the chunk with timeout
        const transcriptionPromise = openai.audio.transcriptions.create({
          file: chunkFile,
          model: 'whisper-1',
          response_format: 'json',
          temperature: 0.2
        });
        
        const transcription = await withTimeout(
          transcriptionPromise,
          API_TIMEOUT,
          `OpenAI API call for chunk ${i + 1}`
        );
        
        console.log(`✅ Chunk ${i + 1} API response received: ${transcription.text?.length || 0} chars`);
        
        chunkResults.push({
          chunkIndex: i + 1,
          transcription: transcription.text || '',
          duration: undefined // OpenAI Whisper doesn't return duration in JSON format
        });
        
        console.log(`✅ Chunk ${i + 1} complete: ${transcription.text?.length || 0} chars`);
        
        // Reset consecutive failures on success
        consecutiveFailures = 0;
        recordSuccess();
        
        // Update progress after successful chunk completion
        if (recordingId) {
          try {
            const { updateTranscriptionProgress } = await import('../../app/api/voice/transcribe/progress/route');
            const progress = Math.round(((i + 1) / chunks.length) * 100);
            updateTranscriptionProgress(recordingId, 'processing', progress, i + 1, chunks.length, `Chunk ${i + 1}/${chunks.length} completed`);
          } catch (error) {
            console.warn('Could not update progress after chunk completion:', error);
          }
        }
        
        // Small delay between chunks to be nice to the API
        if (i < chunks.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
      } catch (chunkError: any) {
        console.error(`❌ Chunk ${i + 1} failed:`, chunkError);
        consecutiveFailures++;
        recordFailure();
        
        // Check if this is a corrupted audio error
        let errorMessage = chunkError instanceof Error ? chunkError.message : 'Unknown error';
        if (errorMessage.includes('could not be decoded') || errorMessage.includes('format is not supported')) {
          errorMessage = 'Audio format corrupted or unsupported';
        }
        
        // Check for timeout errors
        if (errorMessage.includes('timed out')) {
          errorMessage = `API timeout after ${API_TIMEOUT}ms`;
        }
        
        // Continue with other chunks even if one fails
        chunkResults.push({
          chunkIndex: i + 1,
          transcription: `[Chunk ${i + 1} transcription failed: ${errorMessage}]`,
        });
        
        // Update progress even for failed chunks
        if (recordingId) {
          try {
            const { updateTranscriptionProgress } = await import('../../app/api/voice/transcribe/progress/route');
            const progress = Math.round(((i + 1) / chunks.length) * 100);
            updateTranscriptionProgress(recordingId, 'processing', progress, i + 1, chunks.length, `Chunk ${i + 1}/${chunks.length} failed: ${errorMessage}`);
          } catch (error) {
            console.warn('Could not update progress after chunk failure:', error);
          }
        }
        
        // Add delay after failure to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    // Combine all transcriptions
    const fullTranscription = chunkResults
      .map(chunk => chunk.transcription)
      .join(' ')
      .replace(/\s+/g, ' ') // Clean up extra spaces
      .trim();
    
    const successfulChunks = chunkResults.filter(c => !c.transcription.includes('[Chunk'));
    
    console.log(`🎉 Chunked transcription complete: ${successfulChunks.length}/${chunks.length} chunks successful`);
    console.log(`📝 Total transcription length: ${fullTranscription.length} characters`);
    
    // Update final progress
    if (recordingId) {
      try {
        const { updateTranscriptionProgress } = await import('../../app/api/voice/transcribe/progress/route');
        const finalStatus = successfulChunks.length > 0 ? 'completed' : 'error';
        const finalMessage = successfulChunks.length > 0 
          ? `Transcription completed: ${successfulChunks.length}/${chunks.length} chunks successful`
          : 'All chunks failed to transcribe';
        updateTranscriptionProgress(recordingId, finalStatus, 100, chunks.length, chunks.length, finalMessage);
      } catch (error) {
        console.warn('Could not update final progress:', error);
      }
    }
    
    return {
      success: successfulChunks.length > 0,
      fullTranscription,
      chunks: chunkResults,
      totalDuration: totalDuration > 0 ? totalDuration : undefined,
      error: successfulChunks.length === 0 ? 'All chunks failed to transcribe' : undefined
    };
    
  } catch (error) {
    console.error('❌ Chunked transcription failed:', error);
    recordFailure();
    
    // Update progress on overall failure
    if (recordingId) {
      try {
        const { updateTranscriptionProgress } = await import('../../app/api/voice/transcribe/progress/route');
        updateTranscriptionProgress(recordingId, 'error', 0, 0, 0, `Transcription failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } catch (progressError) {
        console.warn('Could not update progress on failure:', progressError);
      }
    }
    
    return {
      success: false,
      fullTranscription: '',
      chunks: [],
      error: error instanceof Error ? error.message : 'Chunking failed'
    };
  }
}

/**
 * Check if a file needs chunking based on size
 * OpenAI Whisper API has a 25MB limit, so we use 23MB to account for overlap
 */
export function needsChunking(fileSize: number, maxSize: number = 23 * 1024 * 1024): boolean {
  return fileSize > maxSize;
}

/**
 * Estimate processing time for chunked transcription
 */
export function estimateChunkingTime(fileSize: number, averageTimePerMB: number = 2): string {
  const fileSizeMB = fileSize / (1024 * 1024);
  const estimatedMinutes = Math.ceil(fileSizeMB * averageTimePerMB);
  
  if (estimatedMinutes < 60) {
    return `${estimatedMinutes} minutes`;
  } else {
    const hours = Math.floor(estimatedMinutes / 60);
    const minutes = estimatedMinutes % 60;
    return `${hours}h ${minutes}m`;
  }
}