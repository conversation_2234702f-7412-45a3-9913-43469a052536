/**
 * Job Monitor API Route
 * 
 * Provides comprehensive job monitoring and management capabilities for the voice processing system.
 * This replaces scattered job tracking across multiple routes with a unified monitoring interface
 * that works with the new job queue service for reliable job tracking and management.
 */

import { NextRequest, NextResponse } from 'next/server';
import { jobStore } from '../../../../archive/legacy-voice-processing/job-persistence';
import { getJob, getJobsByStatus, getProcessingStats, updateJobProgress, getFailedJobs, logMessage } from '@/lib/database/index';
import { createJobLogger, logger, logError, withTiming } from '@/lib/logger';
import { JobFilters } from '@/types/queue';

/**
 * Get job monitoring data with comprehensive filtering and batch tracking
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  const requestLogger = createJobLogger('job-monitor', 'monitor');
  
  try {
    const { searchParams } = new URL(request.url);
    const batchId = searchParams.get('batchId');
    const jobId = searchParams.get('jobId');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const includeStats = searchParams.get('includeStats') !== 'false';
    const includeStuckJobs = searchParams.get('includeStuckJobs') === 'true';
    const includeDetails = searchParams.get('includeDetails') === 'true';

    if (jobId) {
      // Get specific job details
      return await withTiming(requestLogger, 'get-job-details', async () => {
        const job = await getJob(jobId);
        
        if (!job) {
          return NextResponse.json({
            success: false,
            error: 'Job not found'
          }, { status: 404 });
        }

        requestLogger.debug('Retrieved job details', {
          jobId: job.id,
          status: job.status,
          filename: job.filename
        });

        return NextResponse.json({
          success: true,
          job: {
            id: job.id,
            filename: job.filename,
            containerPath: job.containerPath,
            status: job.status,
            retryCount: job.retryCount,
            createdAt: job.createdAt,
            updatedAt: job.updatedAt,
            startedAt: job.startedAt,
            completedAt: job.completedAt,
            errorDetails: job.errorDetails,
            results: job.results,
            metadata: job.metadata,
            processingTimeMs: job.startedAt && job.completedAt ? 
              new Date(job.completedAt).getTime() - new Date(job.startedAt).getTime() : undefined
          }
        });
      });
    }

    // Build filters for job listing
    const filters: JobFilters = {
      limit
    };

    if (status) {
      filters.status = status.split(',') as any[];
    }

    if (batchId) {
      // Note: This requires enhanced job metadata tracking
      // For now, we'll filter jobs by metadata after retrieval
      requestLogger.info('Batch filtering requested', { batchId });
    }

    return await withTiming(requestLogger, 'get-job-listing', async () => {
      // Get jobs based on filters
      const jobs = await jobStore.listJobs(filters);
      
      // Apply batch filtering if specified
      let filteredJobs = jobs;
      if (batchId) {
        filteredJobs = jobs.filter(job => 
          job.metadata?.batchId === batchId
        );
      }

      // Prepare response data
      const responseData: any = {
        success: true,
        jobs: filteredJobs.map(job => ({
          id: job.id,
          filename: job.filename,
          containerPath: job.containerPath,
          status: job.status,
          retryCount: job.retryCount,
          createdAt: job.createdAt,
          updatedAt: job.updatedAt,
          startedAt: job.startedAt,
          completedAt: job.completedAt,
          processingTimeMs: job.startedAt && job.completedAt ? 
            new Date(job.completedAt).getTime() - new Date(job.startedAt).getTime() : undefined,
          error: job.errorDetails?.message,
          type: job.metadata?.type || 'unknown',
          batchId: job.metadata?.batchId,
          ...(includeDetails && {
            errorDetails: job.errorDetails,
            results: job.results,
            metadata: job.metadata
          })
        })),
        totalJobs: filteredJobs.length,
        filters: {
          batchId,
          status,
          limit
        }
      };

      // Include queue statistics if requested
      if (includeStats) {
        const stats = await getProcessingStats();
        responseData.queueStats = stats;
      }

      // Include stuck jobs if requested
      if (includeStuckJobs) {
        const stuckJobs = await jobStore.getStuckJobs();
        responseData.stuckJobs = stuckJobs.map(job => ({
          id: job.id,
          filename: job.filename,
          status: job.status,
          startedAt: job.startedAt,
          retryCount: job.retryCount,
          stuckDurationMs: job.startedAt ? 
            Date.now() - new Date(job.startedAt).getTime() : undefined
        }));
      }

      // Calculate batch statistics if batch filtering is active
      if (batchId && filteredJobs.length > 0) {
        const batchStats = {
          total: filteredJobs.length,
          pending: filteredJobs.filter(j => j.status === 'pending').length,
          processing: filteredJobs.filter(j => j.status === 'processing').length,
          completed: filteredJobs.filter(j => j.status === 'completed').length,
          failed: filteredJobs.filter(j => j.status === 'failed').length,
          retrying: filteredJobs.filter(j => j.status === 'retrying').length,
          progress: filteredJobs.length > 0 ? 
            Math.round((filteredJobs.filter(j => j.status === 'completed').length / filteredJobs.length) * 100) : 0
        };
        responseData.batchStats = batchStats;
      }

      requestLogger.debug('Retrieved job listing', {
        totalJobs: filteredJobs.length,
        batchId,
        status,
        includeStats,
        includeStuckJobs
      });

      return NextResponse.json(responseData);
    });

  } catch (error) {
    logError(requestLogger, error, 'job-monitor-query');
    return NextResponse.json({
      success: false,
      error: 'Failed to retrieve job monitoring data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Manage jobs (retry, cancel, cleanup)
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  const requestLogger = createJobLogger('job-manage', 'monitor');
  
  try {
    const { action, jobId, jobIds, batchId, retryConfig, olderThanDays } = await request.json();

    if (!action) {
      return NextResponse.json({
        success: false,
        error: 'Action is required'
      }, { status: 400 });
    }

    requestLogger.info('Job management action requested', {
      action,
      jobId,
      jobIds: jobIds?.length,
      batchId
    });

    switch (action) {
      case 'retry': {
        if (!jobId) {
          return NextResponse.json({
            success: false,
            error: 'Job ID is required for retry action'
          }, { status: 400 });
        }

        return await withTiming(requestLogger, 'retry-job', async () => {
          const success = await updateJobProgress(jobId, 'pending');
          
          if (success) {
            requestLogger.info('Job retry scheduled successfully', { jobId });
            return NextResponse.json({
              success: true,
              message: 'Job retry scheduled successfully'
            });
          } else {
            requestLogger.warn('Job retry failed - job not eligible', { jobId });
            return NextResponse.json({
              success: false,
              error: 'Job is not eligible for retry'
            }, { status: 400 });
          }
        });
      }

      case 'retry_batch': {
        if (!batchId) {
          return NextResponse.json({
            success: false,
            error: 'Batch ID is required for batch retry action'
          }, { status: 400 });
        }

        return await withTiming(requestLogger, 'retry-batch', async () => {
          // Get all failed jobs from the batch
          const batchJobs = await jobStore.listJobs({
            status: ['failed'],
            limit: 1000
          });
          
          const failedBatchJobs = batchJobs.filter(job => 
            job.metadata?.batchId === batchId
          );

          let retryCount = 0;
          const errors: string[] = [];

          for (const job of failedBatchJobs) {
            try {
              const success = await jobQueueService.retryFailedJob(job.id, retryConfig);
              if (success) {
                retryCount++;
              }
            } catch (error) {
              errors.push(`Failed to retry job ${job.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
          }

          requestLogger.info('Batch retry completed', {
            batchId,
            totalJobs: failedBatchJobs.length,
            retriedJobs: retryCount,
            errors: errors.length
          });

          return NextResponse.json({
            success: retryCount > 0,
            retriedJobs: retryCount,
            totalJobs: failedBatchJobs.length,
            errors: errors.length > 0 ? errors : undefined,
            message: `Retried ${retryCount} of ${failedBatchJobs.length} failed jobs`
          });
        });
      }

      case 'cleanup': {
        const retentionDays = olderThanDays || 30;
        
        return await withTiming(requestLogger, 'cleanup-jobs', async () => {
          const cleanedCount = await jobStore.cleanup(retentionDays);
          
          requestLogger.info('Job cleanup completed', {
            cleanedCount,
            retentionDays
          });

          return NextResponse.json({
            success: true,
            cleanedCount,
            retentionDays,
            message: `Cleaned up ${cleanedCount} old jobs`
          });
        });
      }

      case 'queue_health': {
        return await withTiming(requestLogger, 'get-queue-health', async () => {
          const stats = await getProcessingStats();
          const stuckJobs = await jobStore.getStuckJobs();
          
          // Determine health indicators
          const healthIndicators = {
            queueBacklog: stats.pending > 100 ? 'warning' : 'healthy',
            stuckJobs: stuckJobs.length > 10 ? 'critical' : stuckJobs.length > 5 ? 'warning' : 'healthy',
            failureRate: stats.total > 0 ? (stats.failed / stats.total) : 0,
            processingRate: stats.averageProcessingTime || 0
          };

          const overallHealth = 
            healthIndicators.stuckJobs === 'critical' || healthIndicators.failureRate > 0.5 ? 'unhealthy' :
            healthIndicators.stuckJobs === 'warning' || healthIndicators.failureRate > 0.2 ? 'degraded' : 'healthy';

          requestLogger.debug('Queue health assessment completed', {
            overallHealth,
            queueBacklog: stats.pending,
            stuckJobs: stuckJobs.length,
            failureRate: healthIndicators.failureRate
          });

          return NextResponse.json({
            success: true,
            health: {
              overall: overallHealth,
              indicators: healthIndicators,
              stats,
              stuckJobs: stuckJobs.length,
              lastUpdated: new Date().toISOString()
            }
          });
        });
      }

      default:
        return NextResponse.json({
          success: false,
          error: `Unknown action: ${action}`
        }, { status: 400 });
    }

  } catch (error) {
    logError(requestLogger, error, 'job-management');
    return NextResponse.json({
      success: false,
      error: 'Failed to execute job management action',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Real-time job status updates (Server-Sent Events)
 */
export async function PATCH(request: NextRequest): Promise<NextResponse> {
  const requestLogger = createJobLogger('job-updates', 'monitor');
  
  try {
    const { searchParams } = new URL(request.url);
    const mode = searchParams.get('mode');

    if (mode === 'sse') {
      // Server-Sent Events endpoint for real-time updates
      requestLogger.info('Starting SSE connection for job updates');
      
      const encoder = new TextEncoder();
      const stream = new ReadableStream({
        start(controller) {
          const sendUpdate = async () => {
            try {
              const stats = await getProcessingStats();
              const data = {
                timestamp: new Date().toISOString(),
                stats,
                type: 'queue_stats'
              };
              
              const message = `data: ${JSON.stringify(data)}\n\n`;
              controller.enqueue(encoder.encode(message));
            } catch (error) {
              logError(requestLogger, error, 'sse-update');
            }
          };

          // Send initial update
          sendUpdate();
          
          // Send updates every 5 seconds
          const interval = setInterval(sendUpdate, 5000);
          
          // Cleanup on close
          const cleanup = () => {
            clearInterval(interval);
            controller.close();
          };

          // Handle client disconnect
          setTimeout(cleanup, 300000); // 5 minutes timeout
        }
      });

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control'
        }
      });
    }

    return NextResponse.json({
      success: false,
      error: 'Invalid mode specified'
    }, { status: 400 });

  } catch (error) {
    logError(requestLogger, error, 'job-updates');
    return NextResponse.json({
      success: false,
      error: 'Failed to provide job updates',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}