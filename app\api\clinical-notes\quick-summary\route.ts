import { NextRequest, NextResponse } from 'next/server';
import OpenA<PERSON> from 'openai';
import { writeFile, readFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import crypto from 'crypto';

// Cache directory for summaries
const CACHE_DIR = path.join(process.cwd(), 'data', 'note-summaries');

// Ensure cache directory exists
async function ensureCacheDir() {
  if (!existsSync(CACHE_DIR)) {
    await mkdir(CACHE_DIR, { recursive: true });
  }
}

// Generate cache key from note content
function generateCacheKey(noteText: string): string {
  return crypto.createHash('md5').update(noteText.trim()).digest('hex');
}

// Load cached summary
async function loadCachedSummary(cacheKey: string): Promise<string | null> {
  try {
    const cachePath = path.join(CACHE_DIR, `${cacheKey}.json`);
    if (existsSync(cachePath)) {
      const cached = JSON.parse(await readFile(cachePath, 'utf-8'));
      // Check if cache is less than 30 days old
      const cacheAge = Date.now() - new Date(cached.createdAt).getTime();
      const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days

      if (cacheAge < maxAge) {
        return cached.summary;
      }
    }
  } catch (error) {
    console.log('Cache read error:', error);
  }
  return null;
}

// Save summary to cache
async function saveCachedSummary(cacheKey: string, summary: string, originalText: string) {
  try {
    await ensureCacheDir();
    const cachePath = path.join(CACHE_DIR, `${cacheKey}.json`);
    const cacheData = {
      summary,
      originalText: originalText.substring(0, 200), // Store preview for debugging
      createdAt: new Date().toISOString(),
      model: 'gpt-4o-mini'
    };
    await writeFile(cachePath, JSON.stringify(cacheData, null, 2));
  } catch (error) {
    console.error('Cache write error:', error);
  }
}

// Clean up Dentrix formatting codes
function cleanDentrixText(text: string): string {
  return text
    .replace(/\{LF\}/g, '\n')
    .replace(/\{cm\}/g, ', ')
    .replace(/\{CR\}/g, '\n')
    .replace(/\{TAB\}/g, '  ')
    .replace(/\{[^}]+\}/g, '') // Remove any other formatting codes
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

export async function POST(request: NextRequest) {
  try {
    const { noteText, noteId } = await request.json();

    if (!noteText) {
      return NextResponse.json({ error: 'Note text is required' }, { status: 400 });
    }

    // Clean the text first
    const cleanedText = cleanDentrixText(noteText);

    // Generate cache key
    const cacheKey = noteId || generateCacheKey(cleanedText);

    // Try to load from cache first
    const cachedSummary = await loadCachedSummary(cacheKey);
    if (cachedSummary) {
      return NextResponse.json({
        summary: cachedSummary,
        cleanedText,
        cached: true
      });
    }

    // Initialize OpenAI client with error handling
    let openai: OpenAI;
    try {
      if (!process.env.OPENAI_API_KEY) {
        throw new Error('OpenAI API key not configured');
      }
      openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
    } catch (error) {
      console.error('OpenAI initialization error:', error);
      return NextResponse.json({
        error: 'OpenAI service not available',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, { status: 500 });
    }

    // Create a very short summary using GPT-4o-mini (low cost)
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: `You are a dental assistant creating brief visit summaries.
          Create a 2-3 sentence summary that tells what was done during this dental visit.
          Focus on: procedures performed, teeth treated, conditions diagnosed, treatments provided.
          Use simple language like "Patient had cleaning and exam" or "Filled cavity on tooth #14" or "Routine checkup with no issues found".
          Keep it under 60 words and make it sound natural.
          If the note is mostly administrative, just say "Administrative visit".`
        },
        {
          role: 'user',
          content: `What was done during this dental visit? Summarize briefly:\n\n${cleanedText}`
        }
      ],
      max_tokens: 100,
      temperature: 0.3
    });

    const summary = completion.choices[0]?.message?.content?.trim();

    if (!summary) {
      return NextResponse.json({ error: 'Failed to generate summary' }, { status: 500 });
    }

    // Save to cache
    await saveCachedSummary(cacheKey, summary, cleanedText);

    return NextResponse.json({
      summary,
      cleanedText,
      cached: false,
      usage: completion.usage
    });

  } catch (error) {
    console.error('Quick summary error:', error);

    if (error instanceof Error && error.message.includes('API key')) {
      return NextResponse.json({
        error: 'OpenAI API key not configured properly',
        details: error.message
      }, { status: 500 });
    }

    return NextResponse.json({
      error: 'Failed to generate summary',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
