import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { recordingIds, date, recordings } = await request.json();

    if (!recordingIds || !Array.isArray(recordingIds)) {
      return NextResponse.json(
        { error: 'Recording IDs array is required' },
        { status: 400 }
      );
    }

    console.log(`Starting batch summarization for ${recordingIds.length} recordings`);

    const results = [];
    const errors = [];
    let completed = 0;

    // Process recordings one by one to avoid overwhelming the API
    for (const recordingId of recordingIds) {
      try {
        console.log(`Processing recording ${completed + 1}/${recordingIds.length}: ${recordingId}`);

        // Find the recording data to get the transcription
        const recording = recordings?.find((r: any) => r.id === recordingId);
        if (!recording || !recording.transcription) {
          errors.push({
            recordingId,
            error: 'Recording not found or missing transcription'
          });
          completed++;
          continue;
        }

        // Call the individual summarization API
        const summarizeResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/voice/summarize`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            recordingId,
            transcription: recording.transcription,
            summaryType: 'visit-summary',
            promptId: 'dental-visit-summary'
          }),
        });

        const summarizeResult = await summarizeResponse.json();

        if (summarizeResponse.ok) {
          results.push({
            recordingId,
            success: true,
            summary: summarizeResult.summary,
            cached: summarizeResult.cached
          });
        } else {
          errors.push({
            recordingId,
            error: summarizeResult.error || 'Unknown error'
          });
        }

        completed++;

        // Add a small delay between requests to be respectful to OpenAI API
        if (completed < recordingIds.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error: any) {
        console.error(`Error processing recording ${recordingId}:`, error);
        errors.push({
          recordingId,
          error: error.message || 'Processing failed'
        });
        completed++;
      }
    }

    console.log(`Batch summarization completed: ${results.length} successful, ${errors.length} failed`);

    return NextResponse.json({
      success: true,
      completed: results.length,
      failed: errors.length,
      total: recordingIds.length,
      results,
      errors,
      date
    });

  } catch (error: any) {
    console.error('Batch summarization error:', error);
    return NextResponse.json(
      { error: `Batch summarization failed: ${error.message}` },
      { status: 500 }
    );
  }
}
