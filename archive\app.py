#!/usr/bin/env python3
"""
Dental Appointment Viewer GUI
A simple GUI for viewing dental appointments by operatory
"""

import PySimpleGUI as sg
import sys
import io
import traceback
from datetime import datetime
from detailed_appointments import (
    load_credentials,
    authenticate,
    fetch_appointments_for_date,
    fetch_transactions,
    fetch_medical_notes,
    fetch_patient_chart_numbers,
    display_detailed_appointments
)

# Define operatory names - replace with your actual operatories
OPERATORIES = [
    'DAZ1', 'DAZ2', 'DL01', 'DL02', 'NS02', 'X-PS', 'Y-10', 'ZZ01', 'ZZ02'
]

def pull_from_api(operatories, date_str):
    """Pull appointment data from API for selected operatories and date"""
    # Redirect stdout to capture the output
    old_stdout = sys.stdout
    new_stdout = io.StringIO()
    sys.stdout = new_stdout

    try:
        # Load credentials
        credentials = load_credentials()
        app_id = credentials.get("app_id")
        app_key = credentials.get("app_key")
        office_id = credentials.get("office_id")
        secret_key = credentials.get("secret_key")

        # Authenticate and get request key
        request_key = authenticate(app_id, app_key, office_id, secret_key)

        # Fetch appointments for the specified date
        appointments = fetch_appointments_for_date(request_key, date_str)

        # Fetch procedures for the specified date
        procedures = fetch_transactions(request_key, date_str)

        # Fetch medical notes for the specified date
        medical_notes = fetch_medical_notes(request_key, date_str)

        # Extract unique patient IDs from appointments
        patient_ids = []
        for appt in appointments:
            patient_id = appt.get("patient_id")
            if patient_id and patient_id not in patient_ids:
                patient_ids.append(patient_id)

        # Fetch chart numbers for all patients
        chart_numbers = fetch_patient_chart_numbers(request_key, patient_ids)

        # Display the detailed appointments
        display_detailed_appointments(
            appointments,
            procedures,
            medical_notes,
            chart_numbers,
            date_str,
            selected_operatories=operatories
        )

        # Get the output
        output = new_stdout.getvalue()
        return output

    except Exception as e:
        # Get the full traceback
        error_msg = f"Error: {str(e)}\n\n{traceback.format_exc()}"
        return error_msg

    finally:
        # Restore stdout
        sys.stdout = old_stdout

def main():
    # Set theme if available, otherwise use default
    try:
        sg.theme('SystemDefault')
    except AttributeError:
        # Older versions of PySimpleGUI don't have theme function
        sg.ChangeLookAndFeel('SystemDefault')
    except:
        # If that also fails, just continue with default
        pass

    # Get today's date in YYYY-MM-DD format
    today = datetime.now().strftime("%Y-%m-%d")

    # Define the layout
    layout = [
        [sg.Text('Date (YYYY-MM-DD):', size=(15, 1)),
         sg.Input(today, key='-DATE-', size=(15, 1)),
         sg.Button('Select Date', key='-CALENDAR-')],
        [sg.Text('Select Operatories:', size=(15, 1))],
        [sg.Listbox(values=OPERATORIES, select_mode=sg.LISTBOX_SELECT_MODE_MULTIPLE,
                   size=(30, 6), key='-OPERATORIES-')],
        [sg.Button('Fetch', key='-FETCH-', disabled=True), sg.Button('Exit')]
    ]

    # Create the window
    window = sg.Window('Dental Appointment Viewer', layout, finalize=True)

    # Event loop
    while True:
        event, values = window.read()

        # Enable/disable Fetch button based on operatory selection
        if event != sg.WIN_CLOSED:
            selected_operatories = values['-OPERATORIES-']
            window['-FETCH-'].update(disabled=not selected_operatories)

        if event == sg.WIN_CLOSED or event == 'Exit':
            break

        if event == '-CALENDAR-':
            # Simple date picker using popup
            date_parts = values['-DATE-'].split('-')
            try:
                year, month, day = map(int, date_parts)
                # Create a simple date picker dialog
                layout = [
                    [sg.Text('Select a date:')],
                    [sg.Input(values['-DATE-'], key='-CAL-DATE-', size=(15, 1))],
                    [sg.Text('Format: YYYY-MM-DD')],
                    [sg.Button('OK'), sg.Button('Cancel')]
                ]
                date_window = sg.Window('Date Picker', layout, modal=True)
                date_event, date_values = date_window.read(close=True)
                if date_event == 'OK' and date_values['-CAL-DATE-']:
                    window['-DATE-'].update(date_values['-CAL-DATE-'])
            except:
                sg.popup_error("Invalid date format. Please use YYYY-MM-DD format.")

        if event == '-FETCH-':
            selected_operatories = values['-OPERATORIES-']
            date_str = values['-DATE-']

            # Validate date format
            try:
                datetime.strptime(date_str, "%Y-%m-%d")
            except ValueError:
                sg.popup_error("Invalid date format. Please use YYYY-MM-DD format.")
                continue

            # Check if at least one operatory is selected
            if not selected_operatories:
                sg.popup_error("Please select at least one operatory.")
                continue

            # Show a "please wait" popup
            popup = sg.popup_no_buttons("Fetching data from API...", auto_close=False, non_blocking=True)

            try:
                # Call the API function
                result = pull_from_api(selected_operatories, date_str)

                # Close the "please wait" popup
                if popup:
                    popup.close()

                # Show the result in a scrollable popup
                sg.popup_scrolled(result, title=f"Appointments for {date_str}", size=(100, 30))

            except Exception as e:
                # Close the "please wait" popup
                if popup:
                    popup.close()

                # Show error popup
                sg.popup_error(f"Error fetching data: {str(e)}")

    # Close the window
    window.close()

if __name__ == "__main__":
    main()
