import { AzureStorageService } from './azure-storage';

export interface TranscriptionJob {
  id: string;
  recordingId: string;
  filename: string;
  azurePath?: string;
  
  // Job configuration
  promptId: string;
  customPrompt?: string;
  openaiModel: string;
  enableSpeakerSeparation: boolean;
  priority: 'low' | 'normal' | 'high';
  
  // Job status
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'retrying';
  attempts: number;
  maxAttempts: number;
  
  // Timestamps
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  
  // Results and errors
  transcription?: string;
  error?: string;
  metadata?: any;
  
  // Processing info
  workerId?: string;
  processingTimeMs?: number;
}

export interface QueueStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  total: number;
  oldestPendingJob?: string;
  averageProcessingTime?: number;
}

export class TranscriptionQueue {
  private static readonly QUEUE_FILE = 'metadata/transcription-queue.json';
  private static readonly STATS_FILE = 'metadata/queue-stats.json';
  
  // Add a job to the queue
  static async addJob(job: Omit<TranscriptionJob, 'id' | 'status' | 'attempts' | 'createdAt'>): Promise<string> {
    const jobId = `job-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const fullJob: TranscriptionJob = {
      ...job,
      id: jobId,
      status: 'pending',
      attempts: 0,
      createdAt: new Date().toISOString()
    };
    
    const queue = await this.getQueue();
    queue.push(fullJob);
    await this.saveQueue(queue);
    
    console.log(`📋 Added transcription job to queue: ${jobId} (${job.filename})`);
    await this.updateStats();
    
    return jobId;
  }
  
  // Get next pending job
  static async getNextJob(workerId: string): Promise<TranscriptionJob | null> {
    const queue = await this.getQueue();
    
    // Find highest priority pending job
    const pendingJobs = queue.filter(job => job.status === 'pending');
    if (pendingJobs.length === 0) return null;
    
    // Sort by priority (high > normal > low) then by creation time
    pendingJobs.sort((a, b) => {
      const priorityOrder = { high: 3, normal: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    });
    
    const job = pendingJobs[0];
    
    // Mark as processing
    job.status = 'processing';
    job.startedAt = new Date().toISOString();
    job.workerId = workerId;
    job.attempts++;
    
    await this.saveQueue(queue);
    await this.updateStats();
    
    console.log(`🔄 Worker ${workerId} claimed job: ${job.id} (${job.filename})`);
    return job;
  }
  
  // Mark job as completed
  static async completeJob(jobId: string, transcription: string, metadata?: any): Promise<void> {
    const queue = await this.getQueue();
    const job = queue.find(j => j.id === jobId);
    
    if (job) {
      job.status = 'completed';
      job.completedAt = new Date().toISOString();
      job.transcription = transcription;
      job.metadata = metadata;
      
      if (job.startedAt) {
        job.processingTimeMs = new Date().getTime() - new Date(job.startedAt).getTime();
      }
      
      await this.saveQueue(queue);
      await this.updateStats();
      
      console.log(`✅ Job completed: ${jobId} (${job.filename}) in ${job.processingTimeMs}ms`);
    }
  }
  
  // Mark job as failed
  static async failJob(jobId: string, error: string): Promise<void> {
    const queue = await this.getQueue();
    const job = queue.find(j => j.id === jobId);
    
    if (job) {
      job.error = error;
      
      // Retry logic
      if (job.attempts < job.maxAttempts) {
        job.status = 'retrying';
        console.log(`🔄 Job failed, will retry: ${jobId} (attempt ${job.attempts}/${job.maxAttempts})`);
        
        // Add delay before retry (exponential backoff)
        setTimeout(async () => {
          job.status = 'pending';
          job.startedAt = undefined;
          job.workerId = undefined;
          await this.saveQueue(queue);
        }, Math.pow(2, job.attempts) * 1000); // 2s, 4s, 8s, etc.
        
      } else {
        job.status = 'failed';
        job.completedAt = new Date().toISOString();
        console.log(`❌ Job permanently failed: ${jobId} (${job.filename})`);
      }
      
      await this.saveQueue(queue);
      await this.updateStats();
    }
  }
  
  // Get queue status
  static async getQueueStatus(): Promise<QueueStats> {
    const queue = await this.getQueue();
    
    const stats: QueueStats = {
      pending: queue.filter(j => j.status === 'pending').length,
      processing: queue.filter(j => j.status === 'processing').length,
      completed: queue.filter(j => j.status === 'completed').length,
      failed: queue.filter(j => j.status === 'failed').length,
      total: queue.length
    };
    
    // Find oldest pending job
    const pendingJobs = queue.filter(j => j.status === 'pending');
    if (pendingJobs.length > 0) {
      const oldest = pendingJobs.reduce((oldest, job) => 
        new Date(job.createdAt) < new Date(oldest.createdAt) ? job : oldest
      );
      stats.oldestPendingJob = oldest.createdAt;
    }
    
    // Calculate average processing time
    const completedJobs = queue.filter(j => j.status === 'completed' && j.processingTimeMs);
    if (completedJobs.length > 0) {
      const totalTime = completedJobs.reduce((sum, job) => sum + (job.processingTimeMs || 0), 0);
      stats.averageProcessingTime = Math.round(totalTime / completedJobs.length);
    }
    
    return stats;
  }
  
  // Get all jobs (with optional filtering)
  static async getJobs(status?: TranscriptionJob['status'], limit?: number): Promise<TranscriptionJob[]> {
    const queue = await this.getQueue();
    
    let filteredJobs = status ? queue.filter(job => job.status === status) : queue;
    
    // Sort by creation time (newest first)
    filteredJobs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    if (limit) {
      filteredJobs = filteredJobs.slice(0, limit);
    }
    
    return filteredJobs;
  }
  
  // Clean up old completed jobs
  static async cleanupOldJobs(olderThanDays: number = 7): Promise<number> {
    const queue = await this.getQueue();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    const initialLength = queue.length;
    const filteredQueue = queue.filter(job => {
      if (job.status === 'completed' || job.status === 'failed') {
        const jobDate = new Date(job.completedAt || job.createdAt);
        return jobDate > cutoffDate;
      }
      return true; // Keep pending/processing jobs
    });
    
    await this.saveQueue(filteredQueue);
    const removedCount = initialLength - filteredQueue.length;
    
    if (removedCount > 0) {
      console.log(`🧹 Cleaned up ${removedCount} old jobs from queue`);
    }
    
    return removedCount;
  }
  
  // Private methods
  private static async getQueue(): Promise<TranscriptionJob[]> {
    try {
      if (AzureStorageService.isConfigured()) {
        const queueData = await AzureStorageService.downloadJson(this.QUEUE_FILE);
        return Array.isArray(queueData) ? queueData : [];
      }
    } catch (error) {
      console.log('No existing queue found, starting with empty queue');
    }
    return [];
  }
  
  private static async saveQueue(queue: TranscriptionJob[]): Promise<void> {
    try {
      if (AzureStorageService.isConfigured()) {
        await AzureStorageService.uploadJson(this.QUEUE_FILE, queue);
      }
    } catch (error) {
      console.error('Failed to save transcription queue:', error);
      throw error;
    }
  }
  
  private static async updateStats(): Promise<void> {
    try {
      const stats = await this.getQueueStatus();
      if (AzureStorageService.isConfigured()) {
        await AzureStorageService.uploadJson(this.STATS_FILE, {
          ...stats,
          lastUpdated: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Failed to update queue stats:', error);
    }
  }
}
