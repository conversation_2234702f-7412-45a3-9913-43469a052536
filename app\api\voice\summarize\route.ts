import { NextRequest, NextResponse } from 'next/server';
import OpenA<PERSON> from 'openai';
import { sql } from '@vercel/postgres';
import { VercelDB } from '@/lib/vercel-db';
import { featureFlags } from '@/lib/feature-flags';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

interface SummarizeRequest {
  transcriptionIds?: string[];
  transcriptionText?: string;
  summaryType?: 'clinical' | 'brief' | 'detailed' | 'patient-friendly';
  maxTokens?: number;
}

export async function POST(request: NextRequest) {
  try {
    // Check if new architecture is enabled
    if (!featureFlags.enableTranscriptionOnlyWorkflow) {
      return NextResponse.json({
        error: 'Transcription-only workflow not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { 
      transcriptionIds, 
      transcriptionText, 
      summaryType = 'clinical',
      maxTokens = 300 
    }: SummarizeRequest = await request.json();

    if (!transcriptionIds && !transcriptionText) {
      return NextResponse.json({
        error: 'Either transcription IDs or transcription text is required',
        code: 'MISSING_INPUT'
      }, { status: 400 });
    }

    console.log(`📝 Starting summarization: type=${summaryType}, maxTokens=${maxTokens}`);

    let textToSummarize = transcriptionText;
    let transcriptionsToUpdate: string[] = [];

    // If transcription IDs provided, fetch the text
    if (transcriptionIds && transcriptionIds.length > 0) {
      const placeholders = transcriptionIds.map((_, index) => `$${index + 1}`).join(',');
      const query = `
        SELECT id, transcription_text 
        FROM transcriptions 
        WHERE id IN (${placeholders})
        AND transcription_text IS NOT NULL
        AND transcription_text != ''
      `;

      const result = await sql.query(query, transcriptionIds);
      
      if (result.rows.length === 0) {
        return NextResponse.json({
          error: 'No valid transcriptions found',
          code: 'NO_TRANSCRIPTIONS'
        }, { status: 404 });
      }

      // Combine multiple transcriptions
      textToSummarize = result.rows.map(row => row.transcription_text).join('\n\n');
      transcriptionsToUpdate = result.rows.map(row => row.id);
    }

    if (!textToSummarize || textToSummarize.trim().length < 10) {
      return NextResponse.json({
        error: 'Transcription text too short to summarize',
        code: 'INSUFFICIENT_TEXT'
      }, { status: 400 });
    }

    // Generate summary based on type
    const summary = await generateSummary(textToSummarize, summaryType, maxTokens);

    if (!summary.success) {
      return NextResponse.json({
        error: summary.error,
        code: 'SUMMARIZATION_FAILED'
      }, { status: 500 });
    }

    // Update database if transcription IDs were provided
    const updateResults = [];
    if (transcriptionsToUpdate.length > 0) {
      for (const transcriptionId of transcriptionsToUpdate) {
        try {
          await sql`
            UPDATE transcriptions 
            SET 
              summary_text = ${summary.text},
              metadata = jsonb_set(
                COALESCE(metadata, '{}'::jsonb),
                '{summary_generated}',
                to_jsonb(NOW()::text)
              ),
              updated_at = NOW()
            WHERE id = ${transcriptionId}
          `;

          updateResults.push({
            transcriptionId,
            status: 'updated'
          });

          console.log(`✅ Updated summary for transcription: ${transcriptionId}`);

        } catch (error) {
          console.error(`❌ Failed to update summary for ${transcriptionId}:`, error);
          updateResults.push({
            transcriptionId,
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }

    const response = {
      success: true,
      summary: {
        text: summary.text,
        type: summaryType,
        wordCount: summary.text.split(/\s+/).length,
        characterCount: summary.text.length,
        processingTimeMs: summary.processingTimeMs
      },
      source: {
        transcriptionIds: transcriptionIds || null,
        sourceLength: textToSummarize.length,
        sourceWordCount: textToSummarize.split(/\s+/).length
      },
      ...(updateResults.length > 0 && { 
        updates: {
          total: updateResults.length,
          successful: updateResults.filter(r => r.status === 'updated').length,
          failed: updateResults.filter(r => r.status === 'error').length,
          results: updateResults
        }
      })
    };

    console.log(`🎉 Summarization completed: ${summary.text.length} characters generated`);

    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Summarization error:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Summarization failed',
      code: 'SUMMARIZE_ERROR'
    }, { status: 500 });
  }
}

async function generateSummary(
  text: string, 
  summaryType: string, 
  maxTokens: number
): Promise<{
  success: boolean;
  text?: string;
  error?: string;
  processingTimeMs: number;
}> {
  const startTime = Date.now();

  try {
    // Define prompts based on summary type
    const prompts = {
      clinical: `Please provide a concise clinical summary of this dental transcription. Focus on:
- Key findings and observations
- Procedures performed or planned
- Patient concerns or symptoms
- Treatment recommendations
- Follow-up requirements

Keep it professional and under ${Math.floor(maxTokens * 0.75)} words:

${text}`,

      brief: `Provide a brief summary of this dental visit in 2-3 sentences. Focus on the main points:

${text}`,

      detailed: `Create a comprehensive summary of this dental transcription including:
- Patient presentation and chief complaint
- Clinical examination findings
- Procedures performed
- Treatment plan and recommendations
- Patient education provided
- Next steps

${text}`,

      'patient-friendly': `Create a patient-friendly summary of this dental visit that:
- Uses simple, non-technical language
- Explains any procedures in layman's terms
- Highlights important care instructions
- Is reassuring and informative

${text}`
    };

    const prompt = prompts[summaryType as keyof typeof prompts] || prompts.clinical;

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini', // Cost-effective for summaries
      messages: [
        {
          role: 'system',
          content: 'You are a dental assistant helping to summarize patient visit transcriptions. Be accurate, professional, and focused on dental/medical content.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: maxTokens,
      temperature: 0.3, // Lower temperature for consistent medical summaries
      top_p: 0.9
    });

    const summaryText = completion.choices[0].message.content;

    if (!summaryText || summaryText.trim().length === 0) {
      return {
        success: false,
        error: 'Empty summary generated',
        processingTimeMs: Date.now() - startTime
      };
    }

    return {
      success: true,
      text: summaryText.trim(),
      processingTimeMs: Date.now() - startTime
    };

  } catch (error) {
    console.error('OpenAI summarization error:', error);
    
    // Handle specific OpenAI errors
    if (error instanceof Error) {
      if (error.message.includes('rate limit')) {
        return {
          success: false,
          error: 'Rate limit exceeded. Please try again in a few minutes.',
          processingTimeMs: Date.now() - startTime
        };
      } else if (error.message.includes('insufficient_quota')) {
        return {
          success: false,
          error: 'API quota exceeded. Please check your OpenAI billing.',
          processingTimeMs: Date.now() - startTime
        };
      }
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown summarization error',
      processingTimeMs: Date.now() - startTime
    };
  }
}

/**
 * GET endpoint to retrieve existing summaries
 */
export async function GET(request: NextRequest) {
  try {
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const transcriptionId = searchParams.get('transcriptionId');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50);

    if (transcriptionId) {
      // Get specific summary
      const result = await sql`
        SELECT 
          id,
          filename,
          summary_text,
          metadata->>'summary_generated' as summary_generated_at
        FROM transcriptions 
        WHERE id = ${transcriptionId}
      `;

      if (result.rows.length === 0) {
        return NextResponse.json({
          error: 'Transcription not found',
          code: 'NOT_FOUND'
        }, { status: 404 });
      }

      const transcription = result.rows[0];
      
      return NextResponse.json({
        success: true,
        transcriptionId,
        filename: transcription.filename,
        summary: transcription.summary_text,
        generatedAt: transcription.summary_generated_at,
        hasSummary: !!transcription.summary_text
      });

    } else {
      // Get recent summaries
      const result = await sql`
        SELECT 
          id,
          filename,
          summary_text,
          created_at,
          metadata->>'summary_generated' as summary_generated_at
        FROM transcriptions 
        WHERE summary_text IS NOT NULL 
        AND summary_text != ''
        ORDER BY created_at DESC
        LIMIT ${limit}
      `;

      const summaries = result.rows.map(row => ({
        transcriptionId: row.id,
        filename: row.filename,
        summary: row.summary_text,
        createdAt: row.created_at,
        summaryGeneratedAt: row.summary_generated_at
      }));

      return NextResponse.json({
        success: true,
        count: summaries.length,
        summaries
      });
    }

  } catch (error) {
    console.error('❌ Error retrieving summaries:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to retrieve summaries',
      code: 'RETRIEVE_ERROR'
    }, { status: 500 });
  }
}