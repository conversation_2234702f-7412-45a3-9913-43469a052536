exports.id=6737,exports.ids=[6737],exports.modules={78335:()=>{},91973:(e,t,r)=>{"use strict";r.d(t,{X:()=>s});let i="https://api.sikkasoft.com/v2",a="https://api.sikkasoft.com/v4",n={},o=e=>new Promise(t=>setTimeout(t,e));class s{constructor(e){this.requestKey=null,this.credentials=e}async authenticate(){try{let e=await fetch(`${a}/request_key`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({grant_type:"request_key",office_id:this.credentials.office_id,secret_key:this.credentials.secret_key,app_id:this.credentials.app_id,app_key:this.credentials.app_key})});if(!e.ok){let t=await e.text();throw Error(`Authentication failed with status ${e.status}: ${t}`)}let t=await e.json();if(this.requestKey=t.request_key,!this.requestKey)throw Error("No request key in response");return this.requestKey}catch(e){throw console.error("Error during authentication:",e),Error(`Authentication failed: ${e instanceof Error?e.message:String(e)}`)}}async getRequestKey(){return this.requestKey?this.requestKey:this.authenticate()}async getOperatories(e){let t=`operatories-${e}`,r=n[t];if(r&&Date.now()-r.timestamp<3e5)return r.data;await o(500);let a=await this.getRequestKey();try{let r=await fetch(`${i}/appointments?date=${e}&startdate=${e}&enddate=${e}&date_filter_on=appointment_date`,{headers:{"Request-Key":a}});if(!r.ok)throw Error(`Failed to fetch operatories: ${r.status}`);let o=await r.json(),s=new Set;if(Array.isArray(o)&&o.length>0&&o[0].items)for(let t of o[0].items){let r=t.operatory,i=t.date||t.appointment_date;r&&""!==r&&"N/A"!==r&&"DAZ1"!==r&&"DAZ2"!==r&&(!i||i===e)&&("Blocked"===t.status||"Cancelled"===t.status||t.description&&t.description.toLowerCase().includes("blocked")||s.add(r))}let d={DL01:"Dr. Lowell",DL02:"Dr. Lowell",NS01:"Dr. Soto",NS02:"Dr. Soto",HYG1:"Hygiene",HYG2:"Hygiene",HYG3:"Hygiene",CONS:"Consult Room",LAB:"Lab"},l=Array.from(s).map(e=>{let t=d[e];if(!t){let r=e.match(/^([A-Za-z]+)\d*$/);t=r&&r[1]?r[1].toUpperCase():e}return{id:e,name:e,provider:t,sortKey:e.toLowerCase()}}).sort((e,t)=>e.provider!==t.provider?e.provider.localeCompare(t.provider):e.sortKey.localeCompare(t.sortKey));return n[t]={data:l,timestamp:Date.now()},l}catch(e){throw console.error("Error fetching operatories:",e),Error(`Failed to fetch operatories: ${e instanceof Error?e.message:String(e)}`)}}async getAppointments(e,t){let r=t?[...t].sort().join("-"):"all",a=`appointments-${e}-${r}`,s=n[a];if(s&&Date.now()-s.timestamp<3e5)return s.data;await o(500);let d=await this.getRequestKey();try{let r=await fetch(`${i}/appointments?date=${e}&startdate=${e}&enddate=${e}&date_filter_on=appointment_date`,{headers:{"Request-Key":d}});if(!r.ok)throw Error(`Failed to fetch appointments: ${r.status}`);let o=await r.json(),s=[];if(Array.isArray(o)&&o.length>0&&o[0].items)for(let r of o[0].items){if("DAZ1"===r.operatory||"DAZ2"===r.operatory||t&&t.length>0&&!t.includes(r.operatory)||r.date&&r.date!==e)continue;let i="Blocked"===r.status||r.description&&r.description.toLowerCase().includes("blocked");if(i&&r.description&&"blocked"===r.description.trim().toLowerCase())continue;let a="8:00 AM",n="9:00 AM";if(r.time){let[e,t]=r.time.split(":").map(Number),i=e>=12?"PM":"AM",o=0===e?12:e>12?e-12:e;a=`${o}:${t.toString().padStart(2,"0")} ${i}`;let s=parseFloat(r.length)||60,d=60*e+t+s,l=Math.floor(d/60),c=d%60,p=l>=12?"PM":"AM",m=0===l?12:l>12?l-12:l;n=`${m}:${c.toString().padStart(2,"0")} ${p}`}s.push({id:r.appointment_sr_no||`${e}-${r.operatory}-${r.time}`,appointment_sr_no:r.appointment_sr_no,patient_id:r.patient_id,patient_name:r.patient_name||"No Patient",provider:r.provider,operatory:r.operatory,date:r.date||e,startTime:a,endTime:n,length:r.length||60,type:r.description||"Unknown",description:r.description||"",isBlocked:i,status:r.status})}return n[a]={data:s,timestamp:Date.now()},s}catch(e){throw console.error("Error fetching appointments:",e),Error(`Failed to fetch appointments: ${e instanceof Error?e.message:String(e)}`)}}async getAppointmentById(e){try{let t=await this.getRequestKey(),r=`${i}/appointments/${e}`,a=await fetch(r,{method:"GET",headers:{"Request-Key":t}});if(!a.ok){if(404===a.status)return null;throw Error(`HTTP error! status: ${a.status}`)}if(204===a.status)return null;let n=await a.json(),o=null;if(Array.isArray(n)&&n.length>0?o=n[0].items?.[0]||n[0]:n.items&&Array.isArray(n.items)&&n.items.length>0?o=n.items[0]:(n.id||n.appointment_sr_no)&&(o=n),!o)return null;let s="8:00 AM",d="9:00 AM";if(o.time){let[e,t]=o.time.split(":").map(Number);s=`${0===e?12:e>12?e-12:e}:${t.toString().padStart(2,"0")} ${e>=12?"PM":"AM"}`;let r=parseFloat(o.length)||60,i=60*e+t+r,a=Math.floor(i/60);d=`${0===a?12:a>12?a-12:a}:${(i%60).toString().padStart(2,"0")} ${a>=12?"PM":"AM"}`}return{id:o.appointment_sr_no||o.id||e,appointment_sr_no:o.appointment_sr_no,patient_id:o.patient_id,patient_name:o.patient_name||"No Patient",provider:o.provider,operatory:o.operatory,date:o.date,startTime:s,endTime:d,length:o.length||60,type:o.description||"Unknown",description:o.description||"",isBlocked:"Blocked"===o.status||o.description&&o.description.toLowerCase().includes("blocked"),status:o.status}}catch(e){throw console.error("Error fetching appointment by ID:",e),Error(`Failed to fetch appointment: ${e instanceof Error?e.message:String(e)}`)}}async getPatientById(e){try{let t=await this.getRequestKey(),r=`${i}/patients/${e}`,a=await fetch(r,{method:"GET",headers:{"Request-Key":t}});if(!a.ok){if(404===a.status)return null;throw Error(`HTTP error! status: ${a.status}`)}let n=await a.text();if(!n.trim())return null;let o=JSON.parse(n);if(o)return this.transformPatientData(o);return null}catch(e){return console.error("SikkaClient: Error getting patient by ID:",e),null}}transformPatientData(e){let t=e.firstname||e.first_name||e.firstName||"",r=e.lastname||e.last_name||e.lastName||"",i=e.middlename||e.middle_name||e.middleName||"",a=e.preferred_name||e.preferredName||"",n=i?i.charAt(0).toUpperCase():"",o="",s=[];t&&""!==t.trim()&&s.push(t.trim()),a&&""!==a.trim()&&a.trim()!==t.trim()&&s.push(`(${a.trim()})`),n&&s.push(n),r&&""!==r.trim()&&s.push(r.trim()),o=s.join(" ").trim();let d=e.birthdate||e.date_of_birth||e.dateOfBirth||e.dob,l="";if(d){let e=new Date,t=new Date(d),r=e.getFullYear()-t.getFullYear(),i=e.getMonth()-t.getMonth();l=i<0||0===i&&e.getDate()<t.getDate()?`${r-1}`:`${r}`}let c=e.gender||"",p=c?c.charAt(0).toUpperCase():"";return{id:e.id||e.patient_id||e.patient_sr_no,name:o,firstName:t,lastName:r,middleName:i,middleInitial:n,preferredName:a,dateOfBirth:d,age:l,gender:c,genderInitial:p,phone:e.cell||e.homephone||e.phone||e.phone_number||e.home_phone,email:e.email||e.email_address,lastVisit:e.last_visit||e.lastVisit||"",firstVisit:e.first_visit||e.firstVisit||"",chartNumber:e.chart_number||e.chartNumber||e.id||e.patient_id}}async searchPatients(e){try{let t=await this.getRequestKey(),r=`${i}/patients`,a=new URLSearchParams({limit:"50"});isNaN(parseInt(e))?a.append("search",e):a.append("chart_number",e);let n=await fetch(`${r}?${a}`,{method:"GET",headers:{"Request-Key":t}});if(!n.ok)throw Error(`HTTP error! status: ${n.status}`);let o=await n.text();if(!o.trim())return[];let s=JSON.parse(o),d=[];return Array.isArray(s)&&s.length>0&&s[0].items?d=s[0].items:s.items?d=s.items:s.data&&(d=s.data),d.length,d.map((e,t)=>{let r=e.firstname||e.first_name||e.firstName||"",i=e.lastname||e.last_name||e.lastName||"",a=e.middlename||e.middle_name||e.middleName||"",n=e.preferred_name||e.preferredName||"",o=a?a.charAt(0).toUpperCase():"",s="",d=[];r&&""!==r.trim()&&d.push(r.trim()),n&&""!==n.trim()&&n.trim()!==r.trim()&&d.push(`(${n.trim()})`),o&&d.push(o),i&&""!==i.trim()&&d.push(i.trim()),s=d.join(" ").trim();let l=e.birthdate||e.date_of_birth||e.dateOfBirth||e.dob,c="";if(l){let e=new Date,t=new Date(l),r=e.getFullYear()-t.getFullYear(),i=e.getMonth()-t.getMonth();c=i<0||0===i&&e.getDate()<t.getDate()?`${r-1}`:`${r}`}let p=e.gender||"",m=p?p.charAt(0).toUpperCase():"";return{id:e.id||e.patient_id||e.patient_sr_no,name:s,firstName:r,lastName:i,middleName:a,middleInitial:o,preferredName:n,dateOfBirth:l,age:c,gender:p,genderInitial:m,phone:e.cell||e.homephone||e.phone||e.phone_number||e.home_phone,email:e.email||e.email_address,lastVisit:e.last_visit||e.lastVisit||"",firstVisit:e.first_visit||e.firstVisit||""}}).sort((e,t)=>{let r=e.lastVisit?new Date(e.lastVisit).getTime():0,i=t.lastVisit?new Date(t.lastVisit).getTime():0;return 0===r&&0===i?0:0===r?1:0===i?-1:i-r})}catch(e){throw console.error("Error searching patients:",e),e}}async getMedicalNotes(e,t){try{let r=await this.getRequestKey(),n=`${a}/medical_notes`,o=new URLSearchParams({patient_id:e,limit:"50"});t&&o.append("date",t);let s=await fetch(`${n}?${o}`,{method:"GET",headers:{"Request-Key":r}});if(!s.ok){let e=`${i}/medical_notes`,t=await fetch(`${e}?${o}`,{method:"GET",headers:{"Request-Key":r}});if(!t.ok)return[];let a=await t.json();return Array.isArray(a)?a:a.items||[]}let d=await s.json();return(Array.isArray(d)?d:d.items||[]).filter(e=>{let t="Clinical Note"===e.type||"PatNote"===e.type||"ProcNote"===e.type,r=e.text&&e.text.trim().length>0;if(!t||!r)return!1;let i=e.text.toLowerCase();return!(i.includes("welcome to suncoast dental center")||i.includes("please complete your new patient forms")||i.includes('please respond with "c" to confirm')||i.includes("confirmed the appointment")||i.includes("thank you for visiting suncoast dental center")||i.includes("please follow https://mdnt.io")||i.includes("created by engagement")||i.includes("chosen service:")||i.includes("primary insurance:")||i.includes("note from patient:")||i.includes("appointment confirmation")||i.includes("form completion")||i.includes("text message")||i.includes("sms")||i.includes("modento.io")||i.includes("fill in the forms online")||i.includes("we look forward to seeing you")||i.includes("arrive at least 15 minutes early")||i.includes("new patient appointments without paperwork")||i.includes("require confirmation response")||i.includes("unable to complete the forms")||i.includes("call or text us to let us know")||i.includes("new patient appts without forms")||i.includes("where your comfort and health are our priorities")||i.includes("we have just reserved your appointment")||i.includes("our mission is to make your experience")||e.text.trim().length<10||/^[a-z]$/i.test(e.text.trim())||/^https?:\/\//.test(e.text.trim())||/created by engagement #\d+/i.test(i)||/patient\.modento\.io/.test(i)||/confirmed the appointment on \d{2}\/\d{2}\/\d{4}/.test(i))}).map(t=>({id:t.transaction_sr_no||t.id||`note-${e}-${t.date}`,date:t.date||t.procedure_date,provider:t.provider_id||"Unknown Provider",notes:t.text||t.description||"",appointmentType:t.type||"",procedures:t.procedure_code_id?[t.procedure_code_id]:[],tooth_number:t.tooth_number||"",surface:t.surface||"",rawData:t}))}catch(e){return console.error("Error fetching medical notes:",e),[]}}async getMedicalNotesByAppointment(e,t){try{let r=await this.getRequestKey(),i=`${a}/medical_notes`,n=new URLSearchParams({appointment_id:e,limit:"10"});t&&n.append("patient_id",t);let o=await fetch(`${i}?${n}`,{method:"GET",headers:{"Request-Key":r}});if(!o.ok)return[];let s=await o.json();return(Array.isArray(s)?s:s.items||[]).map(e=>({id:e.id||e.note_id||`note-${Date.now()}-${Math.random()}`,date:e.date||e.created_date||e.appointment_date,text:e.text||e.note_text||e.content||e.description,provider:e.provider||e.provider_name||e.doctor||"Unknown Provider",type:e.type||"Clinical Note",appointment_id:e.appointment_id||e.appt_id,tooth_number:e.tooth_number||"",surface:e.surface||"",rawData:e})).filter(e=>{if(!(e.text&&e.text.trim().length>0))return!1;let t=e.text.toLowerCase();return!(t.includes("welcome to suncoast dental center")||t.includes("please complete your new patient forms")||t.includes('please respond with "c" to confirm')||t.includes("confirmed the appointment")||t.includes("thank you for visiting suncoast dental center")||t.includes("please follow https://mdnt.io")||t.includes("created by engagement")||t.includes("chosen service:")||t.includes("primary insurance:")||t.includes("note from patient:")||t.includes("appointment confirmation")||t.includes("form completion")||t.includes("text message")||t.includes("sms")||t.includes("modento.io")||t.includes("fill in the forms online")||t.includes("we look forward to seeing you")||t.includes("arrive at least 15 minutes early")||t.includes("new patient appointments without paperwork")||t.includes("require confirmation response")||t.includes("unable to complete the forms")||t.includes("call or text us to let us know")||t.includes("new patient appts without forms")||t.includes("where your comfort and health are our priorities")||t.includes("we have just reserved your appointment")||t.includes("our mission is to make your experience")||e.text.trim().length<10||/^[a-z]$/i.test(e.text.trim())||/^https?:\/\//.test(e.text.trim())||/created by engagement #\d+/i.test(t)||/patient\.modento\.io/.test(t)||/confirmed the appointment on \d{2}\/\d{2}\/\d{4}/.test(t))})}catch(e){return console.error("Error fetching medical notes by appointment:",e),[]}}async getMedicalNotesByDate(e){return this.getMedicalNotesByDateWithPagination(e,100)}async getMedicalNotesByDateWithPagination(e,t=500){try{let r=await this.getRequestKey(),i=`${a}/medical_notes`,n=[],o=1;for(;n.length<t;){let t=new URLSearchParams({date:e,limit:"100",page:o.toString()}),a=await fetch(`${i}?${t}`,{method:"GET",headers:{"Request-Key":r}});if(!a.ok)break;let s=await a.json(),d=Array.isArray(s)?s:s.items||[];if(0===d.length||(n.push(...d),d.length<100))break;o++}return n.slice(0,t)}catch(e){return console.error("Error fetching medical notes by date:",e),[]}}async getClinicalNotesByDate(e,t=500){try{let r=await this.getRequestKey(),i=`${a}/medical_notes/Clinical%20Note`,n=[],o=1;for(;n.length<t;){let t=new URLSearchParams({date:e,limit:"100",page:o.toString()}),a=await fetch(`${i}?${t}`,{method:"GET",headers:{"Request-Key":r}});if(a.ok){let e=await a.json(),t=Array.isArray(e)?e:e.items||[];if(0===t.length||(n.push(...t),t.length<100))break;o++}else break}if(n.length>0)return n.slice(0,t);return(await this.getMedicalNotesByDateWithPagination(e,t)).filter(e=>{let t="Clinical Note"===e.type||"PatNote"===e.type||"ProcNote"===e.type,r=e.text&&e.text.trim().length>0;return t&&r})}catch(e){return console.error("Error fetching clinical notes by date:",e),[]}}async createMedicalNote(e){try{let t=await this.getRequestKey(),r=await fetch(`${a}/medical_notes`,{method:"POST",headers:{"Request-Key":t,"Content-Type":"application/json"},body:JSON.stringify(e)});if(!r.ok)throw Error(`HTTP error! status: ${r.status}`);return await r.json()}catch(e){throw console.error("Error creating medical note:",e),e}}async getNoteTypes(){try{let e=await this.getRequestKey(),t=`${i}/note_types`,r=await fetch(t,{method:"GET",headers:{"Request-Key":e}});if(!r.ok)throw Error(`HTTP error! status: ${r.status}`);let a=await r.json();return a.data||a.items||a||[]}catch(e){return console.error("Error fetching note types:",e),[]}}async getPatientClinicalNotes(e,t=500){try{let r=await this.getRequestKey(),i=`${a}/medical_notes/Clinical note`,n=[],o=1;for(;n.length<t;){let t=new URLSearchParams({patient_id:e,limit:"100",page:o.toString()}),a=await fetch(`${i}?${t}`,{method:"GET",headers:{"Request-Key":r}});if(!a.ok)if(1===o)return this.getPatientClinicalNotesLegacy(e);else break;let s=await a.json(),d=Array.isArray(s)?s:s.items||[];if(0===d.length||(n.push(...d),d.length<100))break;o++}return n.map(e=>({id:e.id||e.note_id||`note-${Date.now()}-${Math.random()}`,date:e.date||e.created_date||e.appointment_date,text:e.text||e.note_text||e.content||e.description,provider:e.provider||e.provider_name||e.doctor||"Unknown Provider",type:e.type||"Clinical Note",appointment_id:e.appointment_id||e.appt_id,tooth_number:e.tooth_number||"",surface:e.surface||"",rawData:e})).filter(e=>{if(!(e.text&&e.text.trim().length>0))return!1;let t=e.text.toLowerCase();return!(t.includes("welcome to suncoast dental center")||t.includes("please complete your new patient forms")||t.includes('please respond with "c" to confirm')||t.includes("confirmed the appointment")||t.includes("thank you for visiting suncoast dental center")||t.includes("please follow https://mdnt.io")||t.includes("created by engagement")||t.includes("chosen service:")||t.includes("primary insurance:")||t.includes("note from patient:")||t.includes("appointment confirmation")||t.includes("form completion")||t.includes("text message")||t.includes("sms")||t.includes("modento.io")||t.includes("fill in the forms online")||t.includes("we look forward to seeing you")||t.includes("arrive at least 15 minutes early")||t.includes("new patient appointments without paperwork")||t.includes("require confirmation response")||t.includes("unable to complete the forms")||t.includes("call or text us to let us know")||t.includes("new patient appts without forms")||t.includes("where your comfort and health are our priorities")||t.includes("we have just reserved your appointment")||t.includes("our mission is to make your experience")||t.includes("recall processed on laser postcard")||t.includes("recall processed")||t.includes("service")&&t.includes("was modified")||t.includes("provider was changed from")||e.text.trim().length<10||/^[a-z]$/i.test(e.text.trim())||/^https?:\/\//.test(e.text.trim())||/created by engagement #\d+/i.test(t)||/patient\.modento\.io/.test(t)||/confirmed the appointment on \d{2}\/\d{2}\/\d{4}/.test(t)||/service \d+ was modified:/i.test(t)||/provider was changed from \([^)]*\) to \([^)]*\)/i.test(t))}).sort((e,t)=>{let r=new Date(e.date||"1900-01-01").getTime();return new Date(t.date||"1900-01-01").getTime()-r}).slice(0,t)}catch(t){return console.error("Error fetching clinical notes:",t),this.getPatientClinicalNotesLegacy(e)}}async getPatientClinicalNotesLegacy(e){try{let t=await this.getRequestKey();for(let r of(await this.getNoteTypes(),[`${a}/chart_notes?patient_id=${e}`,`${i}/chart_notes?patient_id=${e}`,`${a}/patients/${e}/chart_notes`,`${i}/patients/${e}/chart_notes`,`${a}/patient_notes?patient_id=${e}`,`${i}/patient_notes?patient_id=${e}`,`${a}/patients/${e}/patient_notes`,`${i}/patients/${e}/patient_notes`,`${a}/notes?patient_id=${e}`,`${i}/notes?patient_id=${e}`,`${a}/clinical_notes?patient_id=${e}`,`${i}/clinical_notes?patient_id=${e}`]))try{let e=await fetch(r,{method:"GET",headers:{"Request-Key":t}});if(e.ok){let t=await e.json(),r=[];if(Array.isArray(t)?r=t:t.data?r=t.data:t.items?r=t.items:t.chart_notes?r=t.chart_notes:t.notes&&(r=t.notes),r.length>0)return r.map(e=>({id:e.id||e.note_id||e.chart_note_id||e.patient_note_id,date:e.date||e.created_date||e.note_date||e.entry_date,provider:e.provider||e.provider_name||e.doctor||e.created_by||"Unknown Provider",text:e.notes||e.note_text||e.chart_note||e.description||e.content||e.text||"",type:e.type||e.note_type||"Clinical Note",appointmentType:e.appointment_type||e.visit_type||"",procedures:e.procedures||e.procedure_codes||[],appointment_id:e.appointment_id||e.appt_id,tooth_number:e.tooth_number||"",surface:e.surface||"",rawData:e})).filter(e=>e.text&&e.text.trim().length>0)}}catch(e){continue}return[]}catch(e){return console.error("Error fetching chart notes:",e),[]}}async getPatientRecentVisitCount(e,t=6){try{let r=await this.getRequestKey(),a=new Date,n=new Date;n.setMonth(n.getMonth()-t);let o=n.toISOString().split("T")[0],s=a.toISOString().split("T")[0],d=`${i}/appointments`,l=new URLSearchParams({patient_id:e,startdate:o,enddate:s,date_filter_on:"appointment_date"}),c=await fetch(`${d}?${l}`,{method:"GET",headers:{"Request-Key":r}});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);let p=await c.json(),m=0;return Array.isArray(p)&&p.length>0&&p[0].items&&(m=p[0].items.filter(e=>"Cancelled"!==e.status&&"No Show"!==e.status&&"Scheduled"!==e.status).length),m}catch(e){return console.error("Error getting patient visit count:",e),0}}async getPatientVisits(e){try{let t=await this.getRequestKey(),r=`${i}/appointments`,a=new URLSearchParams({patient_id:e,limit:"100"}),n=await fetch(`${r}?${a}`,{method:"GET",headers:{"Request-Key":t}});if(!n.ok)throw Error(`HTTP error! status: ${n.status}`);let o=await n.json(),s=[];return Array.isArray(o)&&o.length>0&&o[0].items?s=o[0].items:o.items?s=o.items:o.data&&(s=o.data),s.filter(e=>"Cancelled"!==e.status&&"No Show"!==e.status).map((t,r)=>{let i=[];t.procedures&&Array.isArray(t.procedures)?i=t.procedures:t.procedure_codes&&Array.isArray(t.procedure_codes)?i=t.procedure_codes:t.services&&Array.isArray(t.services)?i=t.services.map(e=>e.code||e.procedure_code||e.name):t.treatments&&Array.isArray(t.treatments)&&(i=t.treatments.map(e=>e.code||e.procedure_code||e.name));let a=null;return t.provider&&"string"==typeof t.provider&&""!==t.provider.trim()&&"Unknown Provider"!==t.provider?a=t.provider:t.provider?.name&&""!==t.provider.name.trim()&&"Unknown Provider"!==t.provider.name&&(a=t.provider.name),{id:t.id||t.appointment_id||`visit-${e}-${r}-${Date.now()}`,date:t.date||t.appointment_date,provider:a,appointmentType:"string"==typeof t.type?t.type:t.appointment_type||"General",status:"string"==typeof t.status?t.status:"Completed",operatory:"string"==typeof t.operatory?t.operatory:t.operatory?.name||t.operatory,procedures:i.filter(e=>e&&""!==e.trim()),notes:t.notes||t.chart_notes||t.clinical_notes||t.provider_notes||t.treatment_notes,description:t.description,rawData:t}}).sort((e,t)=>{let r=new Date(e.date).getTime();return new Date(t.date).getTime()-r})}catch(e){throw console.error("Error fetching patient visits:",e),e}}}},96487:()=>{}};