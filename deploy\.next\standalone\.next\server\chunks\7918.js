"use strict";exports.id=7918,exports.ids=[7918],exports.modules={87918:(e,s,t)=>{t.r(s),t.d(s,{ToolsAndSettings:()=>N,default:()=>k});var a=t(60687),l=t(43210),r=t(80534),d=t(98971),i=t(61611),n=t(73259),c=t(84027),x=t(78122),o=t(43649),m=t(45583),h=t(5336),b=t(34318),p=t(31158),u=t(24366);function j({isDarkMode:e}){let[s,t]=(0,l.useState)(null),[r,d]=(0,l.useState)(!0),[i,n]=(0,l.useState)(null),c=e=>{let s=`/api/tools/usb-portable/download?file=${e}`,t=document.createElement("a");t.href=s,t.download="zip"===e?"USB-Portable-Tool-Package.zip":"dental-usb-uploader-gui.ps1",document.body.appendChild(t),t.click(),document.body.removeChild(t)};if(r)return(0,a.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,a.jsx)(x.A,{className:"h-6 w-6 animate-spin text-blue-600"}),(0,a.jsx)("span",{className:"ml-2 text-slate-600 dark:text-slate-400",children:"Loading version info..."})]});if(i)return(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-red-600 dark:text-red-400"}),(0,a.jsxs)("span",{className:"text-red-800 dark:text-red-200",children:["Failed to load version info: ",i]})]})});let j=s?.version||"Unknown",g=s?.changelog?.[j]||[];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)(m.A,{className:"h-8 w-8"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-xl font-bold",children:["USB Portable Tool v",j]}),(0,a.jsx)("p",{className:"text-blue-100",children:"Corporate-friendly single-file solution with GUI"})]})]})}),(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:["Latest Version: v",j]}),(0,a.jsxs)("p",{className:"text-xs text-green-600 dark:text-green-300 mt-1",children:["Released ",s?.releaseDate," • You have the latest version"]})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,a.jsx)(b.A,{className:"h-6 w-6 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-slate-900 dark:text-white",children:"GUI Version (Recommended)"}),(0,a.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:"Single file with Windows interface"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"text-sm text-slate-600 dark:text-slate-400",children:[(0,a.jsxs)("p",{children:["✅ ",(0,a.jsx)("strong",{children:"Single PowerShell file"})," - No installation needed"]}),(0,a.jsxs)("p",{children:["✅ ",(0,a.jsx)("strong",{children:"Windows Forms GUI"})," - User-friendly interface"]}),(0,a.jsxs)("p",{children:["✅ ",(0,a.jsx)("strong",{children:"Real-time progress"})," - Visual progress tracking"]}),(0,a.jsxs)("p",{children:["✅ ",(0,a.jsx)("strong",{children:"Corporate-friendly"})," - Just PowerShell, no executables"]})]}),(0,a.jsxs)("button",{onClick:()=>c("gui"),className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Download GUI Version"})]}),(0,a.jsx)("div",{className:"text-xs text-slate-500 dark:text-slate-400",children:"File: dental-usb-uploader-gui.ps1 • ~50 KB"})]})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,a.jsx)(u.A,{className:"h-6 w-6 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-slate-900 dark:text-white",children:"Complete Package"}),(0,a.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:"Batch file + PowerShell script"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"text-sm text-slate-600 dark:text-slate-400",children:[(0,a.jsxs)("p",{children:["\uD83D\uDCE6 ",(0,a.jsx)("strong",{children:"ZIP Package"})," - Multiple file options"]}),(0,a.jsxs)("p",{children:["\uD83D\uDE80 ",(0,a.jsx)("strong",{children:"Batch launcher"})," - Easy double-click execution"]}),(0,a.jsxs)("p",{children:["\uD83D\uDD04 ",(0,a.jsx)("strong",{children:"Auto-update"})," - Built-in version checking"]}),(0,a.jsxs)("p",{children:["⚡ ",(0,a.jsx)("strong",{children:"Console mode"})," - Advanced users"]})]}),(0,a.jsxs)("button",{onClick:()=>c("zip"),className:"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center justify-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Download ZIP Package"})]}),(0,a.jsx)("div",{className:"text-xs text-slate-500 dark:text-slate-400",children:"Package: USB-Portable-Tool-Package.zip • ~25 KB"})]})]})]}),(0,a.jsxs)("div",{className:"bg-slate-50 dark:bg-slate-800/50 rounded-lg p-6",children:[(0,a.jsx)("h4",{className:"font-semibold text-slate-900 dark:text-white mb-4",children:"\uD83D\uDE80 How to Use"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-slate-800 dark:text-slate-200 mb-2",children:"GUI Version:"}),(0,a.jsxs)("div",{className:"text-sm text-slate-600 dark:text-slate-400 space-y-1",children:[(0,a.jsxs)("p",{children:["1. Download ",(0,a.jsx)("code",{children:"dental-usb-uploader-gui.ps1"})]}),(0,a.jsx)("p",{children:'2. Right-click → "Run with PowerShell"'}),(0,a.jsx)("p",{children:'3. Click "Start Upload" in the GUI'})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-slate-800 dark:text-slate-200 mb-2",children:"Package Version:"}),(0,a.jsxs)("div",{className:"text-sm text-slate-600 dark:text-slate-400 space-y-1",children:[(0,a.jsx)("p",{children:"1. Download and extract ZIP package"}),(0,a.jsxs)("p",{children:["2. Double-click ",(0,a.jsx)("code",{children:"UPLOAD-RECORDINGS.bat"})]}),(0,a.jsx)("p",{children:"3. Choose from menu options"})]})]})]})]}),g.length>0&&(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6",children:[(0,a.jsxs)("h4",{className:"font-semibold text-slate-900 dark:text-white mb-4",children:["\uD83C\uDD95 What's New in v",j]}),(0,a.jsx)("ul",{className:"space-y-2",children:g.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start space-x-2 text-sm text-slate-600 dark:text-slate-400",children:[(0,a.jsx)("span",{className:"text-green-500 mt-1",children:"•"}),(0,a.jsx)("span",{children:e})]},s))})]})]})}var g=t(75139);function N({isDarkMode:e}){let[s,t]=(0,l.useState)("tools"),o=[{id:"tools",title:"USB Tools",description:"Download USB Portable Tool with GUI interface",icon:r.A},{id:"appearance",title:"Appearance",description:"Theme and display settings",icon:d.A},{id:"storage",title:"Storage",description:"Storage and sync configuration",icon:i.A},{id:"transcription",title:"Transcription",description:"AI transcription settings",icon:n.A},{id:"general",title:"General",description:"General application settings",icon:c.A}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-slate-900 dark:text-white mb-2",children:"Tools & Settings"}),(0,a.jsx)("p",{className:"text-slate-600 dark:text-slate-400",children:"Download USB Portable Tool, configure settings, and customize your experience."})]}),(0,a.jsx)("div",{className:"border-b border-slate-200 dark:border-slate-700",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8 overflow-x-auto",children:o.map(e=>{let l=e.icon,r=s===e.id;return(0,a.jsxs)("button",{onClick:()=>t(e.id),className:`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors whitespace-nowrap ${r?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300"}`,title:e.description,children:[(0,a.jsx)(l,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.title})]},e.id)})})}),(()=>{switch(s){case"tools":return(0,a.jsx)(j,{isDarkMode:e});case"appearance":return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-white mb-4",children:"Theme Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Dark Mode"}),(0,a.jsx)("p",{className:"text-xs text-slate-500 dark:text-slate-400 mt-1",children:"Toggle between light and dark themes"})]}),(0,a.jsx)(g.U,{})]}),(0,a.jsxs)("div",{className:"border-t border-slate-200 dark:border-slate-700 pt-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-700 dark:text-slate-300 mb-3",children:"Theme Preview"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"p-3 bg-slate-50 dark:bg-slate-700 rounded-lg border",children:[(0,a.jsx)("div",{className:"text-xs font-medium text-slate-600 dark:text-slate-300 mb-2",children:"Current Theme"}),(0,a.jsx)("div",{className:"text-sm text-slate-900 dark:text-white",children:e?"Dark Mode":"Light Mode"})]}),(0,a.jsxs)("div",{className:"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800",children:[(0,a.jsx)("div",{className:"text-xs font-medium text-blue-600 dark:text-blue-300 mb-2",children:"Accent Color"}),(0,a.jsx)("div",{className:"text-sm text-blue-900 dark:text-blue-100",children:"Blue Theme"})]})]})]})]})]})});case"storage":return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-white mb-4",children:"Storage Configuration"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Azure Storage"})]}),(0,a.jsx)("p",{className:"text-xs text-green-600 dark:text-green-300",children:"Connected and configured"})]}),(0,a.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,a.jsx)("span",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:"USB Transfer"})]}),(0,a.jsx)("p",{className:"text-xs text-blue-600 dark:text-blue-300",children:"Portable tools available"})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:"Local Backup"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Disabled per user preference"})]})]}),(0,a.jsxs)("div",{className:"border-t border-slate-200 dark:border-slate-700 pt-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-700 dark:text-slate-300 mb-3",children:"Storage Actions"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)("button",{className:"px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Test Connections"})]}),(0,a.jsx)("button",{className:"px-3 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 text-sm",children:"View Storage Stats"})]})]})]})]})});case"transcription":return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-white mb-4",children:"AI Transcription Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2",children:"Transcription Model"}),(0,a.jsxs)("select",{className:"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white",children:[(0,a.jsx)("option",{children:"OpenAI Whisper (Latest)"}),(0,a.jsx)("option",{children:"OpenAI Whisper v1"}),(0,a.jsx)("option",{children:"Azure Speech Services"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2",children:"Summary Model"}),(0,a.jsxs)("select",{className:"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white",children:[(0,a.jsx)("option",{children:"GPT-4.1 Nano (Latest)"}),(0,a.jsx)("option",{children:"GPT-4 Turbo"}),(0,a.jsx)("option",{children:"GPT-3.5 Turbo"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"dental-context",className:"rounded",defaultChecked:!0}),(0,a.jsx)("label",{htmlFor:"dental-context",className:"text-sm text-slate-700 dark:text-slate-300",children:"Include dental terminology context in transcriptions"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"auto-summarize",className:"rounded",defaultChecked:!0}),(0,a.jsx)("label",{htmlFor:"auto-summarize",className:"text-sm text-slate-700 dark:text-slate-300",children:"Automatically generate summaries for all transcriptions"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"speaker-detection",className:"rounded"}),(0,a.jsx)("label",{htmlFor:"speaker-detection",className:"text-sm text-slate-700 dark:text-slate-300",children:"Enable speaker identification (when audio is unclear)"})]})]})]})]})});case"general":return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-white mb-4",children:"General Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2",children:"Default View"}),(0,a.jsxs)("select",{className:"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white",children:[(0,a.jsx)("option",{children:"Schedule"}),(0,a.jsx)("option",{children:"Voice Recordings"}),(0,a.jsx)("option",{children:"Clinical Notes"}),(0,a.jsx)("option",{children:"AI Office Manager"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"auto-refresh",className:"rounded",defaultChecked:!0}),(0,a.jsx)("label",{htmlFor:"auto-refresh",className:"text-sm text-slate-700 dark:text-slate-300",children:"Auto-refresh data every 5 minutes"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"notifications",className:"rounded",defaultChecked:!0}),(0,a.jsx)("label",{htmlFor:"notifications",className:"text-sm text-slate-700 dark:text-slate-300",children:"Show desktop notifications for completed uploads"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"compact-view",className:"rounded"}),(0,a.jsx)("label",{htmlFor:"compact-view",className:"text-sm text-slate-700 dark:text-slate-300",children:"Use compact view for better visibility on smaller screens"})]})]}),(0,a.jsxs)("div",{className:"border-t border-slate-200 dark:border-slate-700 pt-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-700 dark:text-slate-300 mb-3",children:"Application Info"}),(0,a.jsxs)("div",{className:"text-xs text-slate-500 dark:text-slate-400 space-y-1",children:[(0,a.jsx)("div",{children:"Version: 2.1.0"}),(0,a.jsxs)("div",{children:["Build Date: ",new Date().toLocaleDateString()]}),(0,a.jsx)("div",{children:"Environment: Azure Production"})]})]})]})]})});default:return null}})()]})}let k=N}};