import { NextResponse } from 'next/server';

// Helper function to extract date from filename (same as in recordings API)
function extractDateFromFilename(filename: string): string | null {
  // Try to parse date from filename prefix YYYYMMDD
  if (filename.length >= 8 && /^\d{8}/.test(filename)) {
    try {
      const dateStr = filename.substring(0, 8);
      const year = dateStr.substring(0, 4);
      const month = dateStr.substring(4, 6);
      const day = dateStr.substring(6, 8);
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0];
      }
    } catch (error) {
      // Fall through to YYMMDD format
    }
  }

  // Try to parse date from filename prefix YYMMDD (2-digit year)
  if (filename.length >= 6 && /^\d{6}/.test(filename)) {
    try {
      const dateStr = filename.substring(0, 6);
      const year = parseInt(dateStr.substring(0, 2));
      const month = dateStr.substring(2, 4);
      const day = dateStr.substring(4, 6);

      // Handle device clock issues and year parsing
      let fullYear;
      if (year === 26) {
        // Special case: Device was set to 2026 when it should be 2025
        fullYear = 2025;
      } else if (year < 50) {
        fullYear = 2000 + year;
      } else {
        fullYear = 1900 + year;
      }

      const date = new Date(fullYear, parseInt(month) - 1, parseInt(day));
      if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0];
      }
    } catch (error) {
      // Fall through to other patterns
    }
  }

  // Try other common patterns like YYMMDD_HHMM
  const patterns = [
    /^(\d{2})(\d{2})(\d{2})_\d{4}/, // YYMMDD_HHMM
    /^(\d{6})_\d{4}/,               // YYMMDD_HHMM
  ];

  for (const pattern of patterns) {
    const match = filename.match(pattern);
    if (match) {
      try {
        const year = parseInt(match[1]);
        const month = match[2];
        const day = match[3];
        
        const fullYear = year < 50 ? 2000 + year : 1900 + year;
        const date = new Date(fullYear, parseInt(month) - 1, parseInt(day));
        if (!isNaN(date.getTime())) {
          return date.toISOString().split('T')[0];
        }
      } catch (error) {
        continue;
      }
    }
  }

  return null;
}

export async function GET() {
  // Test various filename patterns
  const testFilenames = [
    '250620_1325.mp3',      // YYMMDD_HHMM
    '260103_0900.mp3',      // Year 26 (should be corrected to 2025)
    '20250620_1325.mp3',    // YYYYMMDD_HHMM
    '250620_1325_01.mp3',   // YYMMDD_HHMM_XX
    'recording_001.mp3',    // No date pattern
    '241225_1200.wav',      // Christmas 2024
    '250101_0000.mp3',      // New Year 2025
  ];

  const results = testFilenames.map(filename => {
    const extractedDate = extractDateFromFilename(filename);
    return {
      filename,
      extractedDate,
      isValid: extractedDate !== null,
      formattedDate: extractedDate ? new Date(extractedDate).toLocaleDateString() : 'N/A'
    };
  });

  return NextResponse.json({
    message: 'Date parsing test results',
    results,
    summary: {
      total: testFilenames.length,
      successful: results.filter(r => r.isValid).length,
      failed: results.filter(r => !r.isValid).length
    }
  });
}
