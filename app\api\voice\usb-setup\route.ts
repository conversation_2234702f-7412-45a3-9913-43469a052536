import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import { existsSync } from 'fs';
import path from 'path';

interface USBDeviceInfo {
  deviceId?: string;
  volumeLabel?: string;
  hasRecordingsFolder: boolean;
  isConfigured: boolean;
  suggestedDeviceId?: string;
}

// Function to get the next available device ID
async function getNextDeviceId(): Promise<string> {
  // Check existing device IDs in the server recordings folder
  const serverRecordingsRoot = '\\\\192.168.0.2\\share\\RECORDINGS';
  const localRecordingsRoot = path.join(process.cwd(), 'voice-recordings');
  
  const usedIds = new Set<number>();
  
  // Check both server and local folders for existing device IDs
  const foldersToCheck = [serverRecordingsRoot, localRecordingsRoot];
  
  for (const rootFolder of foldersToCheck) {
    if (existsSync(rootFolder)) {
      try {
        const dateFolders = await fs.readdir(rootFolder, { withFileTypes: true });
        
        for (const dateFolder of dateFolders) {
          if (dateFolder.isDirectory()) {
            const datePath = path.join(rootFolder, dateFolder.name);
            const deviceFolders = await fs.readdir(datePath, { withFileTypes: true });
            
            for (const deviceFolder of deviceFolders) {
              if (deviceFolder.isDirectory()) {
                // Extract device number from device01, device02, etc.
                const match = deviceFolder.name.match(/^device(\d+)$/);
                if (match) {
                  usedIds.add(parseInt(match[1]));
                }
              }
            }
          }
        }
      } catch (error) {
        console.log(`Could not read folder ${rootFolder}:`, error);
      }
    }
  }
  
  // Find the next available ID
  for (let i = 1; i <= 99; i++) {
    if (!usedIds.has(i)) {
      return `device${i.toString().padStart(2, '0')}`;
    }
  }
  
  // Fallback if somehow all IDs are used
  return `device${(usedIds.size + 1).toString().padStart(2, '0')}`;
}

// Function to read device ID from USB root
async function readDeviceId(usbRoot: string): Promise<string | null> {
  const deviceIdFile = path.join(usbRoot, 'device_id.txt');
  
  if (existsSync(deviceIdFile)) {
    try {
      const content = await fs.readFile(deviceIdFile, 'utf-8');
      return content.trim();
    } catch (error) {
      console.error('Error reading device_id.txt:', error);
      return null;
    }
  }
  
  return null;
}

// Function to write device ID to USB root
async function writeDeviceId(usbRoot: string, deviceId: string): Promise<void> {
  const deviceIdFile = path.join(usbRoot, 'device_id.txt');
  
  try {
    await fs.writeFile(deviceIdFile, deviceId, 'utf-8');
    console.log(`Written device ID ${deviceId} to ${deviceIdFile}`);
  } catch (error) {
    console.error('Error writing device_id.txt:', error);
    throw error;
  }
}

// Function to get USB volume label (Windows)
async function getVolumeLabel(usbRoot: string): Promise<string | null> {
  try {
    // Try to read volume label from Windows (this is a simplified approach)
    // In a real implementation, you might use Windows APIs or command line tools
    const driveLetter = usbRoot.charAt(0);
    return `USB Drive ${driveLetter}:`; // Simplified - could be enhanced with actual volume label detection
  } catch (error) {
    return null;
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const usbPath = searchParams.get('usbPath') || 'D:\\';
  
  try {
    const usbRoot = usbPath.endsWith('\\') ? usbPath : usbPath + '\\';
    const recordingsPath = path.join(usbRoot, 'REC_FILE', 'FOLDER01');
    
    // Check if USB drive exists
    if (!existsSync(usbRoot)) {
      return NextResponse.json({
        error: `USB drive not found: ${usbRoot}`,
        isConfigured: false
      }, { status: 404 });
    }
    
    // Check if recordings folder exists
    const hasRecordingsFolder = existsSync(recordingsPath);
    
    // Try to read existing device ID
    const existingDeviceId = await readDeviceId(usbRoot);
    
    // Get volume label
    const volumeLabel = await getVolumeLabel(usbRoot);
    
    // Determine if device is configured
    const isConfigured = existingDeviceId !== null && hasRecordingsFolder;
    
    const deviceInfo: USBDeviceInfo = {
      deviceId: existingDeviceId || undefined,
      volumeLabel: volumeLabel || undefined,
      hasRecordingsFolder,
      isConfigured,
      suggestedDeviceId: existingDeviceId || await getNextDeviceId()
    };
    
    return NextResponse.json(deviceInfo);
    
  } catch (error) {
    console.error('USB setup error:', error);
    return NextResponse.json({
      error: 'Failed to check USB device',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { usbPath, deviceId } = await request.json();
    
    if (!usbPath || !deviceId) {
      return NextResponse.json({
        error: 'USB path and device ID are required'
      }, { status: 400 });
    }
    
    const usbRoot = usbPath.endsWith('\\') ? usbPath : usbPath + '\\';
    
    // Check if USB drive exists
    if (!existsSync(usbRoot)) {
      return NextResponse.json({
        error: `USB drive not found: ${usbRoot}`
      }, { status: 404 });
    }
    
    // Write device ID to USB root
    await writeDeviceId(usbRoot, deviceId);
    
    // Create recordings folder structure if it doesn't exist
    const recordingsPath = path.join(usbRoot, 'REC_FILE', 'FOLDER01');
    if (!existsSync(recordingsPath)) {
      await fs.mkdir(recordingsPath, { recursive: true });
      console.log(`Created recordings folder: ${recordingsPath}`);
    }
    
    // Create a readme file with setup info
    const readmePath = path.join(usbRoot, 'RECORDER_SETUP.txt');
    const readmeContent = `USB Voice Recorder Setup
========================

Device ID: ${deviceId}
Setup Date: ${new Date().toISOString()}
Recordings Folder: REC_FILE\\FOLDER01

This USB recorder is configured for the dental office voice recording system.
Do not delete the device_id.txt file as it identifies this recorder.

For support, contact your IT administrator.
`;
    
    await fs.writeFile(readmePath, readmeContent, 'utf-8');
    
    return NextResponse.json({
      success: true,
      deviceId,
      message: `USB recorder configured as ${deviceId}`
    });
    
  } catch (error) {
    console.error('USB setup error:', error);
    return NextResponse.json({
      error: 'Failed to setup USB device',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
