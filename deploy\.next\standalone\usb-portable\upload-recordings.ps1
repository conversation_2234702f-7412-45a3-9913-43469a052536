# USB Dental Recordings Upload Tool - PowerShell Script
# Version 1.1.1

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true)]
    [string]$USBDrive,
    
    [Parameter(Mandatory=$true)]
    [string]$ScriptDir
)

# Configuration
$CURRENT_VERSION = "1.1.2"
$AZURE_BASE_URL = "https://dentalapp-cpcwgucdawhmb4hu.centralus-01.azurewebsites.net"
$UPDATE_CHECK_URL = "$AZURE_BASE_URL/api/tools/usb-portable/version"
$UPDATE_DOWNLOAD_URL = "$AZURE_BASE_URL/api/tools/usb-portable/download"

# Azure Storage Configuration
$STORAGE_ACCOUNT = "dentalrecordings"
$CONTAINER_NAME = "recordings"
$CLIENT_ID = "f830136a-8621-438b-a3cb-a260f8f3a089"
$CLIENT_SECRET = "****************************************"
$TENANT_ID = "********-eb4a-4bac-ac7c-21c8ce299844"

# Recording paths to scan (relative to USB drive)
$RECORDING_PATHS = @(
    "REC_FILE\FOLDER01",
    "RECORDER\FOLDER_A", 
    "RECORDER\FOLDER_B",
    "RECORDER\FOLDER_C",
    "RECORDER\FOLDER_D",
    "RECORDER\FOLDER_E"
)

# Archive folder
$ARCHIVE_FOLDER = "UPLOADED"

# Statistics
$script:TotalFiles = 0
$script:UploadedFiles = 0
$script:SkippedFiles = 0
$script:FailedFiles = 0

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    
    $colorMap = @{
        "Red" = "Red"
        "Green" = "Green" 
        "Yellow" = "Yellow"
        "Blue" = "Blue"
        "Cyan" = "Cyan"
        "Magenta" = "Magenta"
        "White" = "White"
    }
    
    if ($colorMap.ContainsKey($Color)) {
        Write-Host $Message -ForegroundColor $colorMap[$Color]
    } else {
        Write-Host $Message
    }
}

function Check-ForUpdates {
    Write-ColorOutput "🔍 Checking for updates..." "Cyan"

    try {
        $response = Invoke-RestMethod -Uri $UPDATE_CHECK_URL -Method GET -TimeoutSec 10

        if ($response.version -and $response.version -ne $CURRENT_VERSION) {
            Write-ColorOutput ""
            Write-ColorOutput "📥 NEW VERSION AVAILABLE!" "Green"
            Write-ColorOutput "   Current version: $CURRENT_VERSION" "Yellow"
            Write-ColorOutput "   Latest version:  $($response.version)" "Green"
            Write-ColorOutput ""

            $downloadChoice = Read-Host "Download update now? (Y/N)"
            if ($downloadChoice -match "^[Yy]") {
                Download-Update $response.version
            }
        } else {
            Write-ColorOutput "✅ You have the latest version ($CURRENT_VERSION)" "Green"
        }
    } catch {
        Write-ColorOutput "⚠️  Could not check for updates (offline?)" "Yellow"
    }

    Write-ColorOutput ""
}

function Download-Update {
    param([string]$NewVersion)

    try {
        Write-ColorOutput "⬇️  Pulling latest code for version $NewVersion..." "Cyan"

        # Create temporary updater script
        $tempUpdaterFile = Join-Path $env:TEMP "usb-portable-updater-$(Get-Random).ps1"

        # Fetch latest code content directly from API
        Write-ColorOutput "   📥 Fetching latest batch file code..." "White"
        $latestBatContent = Invoke-RestMethod -Uri "$UPDATE_DOWNLOAD_URL?file=bat`&raw=true" -Method GET -TimeoutSec 30

        Write-ColorOutput "   📥 Fetching latest PowerShell script code..." "White"
        $latestPs1Content = Invoke-RestMethod -Uri "$UPDATE_DOWNLOAD_URL?file=ps1`&raw=true" -Method GET -TimeoutSec 30

        # Create updater script that will replace the files after this script exits
        $updaterScript = @"
# Auto-updater script - replaces files after main script exits
Start-Sleep -Seconds 2

try {
    # Current file paths
    `$currentBat = "$($MyInvocation.MyCommand.Path -replace '\.ps1$', '.bat')"
    `$currentPs1 = "$($MyInvocation.MyCommand.Path)"

    Write-Host "🔄 Updating USB Portable Tool to version $NewVersion..." -ForegroundColor Cyan

    # Update batch file with latest content
    @'
$latestBatContent
'@ | Out-File -FilePath `$currentBat -Encoding UTF8
    Write-Host "   ✅ Updated: UPLOAD-RECORDINGS.bat" -ForegroundColor Green

    # Update PowerShell file with latest content
    @'
$latestPs1Content
'@ | Out-File -FilePath `$currentPs1 -Encoding UTF8
    Write-Host "   ✅ Updated: upload-recordings.ps1" -ForegroundColor Green

    Write-Host ""
    Write-Host "🎉 Update completed successfully!" -ForegroundColor Green
    Write-Host "   You can now run the updated version." -ForegroundColor White
    Write-Host ""

    # Clean up temp directory
    Remove-Item "$tempDir" -Recurse -Force -ErrorAction SilentlyContinue

} catch {
    Write-Host "❌ Update failed: `$(`$_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Please download manually from the dental app." -ForegroundColor Yellow
}

# Clean up this updater script
Remove-Item `$MyInvocation.MyCommand.Path -Force -ErrorAction SilentlyContinue
"@

        # Write updater script
        $updaterScript | Out-File -FilePath $tempUpdaterFile -Encoding UTF8

        Write-ColorOutput "✅ Latest code fetched successfully!" "Green"
        Write-ColorOutput ""
        Write-ColorOutput "🔄 Starting automatic update..." "Cyan"
        Write-ColorOutput "   This script will close and update itself with the latest code." "White"
        Write-ColorOutput "   Please wait a few seconds, then run UPLOAD-RECORDINGS.bat again." "White"
        Write-ColorOutput ""

        # Start the updater script in background and exit this script
        Start-Process powershell.exe -ArgumentList "-ExecutionPolicy Bypass", "-File", "`"$tempUpdaterFile`"" -WindowStyle Hidden

        Write-ColorOutput "🚀 Update process started. Exiting current script..." "Green"
        Start-Sleep -Seconds 1
        exit 0

    } catch {
        Write-ColorOutput "❌ Failed to pull latest code: $($_.Exception.Message)" "Red"
        Write-ColorOutput "   Please download manually from the dental app Tools section." "Yellow"
    }
}

function Get-AccessToken {
    $tokenUrl = "https://login.microsoftonline.com/$TENANT_ID/oauth2/v2.0/token"
    
    $body = @{
        client_id = $CLIENT_ID
        client_secret = $CLIENT_SECRET
        scope = "https://storage.azure.com/.default"
        grant_type = "client_credentials"
    }
    
    try {
        $response = Invoke-RestMethod -Uri $tokenUrl -Method POST -Body $body -ContentType "application/x-www-form-urlencoded"
        return $response.access_token
    } catch {
        Write-ColorOutput "❌ Failed to get Azure access token: $($_.Exception.Message)" "Red"
        return $null
    }
}

function Find-AudioFiles {
    $allFiles = @()
    
    foreach ($path in $RECORDING_PATHS) {
        $fullPath = Join-Path $USBDrive $path
        
        if (Test-Path $fullPath) {
            Write-ColorOutput "📁 Scanning: $fullPath" "Cyan"
            
            $files = Get-ChildItem -Path $fullPath -Filter "*.mp3" -File | Where-Object {
                $_.Name -notmatch "^\..*" # Exclude hidden files
            }
            
            if ($files) {
                Write-ColorOutput "   Found $($files.Count) audio files" "Green"
                $allFiles += $files
            } else {
                Write-ColorOutput "   No audio files found" "Yellow"
            }
        } else {
            Write-ColorOutput "📁 Path not found: $fullPath" "Yellow"
        }
    }
    
    return $allFiles
}

# Main execution
Write-ColorOutput "🎵 USB Dental Recordings Upload Tool v$CURRENT_VERSION" "Cyan"
Write-ColorOutput "================================================" "Cyan"
Write-ColorOutput ""

# Check for updates first
Check-ForUpdates

# Continue with upload process
$continueChoice = Read-Host "Continue with upload process? (Y/N)"
if ($continueChoice -notmatch "^[Yy]") {
    Write-ColorOutput "Upload cancelled by user." "Yellow"
    exit 0
}

Write-ColorOutput ""
Write-ColorOutput "🔍 Scanning for audio files..." "Cyan"
Write-ColorOutput ""

$audioFiles = Find-AudioFiles
$script:TotalFiles = $audioFiles.Count

if ($script:TotalFiles -eq 0) {
    Write-ColorOutput "📭 No audio files found to upload." "Yellow"
    Write-ColorOutput ""
    Write-ColorOutput "Checked paths:" "White"
    foreach ($path in $RECORDING_PATHS) {
        Write-ColorOutput "   $USBDrive\$path" "Gray"
    }
    exit 0
}

Write-ColorOutput ""
Write-ColorOutput "📊 Found $script:TotalFiles audio files to process" "Green"
Write-ColorOutput ""

# Get Azure access token
Write-ColorOutput "🔐 Authenticating with Azure..." "Cyan"
$accessToken = Get-AccessToken

if (-not $accessToken) {
    Write-ColorOutput "❌ Failed to authenticate with Azure. Cannot proceed." "Red"
    exit 1
}

Write-ColorOutput "✅ Azure authentication successful" "Green"
Write-ColorOutput ""

# Create archive folder if it doesn't exist
$archivePath = Join-Path $USBDrive $ARCHIVE_FOLDER
if (-not (Test-Path $archivePath)) {
    New-Item -Path $archivePath -ItemType Directory -Force | Out-Null
    Write-ColorOutput "📁 Created archive folder: $archivePath" "Green"
}

Write-ColorOutput "🚀 Starting upload process..." "Cyan"
Write-ColorOutput ""

# Process each file
foreach ($file in $audioFiles) {
    $fileName = $file.Name
    $filePath = $file.FullName
    $fileSize = [math]::Round($file.Length / 1MB, 2)
    
    Write-ColorOutput "📤 Processing: $fileName ($fileSize MB)" "White"
    
    try {
        # Create Azure blob path with date structure
        $now = Get-Date
        $dateFolder = $now.ToString("yyyy-MM-dd")
        $deviceId = "usb-portable"
        $blobPath = "recordings/$dateFolder/$deviceId/$fileName"
        
        # Upload to Azure
        $blobUrl = "https://$STORAGE_ACCOUNT.blob.core.windows.net/$CONTAINER_NAME/$blobPath"
        
        $headers = @{
            "Authorization" = "Bearer $accessToken"
            "x-ms-blob-type" = "BlockBlob"
            "Content-Type" = "audio/mpeg"
        }
        
        $fileBytes = [System.IO.File]::ReadAllBytes($filePath)
        
        $uploadResponse = Invoke-RestMethod -Uri $blobUrl -Method PUT -Body $fileBytes -Headers $headers -TimeoutSec 300
        
        # Move to archive folder
        $archiveFilePath = Join-Path $archivePath $fileName
        Move-Item -Path $filePath -Destination $archiveFilePath -Force
        
        Write-ColorOutput "   ✅ Uploaded and archived successfully" "Green"
        $script:UploadedFiles++
        
    } catch {
        Write-ColorOutput "   ❌ Failed: $($_.Exception.Message)" "Red"
        $script:FailedFiles++
    }
}

# Show summary
Write-ColorOutput ""
Write-ColorOutput "📊 UPLOAD SUMMARY" "Cyan"
Write-ColorOutput "=================" "Cyan"
Write-ColorOutput "Total files found: $script:TotalFiles" "White"
Write-ColorOutput "Successfully uploaded: $script:UploadedFiles" "Green"
Write-ColorOutput "Failed uploads: $script:FailedFiles" "Red"
Write-ColorOutput ""

# Notify dental app about new files (if any were uploaded)
if ($script:UploadedFiles -gt 0) {
    Write-ColorOutput "🔄 Notifying dental app about new files..." "Cyan"

    try {
        # Try to invalidate cache on the dental app
        $dentalAppUrl = "http://localhost:3000/api/voice/cache-invalidate"
        $notificationBody = @{
            source = "usb-portable-ps1"
            files = @() # Could add specific filenames here if needed
            rebuild = $true
        } | ConvertTo-Json

        $notificationHeaders = @{
            "Content-Type" = "application/json"
        }

        $notificationResponse = Invoke-RestMethod -Uri $dentalAppUrl -Method POST -Body $notificationBody -Headers $notificationHeaders -TimeoutSec 10

        if ($notificationResponse.success) {
            Write-ColorOutput "   ✅ Dental app cache updated successfully" "Green"
        } else {
            Write-ColorOutput "   ⚠️ Dental app notification failed" "Yellow"
        }

    } catch {
        Write-ColorOutput "   ⚠️ Could not notify dental app (app may not be running): $($_.Exception.Message)" "Yellow"
    }
}

if ($script:FailedFiles -eq 0) {
    Write-ColorOutput "🎉 All files uploaded successfully!" "Green"
    exit 0
} else {
    Write-ColorOutput "⚠️  Some uploads failed. Check the output above for details." "Yellow"
    exit 1
}
