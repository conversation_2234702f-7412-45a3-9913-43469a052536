(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4662],{2239:(e,t,n)=>{Promise.resolve().then(n.bind(n,8012))},8012:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>h});var i=n(5155),a=n(2115),s=n(5695),o=n(6496);let r=[{id:"gpt-4o",name:"GPT-4o",description:"Latest GPT-4o model for complex dental documentation",maxTokens:128e3,costPer1kTokens:2.5,recommended:!0},{id:"gpt-4o-mini",name:"GPT-4o Mini",description:"Cost-effective model for routine dental transcription and notes",maxTokens:128e3,costPer1kTokens:.15,recommended:!0},{id:"gpt-4o-2024-11-20",name:"GPT-4o (2024-11-20)",description:"Specific GPT-4o model version for enhanced capabilities",maxTokens:128e3,costPer1kTokens:2.5,recommended:!1},{id:"gpt-4o-2024-08-06",name:"GPT-4o (2024-08-06)",description:"Stable GPT-4o model version for reliable documentation",maxTokens:128e3,costPer1kTokens:2.5,recommended:!1},{id:"gpt-4o-mini-2024-07-18",name:"GPT-4o Mini (2024-07-18)",description:"Specific GPT-4o Mini model version",maxTokens:128e3,costPer1kTokens:.15,recommended:!1},{id:"o1",name:"o1",description:"Advanced reasoning model for complex clinical decisions",maxTokens:128e3,costPer1kTokens:15,recommended:!1},{id:"o1-mini",name:"o1 Mini",description:"Cost-effective reasoning model for clinical analysis",maxTokens:128e3,costPer1kTokens:3,recommended:!1},{id:"o1-2024-12-17",name:"o1 (2024-12-17)",description:"Specific o1 model version",maxTokens:128e3,costPer1kTokens:15,recommended:!1},{id:"o1-mini-2024-09-12",name:"o1-mini (2024-09-12)",description:"Specific o1-mini model version",maxTokens:128e3,costPer1kTokens:3,recommended:!1},{id:"whisper-1",name:"Whisper-1",description:"OpenAI Whisper model for audio transcription",maxTokens:25e6,costPer1kTokens:.006,recommended:!0},{id:"gpt-4-turbo",name:"GPT-4 Turbo",description:"Previous generation high performance model",maxTokens:128e3,costPer1kTokens:10,recommended:!1},{id:"gpt-4",name:"GPT-4",description:"Original GPT-4 model",maxTokens:8192,costPer1kTokens:30,recommended:!1},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",description:"Fast and cost-effective model for simple tasks",maxTokens:16385,costPer1kTokens:.5,recommended:!1}],l=[{id:"dental-transcription-context",name:"Dental Transcription with Context",description:"Dental-specific transcription with US tooth numbering and clinical context",type:"transcription",openaiModel:"whisper-1",isDefault:!0,variables:[],prompt:"Context: Dental visit in the United States. Universal tooth numbering system (1-32) is used. Expect numbers, millimetre pocket depths, dental materials, medication names, and consent language. Focus on Doctor and Patient voices primarily.\n\nUNCERTAINTY HANDLING: Use [UNCLEAR] for inaudible words, [UNCERTAIN: content] when not confident, and [INAUDIBLE] for completely unclear sections. Always indicate uncertainty rather than guessing.",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"speaker-identification",name:"Speaker Identification with Dental Context",description:"Transcription with speaker identification and comprehensive dental terminology",type:"transcription",openaiModel:"gpt-4o-mini-transcribe",isDefault:!1,variables:[],prompt:'Context: This is a dental appointment recording in the United States. Please transcribe with speaker identification.\n\nDENTAL CONTEXT:\n- Universal tooth numbering system (1-32): #1-16 upper jaw, #17-32 lower jaw\n- Common procedures: cleaning, filling, crown, root canal, extraction, scaling, polishing\n- Materials: composite, amalgam, porcelain, gold, ceramic, resin\n- Measurements: pocket depths in millimeters (1-10mm), bleeding on probing\n- Anatomy: mesial, distal, buccal, lingual, occlusal, cervical, apex, pulp\n- Conditions: caries, gingivitis, periodontitis, plaque, tartar, calculus\n- Tools: probe, explorer, scaler, curette, handpiece, suction\n\nSPEAKER LABELS:\n[DOCTOR] - Dentist/oral surgeon\n[PATIENT] - Patient receiving treatment\n[HYGIENIST] - Dental hygienist\n[ASSISTANT] - Dental assistant\n[STAFF] - Other office staff\n[UNKNOWN] - When speaker cannot be identified\n\nFORMAT: "[SPEAKER]: dialogue text"\n\nUNCERTAINTY HANDLING:\n- Use [UNKNOWN] when speaker identity is unclear\n- Use [UNCLEAR] for inaudible words: "tooth [UNCLEAR] needs attention"\n- Use [UNCERTAIN: word/phrase] for questionable content: "pocket depth [UNCERTAIN: 4mm]"\n- Use [BACKGROUND] for distant/unclear voices: "[BACKGROUND: muffled conversation]"\n- Use [INAUDIBLE] for completely unclear sections: "[INAUDIBLE - 3 seconds]"\n\nFocus on accurate dental terminology, tooth numbers, procedure details, patient concerns, treatment plans, and clinical observations. Include speaker names when mentioned. Always indicate uncertainty rather than guessing.',createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"detailed-medical",name:"Detailed Medical Documentation",description:"Comprehensive medical transcription for detailed clinical notes",type:"transcription",openaiModel:"gpt-4o-transcribe",isDefault:!1,variables:[],prompt:"Context: Detailed dental medical documentation. Transcribe with maximum accuracy for clinical records.\n\nMEDICAL DOCUMENTATION FOCUS:\n- Patient medical history and medications\n- Allergies and contraindications\n- Vital signs and patient condition\n- Detailed procedure descriptions\n- Post-operative instructions\n- Treatment outcomes and complications\n- Insurance and billing discussions\n- Consent forms and patient education\n\nDENTAL SPECIFICS:\n- Precise tooth numbering (Universal 1-32 system)\n- Exact measurements (pocket depths, crown lengths, etc.)\n- Material specifications and lot numbers\n- Anesthesia types and dosages\n- Radiographic findings\n- Periodontal charting details\n- Occlusion and bite analysis\n\nACCURACY REQUIREMENTS:\n- Spell out numbers when unclear\n- Include all medical terminology\n- Capture dosages and measurements precisely\n- Note any patient concerns or questions\n- Document all staff interactions\n- Include time references when mentioned\n\nUNCERTAINTY HANDLING:\n- Use [UNCLEAR] for inaudible words or phrases\n- Use [UNCERTAIN: content] when not confident about specific terms\n- Use [INAUDIBLE - duration] for completely unclear sections\n- Use [BACKGROUND] for distant conversations\n- Use [MEDICATION UNCLEAR] when drug names are uncertain\n- Use [DOSAGE UNCERTAIN] when measurements are questionable\n- Always indicate uncertainty rather than making assumptions\n\nTranscribe everything spoken, including background conversations that may contain clinical information. Prioritize accuracy over completeness - it's better to mark something as unclear than to guess incorrectly.",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"speaker-identification",name:"Speaker Identification",description:"Transcription with speaker identification and naming",type:"transcription",openaiModel:"gpt-4o-mini-transcribe",isDefault:!1,variables:[],prompt:'Context: Dental appointment recording. Please transcribe the audio and identify speakers. Use these speaker labels: [DOCTOR], [PATIENT], [ASSISTANT], [HYGIENIST]. Include speaker names when mentioned. Format: "[SPEAKER]: dialogue text". Focus on dental terminology, tooth numbers (1-32), procedures, and clinical observations.',createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"default-transcription",name:"Default Dental Transcription",description:"Professional medical transcription for dental appointments",type:"transcription",openaiModel:"whisper-1",isDefault:!1,variables:[],prompt:"Professional dental transcription. Use proper medical terminology, correct speech errors, include all clinical details.",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"detailed-transcription",name:"Detailed Clinical Transcription",description:"Comprehensive transcription with emphasis on clinical accuracy",type:"transcription",openaiModel:"whisper-1",isDefault:!1,variables:[],prompt:"Detailed medical transcription for dental procedures. Focus on clinical accuracy, proper terminology, treatment details, patient responses.",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"dental-notes-clarifier-draft",name:"Dental Notes Clarifier + Draft Writer (Prompt A)",description:"Primary dental note generation with clarification and escalation flagging",type:"professionalization",openaiModel:"gpt-4o-mini",isDefault:!0,variables:["VOICE_NOTES","DENTRIX_NOTES","PATIENT_INFO","APPOINTMENT_INFO"],prompt:'You are Dental Note AI, an expert dental scribe and compliance officer.\nMission: produce a precise, legally safe note **only after** every required detail is confirmed.\n\nRequired fields ─ Doctor, Assistant, Visit reason, Medical history review, Radiographs (type + justification), Clinical findings, Diagnoses, Treatment plan (with alternatives), Procedures (step-by-step), Patient communication (consent / risks / benefits / instructions), Next visit.\n\nRules\n1. If any required item is missing or unclear, ask ONE direct question and wait.\n2. Repeat until all items are complete.\n3. Draft the note using the template below.\n4. Add `EscalationFlag:` at the very end in ALL-CAPS:\n     LOW    – single straightforward procedure, no complications\n     MEDIUM – multi-procedure, moderate medical history, or extra insurance justification\n     HIGH   – contradictions, medico-legal red flags, advanced surgeries, unclear info\n\nFormatting\n• plain ASCII (no smart quotes)\n• one blank line between sections\n• "- " bullets, two-space indents, no asterisks\n• **Use universal (USA) tooth numbering 1 – 32 whenever you reference teeth.**\n\nTemplate\n\nDoctor:\nAssistant:\nReason for visit:\nMedical history review:\nRadiographs taken (type and justification):\nClinical findings (tooth-specific):\nDiagnoses (dental / pulpal / apical):\nTreatment plan (with alternatives):\nProcedures performed (step-by-step):\nPatient communication (consent, risks / benefits, instructions):\nNext visit / follow-up:\n\n**Voice Transcription:**\n[VOICE_NOTES]\n\n**Existing Dentrix Notes:**\n[DENTRIX_NOTES]\n\n**Patient Information:**\n[PATIENT_INFO]\n\n**Appointment Information:**\n[APPOINTMENT_INFO]\n\nEscalationFlag: LOW',createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"default-professionalization",name:"Default Clinical Note Professionalization",description:"Combines voice transcriptions and existing notes into professional clinical documentation",type:"professionalization",openaiModel:"gpt-4o-mini-2024-07-18",isDefault:!1,variables:["VOICE_NOTES","DENTRIX_NOTES","PATIENT_INFO","APPOINTMENT_INFO"],prompt:"You are a professional dental clinical documentation specialist. Your task is to create a comprehensive, professional clinical note by combining voice transcriptions from dental appointments with existing Dentrix medical notes.\n\n**Instructions:**\n1. Combine the voice transcription and existing notes into a cohesive, professional clinical record\n2. Use proper dental terminology and SOAP format when appropriate\n3. Maintain accuracy - do not add information not present in the source materials\n4. Organize information logically (chief complaint, examination findings, diagnosis, treatment, plan)\n5. Ensure the note is suitable for inclusion in a patient's permanent dental record\n6. Remove any redundant information between voice notes and existing notes\n7. Maintain professional tone throughout\n\n**Voice Transcription:**\n[VOICE_NOTES]\n\n**Existing Dentrix Notes:**\n[DENTRIX_NOTES]\n\n**Patient Information:**\n[PATIENT_INFO]\n\n**Appointment Information:**\n[APPOINTMENT_INFO]\n\nPlease create a professional clinical note that combines this information appropriately:",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"dental-notes-senior-reviewer",name:"Senior Risk Reviewer (Prompt B)",description:"O1 model for complex dental case review and escalation",type:"professionalization",openaiModel:"o1-mini",isDefault:!1,variables:["RAW_NOTES","DRAFT_NOTE","ESCALATION_FLAG"],prompt:"You are the Senior Risk Reviewer for dental clinical documentation. Your role is to review complex cases that have been escalated from the primary AI system.\n\n**Your Task:**\nReview the draft note and original voice transcription for:\n1. Medical-legal compliance and risk factors\n2. Clinical accuracy and completeness\n3. Proper documentation standards\n4. Risk mitigation recommendations\n\n**Original Voice Notes:**\n[RAW_NOTES]\n\n**Draft Note from Primary AI:**\n[DRAFT_NOTE]\n\n**Escalation Reason:**\n[ESCALATION_FLAG]\n\n**Instructions:**\n- If the draft is acceptable with minor edits, provide the corrected version\n- If significant issues exist, flag for human review with specific concerns\n- Ensure all high-risk procedures are properly documented\n- Verify consent documentation is adequate\n- Check for any contradictions or missing critical information\n\n**Response Format:**\nStatus: OK | NEEDS_HUMAN_REVIEW\nFinal Note: [corrected note or original if acceptable]\nReview Comments: [specific feedback for improvement]\nRisk Factors: [any identified risks that need attention]",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"soap-format",name:"SOAP Format Clinical Note",description:"Structures clinical information using SOAP (Subjective, Objective, Assessment, Plan) format",type:"professionalization",openaiModel:"gpt-4o-mini",isDefault:!1,variables:["VOICE_NOTES","DENTRIX_NOTES","PATIENT_INFO"],prompt:"Create a professional dental clinical note using SOAP format (Subjective, Objective, Assessment, Plan) based on the provided information.\n\n**SOAP Format Guidelines:**\n- **Subjective:** Patient's chief complaint, symptoms, history as reported by patient\n- **Objective:** Clinical findings, examination results, measurements, observations\n- **Assessment:** Diagnosis, clinical impression, risk factors\n- **Plan:** Treatment recommendations, follow-up, patient education, prescriptions\n\n**Source Information:**\n\nVoice Transcription:\n[VOICE_NOTES]\n\nExisting Notes:\n[DENTRIX_NOTES]\n\nPatient Info:\n[PATIENT_INFO]\n\nFormat the clinical note using clear SOAP headings:",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"brief-summary",name:"Brief Clinical Summary",description:"Creates concise clinical summaries for quick reference",type:"summarization",openaiModel:"gpt-4o-mini-2024-07-18",isDefault:!1,variables:["VOICE_NOTES","DENTRIX_NOTES"],prompt:"Create a brief, professional clinical summary (2-3 sentences) that captures the essential information from this dental appointment.\n\nVoice Notes: [VOICE_NOTES]\nExisting Notes: [DENTRIX_NOTES]\n\nFocus on: chief complaint, key findings, treatment provided, and follow-up needed.",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}];class c{static getAllPrompts(){return this.prompts}static getPromptById(e){return this.prompts.find(t=>t.id===e)}static getPromptsByType(e){return this.prompts.filter(t=>t.type===e)}static getDefaultPrompt(e){return this.prompts.find(t=>t.type===e&&t.isDefault)}static addPrompt(e){let t={...e,id:"custom-".concat(Date.now()),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return this.prompts.push(t),t}static updatePrompt(e,t){let n=this.prompts.findIndex(t=>t.id===e);return -1===n?null:(this.prompts[n]={...this.prompts[n],...t,updatedAt:new Date().toISOString()},this.prompts[n])}static deletePrompt(e){let t=this.prompts.findIndex(t=>t.id===e);return -1!==t&&(this.prompts.splice(t,1),!0)}static processPromptVariables(e,t){let n=e;if(t.voiceNotes){let e=t.voiceNotes.join("\n\n");n=n.replace(/\[VOICE_NOTES\]/g,e)}if(t.dentrixNotes){let e=t.dentrixNotes.join("\n\n");n=n.replace(/\[DENTRIX_NOTES\]/g,e)}if(t.patientInfo){let e=JSON.stringify(t.patientInfo,null,2);n=n.replace(/\[PATIENT_INFO\]/g,e)}if(t.appointmentInfo){let e=JSON.stringify(t.appointmentInfo,null,2);n=n.replace(/\[APPOINTMENT_INFO\]/g,e)}return n}}c.prompts=[...l];var d=n(381),m=n(9588),p=n(7434),u=n(133),f=n(4229);let g={transcriptionModel:"whisper-1",transcriptionPrompt:"dental-transcription-context",summarizationModel:"gpt-4o-mini-2024-07-18",summarizationPrompt:"brief-summary",customTranscriptionPrompt:"",customSummarizationPrompt:""};function h(){let e=(0,s.useRouter)(),[t,n]=(0,a.useState)(g),[l,h]=(0,a.useState)(!0),[x,b]=(0,a.useState)(!1),[N,T]=(0,a.useState)(null);(0,a.useEffect)(()=>{k()},[]);let k=async()=>{try{let e=await fetch("/api/settings");if(e.ok){let t=await e.json();n({...g,...t.settings})}}catch(e){console.error("Failed to load settings:",e)}finally{h(!1)}},S=async()=>{b(!0);try{if((await fetch("/api/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({settings:t})})).ok)T({type:"success",text:"Settings saved successfully!"}),setTimeout(()=>T(null),3e3);else throw Error("Failed to save settings")}catch(e){console.error("Failed to save settings:",e),T({type:"error",text:"Failed to save settings. Please try again."}),setTimeout(()=>T(null),3e3)}finally{b(!1)}},I=()=>{e.push("/")},v=r.filter(e=>"whisper-1"===e.id||e.id.includes("audio")),w=r.filter(e=>!e.id.includes("whisper")&&!e.id.includes("audio")),A=c.getPromptsByType("transcription"),y=c.getPromptsByType("summarization");return l?(0,i.jsxs)("div",{className:"min-h-screen bg-slate-50 dark:bg-slate-900",children:[(0,i.jsx)(o.z,{title:"Settings",showBackButton:!0,onBackClick:I}),(0,i.jsx)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,i.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,i.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})})})]}):(0,i.jsxs)("div",{className:"min-h-screen bg-slate-50 dark:bg-slate-900",children:[(0,i.jsx)(o.z,{title:"Settings",showBackButton:!0,onBackClick:I,activeTab:"settings"}),(0,i.jsxs)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,i.jsx)(d.A,{className:"h-8 w-8 text-blue-600"}),(0,i.jsx)("h1",{className:"text-3xl font-bold text-slate-900 dark:text-white",children:"AI Transcription & Summarization Settings"})]}),(0,i.jsx)("p",{className:"text-slate-600 dark:text-slate-400",children:"Configure OpenAI models and prompts for audio transcription and summarization"})]}),N&&(0,i.jsx)("div",{className:"mb-6 p-4 rounded-md ".concat("success"===N.type?"bg-green-50 border border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200":"bg-red-50 border border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200"),children:N.text}),(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,i.jsx)(m.A,{className:"h-6 w-6 text-blue-600"}),(0,i.jsx)("h2",{className:"text-xl font-semibold text-slate-900 dark:text-white",children:"Audio Transcription Settings"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2",children:"Transcription Model"}),(0,i.jsx)("select",{value:t.transcriptionModel,onChange:e=>n(t=>({...t,transcriptionModel:e.target.value})),className:"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",children:v.map(e=>(0,i.jsxs)("option",{value:e.id,children:[e.name," - $",e.costPer1kTokens,"/1k tokens"]},e.id))}),(0,i.jsx)("p",{className:"mt-1 text-xs text-slate-500 dark:text-slate-400",children:"Model used for converting audio to text"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2",children:"Transcription Prompt"}),(0,i.jsxs)("select",{value:t.transcriptionPrompt,onChange:e=>n(t=>({...t,transcriptionPrompt:e.target.value})),className:"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",children:[A.map(e=>(0,i.jsx)("option",{value:e.id,children:e.name},e.id)),(0,i.jsx)("option",{value:"custom",children:"Custom Prompt"})]}),(0,i.jsx)("p",{className:"mt-1 text-xs text-slate-500 dark:text-slate-400",children:"Context and instructions for transcription"})]})]}),"custom"===t.transcriptionPrompt&&(0,i.jsxs)("div",{className:"mt-4",children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2",children:"Custom Transcription Prompt"}),(0,i.jsx)("textarea",{value:t.customTranscriptionPrompt,onChange:e=>n(t=>({...t,customTranscriptionPrompt:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter your custom transcription prompt..."})]})]}),(0,i.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,i.jsx)(p.A,{className:"h-6 w-6 text-purple-600"}),(0,i.jsx)("h2",{className:"text-xl font-semibold text-slate-900 dark:text-white",children:"Summarization Settings"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2",children:"Summarization Model"}),(0,i.jsx)("select",{value:t.summarizationModel,onChange:e=>n(t=>({...t,summarizationModel:e.target.value})),className:"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",children:w.map(e=>(0,i.jsxs)("option",{value:e.id,children:[e.name," - $",e.costPer1kTokens,"/1k tokens",e.recommended?" (Recommended)":""]},e.id))}),(0,i.jsx)("p",{className:"mt-1 text-xs text-slate-500 dark:text-slate-400",children:"Model used for generating 2-3 sentence summaries"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2",children:"Summarization Prompt"}),(0,i.jsxs)("select",{value:t.summarizationPrompt,onChange:e=>n(t=>({...t,summarizationPrompt:e.target.value})),className:"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",children:[y.map(e=>(0,i.jsx)("option",{value:e.id,children:e.name},e.id)),(0,i.jsx)("option",{value:"custom",children:"Custom Prompt"})]}),(0,i.jsx)("p",{className:"mt-1 text-xs text-slate-500 dark:text-slate-400",children:"Instructions for generating summaries"})]})]}),"custom"===t.summarizationPrompt&&(0,i.jsxs)("div",{className:"mt-4",children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2",children:"Custom Summarization Prompt"}),(0,i.jsx)("textarea",{value:t.customSummarizationPrompt,onChange:e=>n(t=>({...t,customSummarizationPrompt:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Enter your custom summarization prompt..."})]})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center pt-6",children:[(0,i.jsxs)("button",{onClick:()=>{n(g),T({type:"success",text:"Settings reset to defaults"}),setTimeout(()=>T(null),3e3)},className:"flex items-center space-x-2 px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors",children:[(0,i.jsx)(u.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:"Reset to Defaults"})]}),(0,i.jsxs)("button",{onClick:S,disabled:x,className:"flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,i.jsx)(f.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:x?"Saving...":"Save Settings"})]})]})]})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8096,6496,7358],()=>t(2239)),_N_E=e.O()}]);