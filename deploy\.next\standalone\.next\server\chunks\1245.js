"use strict";exports.id=1245,exports.ids=[1245],exports.modules={61245:(e,t,r)=>{r.d(t,{needsChunking:()=>a,transcribeWithChunking:()=>s});var n=r(31630);let o={failures:0,lastFailureTime:0,state:"closed"};function i(){o.failures++,o.lastFailureTime=Date.now(),o.failures>=3&&(o.state="open",console.warn(`🚨 Circuit breaker opened after ${o.failures} failures`))}async function s(e,t,a="audio/mpeg",l){try{if(!function(){let e=Date.now();return"open"!==o.state||e-o.lastFailureTime>3e4&&(o.state="half-open",!0)}()){let e="Circuit breaker is open - too many recent failures";if(console.error(`❌ ${e}`),l)try{let{updateTranscriptionProgress:t}=await Promise.all([r.e(8096),r.e(1369)]).then(r.bind(r,71369));t(l,"error",0,0,0,e)}catch(e){console.warn("Could not update progress on circuit breaker failure:",e)}return{success:!1,fullTranscription:"",chunks:[],error:e}}let s=function(e,t=0x1700000,r=1048576){let n=[],o=0;for(;o<e.length;){let i=Math.min(o+t,e.length),s=e.slice(o,i);if(n.push(s),i<e.length)o=i-r;else break}return n}(e);if(l)try{let{updateTranscriptionProgress:e}=await Promise.all([r.e(8096),r.e(1369)]).then(r.bind(r,71369));e(l,"processing",0,0,s.length,"Starting chunked transcription...")}catch(e){console.warn("Could not initialize progress tracking:",e)}let c=[],u=0;for(let e=0;e<s.length;e++){let h=s[e],d=`${t.replace(/\.[^/.]+$/,"")}_chunk_${e+1}.${t.split(".").pop()}`;if(l)try{let{updateTranscriptionProgress:t}=await Promise.all([r.e(8096),r.e(1369)]).then(r.bind(r,71369)),n=Math.round(e/s.length*100);t(l,"processing",n,e,s.length,`Processing chunk ${e+1}/${s.length}...`)}catch(e){console.warn("Could not update progress for chunk:",e)}if(u>=2){let e=`Too many consecutive failures (${u}) - stopping transcription`;if(console.error(`❌ ${e}`),l)try{let{updateTranscriptionProgress:t}=await Promise.all([r.e(8096),r.e(1369)]).then(r.bind(r,71369));t(l,"error",0,0,0,e)}catch(e){console.warn("Could not update progress on consecutive failure:",e)}return{success:!1,fullTranscription:"",chunks:c,error:e}}try{let t=new File([h],d,{type:a}),i=new n.OpenAI({apiKey:process.env.OPENAI_API_KEY}).audio.transcriptions.create({file:t,model:"whisper-1",response_format:"json",temperature:.2}),p=await function(e,t,r){return Promise.race([e,new Promise((e,t)=>{setTimeout(()=>{t(Error(`${r} timed out after 60000ms`))},6e4)})])}(i,6e4,`OpenAI API call for chunk ${e+1}`);if(c.push({chunkIndex:e+1,transcription:p.text||"",duration:void 0}),u=0,o.failures=0,o.state="closed",l)try{let{updateTranscriptionProgress:t}=await Promise.all([r.e(8096),r.e(1369)]).then(r.bind(r,71369)),n=Math.round((e+1)/s.length*100);t(l,"processing",n,e+1,s.length,`Chunk ${e+1}/${s.length} completed`)}catch(e){console.warn("Could not update progress after chunk completion:",e)}e<s.length-1&&await new Promise(e=>setTimeout(e,1e3))}catch(n){console.error(`❌ Chunk ${e+1} failed:`,n),u++,i();let t=n instanceof Error?n.message:"Unknown error";if((t.includes("could not be decoded")||t.includes("format is not supported"))&&(t="Audio format corrupted or unsupported"),t.includes("timed out")&&(t="API timeout after 60000ms"),c.push({chunkIndex:e+1,transcription:`[Chunk ${e+1} transcription failed: ${t}]`}),l)try{let{updateTranscriptionProgress:n}=await Promise.all([r.e(8096),r.e(1369)]).then(r.bind(r,71369)),o=Math.round((e+1)/s.length*100);n(l,"processing",o,e+1,s.length,`Chunk ${e+1}/${s.length} failed: ${t}`)}catch(e){console.warn("Could not update progress after chunk failure:",e)}await new Promise(e=>setTimeout(e,2e3))}}let h=c.map(e=>e.transcription).join(" ").replace(/\s+/g," ").trim(),d=c.filter(e=>!e.transcription.includes("[Chunk"));if(l)try{let{updateTranscriptionProgress:e}=await Promise.all([r.e(8096),r.e(1369)]).then(r.bind(r,71369)),t=d.length>0?"completed":"error",n=d.length>0?`Transcription completed: ${d.length}/${s.length} chunks successful`:"All chunks failed to transcribe";e(l,t,100,s.length,s.length,n)}catch(e){console.warn("Could not update final progress:",e)}return{success:d.length>0,fullTranscription:h,chunks:c,totalDuration:void 0,error:0===d.length?"All chunks failed to transcribe":void 0}}catch(e){if(console.error("❌ Chunked transcription failed:",e),i(),l)try{let{updateTranscriptionProgress:t}=await Promise.all([r.e(8096),r.e(1369)]).then(r.bind(r,71369));t(l,"error",0,0,0,`Transcription failed: ${e instanceof Error?e.message:"Unknown error"}`)}catch(e){console.warn("Could not update progress on failure:",e)}return{success:!1,fullTranscription:"",chunks:[],error:e instanceof Error?e.message:"Chunking failed"}}}function a(e,t=0x1700000){return e>t}}};