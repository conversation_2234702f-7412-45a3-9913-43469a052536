{"permissions": {"allow": ["Bash(pip install:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__gemini-collab__ask_gemini", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git rm:*)", "Bash(git push:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(python:*)", "Bash(rm:*)", "<PERSON><PERSON>(vercel env:*)", "Bash(grep:*)", "Bash(az storage container:*)", "Bash(az storage blob list:*)", "Bash(vercel project:*)", "Bash(npm run dev:*)", "Bash(npm run migrate-existing-data:*)", "Bash(tsx scripts/migrate-existing-data.ts:*)", "Bash(npm run test-database-connection:*)", "Bash(npm run:*)", "<PERSON><PERSON>(tsx:*)", "Bash(node:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(az account show:*)", "Bash(az resource list:*)", "Bash(az sql server show:*)", "Bash(az sql db list:*)", "<PERSON><PERSON>(az webapp show:*)", "Bash(az sql server delete:*)", "Bash(az functionapp delete:*)", "Bash(az appservice plan delete:*)", "Bash(az webapp list:*)", "<PERSON><PERSON>(az keyvault show:*)", "Bash(az keyvault delete:*)", "Ba<PERSON>(az sql db show:*)", "Bash(az appservice plan show:*)", "Bash(az webapp delete:*)", "Bash(az monitor app-insights component delete:*)", "Bash(vercel logs:*)", "Bash(vercel ls:*)", "mcp__gemini-collab__gemini_brainstorm", "<PERSON><PERSON>(touch:*)", "Bash(echo $env:USERPROFILE)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(git clone:*)", "Bash(pip --version)", "Bash(.zen_venvScriptsactivate)", "Bash(.zen_venv\\Scripts\\activate.bat)", "Bash(.zen_venv/Scripts/pip install:*)", "Bash(.zen_venv/Scripts/python:*)", "Bash(gemini:*)", "Bash(find:*)", "Bash(npm install:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(vercel:*)", "Bash(az sql server list:*)", "Bash(az postgres flexible-server create:*)", "Bash(az provider register:*)", "Bash(az provider show:*)", "Bash(az postgres flexible-server list:*)", "Bash(az postgres flexible-server show:*)", "Bash(az postgres flexible-server db create:*)", "Bash(az postgres flexible-server firewall-rule create:*)", "Bash(npx tsx:*)", "<PERSON><PERSON>(pkill:*)", "Bash(start cmd /k:*)", "Bash(cmd /c:*)", "Bash(ping:*)", "<PERSON><PERSON>(timeout:*)", "Bash(npx tsc:*)", "Ba<PERSON>(unzip:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["zen"]}