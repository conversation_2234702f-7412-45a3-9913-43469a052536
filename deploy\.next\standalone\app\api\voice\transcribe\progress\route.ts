import { NextRequest, NextResponse } from 'next/server';

// In-memory progress tracking
const transcriptionProgress = new Map<string, {
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number; // 0-100
  currentChunk?: number;
  totalChunks?: number;
  message?: string;
  startTime?: number;
}>();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const recordingId = searchParams.get('recordingId');

    if (!recordingId) {
      return NextResponse.json(
        { error: 'Recording ID is required' },
        { status: 400 }
      );
    }

    const progress = transcriptionProgress.get(recordingId);
    
    if (!progress) {
      return NextResponse.json({
        status: 'not_found',
        progress: 0,
        message: 'Transcription not started'
      });
    }

    return NextResponse.json(progress);

  } catch (error: any) {
    console.error('Get transcription progress error:', error);
    return NextResponse.json(
      { error: `Failed to get progress: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { recordingId, status, progress, currentChunk, totalChunks, message } = await request.json();

    if (!recordingId) {
      return NextResponse.json(
        { error: 'Recording ID is required' },
        { status: 400 }
      );
    }

    const progressData = {
      status,
      progress: Math.min(100, Math.max(0, progress || 0)),
      currentChunk,
      totalChunks,
      message,
      startTime: transcriptionProgress.get(recordingId)?.startTime || Date.now()
    };

    transcriptionProgress.set(recordingId, progressData);

    return NextResponse.json({ success: true });

  } catch (error: any) {
    console.error('Update transcription progress error:', error);
    return NextResponse.json(
      { error: `Failed to update progress: ${error.message}` },
      { status: 500 }
    );
  }
}

// Helper function to update progress (can be imported by transcribe route)
export function updateTranscriptionProgress(
  recordingId: string, 
  status: 'pending' | 'processing' | 'completed' | 'error',
  progress: number,
  currentChunk?: number,
  totalChunks?: number,
  message?: string
) {
  const progressData = {
    status,
    progress: Math.min(100, Math.max(0, progress)),
    currentChunk,
    totalChunks,
    message,
    startTime: transcriptionProgress.get(recordingId)?.startTime || Date.now()
  };

  transcriptionProgress.set(recordingId, progressData);
}

// Clean up completed progress after 5 minutes
setInterval(() => {
  const now = Date.now();
  for (const [recordingId, progress] of transcriptionProgress.entries()) {
    if (progress.status === 'completed' || progress.status === 'error') {
      const age = now - (progress.startTime || 0);
      if (age > 5 * 60 * 1000) { // 5 minutes
        transcriptionProgress.delete(recordingId);
      }
    }
  }
}, 60000); // Check every minute
