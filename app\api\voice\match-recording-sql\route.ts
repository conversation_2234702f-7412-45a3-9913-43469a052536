import { NextRequest, NextResponse } from 'next/server';
import { matchRecordingToAppointment, unmatchRecording, getMatchHistory, logMessage, checkDatabaseHealth, executeQuery, withTransaction } from '@/lib/database/index';

// GUID validation regex
const GUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

function isValidGuid(guid: string): boolean {
  return GUID_REGEX.test(guid);
}

export async function POST(request: NextRequest) {
  try {
    const { recording_id, appointment_id } = await request.json();

    // Validate required fields
    if (!recording_id || !appointment_id) {
      return NextResponse.json(
        { error: 'Recording ID and appointment ID are required' },
        { status: 400 }
      );
    }

    // Validate GUID format
    if (!isValidGuid(recording_id)) {
      return NextResponse.json(
        { error: 'Invalid recording ID format (must be GUID)' },
        { status: 400 }
      );
    }

    await matchRecordingToAppointment(recording_id, appointment_id, {
      matchMethod: 'manual',
      reasoning: 'Matched via match-recording-sql API'
    });

    // Return response compatible with existing endpoint
    return NextResponse.json({
      success: true,
      recordingId: recording_id,
      appointmentId: appointment_id,
      message: 'Recording matched successfully'
    });

  } catch (error: any) {
    console.error('Match recording error:', error);
    
    // Log error
    if (recording_id && isValidGuid(recording_id)) {
      await logMessage(
        'error',
        `Failed to match recording: ${error.message}`,
        recording_id,
        undefined,
        { error: error.message, stack: error.stack }
      );
    }

    return NextResponse.json(
      { error: `Failed to match recording: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const recordingId = searchParams.get('recordingId');
    const appointmentId = searchParams.get('appointmentId');

    if (recordingId) {
      // Validate GUID format
      if (!isValidGuid(recordingId)) {
        return NextResponse.json(
          { error: 'Invalid recording ID format (must be GUID)' },
          { status: 400 }
        );
      }

      // Get match for specific recording
      const matches = await getMatchHistory({ recordingId, limit: 1 });

      if (matches.length === 0) {
        return NextResponse.json(
          { error: 'Match not found' },
          { status: 404 }
        );
      }

      const match = matches[0];
      return NextResponse.json({ 
        recordingId, 
        match: {
          recordingId: match.recordingId,
          appointmentId: match.appointmentId,
          patientName: match.patientName,
          appointmentDate: match.appointmentDate,
          operatory: match.operatory,
          provider: match.provider,
          matchedAt: match.matchedAt
        }
      });
    }

    if (appointmentId) {
      // Get all recordings matched to specific appointment
      const matchedRecordings = await getMatchHistory({ appointmentId });

      return NextResponse.json({ 
        appointmentId, 
        recordings: matchedRecordings.map(record => ({
          recordingId: record.recordingId,
          appointmentId: record.appointmentId,
          patientName: record.patientName,
          appointmentDate: record.appointmentDate,
          operatory: record.operatory,
          provider: record.provider,
          filename: record.recordingFilename,
          file_size: record.file_size, // This might not be available from getMatchHistory
          status: record.status, // This might not be available from getMatchHistory
          created_at: record.created_at, // This might not be available from getMatchHistory
          matchedAt: record.matchedAt
        }))
      });
    }

    // Get all matches
    const matches = await getMatchHistory();

    const responseMatches: Record<string, any> = {};
    matches.forEach(record => {
      responseMatches[record.recordingId] = {
        recordingId: record.recordingId,
        appointmentId: record.appointmentId,
        patientName: record.patientName,
        appointmentDate: record.appointmentDate,
        operatory: record.operatory,
        provider: record.provider,
        filename: record.recordingFilename,
        matchedAt: record.matchedAt
      };
    });

    return NextResponse.json({ matches: responseMatches });

  } catch (error: any) {
    console.error('Get matches error:', error);
    return NextResponse.json(
      { error: `Failed to get matches: ${error.message}` },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const recordingId = searchParams.get('recordingId');

    if (!recordingId) {
      return NextResponse.json(
        { error: 'Recording ID is required' },
        { status: 400 }
      );
    }

    // Validate GUID format
    if (!isValidGuid(recordingId)) {
      return NextResponse.json(
        { error: 'Invalid recording ID format (must be GUID)' },
        { status: 400 }
      );
    }

    await unmatchRecording(recordingId, {
      reason: 'Unmatched via match-recording-sql API'
    });

    return NextResponse.json({
      success: true,
      recordingId,
      message: 'Recording unlinked from appointment successfully'
    });

  } catch (error: any) {
    console.error('Unlink recording error:', error);
    
    // Log error
    if (recordingId && isValidGuid(recordingId)) {
      await logMessage(
        'error',
        `Failed to unmatch recording: ${error.message}`,
        recordingId,
        undefined,
        { error: error.message, stack: error.stack }
      );
    }

    return NextResponse.json(
      { error: `Failed to unlink recording: ${error.message}` },
      { status: 500 }
    );
  }
}