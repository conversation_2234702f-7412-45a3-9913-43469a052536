@echo off
REM Office Schedule Viewer
REM This batch file runs the office_schedule.py script with the specified parameters

echo Office Schedule Viewer
echo --------------------

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Python is not installed or not in your PATH.
    echo Please install Python from https://www.python.org/downloads/
    pause
    exit /b
)

REM Check if required packages are installed
python -c "import requests" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing required packages...
    pip install requests
)

REM Get date (default to today)
set /p date="Enter date (YYYY-MM-DD) [today]: "
if "%date%"=="" (
    for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (
        set mm=%%a
        set dd=%%b
        set yy=%%c
    )
    set date=%yy%-%mm%-%dd%
)

REM Get provider ID
set /p provider="Enter provider ID (e.g., LL01): "
if "%provider%"=="" (
    echo Provider ID is required.
    pause
    exit /b
)

REM Ask if user wants to list available operatories
set /p list_ops="Do you want to see a list of available operatories? (y/n) [n]: "
if /i "%list_ops%"=="y" (
    echo Listing available operatories...
    python office_schedule.py %date% %provider% --list-operatories
    pause

    REM Ask if user wants to continue after seeing the list
    set /p continue="Continue with schedule generation? (y/n) [y]: "
    if /i not "%continue%"=="y" (
        if not "%continue%"=="" (
            exit /b
        )
    )
)

REM Ask if user wants to filter by operatories
set /p filter_ops="Do you want to filter by specific operatories? (y/n) [n]: "
if /i "%filter_ops%"=="y" (
    set /p operatories="Enter comma-separated list of operatories (e.g., DL01,DL02): "
    if not "%operatories%"=="" (
        set operatory_flag=--operatories=%operatories%
    ) else (
        set operatory_flag=
    )
) else (
    set operatory_flag=
)

REM Ask if user wants to see procedures from all providers
set /p all_providers="Do you want to see ALL procedures (including those not in your operatories)? (y/n) [n]: "
if /i "%all_providers%"=="y" (
    set provider_flag=--show-all-providers
) else (
    set provider_flag=
)

REM Run the script
echo Running office schedule for %provider% on %date%...
python office_schedule.py %date% %provider% %operatory_flag% %provider_flag%

pause
