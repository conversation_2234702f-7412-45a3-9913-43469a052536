'use client';

import { RealTimeTranscription } from '@/components/voice/real-time-transcription';

export default function TranscriptionControlPage() {
  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            🎯 Real-Time Transcription Control
          </h1>
          <p className="text-gray-600">
            Live streaming transcription with real-time progress updates
          </p>
        </div>
        
        <RealTimeTranscription />
        
        <div className="bg-green-50 border border-green-200 rounded p-4">
          <h4 className="font-semibold text-green-800 mb-2">✨ New Features:</h4>
          <ul className="text-sm text-green-700 space-y-1">
            <li>• <strong>Real-time streaming</strong> - See exactly what's happening as it happens</li>
            <li>• <strong>Live progress bars</strong> - Visual feedback for every step</li>
            <li>• <strong>Instant error reporting</strong> - Know immediately if something fails</li>
            <li>• <strong>File-by-file updates</strong> - Track each transcription individually</li>
            <li>• <strong>No more mystery failures</strong> - Everything is visible and logged</li>
          </ul>
        </div>
      </div>
    </div>
  );
}