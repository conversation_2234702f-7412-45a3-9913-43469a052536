"use strict";exports.id=9078,exports.ids=[9078],exports.modules={49078:(e,s,t)=>{t.r(s),t.d(s,{default:()=>g});var a=t(60687),r=t(43210),n=t(99270),l=t(78122),i=t(93613),d=t(10022),o=t(40228),c=t(58869),x=t(47158),m=t(78200),h=t(70615);function g({isDarkMode:e}){let[s,t]=(0,r.useState)(!1),[g,p]=(0,r.useState)(""),[b,j]=(0,r.useState)([]),[y,u]=(0,r.useState)(!1),[N,f]=(0,r.useState)(null),[w,v]=(0,r.useState)(new Set),[C,S]=(0,r.useState)({patientId:"",appointmentId:"",date:"",dateRange:"7"}),k=async()=>{u(!0),f(null);try{let e=new URLSearchParams;C.dateRange&&e.append("date_range",C.dateRange),C.patientId&&e.append("patient_id",C.patientId),C.appointmentId&&e.append("appointment_id",C.appointmentId),C.date&&e.append("date",C.date);let s=await fetch(`/api/clinical-notes?${e.toString()}`);if(!s.ok)throw Error(`Failed to load clinical notes: ${s.status}`);let t=await s.json();j(t.notes||[])}catch(e){console.error("Error loading clinical notes:",e),f(e instanceof Error?e.message:"Failed to load clinical notes")}finally{u(!1)}},A=async()=>{t(!0),f(null);try{let e=b.filter(e=>w.has(e.id));if(0===e.length)throw Error("Please select at least one clinical note to professionalize");let s=e.map(e=>e.notes).join("\n\n"),t=await fetch("/api/clinical-notes/professionalize",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({notes:s,context:{patientCount:new Set(e.map(e=>e.patient_id)).size,dateRange:e.length>1?`${e[e.length-1].date} to ${e[0].date}`:e[0].date}})});if(!t.ok)throw Error(`Failed to professionalize notes: ${t.status}`);let a=await t.json();p(a.professionalNote||a.result||"No result returned")}catch(e){console.error("Failed to professionalize notes:",e),f(e instanceof Error?e.message:"Failed to professionalize notes")}finally{t(!1)}},$=e=>{navigator.clipboard.writeText(e)},I=e=>{let s=new Set(w);s.has(e)?s.delete(e):s.add(e),v(s)};return(0,a.jsx)("div",{className:`min-h-screen p-6 ${e?"bg-gray-900 text-white":"bg-gray-50 text-gray-900"}`,children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Clinical Notes Generator"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Transform clinical notes into professional documentation using AI"})]}),(0,a.jsxs)("div",{className:`rounded-lg border ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"} p-6`,children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,a.jsx)(n.A,{className:"w-5 h-5 mr-2"}),"Search Clinical Notes"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Patient ID"}),(0,a.jsx)("input",{type:"text",value:C.patientId,onChange:e=>S(s=>({...s,patientId:e.target.value})),className:`w-full px-3 py-2 border rounded-md ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300"}`,placeholder:"Enter Patient ID"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Appointment ID"}),(0,a.jsx)("input",{type:"text",value:C.appointmentId,onChange:e=>S(s=>({...s,appointmentId:e.target.value})),className:`w-full px-3 py-2 border rounded-md ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300"}`,placeholder:"Enter Appointment ID"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Specific Date"}),(0,a.jsx)("input",{type:"date",value:C.date,onChange:e=>S(s=>({...s,date:e.target.value})),className:`w-full px-3 py-2 border rounded-md ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300"}`})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Date Range (days)"}),(0,a.jsxs)("select",{value:C.dateRange,onChange:e=>S(s=>({...s,dateRange:e.target.value})),className:`w-full px-3 py-2 border rounded-md ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300"}`,children:[(0,a.jsx)("option",{value:"1",children:"1 day"}),(0,a.jsx)("option",{value:"3",children:"3 days"}),(0,a.jsx)("option",{value:"7",children:"7 days"}),(0,a.jsx)("option",{value:"14",children:"14 days"}),(0,a.jsx)("option",{value:"30",children:"30 days"})]})]})]}),(0,a.jsx)("button",{onClick:()=>{k()},disabled:y,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:y?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{className:"w-4 h-4 animate-spin"}),(0,a.jsx)("span",{children:"Loading..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Search Notes"})]})})]}),N&&(0,a.jsx)("div",{className:`rounded-lg border border-red-200 p-4 ${e?"bg-red-900/20 text-red-300":"bg-red-50 text-red-800"}`,children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:N})]})}),(0,a.jsxs)("div",{className:`rounded-lg border ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"} p-6`,children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold flex items-center",children:[(0,a.jsx)(d.A,{className:"w-5 h-5 mr-2"}),"Clinical Notes (",b.length,")"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Selected: ",w.size]})]}),y?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)(l.A,{className:"w-8 h-8 animate-spin text-blue-600"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading clinical notes..."})]}):0===b.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(d.A,{className:"w-12 h-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("p",{children:"No clinical notes found. Try adjusting your search criteria."})]}):(0,a.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:b.map(s=>(0,a.jsxs)("div",{className:`p-4 rounded-lg border cursor-pointer transition-colors ${w.has(s.id)?e?"bg-blue-900/30 border-blue-600":"bg-blue-50 border-blue-300":e?"bg-gray-700 border-gray-600 hover:bg-gray-600":"bg-gray-50 border-gray-200 hover:bg-gray-100"}`,onClick:()=>I(s.id),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:w.has(s.id),onChange:()=>I(s.id),className:"rounded"}),(0,a.jsx)(o.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:s.date}),s.patient_id&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsxs)("span",{className:"text-sm",children:["Patient: ",s.patient_id]})]})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[s.wordCount," words"]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 line-clamp-3",children:s.preview})]},s.id))})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("button",{onClick:A,disabled:s||0===w.size,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{className:"w-5 h-5 animate-spin"}),(0,a.jsx)("span",{children:"Generating Clinical Notes..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"w-5 h-5"}),(0,a.jsxs)("span",{children:["Generate Professional Clinical Notes (",w.size," selected)"]})]})})}),g&&(0,a.jsxs)("div",{className:`rounded-lg border ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"} p-6`,children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold flex items-center",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 mr-2"}),"Professional Clinical Notes"]}),(0,a.jsx)("button",{onClick:()=>$(g),className:"p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-600",title:"Copy to clipboard",children:(0,a.jsx)(h.A,{className:"w-4 h-4"})})]}),(0,a.jsx)("div",{className:`p-4 rounded-lg ${e?"bg-gray-700":"bg-gray-50"}`,children:(0,a.jsx)("pre",{className:"text-sm whitespace-pre-wrap font-mono",children:g})})]}),(0,a.jsxs)("div",{className:`rounded-lg border ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"} p-6`,children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,a.jsx)(d.A,{className:"w-5 h-5 mr-2"}),"How to Use"]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsxs)("p",{children:["1. ",(0,a.jsx)("strong",{children:"Search:"})," Use the search controls to find clinical notes by patient, appointment, or date"]}),(0,a.jsxs)("p",{children:["2. ",(0,a.jsx)("strong",{children:"Select:"})," Check the boxes next to the notes you want to professionalize"]}),(0,a.jsxs)("p",{children:["3. ",(0,a.jsx)("strong",{children:"Generate:"}),' Click the "Generate Professional Clinical Notes" button to transform selected notes']}),(0,a.jsxs)("p",{children:["4. ",(0,a.jsx)("strong",{children:"Copy & Use:"})," Copy the generated professional notes to your dental practice management system"]}),(0,a.jsxs)("p",{children:["5. ",(0,a.jsx)("strong",{children:"Integration:"})," The generated notes are formatted for direct paste into Dentrix and other dental software"]})]})]})]})})}}};