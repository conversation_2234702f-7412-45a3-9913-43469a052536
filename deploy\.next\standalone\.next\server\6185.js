"use strict";exports.id=6185,exports.ids=[6185],exports.modules={15177:(e,t,a)=>{a.d(t,{A:()=>u});var s=a(60687),r=a(43210),l=a(43125),n=a(5336),i=a(35071),d=a(43649),o=a(45989),c=a(97840);function u({isDarkMode:e=!1}){let[t,a]=(0,r.useState)(!1),[u,m]=(0,r.useState)([]),g=(e,t=1048576)=>new File([new ArrayBuffer(t)],e,{type:"audio/mpeg"}),p=[{name:"Mock USB Connection",test:async()=>(await new Promise(e=>setTimeout(e,1e3)),{status:"passed",message:"Mock USB drive connected successfully",details:"Simulated F: drive with REC_FILE/FOLDER01 structure"})},{name:"Mock File Scanning",test:async()=>{await new Promise(e=>setTimeout(e,800));let e=["REC001.mp3","REC002.mp3","REC003.wav"];return{status:"passed",message:`Found ${e.length} audio files`,details:`Files: ${e.join(", ")}`}}},{name:"Mock File Upload",test:async()=>{try{let e=[g("REC001.mp3",2097152),g("REC002.mp3",1572864)],t=new FormData;e.forEach(e=>{t.append("files",e)}),t.append("enableSmartSort","true"),t.append("deviceId","mock-test"),t.append("archiveEnabled","true");let a=await fetch("/api/voice/webusb-upload",{method:"POST",body:t});if(!a.ok){let e=await a.json();throw Error(`Upload failed: ${e.error||a.statusText}`)}let s=await a.json();return{status:"passed",message:`Uploaded ${s.successfulUploads}/${s.totalFiles} files`,details:`Smart sort: ${s.smartSortTriggered?"triggered":"skipped"}`}}catch(e){return{status:"failed",message:"Upload test failed",details:e instanceof Error?e.message:"Unknown error"}}}},{name:"Mock USB Archiving",test:async()=>{await new Promise(e=>setTimeout(e,1200));let e=new Date().toISOString().split("T")[0];return{status:"passed",message:"Files archived successfully",details:`Created ARCHIVE/${e}/ folder and moved 2 files`}}},{name:"Mock Archive Cleanup",test:async()=>(await new Promise(e=>setTimeout(e,600)),{status:"passed",message:"Archive cleanup completed",details:"Removed 3 old archive folders (30+ days)"})},{name:"End-to-End Workflow",test:async()=>(await new Promise(e=>setTimeout(e,500)),{status:"passed",message:"Complete workflow simulation successful",details:"USB → Scan → Upload → Archive → Cleanup pipeline verified"})}],x=async()=>{for(let e of(a(!0),m([]),p)){let t=Date.now();m(t=>[...t,{name:e.name,status:"running",message:"Running test..."}]);try{let a=await e.test(),s=Date.now()-t;m(t=>t.map(t=>t.name===e.name?{name:e.name,duration:s,...a}:t))}catch(s){let a=Date.now()-t;m(t=>t.map(t=>t.name===e.name?{name:e.name,status:"failed",message:"Test execution failed",details:s instanceof Error?s.message:"Unknown error",duration:a}:t))}await new Promise(e=>setTimeout(e,300))}a(!1)},h=e=>{switch(e){case"running":return(0,s.jsx)(l.A,{className:"w-4 h-4 text-blue-600 animate-spin"});case"passed":return(0,s.jsx)(n.A,{className:"w-4 h-4 text-green-600"});case"failed":return(0,s.jsx)(i.A,{className:"w-4 h-4 text-red-600"});case"warning":return(0,s.jsx)(d.A,{className:"w-4 h-4 text-yellow-600"});default:return(0,s.jsx)("div",{className:"w-4 h-4 rounded-full bg-gray-300"})}},b=t=>{switch(t){case"running":return"text-blue-600";case"passed":return"text-green-600";case"failed":return"text-red-600";case"warning":return"text-yellow-600";default:return e?"text-gray-400":"text-gray-600"}},f=u.filter(e=>"passed"===e.status).length,y=u.filter(e=>"failed"===e.status).length,w=u.reduce((e,t)=>e+(t.duration||0),0);return(0,s.jsxs)("div",{className:`p-6 rounded-lg border ${e?"border-gray-700 bg-gray-800":"border-gray-200 bg-white"}`,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(o.A,{className:`w-6 h-6 ${e?"text-purple-400":"text-purple-600"}`}),(0,s.jsx)("h2",{className:`text-xl font-semibold ${e?"text-white":"text-gray-900"}`,children:"WebUSB Mock Test Suite"})]}),(0,s.jsxs)("button",{onClick:x,disabled:t,className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[(0,s.jsx)(c.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:t?"Running Tests...":"Run Mock Tests"})]})]}),(0,s.jsx)("div",{className:`mb-4 p-3 rounded-lg ${e?"bg-purple-900/20 border border-purple-800":"bg-purple-50 border border-purple-200"}`,children:(0,s.jsxs)("p",{className:`text-sm ${e?"text-purple-200":"text-purple-800"}`,children:[(0,s.jsx)("strong",{children:"Mock Test Suite:"})," This simulates the complete WebUSB workflow including USB connection, file scanning, upload, archiving, and cleanup. It tests the actual API endpoints with mock data."]})}),u.length>0&&(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)("div",{className:`p-3 rounded-lg ${e?"bg-gray-700":"bg-gray-50"}`,children:(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsxs)("span",{className:e?"text-gray-300":"text-gray-600",children:["Progress: ",u.length,"/",p.length," tests"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[f>0&&(0,s.jsxs)("span",{className:"text-green-600",children:["✓ ",f," passed"]}),y>0&&(0,s.jsxs)("span",{className:"text-red-600",children:["✗ ",y," failed"]}),w>0&&(0,s.jsxs)("span",{className:e?"text-gray-400":"text-gray-500",children:[(w/1e3).toFixed(1),"s"]})]})]})})}),(0,s.jsx)("div",{className:"space-y-3",children:p.map((t,a)=>{let r=u.find(e=>e.name===t.name);return(0,s.jsx)("div",{className:`p-4 rounded-lg border ${r?.status==="passed"?"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20":r?.status==="failed"?"border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20":r?.status==="running"?"border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20":e?"border-gray-600 bg-gray-700":"border-gray-200 bg-gray-50"}`,children:(0,s.jsx)("div",{className:"flex items-start justify-between",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[h(r?.status||"pending"),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:`font-medium ${e?"text-white":"text-gray-900"}`,children:t.name}),r&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{className:`text-sm mt-1 ${b(r.status)}`,children:r.message}),r.details&&(0,s.jsx)("p",{className:`text-xs mt-1 ${e?"text-gray-400":"text-gray-600"}`,children:r.details}),r.duration&&(0,s.jsxs)("p",{className:"text-xs mt-1 text-gray-500",children:["Completed in ",r.duration,"ms"]})]})]})]})})},a)})}),u.length>0&&!t&&(0,s.jsxs)("div",{className:`mt-6 p-4 rounded-lg ${0===y?"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20":"border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20"}`,children:[(0,s.jsx)("h4",{className:`font-medium mb-2 ${0===y?"text-green-800 dark:text-green-200":"text-red-800 dark:text-red-200"}`,children:0===y?"\uD83C\uDF89 Mock Test Suite Passed!":"❌ Some Tests Failed"}),(0,s.jsx)("p",{className:`text-sm ${0===y?"text-green-700 dark:text-green-300":"text-red-700 dark:text-red-300"}`,children:0===y?`All ${f} tests passed successfully. The WebUSB workflow simulation completed in ${(w/1e3).toFixed(1)} seconds.`:`${y} test(s) failed out of ${u.length}. Please check the API endpoints and configuration.`})]})]})}},52619:(e,t,a)=>{a.d(t,{A:()=>c});var s=a(60687),r=a(43210),l=a(43125),n=a(5336),i=a(35071),d=a(43649),o=a(45989);function c({isDarkMode:e=!1}){let[t,c]=(0,r.useState)(!1),[u,m]=(0,r.useState)([]),g=[{name:"Browser Compatibility",test:async()=>({status:"failed",message:"File System Access API not supported",details:"Requires Chrome 86+ or Edge 86+"})},{name:"Azure Storage Configuration",test:async()=>{try{let e=await fetch("/api/voice/webusb-upload",{method:"POST",body:new FormData});if(400===e.status)return{status:"passed",message:"WebUSB upload endpoint accessible",details:"API endpoint responding correctly"};if(500===e.status){let t=await e.json();if(t.error?.includes("Azure Storage not configured"))return{status:"failed",message:"Azure Storage not configured",details:"AZURE_STORAGE_CONNECTION_STRING environment variable missing"}}return{status:"warning",message:"Unexpected API response",details:`Status: ${e.status}`}}catch(e){return{status:"failed",message:"API endpoint not accessible",details:e instanceof Error?e.message:"Unknown error"}}}},{name:"Smart Sort Integration",test:async()=>{try{let e=await fetch("/api/voice/smart-sort",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({audioFiles:[],uploadDate:new Date().toISOString().split("T")[0]})});if(e.ok||400===e.status)return{status:"passed",message:"Smart sort endpoint accessible",details:"Integration ready for patient matching"};return{status:"warning",message:"Smart sort endpoint issues",details:`Status: ${e.status}`}}catch(e){return{status:"failed",message:"Smart sort endpoint not accessible",details:e instanceof Error?e.message:"Unknown error"}}}},{name:"Auto-Transcription Pipeline",test:async()=>({status:"passed",message:"Auto-transcription pipeline ready",details:"Triggered automatically when files are uploaded to Azure Storage"})},{name:"USB File Management Utils",test:async()=>{try{let{isFileSystemAccessSupported:e}=await a.e(7018).then(a.bind(a,37018)),t=e();return{status:t?"passed":"failed",message:t?"USB file utilities loaded":"USB file utilities not supported",details:t?"File management functions available":"Browser compatibility issues"}}catch(e){return{status:"failed",message:"USB utilities import failed",details:e instanceof Error?e.message:"Unknown error"}}}}],p=async()=>{for(let e of(c(!0),m([]),g)){m(t=>[...t,{name:e.name,status:"running",message:"Running test..."}]);try{let t=await e.test();m(a=>a.map(a=>a.name===e.name?{name:e.name,...t}:a))}catch(t){m(a=>a.map(a=>a.name===e.name?{name:e.name,status:"failed",message:"Test execution failed",details:t instanceof Error?t.message:"Unknown error"}:a))}await new Promise(e=>setTimeout(e,500))}c(!1)},x=e=>{switch(e){case"running":return(0,s.jsx)(l.A,{className:"w-4 h-4 text-blue-600 animate-spin"});case"passed":return(0,s.jsx)(n.A,{className:"w-4 h-4 text-green-600"});case"failed":return(0,s.jsx)(i.A,{className:"w-4 h-4 text-red-600"});case"warning":return(0,s.jsx)(d.A,{className:"w-4 h-4 text-yellow-600"});default:return(0,s.jsx)("div",{className:"w-4 h-4 rounded-full bg-gray-300"})}},h=t=>{switch(t){case"running":return"text-blue-600";case"passed":return"text-green-600";case"failed":return"text-red-600";case"warning":return"text-yellow-600";default:return e?"text-gray-400":"text-gray-600"}},b=u.filter(e=>"passed"===e.status).length,f=u.filter(e=>"failed"===e.status).length,y=u.filter(e=>"warning"===e.status).length;return(0,s.jsxs)("div",{className:`p-6 rounded-lg border ${e?"border-gray-700 bg-gray-800":"border-gray-200 bg-white"}`,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(o.A,{className:`w-6 h-6 ${e?"text-blue-400":"text-blue-600"}`}),(0,s.jsx)("h2",{className:`text-xl font-semibold ${e?"text-white":"text-gray-900"}`,children:"WebUSB Integration Test"})]}),(0,s.jsx)("button",{onClick:p,disabled:t,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:t?"Running Tests...":"Run Tests"})]}),u.length>0&&(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)("div",{className:`p-3 rounded-lg ${e?"bg-gray-700":"bg-gray-50"}`,children:(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsxs)("span",{className:e?"text-gray-300":"text-gray-600",children:["Test Results: ",u.length," total"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[b>0&&(0,s.jsxs)("span",{className:"text-green-600",children:["✓ ",b," passed"]}),y>0&&(0,s.jsxs)("span",{className:"text-yellow-600",children:["⚠ ",y," warnings"]}),f>0&&(0,s.jsxs)("span",{className:"text-red-600",children:["✗ ",f," failed"]})]})]})})}),(0,s.jsx)("div",{className:"space-y-3",children:g.map((t,a)=>{let r=u.find(e=>e.name===t.name);return(0,s.jsx)("div",{className:`p-4 rounded-lg border ${r?.status==="passed"?"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20":r?.status==="failed"?"border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20":r?.status==="warning"?"border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20":r?.status==="running"?"border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20":e?"border-gray-600 bg-gray-700":"border-gray-200 bg-gray-50"}`,children:(0,s.jsx)("div",{className:"flex items-start justify-between",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[x(r?.status||"pending"),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:`font-medium ${e?"text-white":"text-gray-900"}`,children:t.name}),r&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{className:`text-sm mt-1 ${h(r.status)}`,children:r.message}),r.details&&(0,s.jsx)("p",{className:`text-xs mt-1 ${e?"text-gray-400":"text-gray-600"}`,children:r.details})]})]})]})})},a)})}),u.length>0&&!t&&(0,s.jsxs)("div",{className:`mt-6 p-4 rounded-lg ${0===f?"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20":"border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20"}`,children:[(0,s.jsx)("h4",{className:`font-medium mb-2 ${0===f?"text-green-800 dark:text-green-200":"text-red-800 dark:text-red-200"}`,children:0===f?"✅ All Systems Ready":"❌ Issues Detected"}),(0,s.jsx)("p",{className:`text-sm ${0===f?"text-green-700 dark:text-green-300":"text-red-700 dark:text-red-300"}`,children:0===f?"WebUSB file transfer system is ready for use. All components are properly configured and accessible.":`${f} test(s) failed. Please resolve the issues above before using the WebUSB file transfer system.`})]})]})}},90995:(e,t,a)=>{a.d(t,{A:()=>p});var s=a(60687),r=a(43210),l=a(5336),n=a(93613),i=a(80534),d=a(96882),o=a(78122),c=a(16023),u=a(77026);let m=[".mp3",".wav",".m4a",".aac",".wma",".ogg",".flac"],g=["REC_FILE/FOLDER01","RECORDER/FOLDER_A","RECORDER/FOLDER_B","RECORDER/FOLDER_C","RECORDER/FOLDER_D","RECORDER/FOLDER_E"];function p({onFilesUploaded:e,onError:t,isDarkMode:a=!1}){var p;let[x,h]=(0,r.useState)(null),[b,f]=(0,r.useState)(!1),[y,w]=(0,r.useState)(!1),[v,j]=(0,r.useState)(!1),[S,N]=(0,r.useState)([]),[$,k]=(0,r.useState)([]),[U,E]=(0,r.useState)(new Set),[C,A]=(0,r.useState)(""),[D,F]=(0,r.useState)(""),[R,T]=(0,r.useState)({created:0,moved:0,deleted:0}),[B,I]=(0,r.useState)(!1),[P,z]=(0,r.useState)(null),[M,O]=(0,r.useState)({completed:0,total:0,speed:0}),[H,L]=(0,r.useState)(!0),[_,W]=(0,r.useState)({deviceId:"webusb-upload"}),[V,q]=(0,r.useState)(null),G=(0,r.useRef)(null);(0,r.useCallback)(async e=>{try{try{let t=await e.getFileHandle("dental_device_info.json"),a=await t.getFile(),s=await a.text(),r=JSON.parse(s);return W(r),r}catch{let t="webusb-upload",a=!1;try{let a=await e.getFileHandle("device_id.txt"),s=await a.getFile(),r=(await s.text()).trim();r&&(t=`device-${r}`)}catch{let s=e.name||"Unknown",r=Date.now().toString(36),l=Math.random().toString(36).substring(2,5),n=s.replace(/[^a-zA-Z0-9]/g,"").substring(0,8);t=`device-${n}-${r}-${l}`,a=!0;try{let t=await e.getFileHandle("device_id.txt",{create:!0}),a=await t.createWritable();await a.write(`${n}-${r}-${l}`),await a.close()}catch(e){console.warn("⚠️  Could not create device_id.txt:",e)}}let s={deviceId:t,deviceName:e.name||"Unknown Device",createdAt:new Date().toISOString(),isNewDevice:a,totalUploads:0,totalSyncs:0,syncHistory:[],deviceIdSource:a?"generated":"legacy"};return W(s),await J(e,s),s}}catch(t){console.error("❌ Error reading device metadata:",t);let e={deviceId:"webusb-upload"};return W(e),e}},[]);let J=(0,r.useCallback)(async(e,t)=>{try{let a=await e.getFileHandle("dental_device_info.json",{create:!0}),s=await a.createWritable();await s.write(JSON.stringify(t,null,2)),await s.close()}catch(e){console.error("❌ Error saving device metadata:",e)}},[]);(0,r.useCallback)(async(e,t)=>{try{let a={..._,lastSyncStarted:new Date().toISOString(),lastSyncFileCount:t,syncInProgress:!0,lastSyncStatus:"started"};W(a),await J(e,a)}catch(e){console.error("❌ Error updating pre-sync metadata:",e)}},[_,J]),(0,r.useCallback)(async(e,t,a=!1)=>{try{let s={..._,lastRun:new Date().toISOString(),lastSyncStarted:_.lastSyncStarted,totalUploads:(_.totalUploads||0)+t.successfulUploads,lastUploadCount:t.successfulUploads,lastSkippedCount:t.skippedUploads,lastFailedCount:t.failedUploads,lastArchiveSuccess:a,syncInProgress:!1,lastSyncStatus:t.failedUploads>0?"partial":"success",lastSyncDuration:_.lastSyncStarted?Math.round((new Date().getTime()-new Date(_.lastSyncStarted).getTime())/1e3):void 0};W(s),await J(e,s)}catch(e){console.error("❌ Error updating post-sync metadata:",e)}},[_,J]);let K=(0,r.useCallback)(e=>{let t={timestamp:new Date().toISOString(),status:"started",files:[],summary:{totalFiles:e,uploaded:0,skipped:0,failed:0,archived:0}};return q(t),t},[]),Z=(0,r.useCallback)(e=>{q(t=>{if(!t)return t;let a=[...t.files,e],s={...t.summary};switch(e.action){case"uploaded":s.uploaded++;break;case"skipped":s.skipped++;break;case"failed":s.failed++;break;case"archived":s.archived++}return{...t,files:a,summary:s}})},[]),Y=(0,r.useCallback)(async(e,t)=>{if(!V)return;let a=new Date(V.timestamp).getTime(),s=Math.round((new Date().getTime()-a)/1e3),r={...V,status:t,duration:s},l={..._,lastRun:new Date().toISOString(),totalUploads:(_.totalUploads||0)+r.summary.uploaded,totalSyncs:(_.totalSyncs||0)+1,syncHistory:[...(_.syncHistory||[]).slice(-9),r]};W(l),await J(e,l),q(null)},[V,_,J]);(0,r.useCallback)(async(e,t)=>{try{let a={..._,lastRun:new Date().toISOString(),lastSyncStarted:_.lastSyncStarted,syncInProgress:!1,lastSyncStatus:"failed",lastSyncError:t,lastSyncDuration:_.lastSyncStarted?Math.round((new Date().getTime()-new Date(_.lastSyncStarted).getTime())/1e3):void 0};W(a),await J(e,a)}catch(e){console.error("❌ Error updating error metadata:",e)}},[_,J]),(0,r.useCallback)(e=>{let t=e.name.toLowerCase(),a=[/^[d-z]:?$/i,/^[d-z]$/i,/usb/i,/removable/i,/external/i,/portable/i,/flash/i,/thumb/i,/stick/i,/recorder/i,/record/i,/audio/i,/voice/i,/^disk/i,/^drive/i,/^volume/i].some(e=>e.test(t)),s=e.name;return a&&(s=/^[d-z]:?$/i.test(t)?`Drive ${t.toUpperCase()}`:/recorder|record|audio|voice/i.test(t)?`Audio Recorder (${e.name})`:`USB Drive (${e.name})`),{isLikelyUSB:a,description:s,name:e.name}},[]);let Q=(0,r.useCallback)(()=>!0,[B]),X=(0,r.useCallback)(async()=>{try{{let e="File System Access API not available. Please use Chrome 86+ or Edge 86+ with HTTPS or localhost.";console.error("❌ File System Access API not available"),F(e),t?.(e);return}}catch(e){if(console.error("❌ USB connection error:",e),e instanceof Error&&"AbortError"===e.name)A('USB connection cancelled - click "Connect USB Drive" to try again');else{let a=`Failed to connect to USB drive: ${e instanceof Error?e.message:"Unknown error"}`;console.error("❌ Connection failed:",a),F(a),t?.(a)}}},[t]),ee=(0,r.useCallback)(async e=>{let a=e||x;if(a){w(!0),A("Scanning for audio files..."),F("");try{let e=[];for(let t of g)try{let s=t.split("/"),r=a;for(let e of s)try{r=await r.getDirectoryHandle(e)}catch{break}r!==a&&await et(r,t,e)}catch(e){}N(e),A(`Found ${e.length} audio files`),E(new Set(e.map(e=>e.path))),!(e.length>0)||v||y||(A(`✅ Found ${e.length} audio files - auto-upload starting in 2 seconds...`),setTimeout(()=>{if(!v&&e.length>0){A(`🚀 Auto-uploading ${e.length} files to Azure...`);let t=document.querySelector("[data-upload-button]");t&&!t.disabled?t.click():console.warn("Upload button not found or disabled")}},2e3))}catch(a){let e=`Failed to scan for audio files: ${a instanceof Error?a.message:"Unknown error"}`;F(e),t?.(e)}finally{w(!1)}}},[x,t]),et=async(e,t,a)=>{try{for await(let[s,r]of e.entries())if("file"===r.kind){let e="."+s.split(".").pop()?.toLowerCase();if(m.includes(e)){if(s.includes("ARCHIVE")||s.includes("TRASH"))continue;let e=await r.getFile();a.push({name:s,path:`${t}/${s}`,size:e.size,handle:r,folder:t,lastModified:e.lastModified})}}}catch(e){console.error(`Error scanning directory ${t}:`,e)}},ea=(0,r.useCallback)(e=>{E(t=>{let a=new Set(t);return a.has(e)?a.delete(e):a.add(e),a})},[]),es=(0,r.useCallback)(()=>{E(new Set(S.map(e=>e.path)))},[S]),er=(0,r.useCallback)(()=>{E(new Set)},[]),el=(0,r.useCallback)(async()=>{let a=S.filter(e=>U.has(e.path));if(0===a.length)return void F("No files selected for upload");j(!0),A(`🚀 Starting upload of ${a.length} files to Azure Blob Storage...`),F(""),z(Date.now()),O({completed:0,total:a.length,speed:0}),K(a.length),k(a.map(e=>({fileName:e.name,progress:0,status:"pending"}))),G.current=new AbortController;try{A("\uD83D\uDCD6 Reading files for bulk upload...");let t=new FormData,s=new Map,r=0;for(let e=0;e<a.length;e++){let l=a[e];if(G.current?.signal.aborted)break;A(`📖 Reading file ${e+1}/${a.length}: ${l.name}...`),k(e=>e.map(e=>e.fileName===l.name?{...e,status:"uploading",progress:25}:e));try{let n=await l.handle.getFile(),i=await n.arrayBuffer(),d=new Blob([i],{type:n.type||"audio/mpeg"});r+=d.size,t.append("files",d,l.name),s.set(l.name,l);let o=Math.round(d.size/1024/1024*100)/100;A(`📖 Read file ${e+1}/${a.length}: ${l.name} (${o} MB)`)}catch(e){k(t=>t.map(t=>t.fileName===l.name?{...t,status:"error",error:e instanceof Error?e.message:"Failed to read file"}:t))}}t.append("enableSmartSort","true"),t.append("deviceId",_.deviceId),t.append("archiveEnabled","true"),t.append("totalSize",r.toString()),k(e=>e.map(e=>({...e,status:"uploading",progress:50})));let l=Math.round(r/1024/1024*100)/100;A(`☁️ Uploading ${s.size} files to Azure Blob Storage (${l} MB total)...`);let n=Date.now(),i=await fetch("/api/voice/webusb-upload",{method:"POST",body:t,signal:G.current?.signal}),d=Date.now();if(!i.ok)throw Error(`Bulk upload failed: ${i.statusText}`);let o=await i.json(),c=[];if(o.results)for(let e of o.results){let t=s.get(e.fileName);("completed"===e.status||"skipped"===e.status)&&t&&c.push(t),"completed"===e.status?Z({name:e.fileName,size:t?.size||0,action:"uploaded",azurePath:e.azureUrl||`recordings/${new Date().toISOString().split("T")[0]}/${_.deviceId}/${e.fileName}`}):"skipped"===e.status?Z({name:e.fileName,size:t?.size||0,action:"skipped",reason:e.skipReason||"File already exists",azurePath:e.existingPath||e.azureUrl}):"error"===e.status&&Z({name:e.fileName,size:t?.size||0,action:"failed",error:e.error||"Upload failed"})}k(e=>e.map(e=>{let t=o.results?.find(t=>t.fileName===e.fileName);if(t){if("completed"===t.status||"skipped"===t.status)return{...e,progress:100,status:t.status};else if("error"===t.status)return{...e,status:"error",error:t.error||"Upload failed"}}return{...e,progress:100,status:"completed"}}));let u=Date.now(),m=Math.round((u-(n||u))/1e3*10)/10,g=Math.round(l/m*10)/10;O({completed:o.successfulUploads,total:o.totalFiles,speed:g});let p=`✅ Upload completed: ${o.successfulUploads}/${o.totalFiles} files`;if(o.skippedUploads>0&&(p+=`, ${o.skippedUploads} skipped (duplicates)`),p+=` (${l} MB in ${m}s) - Starting archive...`,A(p),c.length>0){await en(c);let e=`🎉 COMPLETE! Processed ${c.length} files`;if(o.successfulUploads>0||o.skippedUploads>0){e+=" (";let t=[];o.successfulUploads>0&&t.push(`${o.successfulUploads} uploaded to Azure`),o.skippedUploads>0&&t.push(`${o.skippedUploads} skipped - already exist in Azure`),e+=t.join(", ")+")"}e+=` and ${c.length} files archived to USB drive`,A(e)}else A(`✅ Upload completed: ${o.successfulUploads}/${o.totalFiles} files successful`);if(e?.(c),x){let e=o.failedUploads>0?"partial":"success";await Y(x,e)}E(new Set),await ee()}catch(e){if(e instanceof Error&&"AbortError"===e.name)A("Upload cancelled by user");else{let a=`Upload failed: ${e instanceof Error?e.message:"Unknown error"}`;F(a),t?.(a)}x&&await Y(x,"failed")}finally{j(!1),z(null),O({completed:0,total:0,speed:0}),G.current=null}},[S,U,e,t]),en=async e=>{if(!x)return;A("Archiving files on USB drive...");let a={created:0,moved:0,deleted:0};try{let t,s,r=new Date,l=`${r.getFullYear()}-${String(r.getMonth()+1).padStart(2,"0")}-${String(r.getDate()).padStart(2,"0")}`;try{t=await x.getDirectoryHandle("ARCHIVE")}catch{t=await x.getDirectoryHandle("ARCHIVE",{create:!0}),a.created++}try{s=await t.getDirectoryHandle(l)}catch{s=await t.getDirectoryHandle(l,{create:!0}),a.created++}for(let t of e)try{A(`Archiving ${t.name}...`);let e=await t.handle.getFile(),r=e.size,n=await e.arrayBuffer(),i=await s.getFileHandle(t.name,{create:!0}),d=await i.createWritable();await d.write(n),await d.close();let o=await i.getFile(),c=o.size;if(c!==r)throw Error(`Size mismatch: original ${r} bytes, archived ${c} bytes`);let u=await o.arrayBuffer(),m=new Uint8Array(n),g=new Uint8Array(u),p=Math.min(1024,r);for(let e=0;e<p;e++)if(m[e]!==g[e])throw Error(`Content mismatch at byte ${e}`);if(r>1024){let e=r-1024;for(let t=0;t<1024;t++)if(m[e+t]!==g[e+t])throw Error(`Content mismatch at byte ${e+t}`)}Z({name:t.name,size:r,action:"archived",archivePath:`ARCHIVE/${l}/${t.name}`});let h=t.folder.split("/"),b=x;for(let e of h)try{b=await b.getDirectoryHandle(e)}catch(t){throw console.warn(`   ⚠️ Could not navigate to directory ${e} for deletion`),t}let f=b;"function"==typeof f.removeEntry?(await f.removeEntry(t.name),a.deleted++):console.warn("removeEntry is not supported in this browser. File deletion skipped:",t.name)}catch(e){console.error(`Error archiving file ${t.name}:`,e)}T(e=>({...e,created:e.created+a.created,moved:e.moved+a.moved,deleted:e.deleted+a.deleted}))}catch(e){console.error("❌ Error archiving files:",e),F("Failed to archive files"),t?.(e instanceof Error?e.message:"Unknown error")}},ei=e=>{if(0===e)return"0 B";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB"][t]},ed=(()=>{let e=new Date;e.setDate(e.getDate()-3);let t=[];if(_?.syncHistory){let a=_.syncHistory.filter(t=>new Date(t.timestamp)>e).slice(-10);t.push(...a.map(e=>({...e,deviceName:_.deviceName||"Unknown Device",deviceId:_.deviceId})))}return t.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime())})();return(0,s.jsxs)("div",{className:`p-6 rounded-lg border ${a?"border-gray-700 bg-gray-800":"border-gray-200 bg-white"}`,children:[C&&!b&&(0,s.jsx)("div",{className:`mb-4 p-4 rounded-lg border ${a?"border-green-700 bg-green-900/20":"border-green-200 bg-green-50"}`,children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(l.A,{className:"w-5 h-5 text-green-600 mr-2"}),(0,s.jsx)("p",{className:`text-sm font-medium ${a?"text-green-200":"text-green-800"}`,children:C})]})}),ed.length>0&&(0,s.jsxs)("div",{className:`mb-6 border rounded-lg ${a?"border-gray-600":"border-gray-200"}`,children:[(0,s.jsx)("div",{className:`p-3 border-b ${a?"border-gray-600 bg-gray-700":"border-gray-200 bg-gray-50"}`,children:(0,s.jsx)("h3",{className:`font-medium ${a?"text-white":"text-gray-900"}`,children:"Recent Uploads (Last 3 Days)"})}),(0,s.jsx)("div",{className:"max-h-40 overflow-y-auto",children:ed.map((e,t)=>(0,s.jsx)("div",{className:`p-3 border-b last:border-b-0 ${a?"border-gray-600":"border-gray-200"}`,children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:`font-medium text-sm ${a?"text-white":"text-gray-900"}`,children:[e.deviceName," (",e.deviceId,")"]}),(0,s.jsxs)("p",{className:`text-xs ${a?"text-gray-400":"text-gray-600"}`,children:[new Date(e.timestamp).toLocaleString(),e.duration&&` • ${e.duration}s`]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:`text-sm ${"success"===e.status?"text-green-600":"partial"===e.status?"text-yellow-600":"text-red-600"}`,children:[e.summary.uploaded," uploaded, ",e.summary.skipped," skipped, ",e.summary.archived," archived"]}),(0,s.jsxs)("p",{className:`text-xs ${a?"text-gray-400":"text-gray-600"}`,children:[e.summary.totalFiles," total files"]})]})]})},t))})]}),!Q()&&(0,s.jsxs)("div",{className:`mb-4 p-4 rounded-lg ${a?"bg-red-900/20 border-red-700":"bg-red-50 border-red-200"} border`,children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(n.A,{className:"w-5 h-5 text-red-600 mr-2"}),(0,s.jsx)("h3",{className:`font-semibold ${a?"text-red-200":"text-red-800"}`,children:"Browser Not Supported"})]}),(0,s.jsx)("p",{className:`mt-2 text-sm ${a?"text-red-300":"text-red-700"}`,children:"WebUSB requires Chrome 86+ or Edge 86+ with HTTPS or localhost. Please use a supported browser."})]}),b?(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(l.A,{className:"w-5 h-5 text-green-600 mr-2"}),(0,s.jsx)("h3",{className:`font-semibold ${a?"text-white":"text-gray-900"}`,children:"USB Drive Connected"})]}),(0,s.jsx)("button",{onClick:()=>{f(!1),h(null),N([]),E(new Set),F(""),A("")},className:`px-3 py-1 text-sm rounded border ${a?"border-gray-600 text-gray-300 hover:bg-gray-700":"border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:"Disconnect"})]}),_&&(0,s.jsx)("div",{className:`mb-4 p-3 rounded border ${a?"border-gray-600 bg-gray-700":"border-gray-200 bg-gray-50"}`,children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:`text-sm font-medium ${a?"text-white":"text-gray-900"}`,children:["Device: ",_.deviceName||"Unknown"]}),(0,s.jsxs)("p",{className:`text-xs ${a?"text-gray-400":"text-gray-600"}`,children:["ID: ",_.deviceId]})]}),_.totalUploads&&(0,s.jsx)("div",{className:"text-right",children:(0,s.jsxs)("p",{className:`text-sm ${a?"text-gray-300":"text-gray-700"}`,children:[_.totalUploads," uploads"]})})]})}),C&&(0,s.jsx)("div",{className:`mb-4 p-3 rounded border ${C.includes("COMPLETE")||C.includes("✅")?a?"border-green-700 bg-green-900/20":"border-green-200 bg-green-50":a?"border-blue-700 bg-blue-900/20":"border-blue-200 bg-blue-50"}`,children:(0,s.jsxs)("div",{className:"flex items-center",children:[C.includes("COMPLETE")||C.includes("✅")?(0,s.jsx)(l.A,{className:"w-4 h-4 text-green-600 mr-2"}):(0,s.jsx)(d.A,{className:"w-4 h-4 text-blue-600 mr-2"}),(0,s.jsx)("p",{className:`text-sm font-medium ${C.includes("COMPLETE")||C.includes("✅")?a?"text-green-200":"text-green-800":a?"text-blue-200":"text-blue-800"}`,children:C})]})}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-2 mb-4",children:[(0,s.jsxs)("button",{onClick:()=>ee(),disabled:y,className:`px-4 py-2 rounded-lg font-medium flex items-center space-x-2 ${y?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:[(0,s.jsx)(o.A,{className:`w-4 h-4 ${y?"animate-spin":""}`}),(0,s.jsx)("span",{children:y?"Scanning...":"Scan Files"})]}),S.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("button",{onClick:es,className:`px-3 py-2 text-sm rounded border ${a?"border-gray-600 text-gray-300 hover:bg-gray-700":"border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:["Select All (",S.length,")"]}),(0,s.jsx)("button",{onClick:er,className:`px-3 py-2 text-sm rounded border ${a?"border-gray-600 text-gray-300 hover:bg-gray-700":"border-gray-300 text-gray-700 hover:bg-gray-50"}`,children:"Clear Selection"})]})]}),D&&(0,s.jsx)("div",{className:`mb-4 p-3 rounded border ${a?"border-red-700 bg-red-900/20":"border-red-200 bg-red-50"}`,children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(n.A,{className:"w-4 h-4 text-red-600 mr-2"}),(0,s.jsx)("p",{className:`text-sm ${a?"text-red-200":"text-red-800"}`,children:D})]})}),v&&$.length>0&&(0,s.jsxs)("div",{className:`mb-4 p-4 rounded border ${a?"border-gray-600 bg-gray-700":"border-gray-200 bg-gray-50"}`,children:[(0,s.jsxs)("h4",{className:`font-medium mb-3 ${a?"text-white":"text-gray-900"}`,children:["Upload Progress (",M.completed,"/",M.total,")",M.speed>0&&` - ${(p=M.speed)<1?`${(1e3*p).toFixed(0)} KB/s`:`${p.toFixed(1)} MB/s`}`]}),(0,s.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:$.map((e,t)=>(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsx)("span",{className:`truncate ${a?"text-gray-300":"text-gray-700"}`,children:e.fileName}),(0,s.jsx)("span",{className:`text-xs ${"completed"===e.status?"text-green-600":"error"===e.status?"text-red-600":"skipped"===e.status?"text-yellow-600":a?"text-gray-400":"text-gray-600"}`,children:"completed"===e.status?"✅":"error"===e.status?"❌":"skipped"===e.status?"⏭️":"⏳"})]}),"uploading"===e.status&&(0,s.jsx)("div",{className:`w-full bg-gray-200 rounded-full h-1.5 ${a?"bg-gray-600":""}`,children:(0,s.jsx)("div",{className:"bg-blue-600 h-1.5 rounded-full transition-all duration-300",style:{width:`${e.progress}%`}})}),e.error&&(0,s.jsx)("p",{className:"text-xs text-red-600 mt-1",children:e.error})]})},t))})]}),S.length>0&&(0,s.jsxs)("div",{className:`mb-4 border rounded-lg ${a?"border-gray-600":"border-gray-200"}`,children:[(0,s.jsx)("div",{className:`p-3 border-b ${a?"border-gray-600 bg-gray-700":"border-gray-200 bg-gray-50"}`,children:(0,s.jsxs)("h4",{className:`font-medium ${a?"text-white":"text-gray-900"}`,children:["Audio Files (",S.length,") - ",U.size," selected"]})}),(0,s.jsx)("div",{className:"max-h-64 overflow-y-auto",children:S.map((e,t)=>(0,s.jsxs)("div",{className:`p-3 border-b last:border-b-0 flex items-center space-x-3 ${a?"border-gray-600":"border-gray-200"} ${U.has(e.path)?a?"bg-blue-900/20":"bg-blue-50":""}`,children:[(0,s.jsx)("input",{type:"checkbox",checked:U.has(e.path),onChange:()=>ea(e.path),className:"w-4 h-4 text-blue-600 rounded"}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:`font-medium truncate ${a?"text-white":"text-gray-900"}`,children:e.name}),(0,s.jsxs)("p",{className:`text-xs ${a?"text-gray-400":"text-gray-600"}`,children:[e.folder," • ",ei(e.size)," • ",new Date(e.lastModified).toLocaleString()]})]})]},e.path))})]}),U.size>0&&(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("button",{onClick:el,disabled:v,"data-upload-button":!0,className:`flex-1 px-6 py-3 rounded-lg font-medium flex items-center justify-center space-x-2 ${v?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-green-600 hover:bg-green-700 text-white"}`,children:[(0,s.jsx)(c.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:v?"Uploading...":`Upload ${U.size} File${1!==U.size?"s":""}`})]}),v&&G.current&&(0,s.jsx)("button",{onClick:()=>G.current?.abort(),className:"px-4 py-3 rounded-lg font-medium bg-red-600 hover:bg-red-700 text-white",children:"Cancel"})]}),(R.created>0||R.moved>0||R.deleted>0)&&(0,s.jsx)("div",{className:`mt-4 p-3 rounded border ${a?"border-green-700 bg-green-900/20":"border-green-200 bg-green-50"}`,children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.A,{className:"w-4 h-4 text-green-600 mr-2"}),(0,s.jsxs)("p",{className:`text-sm ${a?"text-green-200":"text-green-800"}`,children:["Archive: ",R.created," folders created, ",R.moved," files moved, ",R.deleted," files deleted"]})]})})]}):(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsx)(i.A,{className:`w-16 h-16 mx-auto ${a?"text-gray-400":"text-gray-500"}`})}),(0,s.jsxs)("button",{onClick:X,disabled:!Q(),className:`px-6 py-3 rounded-lg font-medium flex items-center mx-auto space-x-2 ${!Q()?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:[(0,s.jsx)(i.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Connect USB Drive"})]}),(0,s.jsx)("p",{className:`text-xs mt-3 ${a?"text-gray-500":"text-gray-600"}`,children:'File picker will open to "This PC" - navigate to your USB recorder drive (look for D:, E:, F:, etc.)'})]})]})}}};