"use client";

import React, { useState, useEffect } from 'react';
import { 
  Copy, 
  Check, 
  FileText, 
  User, 
  Calendar, 
  MapPin, 
  Stethoscope,
  AlertCircle,
  RefreshCw,
  CheckCircle,
  Clock,
  Eye,
  EyeOff
} from 'lucide-react';

interface CompletedNote {
  appointmentId: string;
  patientName: string;
  provider: string;
  operatory: string;
  appointmentDate: string;
  appointmentType: string;
  existingNote?: string;
  transcription?: string;
  professionalizedNote: string;
  procedures?: string[];
  toothNumbers?: string[];
}

interface DayCompletionStatus {
  date: string;
  isComplete: boolean;
  totalAppointments: number;
  matchedRecordings: number;
  transcribedRecordings: number;
  readyForCopyPaste: boolean;
  completedNotes: CompletedNote[];
  missingItems: string[];
}

interface CopyPasteModeProps {
  selectedDate: string;
  isDarkMode?: boolean;
  onClose?: () => void;
}

export function CopyPasteMode({ selectedDate, isDarkMode = false, onClose }: CopyPasteModeProps) {
  const [status, setStatus] = useState<DayCompletionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copiedNotes, setCopiedNotes] = useState<Set<string>>(new Set());
  const [expandedNotes, setExpandedNotes] = useState<Set<string>>(new Set());
  const [showOriginals, setShowOriginals] = useState<Set<string>>(new Set());

  useEffect(() => {
    checkCopyPasteStatus();
  }, [selectedDate]);

  const checkCopyPasteStatus = async (force = false) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/voice/copy-paste-mode?date=${selectedDate}&force=${force}`);
      if (!response.ok) {
        throw new Error('Failed to check copy-paste status');
      }
      
      const data = await response.json();
      setStatus(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (note: CompletedNote) => {
    try {
      await navigator.clipboard.writeText(note.professionalizedNote);
      setCopiedNotes(prev => new Set([...prev, note.appointmentId]));
      
      // Clear the copied state after 3 seconds
      setTimeout(() => {
        setCopiedNotes(prev => {
          const newSet = new Set(prev);
          newSet.delete(note.appointmentId);
          return newSet;
        });
      }, 3000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const toggleExpanded = (appointmentId: string) => {
    setExpandedNotes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(appointmentId)) {
        newSet.delete(appointmentId);
      } else {
        newSet.add(appointmentId);
      }
      return newSet;
    });
  };

  const toggleShowOriginal = (appointmentId: string) => {
    setShowOriginals(prev => {
      const newSet = new Set(prev);
      if (newSet.has(appointmentId)) {
        newSet.delete(appointmentId);
      } else {
        newSet.add(appointmentId);
      }
      return newSet;
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className={`p-6 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
        <div className="flex items-center justify-center space-x-3">
          <RefreshCw className="h-5 w-5 animate-spin text-blue-600" />
          <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>
            Checking copy-paste readiness...
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-6 rounded-lg border ${isDarkMode ? 'bg-red-900/20 border-red-800' : 'bg-red-50 border-red-200'}`}>
        <div className="flex items-center space-x-3">
          <AlertCircle className="h-5 w-5 text-red-600" />
          <div>
            <h3 className="font-medium text-red-800 dark:text-red-200">Error</h3>
            <p className="text-sm text-red-600 dark:text-red-300">{error}</p>
          </div>
        </div>
        <button
          onClick={() => checkCopyPasteStatus()}
          className="mt-3 px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!status) {
    return null;
  }

  return (
    <div className={`space-y-6 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
      {/* Header */}
      <div className={`p-6 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <span>Copy-Paste Mode</span>
            </h2>
            <p className={`text-sm mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {formatDate(selectedDate)}
            </p>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className={`px-3 py-1 text-sm rounded transition-colors ${
                isDarkMode 
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Close
            </button>
          )}
        </div>

        {/* Status Summary */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{status.totalAppointments}</div>
            <div className="text-xs text-gray-500">Appointments</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{status.matchedRecordings}</div>
            <div className="text-xs text-gray-500">Matched</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{status.transcribedRecordings}</div>
            <div className="text-xs text-gray-500">Transcribed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{status.completedNotes.length}</div>
            <div className="text-xs text-gray-500">Ready to Copy</div>
          </div>
        </div>

        {/* Completion Status */}
        <div className="mt-4">
          {status.isComplete ? (
            <div className="flex items-center space-x-2 text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Day is complete and ready for copy-paste!</span>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-orange-600">
                <Clock className="h-4 w-4" />
                <span className="text-sm font-medium">Day is not yet complete</span>
              </div>
              {status.missingItems.length > 0 && (
                <div className="text-xs text-gray-500">
                  Missing: {status.missingItems.join(', ')}
                </div>
              )}
              <button
                onClick={() => checkCopyPasteStatus(true)}
                className="text-xs text-blue-600 hover:text-blue-800 underline"
              >
                Generate notes anyway
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Notes List */}
      {status.completedNotes.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Professional Clinical Notes</h3>
          
          {status.completedNotes.map((note) => {
            const isCopied = copiedNotes.has(note.appointmentId);
            const isExpanded = expandedNotes.has(note.appointmentId);
            const showOriginal = showOriginals.has(note.appointmentId);
            
            return (
              <div
                key={note.appointmentId}
                className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}
              >
                {/* Note Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <User className="h-4 w-4 text-blue-600" />
                    <div>
                      <div className="font-medium">{note.patientName}</div>
                      <div className="text-xs text-gray-500 flex items-center space-x-2">
                        <span>{note.provider}</span>
                        <span>•</span>
                        <span>{note.operatory}</span>
                        <span>•</span>
                        <span>{note.appointmentType}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {(note.existingNote || note.transcription) && (
                      <button
                        onClick={() => toggleShowOriginal(note.appointmentId)}
                        className={`p-1 rounded text-xs transition-colors ${
                          showOriginal
                            ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                            : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
                        }`}
                        title={showOriginal ? 'Hide original' : 'Show original'}
                      >
                        {showOriginal ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                      </button>
                    )}
                    
                    <button
                      onClick={() => copyToClipboard(note)}
                      className={`flex items-center space-x-1 px-3 py-1 rounded text-sm transition-colors ${
                        isCopied
                          ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                          : 'bg-blue-600 text-white hover:bg-blue-700'
                      }`}
                    >
                      {isCopied ? (
                        <>
                          <Check className="h-3 w-3" />
                          <span>Copied!</span>
                        </>
                      ) : (
                        <>
                          <Copy className="h-3 w-3" />
                          <span>Copy</span>
                        </>
                      )}
                    </button>
                  </div>
                </div>

                {/* Professional Note */}
                <div className={`p-3 rounded border-l-4 border-blue-500 ${isDarkMode ? 'bg-gray-900/50' : 'bg-blue-50'}`}>
                  <div className="text-sm whitespace-pre-wrap">
                    {isExpanded ? note.professionalizedNote : `${note.professionalizedNote.substring(0, 200)}${note.professionalizedNote.length > 200 ? '...' : ''}`}
                  </div>
                  
                  {note.professionalizedNote.length > 200 && (
                    <button
                      onClick={() => toggleExpanded(note.appointmentId)}
                      className="mt-2 text-xs text-blue-600 hover:text-blue-800 underline"
                    >
                      {isExpanded ? 'Show less' : 'Show more'}
                    </button>
                  )}
                </div>

                {/* Original Sources (when toggled) */}
                {showOriginal && (
                  <div className="mt-3 space-y-2">
                    {note.existingNote && (
                      <div>
                        <div className="text-xs font-medium text-gray-500 mb-1">Existing Clinical Note:</div>
                        <div className={`p-2 text-xs rounded ${isDarkMode ? 'bg-gray-900/30' : 'bg-gray-100'}`}>
                          {note.existingNote}
                        </div>
                      </div>
                    )}
                    
                    {note.transcription && (
                      <div>
                        <div className="text-xs font-medium text-gray-500 mb-1">Voice Transcription:</div>
                        <div className={`p-2 text-xs rounded ${isDarkMode ? 'bg-gray-900/30' : 'bg-gray-100'}`}>
                          {note.transcription}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}

      {/* No Notes Available */}
      {status.completedNotes.length === 0 && status.readyForCopyPaste && (
        <div className={`p-6 text-center rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'}`}>
          <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500">No completed notes available for this date.</p>
          <p className="text-xs text-gray-400 mt-1">
            Make sure recordings are matched and transcribed.
          </p>
        </div>
      )}
    </div>
  );
}
