'use client';

import React from 'react';
import { Usb } from 'lucide-react';
import { PageHeader } from '../../src/components/ui/page-header';
import BrowserCompatibility from '../../src/components/usb/browser-compatibility';
import { WebUSBInterface } from '../../src/components/usb/WebUSBInterface';

export default function WebUSBTransferPage() {
  return (
    <div className="min-h-screen bg-gray-50 text-gray-900">
      <PageHeader
        title="Dentalapp"
        isHomePage={false}
      />

      <main className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Usb className="w-12 h-12 text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900">
              WebUSB File Transfer
            </h1>
          </div>
          <p className="text-xl text-gray-600">
            Direct browser-based USB recorder file transfer with automatic archiving
          </p>
        </div>

        {/* Browser Compatibility Section */}
        <div className="mb-8">
          <BrowserCompatibility />
        </div>

        {/* Main WebUSB Component */}
        <div className="mb-8">
          <WebUSBInterface />
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-600">
          <p>
            WebUSB File Transfer requires Chrome 120+ or Edge 120+ for full functionality.
          </p>
        </div>
      </main>
    </div>
  );
}
