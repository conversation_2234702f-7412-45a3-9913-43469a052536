import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

export async function GET() {
  try {
    const apiKey = process.env.OPENAI_API_KEY;
    
    if (!apiKey) {
      return NextResponse.json({
        configured: false,
        error: 'OpenAI API key not found in environment variables',
        suggestion: 'Set OPENAI_API_KEY environment variable'
      });
    }

    if (apiKey === 'your_openai_api_key_here' || apiKey.trim() === '') {
      return NextResponse.json({
        configured: false,
        error: 'OpenAI API key is placeholder or empty',
        suggestion: 'Replace with actual OpenAI API key'
      });
    }

    // Test the API key with a minimal request
    try {
      const openai = new OpenAI({ apiKey });
      const response = await openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [{ role: 'user', content: 'Test' }],
        max_tokens: 5
      });

      return NextResponse.json({
        configured: true,
        status: 'API key is valid and working',
        keyPrefix: apiKey.substring(0, 7) + '...',
        testResponse: response.choices[0]?.message?.content || 'Success'
      });
    } catch (apiError: any) {
      return NextResponse.json({
        configured: false,
        error: 'API key is invalid or quota exceeded',
        details: apiError.message,
        keyPrefix: apiKey.substring(0, 7) + '...'
      }, { status: 401 });
    }

  } catch (error: any) {
    return NextResponse.json({
      configured: false,
      error: 'Failed to check OpenAI configuration',
      details: error.message
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { apiKey } = await request.json();

    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key is required' },
        { status: 400 }
      );
    }

    // Test the provided API key
    try {
      const openai = new OpenAI({ apiKey });
      const response = await openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [{ role: 'user', content: 'Test' }],
        max_tokens: 5
      });

      return NextResponse.json({
        valid: true,
        message: 'API key is valid',
        testResponse: response.choices[0]?.message?.content || 'Success',
        note: 'API key validated but not saved. Set OPENAI_API_KEY environment variable to persist.'
      });
    } catch (apiError: any) {
      return NextResponse.json({
        valid: false,
        error: 'Invalid API key or quota exceeded',
        details: apiError.message
      }, { status: 401 });
    }

  } catch (error: any) {
    return NextResponse.json({
      error: 'Failed to validate API key',
      details: error.message
    }, { status: 500 });
  }
}
