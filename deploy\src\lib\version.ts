// Version information for the application

// Safe date formatting function that handles invalid dates
function getSafeDateString(): string {
  try {
    const now = new Date();
    if (isNaN(now.getTime())) {
      return '2025-01-01'; // Fallback date
    }
    return now.toISOString().split('T')[0];
  } catch (error) {
    console.warn('Error formatting date:', error);
    return '2025-01-01'; // Fallback date
  }
}

// Safe time formatting function that handles invalid dates
function getSafeTimeString(): string {
  try {
    const now = new Date();
    if (isNaN(now.getTime())) {
      return '1200'; // Fallback time
    }
    return now.toTimeString().slice(0, 5).replace(':', '');
  } catch (error) {
    console.warn('Error formatting time:', error);
    return '1200'; // Fallback time
  }
}

export const VERSION_INFO = {
  version: '2.1.0-SIMPLE-TRANSCRIPTION',
  buildDate: process.env.NEXT_PUBLIC_BUILD_DATE || getSafeDateString(),
  buildTime: process.env.NEXT_PUBLIC_BUILD_TIME || getSafeTimeString(),
  buildNumber: process.env.NEXT_PUBLIC_BUILD_NUMBER || 'local',
  commitHash: process.env.NEXT_PUBLIC_COMMIT_HASH || 'dev'
};
