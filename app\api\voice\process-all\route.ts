import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { limit = 10 } = await request.json().catch(() => ({}));
    
    console.log(`🚀 Starting process all with limit: ${limit}`);
    
    // Get current recordings status
    const baseUrl = request.url.split('/api')[0];
    const recordingsResponse = await fetch(`${baseUrl}/api/voice/recordings`);
    
    if (!recordingsResponse.ok) {
      throw new Error('Failed to fetch recordings');
    }
    
    const recordingsData = await recordingsResponse.json();
    const recordings = recordingsData.recordings || [];
    
    // Filter recordings that need processing
    const needsProcessing = recordings.filter(recording => {
      const hasTranscription = !!(recording.transcription && recording.transcription.trim());
      const hasSummary = !!(recording.summary && recording.summary.trim());
      return !hasTranscription || !hasSummary;
    });
    
    if (needsProcessing.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'All recordings are already fully processed',
        processed: 0,
        total: recordings.length
      });
    }
    
    // Limit the batch size
    const toProcess = needsProcessing.slice(0, limit);
    
    console.log(`📋 Found ${needsProcessing.length} recordings needing processing, processing ${toProcess.length}`);
    
    const results = {
      success: true,
      total: toProcess.length,
      processed: 0,
      failed: 0,
      details: [] as any[]
    };
    
    // Process recordings sequentially to avoid overwhelming the system
    for (const recording of toProcess) {
      try {
        console.log(`🎵 Processing: ${recording.name}`);
        
        const hasTranscription = !!(recording.transcription && recording.transcription.trim());
        const hasSummary = !!(recording.summary && recording.summary.trim());
        
        let transcriptionResult = null;
        let summaryResult = null;
        
        // Step 1: Transcribe if needed
        if (!hasTranscription) {
          console.log(`   📝 Transcribing: ${recording.name}`);
          
          const transcribeResponse = await fetch(`${baseUrl}/api/voice/transcribe`, {
            method: 'POST',
            headers: { 
              'Content-Type': 'application/json',
              'X-Internal-Call': 'true'
            },
            body: JSON.stringify({
              fileName: recording.fileName,
              recordingId: recording.id
            })
          });
          
          if (transcribeResponse.ok) {
            transcriptionResult = await transcribeResponse.json();
            console.log(`   ✅ Transcribed: ${recording.name} (${transcriptionResult.transcription?.length || 0} chars)`);
          } else {
            const error = await transcribeResponse.text();
            throw new Error(`Transcription failed: ${error}`);
          }
        } else {
          console.log(`   ⏭️ Already transcribed: ${recording.name}`);
        }
        
        // Step 2: Summarize if needed (use existing transcription or new one)
        if (!hasSummary) {
          console.log(`   📄 Summarizing: ${recording.name}`);
          
          const transcriptionText = transcriptionResult?.transcription || recording.transcription;
          
          if (transcriptionText && transcriptionText.trim()) {
            const summarizeResponse = await fetch(`${baseUrl}/api/voice/summarize`, {
              method: 'POST',
              headers: { 
                'Content-Type': 'application/json',
                'X-Internal-Call': 'true'
              },
              body: JSON.stringify({
                recordingId: recording.id,
                transcription: transcriptionText
              })
            });
            
            if (summarizeResponse.ok) {
              summaryResult = await summarizeResponse.json();
              console.log(`   ✅ Summarized: ${recording.name} (${summaryResult.summary?.length || 0} chars)`);
            } else {
              const error = await summarizeResponse.text();
              console.warn(`   ⚠️ Summary failed for ${recording.name}: ${error}`);
              // Don't fail the whole operation for summary failures
            }
          } else {
            console.warn(`   ⚠️ No transcription available to summarize for ${recording.name}`);
          }
        } else {
          console.log(`   ⏭️ Already summarized: ${recording.name}`);
        }
        
        results.processed++;
        results.details.push({
          filename: recording.name,
          status: 'success',
          transcribed: !hasTranscription ? !!transcriptionResult : true,
          summarized: !hasSummary ? !!summaryResult : true,
          transcriptionLength: transcriptionResult?.transcription?.length || recording.transcription?.length || 0,
          summaryLength: summaryResult?.summary?.length || recording.summary?.length || 0
        });
        
      } catch (error) {
        console.error(`❌ Failed processing ${recording.name}:`, error);
        results.failed++;
        results.details.push({
          filename: recording.name,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
      
      // Small delay between files
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log(`🎉 Batch processing complete: ${results.processed} processed, ${results.failed} failed`);
    
    return NextResponse.json(results);
    
  } catch (error) {
    console.error('Process all error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}