(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4227,4320],{2501:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>l});var s=t(5155),r=t(4227),n=t(6496),i=t(1362);function l(){let{resolvedTheme:e}=(0,i.D)();return(0,s.jsxs)("div",{className:"min-h-screen bg-slate-50 dark:bg-slate-900",children:[(0,s.jsx)(n.z,{title:"Dentalapp",isHomePage:!1,activeTab:"ai-assistant"}),(0,s.jsx)("main",{className:"max-w-4xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"AI Office Manager"})}),(0,s.jsx)("div",{className:"rounded-lg shadow",children:(0,s.jsx)(r.AgentChat,{isDarkMode:"dark"===e})}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"About AI Office Manager"}),(0,s.jsx)("p",{className:"mb-3",children:"The AI Office Manager helps you analyze practice data while keeping all patient information secure and private."}),(0,s.jsx)("h3",{className:"font-medium mb-2",children:"Example questions you can ask:"}),(0,s.jsxs)("ul",{className:"list-disc pl-5 space-y-1",children:[(0,s.jsx)("li",{children:"How many appointments do we have scheduled next week?"}),(0,s.jsx)("li",{children:"What's our busiest day for the current month?"}),(0,s.jsx)("li",{children:"Which operatories are most frequently used?"}),(0,s.jsx)("li",{children:"What procedures are most common in our practice?"}),(0,s.jsx)("li",{children:"How many new patients did we see last month?"})]})]})]})})]})}},4227:(e,a,t)=>{"use strict";t.r(a),t.d(a,{AgentChat:()=>l});var s=t(5155),r=t(2115),n=t(5657),i=t(2486);function l(e){let{isDarkMode:a}=e,[t,l]=(0,r.useState)([{id:"1",type:"assistant",content:"Hello! I'm your AI Office Manager. I can help you analyze practice data while keeping all patient information secure and private. What would you like to know about your practice?",timestamp:new Date}]),[o,c]=(0,r.useState)(""),[d,m]=(0,r.useState)(!1),u=(0,r.useRef)(null);(0,r.useEffect)(()=>{var e;null==(e=u.current)||e.scrollIntoView({behavior:"smooth"})},[t]);let h=async e=>{if(e.preventDefault(),!o.trim()||d)return;let a={id:Date.now().toString(),type:"user",content:o.trim(),timestamp:new Date};l(e=>[...e,a]),c(""),m(!0);try{let e={id:(Date.now()+1).toString(),type:"assistant",content:"",timestamp:new Date,isLoading:!0};l(a=>[...a,e]);let s=await fetch("/api/ai/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:a.content,history:t.slice(-10)})});if(!s.ok)throw Error("Failed to get AI response");let r=await s.json();l(e=>e.filter(e=>!e.isLoading).concat({id:(Date.now()+2).toString(),type:"assistant",content:r.response||"Sorry, I couldn't process that request.",timestamp:new Date}))}catch(e){console.error("Error in AI chat:",e),l(e=>e.filter(e=>!e.isLoading).concat({id:(Date.now()+2).toString(),type:"assistant",content:"Sorry, there was an error processing your request. Please try again later.",timestamp:new Date}))}finally{m(!1)}};return(0,s.jsxs)("div",{className:"flex flex-col h-[600px] rounded-lg border ".concat(a?"bg-gray-800 border-gray-700":"bg-white border-gray-200"),children:[(0,s.jsxs)("div",{className:"p-4 border-b flex items-center",children:[(0,s.jsx)(n.A,{className:"w-5 h-5 mr-2 text-blue-500"}),(0,s.jsx)("h2",{className:"font-semibold",children:"AI Office Manager"})]}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[t.map(e=>(0,s.jsx)("div",{className:"flex ".concat("user"===e.type?"justify-end":"justify-start"),children:(0,s.jsx)("div",{className:"max-w-[80%] rounded-lg p-3 ".concat("user"===e.type?a?"bg-blue-600 text-white":"bg-blue-100 text-gray-800":a?"bg-gray-700 text-white":"bg-gray-100 text-gray-800"," ").concat(e.isLoading?"animate-pulse":""),children:e.isLoading?(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}):(0,s.jsx)("div",{className:"whitespace-pre-wrap",children:e.content})})},e.id)),(0,s.jsx)("div",{ref:u})]}),(0,s.jsxs)("form",{onSubmit:h,className:"p-4 border-t flex",children:[(0,s.jsx)("input",{type:"text",value:o,onChange:e=>{c(e.target.value)},placeholder:"Ask about appointments, patients, or practice metrics...",className:"flex-1 p-2 rounded-l-md border-r-0 focus:outline-none ".concat(a?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"),disabled:d}),(0,s.jsx)("button",{type:"submit",disabled:d||!o.trim(),className:"px-4 py-2 rounded-r-md ".concat(d||!o.trim()?"bg-gray-300 dark:bg-gray-600 cursor-not-allowed":"bg-blue-500 hover:bg-blue-600 text-white"),children:(0,s.jsx)(i.A,{className:"w-5 h-5"})})]})]})}},6193:(e,a,t)=>{Promise.resolve().then(t.bind(t,2501))}},e=>{var a=a=>e(e.s=a);e.O(0,[8096,6496,7358],()=>a(6193)),_N_E=e.O()}]);