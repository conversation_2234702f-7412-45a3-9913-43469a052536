/**
 * Environment Variable Validation for Voice Processing System
 * 
 * This module validates all required environment variables for the dental voice
 * processing pipeline and provides detailed error messages for missing configuration.
 */

import OpenAI from 'openai';
import { BlobServiceClient } from '@azure/storage-blob';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  missingRequired: string[];
  missingOptional: string[];
  connectivity: {
    openai: boolean;
    azureStorage: boolean;
    azureSpeech: boolean;
  };
}

export interface VoiceProcessingConfig {
  openai: {
    apiKey: string | null;
    organization?: string;
    transcriptionModel: string;
    chatModel: string;
    maxTokens: number;
    temperature: number;
  };
  
  azure: {
    storageConnectionString: string | null;
    recordingsContainer: string;
    jobsContainer: string;
    speechKey?: string;
    speechRegion?: string;
    webJobsStorage: string | null;
  };
  
  sikka: {
    apiKey: string | null;
    practiceId: string | null;
    baseUrl: string;
    timeout: number;
  };
  
  jobProcessing: {
    retentionDays: number;
    maxRetryAttempts: number;
    retryDelayBase: number;
    batchSize: number;
    processingTimeout: number;
    queuePollInterval: number;
    statusUpdateInterval: number;
  };
  
  monitoring: {
    appInsightsKey?: string;
    sentryDsn?: string;
    logLevel: string;
    logFormat: string;
    logToFile: boolean;
    logFilePath: string;
  };
  
  app: {
    nodeEnv: string;
    port: number;
    host: string;
    debugMode: boolean;
    verboseLogging: boolean;
    requestTimeout: number;
    maxFileSize: number;
  };
}

/**
 * Validate OpenAI API key format
 */
function validateOpenAIKey(apiKey: string): boolean {
  return apiKey.startsWith('sk-') && apiKey.length > 20;
}

/**
 * Validate Azure Storage connection string format
 */
function validateAzureConnectionString(connectionString: string): boolean {
  return connectionString.includes('AccountName=') && 
         connectionString.includes('AccountKey=') &&
         connectionString.includes('DefaultEndpointsProtocol=');
}

/**
 * Test OpenAI API connectivity
 */
async function testOpenAIConnectivity(apiKey: string): Promise<boolean> {
  try {
    const openai = new OpenAI({ apiKey });
    await openai.models.list();
    return true;
  } catch (error) {
    console.warn('OpenAI connectivity test failed:', error instanceof Error ? error.message : 'Unknown error');
    return false;
  }
}

/**
 * Test Azure Storage connectivity
 */
async function testAzureStorageConnectivity(connectionString: string): Promise<boolean> {
  try {
    const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
    await blobServiceClient.getProperties();
    return true;
  } catch (error) {
    console.warn('Azure Storage connectivity test failed:', error instanceof Error ? error.message : 'Unknown error');
    return false;
  }
}

/**
 * Comprehensive environment validation for voice processing
 */
export async function validateVoiceProcessingEnvironment(
  env: NodeJS.ProcessEnv = process.env,
  testConnectivity: boolean = false
): Promise<ValidationResult> {
  const errors: string[] = [];
  const warnings: string[] = [];
  const missingRequired: string[] = [];
  const missingOptional: string[] = [];
  const connectivity = {
    openai: false,
    azureStorage: false,
    azureSpeech: false
  };

  // Helper function to check required variables
  const checkRequired = (key: string, description: string, validator?: (value: string) => boolean): string | null => {
    const value = env[key];
    if (!value || value.trim() === '' || value.includes('your_') || value.includes('_here')) {
      missingRequired.push(key);
      errors.push(`${key} is required for ${description}`);
      return null;
    }
    
    const trimmedValue = value.trim();
    if (validator && !validator(trimmedValue)) {
      errors.push(`${key} has invalid format for ${description}`);
      return null;
    }
    
    return trimmedValue;
  };

  // Helper function to check optional variables
  const checkOptional = (key: string, description: string, validator?: (value: string) => boolean): string | undefined => {
    const value = env[key];
    if (!value || value.trim() === '' || value.includes('your_') || value.includes('_here')) {
      missingOptional.push(key);
      warnings.push(`${key} is recommended for ${description}`);
      return undefined;
    }
    
    const trimmedValue = value.trim();
    if (validator && !validator(trimmedValue)) {
      warnings.push(`${key} has invalid format for ${description}`);
      return undefined;
    }
    
    return trimmedValue;
  };

  // Validate OpenAI configuration
  const openaiApiKey = checkRequired(
    'OPENAI_API_KEY',
    'voice transcription and AI summarization',
    validateOpenAIKey
  );
  
  const openaiOrg = checkOptional('OPENAI_ORGANIZATION', 'OpenAI billing organization');
  
  // Validate OpenAI model configuration
  const transcriptionModel = env.OPENAI_TRANSCRIPTION_MODEL || 'whisper-1';
  const chatModel = env.OPENAI_CHAT_MODEL || 'gpt-4';
  const maxTokens = parseInt(env.OPENAI_MAX_TOKENS || '4000');
  const temperature = parseFloat(env.OPENAI_TEMPERATURE || '0.3');
  
  if (maxTokens < 100 || maxTokens > 32000) {
    warnings.push('OPENAI_MAX_TOKENS should be between 100 and 32000');
  }
  
  if (temperature < 0 || temperature > 2) {
    warnings.push('OPENAI_TEMPERATURE should be between 0 and 2');
  }

  // Validate Azure Storage configuration
  const azureStorageConnectionString = env.NODE_ENV === 'production'
    ? checkRequired(
        'AZURE_STORAGE_CONNECTION_STRING',
        'production voice recording storage and job persistence',
        validateAzureConnectionString
      )
    : checkOptional(
        'AZURE_STORAGE_CONNECTION_STRING',
        'voice recording storage and job persistence',
        validateAzureConnectionString
      );

  const recordingsContainer = env.AZURE_STORAGE_CONTAINER_NAME || 'dentalrecordings';
  const jobsContainer = env.AZURE_JOBS_CONTAINER_NAME || 'processingjobs';
  
  // Validate Azure Functions configuration
  const webJobsStorage = env.NODE_ENV === 'production'
    ? checkRequired(
        'AzureWebJobsStorage',
        'Azure Functions background processing',
        validateAzureConnectionString
      )
    : checkOptional(
        'AzureWebJobsStorage',
        'Azure Functions background processing',
        validateAzureConnectionString
      );

  // Validate Azure Speech Service (optional)
  const azureSpeechKey = checkOptional('AZURE_SPEECH_KEY', 'backup transcription service');
  const azureSpeechRegion = checkOptional('AZURE_SPEECH_REGION', 'Azure Speech Service region');
  
  if (azureSpeechKey && !azureSpeechRegion) {
    warnings.push('AZURE_SPEECH_REGION is required when AZURE_SPEECH_KEY is provided');
  }

  // Validate Sikka API configuration
  const sikkaApiKey = checkRequired('SIKKA_API_KEY', 'dental practice management integration');
  const sikkaPracticeId = checkRequired('SIKKA_PRACTICE_ID', 'practice-specific data access');
  const sikkaBaseUrl = env.SIKKA_API_BASE_URL || 'https://api.sikka.com/v1';
  const sikkaTimeout = parseInt(env.SIKKA_API_TIMEOUT || '30000');

  if (sikkaTimeout < 5000 || sikkaTimeout > 300000) {
    warnings.push('SIKKA_API_TIMEOUT should be between 5000 and 300000 milliseconds');
  }

  // Validate job processing configuration
  const retentionDays = parseInt(env.JOB_RETENTION_DAYS || '30');
  const maxRetryAttempts = parseInt(env.MAX_RETRY_ATTEMPTS || '3');
  const retryDelayBase = parseInt(env.RETRY_DELAY_BASE_MS || '1000');
  const batchSize = parseInt(env.BATCH_SIZE || '10');
  const processingTimeout = parseInt(env.PROCESSING_TIMEOUT_MS || '300000');
  const queuePollInterval = parseInt(env.QUEUE_POLL_INTERVAL_MS || '5000');
  const statusUpdateInterval = parseInt(env.STATUS_UPDATE_INTERVAL_MS || '2000');

  // Validate job processing ranges
  if (retentionDays < 1 || retentionDays > 365) {
    warnings.push('JOB_RETENTION_DAYS should be between 1 and 365');
  }
  
  if (maxRetryAttempts < 1 || maxRetryAttempts > 10) {
    warnings.push('MAX_RETRY_ATTEMPTS should be between 1 and 10');
  }
  
  if (batchSize < 1 || batchSize > 100) {
    warnings.push('BATCH_SIZE should be between 1 and 100');
  }

  // Validate monitoring configuration
  const appInsightsKey = checkOptional('APPINSIGHTS_INSTRUMENTATIONKEY', 'application monitoring');
  const sentryDsn = checkOptional('SENTRY_DSN', 'error tracking');
  
  const logLevel = env.LOG_LEVEL || 'info';
  const validLogLevels = ['error', 'warn', 'info', 'debug'];
  if (!validLogLevels.includes(logLevel)) {
    warnings.push(`LOG_LEVEL should be one of: ${validLogLevels.join(', ')}`);
  }

  // Validate application configuration
  const port = parseInt(env.PORT || '3000');
  if (port < 1 || port > 65535) {
    errors.push('PORT must be between 1 and 65535');
  }

  const maxFileSize = parseInt(env.MAX_FILE_SIZE_MB || '100');
  if (maxFileSize < 1 || maxFileSize > 1000) {
    warnings.push('MAX_FILE_SIZE_MB should be between 1 and 1000');
  }

  // Environment-specific validations
  if (env.NODE_ENV === 'production') {
    if (!azureStorageConnectionString) {
      errors.push('Azure Storage is required for production voice processing');
    }
    
    if (!webJobsStorage) {
      errors.push('Azure Functions configuration is required for production background processing');
    }
    
    if (!openaiApiKey) {
      errors.push('OpenAI API key is required for production voice processing');
    }
    
    if (!sikkaApiKey || !sikkaPracticeId) {
      errors.push('Sikka API credentials are required for production practice integration');
    }
    
    if (!appInsightsKey && !sentryDsn) {
      warnings.push('Application monitoring (Application Insights or Sentry) is recommended for production');
    }
  }

  // Development-specific warnings
  if (env.NODE_ENV === 'development') {
    if (!openaiApiKey) {
      warnings.push('OpenAI API key is recommended for testing voice processing features');
    }
    
    if (!azureStorageConnectionString) {
      warnings.push('Azure Storage is recommended for testing cloud storage features');
    }
  }

  // Test connectivity if requested and credentials are available
  if (testConnectivity) {
    if (openaiApiKey) {
      connectivity.openai = await testOpenAIConnectivity(openaiApiKey);
      if (!connectivity.openai) {
        warnings.push('OpenAI API connectivity test failed - check your API key and network connection');
      }
    }
    
    if (azureStorageConnectionString) {
      connectivity.azureStorage = await testAzureStorageConnectivity(azureStorageConnectionString);
      if (!connectivity.azureStorage) {
        warnings.push('Azure Storage connectivity test failed - check your connection string and network access');
      }
    }
    
    // Azure Speech connectivity test would go here if needed
    connectivity.azureSpeech = !!azureSpeechKey && !!azureSpeechRegion;
  }

  const isValid = errors.length === 0;

  return {
    isValid,
    errors,
    warnings,
    missingRequired,
    missingOptional,
    connectivity
  };
}

/**
 * Get parsed voice processing configuration
 */
export function getVoiceProcessingConfig(env: NodeJS.ProcessEnv = process.env): VoiceProcessingConfig {
  return {
    openai: {
      apiKey: env.OPENAI_API_KEY || null,
      organization: env.OPENAI_ORGANIZATION,
      transcriptionModel: env.OPENAI_TRANSCRIPTION_MODEL || 'whisper-1',
      chatModel: env.OPENAI_CHAT_MODEL || 'gpt-4',
      maxTokens: parseInt(env.OPENAI_MAX_TOKENS || '4000'),
      temperature: parseFloat(env.OPENAI_TEMPERATURE || '0.3'),
    },
    azure: {
      storageConnectionString: env.AZURE_STORAGE_CONNECTION_STRING || null,
      recordingsContainer: env.AZURE_STORAGE_CONTAINER_NAME || 'dentalrecordings',
      jobsContainer: env.AZURE_JOBS_CONTAINER_NAME || 'processingjobs',
      speechKey: env.AZURE_SPEECH_KEY,
      speechRegion: env.AZURE_SPEECH_REGION,
      webJobsStorage: env.AzureWebJobsStorage || null,
    },
    sikka: {
      apiKey: env.SIKKA_API_KEY || null,
      practiceId: env.SIKKA_PRACTICE_ID || null,
      baseUrl: env.SIKKA_API_BASE_URL || 'https://api.sikka.com/v1',
      timeout: parseInt(env.SIKKA_API_TIMEOUT || '30000'),
    },
    jobProcessing: {
      retentionDays: parseInt(env.JOB_RETENTION_DAYS || '30'),
      maxRetryAttempts: parseInt(env.MAX_RETRY_ATTEMPTS || '3'),
      retryDelayBase: parseInt(env.RETRY_DELAY_BASE_MS || '1000'),
      batchSize: parseInt(env.BATCH_SIZE || '10'),
      processingTimeout: parseInt(env.PROCESSING_TIMEOUT_MS || '300000'),
      queuePollInterval: parseInt(env.QUEUE_POLL_INTERVAL_MS || '5000'),
      statusUpdateInterval: parseInt(env.STATUS_UPDATE_INTERVAL_MS || '2000'),
    },
    monitoring: {
      appInsightsKey: env.APPINSIGHTS_INSTRUMENTATIONKEY,
      sentryDsn: env.SENTRY_DSN,
      logLevel: env.LOG_LEVEL || 'info',
      logFormat: env.LOG_FORMAT || 'json',
      logToFile: env.LOG_TO_FILE === 'true',
      logFilePath: env.LOG_FILE_PATH || './logs/voice-processing.log',
    },
    app: {
      nodeEnv: env.NODE_ENV || 'development',
      port: parseInt(env.PORT || '3000'),
      host: env.HOST || '0.0.0.0',
      debugMode: env.DEBUG_MODE === 'true',
      verboseLogging: env.VERBOSE_LOGGING === 'true',
      requestTimeout: parseInt(env.REQUEST_TIMEOUT_MS || '30000'),
      maxFileSize: parseInt(env.MAX_FILE_SIZE_MB || '100'),
    },
  };
}

/**
 * Log validation results with structured output
 */
export function logValidationResults(result: ValidationResult): void {
  if (result.isValid) {
    console.log('✅ Voice processing environment validation passed');
  } else {
    console.error('❌ Voice processing environment validation failed');
    result.errors.forEach(error => console.error(`  ERROR: ${error}`));
  }

  if (result.warnings.length > 0) {
    console.warn('⚠️  Environment warnings:');
    result.warnings.forEach(warning => console.warn(`  WARNING: ${warning}`));
  }

  if (result.missingRequired.length > 0) {
    console.error('📋 Missing required environment variables:');
    result.missingRequired.forEach(key => console.error(`  - ${key}`));
  }

  if (result.missingOptional.length > 0) {
    console.info('💡 Missing optional environment variables:');
    result.missingOptional.forEach(key => console.info(`  - ${key}`));
  }

  // Log connectivity results
  console.info('🔗 Service connectivity status:');
  console.info(`  OpenAI: ${result.connectivity.openai ? '✅' : '❌'}`);
  console.info(`  Azure Storage: ${result.connectivity.azureStorage ? '✅' : '❌'}`);
  console.info(`  Azure Speech: ${result.connectivity.azureSpeech ? '✅' : '❌'}`);
}

/**
 * Validate environment and exit if critical errors (for production)
 */
export async function validateEnvironmentOrExit(testConnectivity: boolean = false): Promise<VoiceProcessingConfig> {
  const result = await validateVoiceProcessingEnvironment(process.env, testConnectivity);
  logValidationResults(result);

  if (!result.isValid) {
    console.error('\n🔧 Voice processing configuration errors detected:');
    console.error('   1. Copy .env.sample to .env.local');
    console.error('   2. Update the placeholder values with actual credentials');
    console.error('   3. Ensure Azure Storage and OpenAI API keys are valid');
    console.error('   4. Run the validate-environment script to verify configuration');
    console.error('   5. Restart the application\n');
    
    if (process.env.NODE_ENV === 'production') {
      console.error('💥 Exiting due to critical configuration errors in production mode');
      process.exit(1);
    } else {
      console.warn('⚠️  Continuing in development mode with incomplete configuration...\n');
      console.warn('🚨 Voice processing features may not work properly without proper configuration\n');
    }
  }

  return getVoiceProcessingConfig();
}