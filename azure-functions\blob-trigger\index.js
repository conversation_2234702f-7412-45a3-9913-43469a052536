const { BlobServiceClient } = require("@azure/storage-blob");

module.exports = async function (context, myBlob) {
    const blobName = context.bindingData.name;
    
    // Only process audio files
    if (!isAudioFile(blobName)) {
        context.log(`⏭️ Skipping non-audio file: ${blobName}`);
        return;
    }

    context.log(`🎵 NEW AUDIO FILE DETECTED: ${blobName}`);
    context.log(`📊 File size: ${myBlob.length} bytes`);

    try {
        // Check if file is already processed
        const baseName = blobName.replace(/\.[^/.]+$/, '');
        const transcriptionJsonPath = `${baseName}.json`;
        const failureJsonPath = `${baseName}.failure.json`;
        
        const blobServiceClient = BlobServiceClient.fromConnectionString(process.env.AZURE_STORAGE_CONNECTION_STRING);
        const containerClient = blobServiceClient.getContainerClient('recordings');
        
        // Check if already processed
        const transcriptionExists = await checkFileExists(containerClient, transcriptionJsonPath);
        const failureExists = await checkFileExists(containerClient, failureJsonPath);
        
        if (transcriptionExists) {
            context.log(`✅ File already processed: ${blobName}`);
            return;
        }
        
        if (failureExists) {
            context.log(`❌ File previously failed (permanent): ${blobName}`);
            return;
        }

        // Basic validation
        if (myBlob.length < 1024) {
            const failureData = {
                fileName: blobName,
                error: `File too small (${myBlob.length} bytes) - likely corrupted or empty`,
                failureType: 'validation_failed',
                failedAt: new Date().toISOString(),
                isPermanentFailure: true,
                processedBy: 'azure-function-blob-trigger'
            };
            
            await uploadJsonToBlob(containerClient, failureJsonPath, failureData);
            context.log(`💾 Permanent failure saved: ${failureJsonPath}`);
            return;
        }

        // Trigger transcription via API
        const transcriptionUrl = process.env.TRANSCRIPTION_API_URL;
        const response = await fetch(transcriptionUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Internal-Call': 'true'
            },
            body: JSON.stringify({
                fileName: blobName,
                forceRetranscribe: false
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            context.log(`❌ Transcription API failed: ${response.status} ${errorText}`);
            
            // Only mark as permanent failure for validation errors
            if (response.status === 400 && errorText.includes('validation')) {
                const failureData = {
                    fileName: blobName,
                    error: errorText,
                    failureType: 'validation_failed',
                    failedAt: new Date().toISOString(),
                    isPermanentFailure: true,
                    processedBy: 'azure-function-blob-trigger'
                };
                
                await uploadJsonToBlob(containerClient, failureJsonPath, failureData);
                context.log(`💾 Permanent failure saved: ${failureJsonPath}`);
            } else {
                // Let Azure Functions retry for other errors
                throw new Error(`Transcription API failed: ${response.status} ${errorText}`);
            }
            return;
        }

        const result = await response.json();
        context.log(`✅ Transcription completed for: ${blobName}`);

        // Generate summary if transcription was successful
        if (result.transcription && result.transcription.length > 50) {
            const summaryUrl = process.env.SUMMARY_API_URL;
            
            try {
                const summaryResponse = await fetch(summaryUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Internal-Call': 'true'
                    },
                    body: JSON.stringify({
                        fileName: blobName,
                        transcription: result.transcription
                    })
                });

                if (summaryResponse.ok) {
                    context.log(`✅ Summarization completed for: ${blobName}`);
                } else {
                    context.log(`⚠️ Summarization failed for ${blobName}: ${summaryResponse.status}`);
                }
            } catch (summaryError) {
                context.log(`⚠️ Failed to generate summary for ${blobName}:`, summaryError.message);
            }
        }

    } catch (error) {
        context.log(`❌ Error processing ${blobName}:`, error.message);
        throw error; // Let Azure Functions handle the retry
    }
};

function isAudioFile(fileName) {
    const audioExtensions = ['.mp3', '.wav', '.m4a', '.mp4', '.webm', '.ogg', '.aac', '.flac'];
    const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return audioExtensions.includes(ext);
}

async function checkFileExists(containerClient, fileName) {
    try {
        const blobClient = containerClient.getBlobClient(fileName);
        return await blobClient.exists();
    } catch {
        return false;
    }
}

async function uploadJsonToBlob(containerClient, fileName, data) {
    const blobClient = containerClient.getBlockBlobClient(fileName);
    const jsonContent = JSON.stringify(data, null, 2);
    await blobClient.upload(jsonContent, jsonContent.length, {
        blobHTTPHeaders: { blobContentType: 'application/json' }
    });
}