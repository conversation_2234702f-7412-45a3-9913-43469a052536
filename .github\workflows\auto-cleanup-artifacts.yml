name: Auto Cleanup Artifacts

on:
  schedule:
    # Run once daily at 2 AM UTC to check storage
    - cron: '0 2 * * *'
  workflow_dispatch: # Allow manual trigger
    inputs:
      force_cleanup:
        description: 'Force cleanup regardless of storage level'
        required: false
        default: false
        type: boolean
      max_artifacts:
        description: 'Maximum number of artifacts to keep'
        required: false
        default: '50'
        type: string

jobs:
  cleanup-artifacts:
    runs-on: ubuntu-latest
    permissions:
      actions: write
      contents: read

    steps:
      - name: Cleanup old artifacts when storage is high
        id: cleanup
        uses: actions/github-script@v7
        with:
          script: |
            const owner = context.repo.owner;
            const repo = context.repo.repo;

            // Configuration
            const MAX_ARTIFACTS = parseInt('${{ github.event.inputs.max_artifacts }}') || 50;
            const FORCE_CLEANUP = '${{ github.event.inputs.force_cleanup }}' === 'true';
            const MAX_STORAGE_MB = 400; // Trigger cleanup when approaching 500MB limit
            const CLEANUP_BATCH_SIZE = 20; // Delete this many at once when triggered

            console.log(`🔍 Daily artifact storage check (Max: ${MAX_ARTIFACTS} artifacts, Storage limit: ${MAX_STORAGE_MB}MB)`);

            try {
              // Get all artifacts
              const artifacts = await github.paginate(github.rest.actions.listArtifactsForRepo, {
                owner,
                repo,
                per_page: 100
              });

              console.log(`📦 Found ${artifacts.length} total artifacts`);

              if (artifacts.length === 0) {
                console.log('✅ No artifacts found, nothing to clean up');
                return;
              }

              // Calculate total storage usage
              const totalSizeMB = artifacts.reduce((sum, artifact) => sum + (artifact.size_in_bytes / (1024 * 1024)), 0);
              console.log(`💾 Total storage usage: ${totalSizeMB.toFixed(1)} MB`);

              // Sort artifacts by creation date (oldest first)
              const sortedArtifacts = artifacts.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

              // Determine if cleanup is needed
              let shouldCleanup = FORCE_CLEANUP;
              let reason = 'Manual force cleanup';

              if (!shouldCleanup && totalSizeMB > MAX_STORAGE_MB) {
                shouldCleanup = true;
                reason = `Storage usage (${totalSizeMB.toFixed(1)}MB) exceeds limit (${MAX_STORAGE_MB}MB)`;
              }

              if (!shouldCleanup && artifacts.length > MAX_ARTIFACTS) {
                shouldCleanup = true;
                reason = `Artifact count (${artifacts.length}) exceeds limit (${MAX_ARTIFACTS})`;
              }

              if (!shouldCleanup) {
                console.log(`✅ No cleanup needed. Storage: ${totalSizeMB.toFixed(1)}MB/${MAX_STORAGE_MB}MB, Count: ${artifacts.length}/${MAX_ARTIFACTS}`);
                return;
              }

              console.log(`🧹 Cleanup triggered: ${reason}`);

              // Calculate how many to delete
              let artifactsToDelete;
              if (FORCE_CLEANUP) {
                // Keep only the newest MAX_ARTIFACTS
                artifactsToDelete = sortedArtifacts.slice(0, Math.max(0, artifacts.length - MAX_ARTIFACTS));
              } else if (totalSizeMB > MAX_STORAGE_MB) {
                // Delete oldest artifacts until we're under the storage limit
                let currentSize = totalSizeMB;
                artifactsToDelete = [];
                for (const artifact of sortedArtifacts) {
                  if (currentSize <= MAX_STORAGE_MB * 0.8) break; // Leave 20% buffer
                  artifactsToDelete.push(artifact);
                  currentSize -= (artifact.size_in_bytes / (1024 * 1024));
                }
              } else {
                // Delete oldest artifacts to get under count limit
                artifactsToDelete = sortedArtifacts.slice(0, Math.max(0, artifacts.length - MAX_ARTIFACTS));
              }

              // Limit batch size to avoid overwhelming the API
              if (artifactsToDelete.length > CLEANUP_BATCH_SIZE) {
                console.log(`⚠️  Limiting deletion to ${CLEANUP_BATCH_SIZE} artifacts this run (${artifactsToDelete.length} total need deletion)`);
                artifactsToDelete = artifactsToDelete.slice(0, CLEANUP_BATCH_SIZE);
              }

              if (artifactsToDelete.length === 0) {
                console.log('✅ No artifacts need to be deleted');
                return;
              }

              console.log(`🗑️  Will delete ${artifactsToDelete.length} oldest artifacts:`);

              // Delete artifacts
              let deletedCount = 0;
              let freedSpaceMB = 0;

              for (const artifact of artifactsToDelete) {
                try {
                  const sizeMB = artifact.size_in_bytes / (1024 * 1024);
                  const createdDate = new Date(artifact.created_at).toISOString().split('T')[0];

                  console.log(`  🗑️  Deleting: ${artifact.name} (${sizeMB.toFixed(1)}MB, ${createdDate}) - ID: ${artifact.id}`);

                  await github.rest.actions.deleteArtifact({
                    owner,
                    repo,
                    artifact_id: artifact.id
                  });

                  deletedCount++;
                  freedSpaceMB += sizeMB;
                  console.log(`    ✅ Deleted successfully`);

                  // Small delay to avoid rate limiting
                  await new Promise(resolve => setTimeout(resolve, 200));

                } catch (error) {
                  console.error(`    ❌ Failed to delete artifact ${artifact.id}:`, error.message);
                }
              }

              const remainingArtifacts = artifacts.length - deletedCount;
              const remainingStorageMB = totalSizeMB - freedSpaceMB;

              console.log(`🎉 Cleanup complete!`);
              console.log(`   📊 Deleted: ${deletedCount} artifacts`);
              console.log(`   💾 Freed: ${freedSpaceMB.toFixed(1)} MB`);
              console.log(`   📦 Remaining: ${remainingArtifacts} artifacts (${remainingStorageMB.toFixed(1)} MB)`);

              // Set output for other workflows
              core.setOutput('deleted_count', deletedCount);
              core.setOutput('freed_space_mb', freedSpaceMB.toFixed(1));
              core.setOutput('remaining_artifacts', remainingArtifacts);
              core.setOutput('remaining_storage_mb', remainingStorageMB.toFixed(1));

            } catch (error) {
              console.error('❌ Error during artifact cleanup:', error);
              core.setFailed(`Artifact cleanup failed: ${error.message}`);
            }

      - name: Create cleanup summary
        if: always()
        run: |
          echo "## 🧹 Artifact Cleanup Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Deleted:** ${{ steps.cleanup.outputs.deleted_count || 0 }} artifacts" >> $GITHUB_STEP_SUMMARY
          echo "- **Space Freed:** ${{ steps.cleanup.outputs.freed_space_mb || 0 }} MB" >> $GITHUB_STEP_SUMMARY
          echo "- **Remaining:** ${{ steps.cleanup.outputs.remaining_artifacts || 'Unknown' }} artifacts" >> $GITHUB_STEP_SUMMARY
          echo "- **Storage Used:** ${{ steps.cleanup.outputs.remaining_storage_mb || 'Unknown' }} MB" >> $GITHUB_STEP_SUMMARY
          echo "- **Trigger:** ${{ github.event_name == 'workflow_dispatch' && 'Manual' || 'Scheduled' }}" >> $GITHUB_STEP_SUMMARY
