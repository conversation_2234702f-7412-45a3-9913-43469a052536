"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9335],{9335:(e,s,t)=>{t.r(s),t.d(s,{USBTransferDownload:()=>o,default:()=>x});var a=t(5155),l=t(2115),r=t(3286),d=t(3904),n=t(5339),i=t(646),c=t(1788);function o(e){let{isDarkMode:s=!1}=e,[t,o]=(0,l.useState)(null),[x,m]=(0,l.useState)(null),[h,u]=(0,l.useState)(!0),[b,j]=(0,l.useState)(!1),[p,g]=(0,l.useState)(!1);(0,l.useEffect)(()=>{v(),N()},[]);let v=async()=>{try{let e=await fetch("/api/tools/usb-transfer/version");if(e.ok){let s=await e.json();o(s.version)}}catch(e){console.error("Failed to load version info:",e)}finally{u(!1)}},N=async()=>{try{let e=localStorage.getItem("usb-transfer-installed-version");e&&m(e)}catch(e){console.error("Failed to check installed version:",e)}},k=async()=>{if(t){j(!0);try{await fetch("/api/tools/usb-transfer/download",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({version:t.version})});let e=document.createElement("a");e.href=t.downloadUrl,e.download="USB-Tool-Package-v".concat(t.version,".zip"),document.body.appendChild(e),e.click(),document.body.removeChild(e),localStorage.setItem("usb-transfer-installed-version",t.version),m(t.version)}catch(e){console.error("Download failed:",e)}finally{j(!1)}}},f=async()=>{g(!0),await v(),g(!1)},w=x&&t&&x!==t.version;return h?(0,a.jsx)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-6 bg-slate-200 dark:bg-slate-700 rounded w-1/3 mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-slate-200 dark:bg-slate-700 rounded w-2/3 mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2"})]})}):(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg",children:(0,a.jsx)(r.A,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-slate-900 dark:text-white",children:["PowerShell USB Transfer Tool v",(null==t?void 0:t.version)||"3.0.0"]}),(0,a.jsx)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:"Lightweight PowerShell script with automatic USB detection and Azure upload"})]})]}),t&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Latest Version:"}),(0,a.jsxs)("span",{className:"px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-sm font-mono rounded",children:["v",t.version]}),(0,a.jsxs)("span",{className:"text-xs text-slate-500 dark:text-slate-400",children:["Build ",t.buildNumber]})]}),(0,a.jsxs)("p",{className:"text-xs text-slate-500 dark:text-slate-400 mt-1",children:["Released ",new Date(t.releaseDate).toLocaleDateString()]})]}),(0,a.jsx)("button",{onClick:f,disabled:p,className:"p-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors",title:"Check for updates",children:(0,a.jsx)(d.A,{className:"h-4 w-4 ".concat(p?"animate-spin":"")})})]}),x?(0,a.jsx)("div",{className:"flex items-center space-x-2 mb-4",children:w?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A,{className:"h-4 w-4 text-amber-500"}),(0,a.jsxs)("span",{className:"text-sm text-amber-600 dark:text-amber-400",children:["Update available! You have v",x]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A,{className:"h-4 w-4 text-green-500"}),(0,a.jsx)("span",{className:"text-sm text-green-600 dark:text-green-400",children:"You have the latest version installed"})]})}):(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(n.A,{className:"h-4 w-4 text-blue-500"}),(0,a.jsx)("span",{className:"text-sm text-slate-600 dark:text-slate-400",children:"Not installed yet"})]}),t.changelog.length>0&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-700 dark:text-slate-300 mb-2",children:"What's New:"}),(0,a.jsx)("ul",{className:"text-sm text-slate-600 dark:text-slate-400 space-y-1",children:t.changelog.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,a.jsx)("span",{className:"text-blue-500 mt-1",children:"•"}),(0,a.jsx)("span",{children:e})]},s))})]})]}),(0,a.jsxs)("div",{className:"border-t border-slate-200 dark:border-slate-700 pt-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-700 dark:text-slate-300",children:"Download USB Tool Package"}),(0,a.jsx)("p",{className:"text-xs text-slate-500 dark:text-slate-400 mt-1",children:t?"".concat(t.size," • Complete package with both files"):"Complete USB tool package"})]}),(0,a.jsx)("div",{className:"flex items-center space-x-3",children:(0,a.jsxs)("button",{onClick:k,disabled:b||!t,className:"flex items-center space-x-2 px-4 py-2 rounded-md font-medium transition-colors ".concat(w?"bg-amber-600 hover:bg-amber-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white"," disabled:opacity-50 disabled:cursor-not-allowed"),children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:b?"Downloading...":w?"Update":"Download"})]})})]}),(0,a.jsxs)("div",{className:"mt-4 p-4 bg-slate-50 dark:bg-slate-700 rounded-md",children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-slate-700 dark:text-slate-300 mb-2",children:"Package Contents & Usage:"}),(0,a.jsxs)("div",{className:"text-xs text-slate-600 dark:text-slate-400 space-y-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-slate-700 dark:text-slate-300",children:"\uD83D\uDCE6 Complete Package Includes:"}),(0,a.jsxs)("ul",{className:"ml-4 space-y-1",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"upload-usb-recordings.bat"})," - Easy launcher with menu"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Upload-USBRecordings.ps1"})," - PowerShell script for advanced users"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"README.txt"})," - Detailed instructions"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-slate-700 dark:text-slate-300",children:"\uD83D\uDE80 Easy Way (Recommended):"}),(0,a.jsx)("p",{className:"ml-4",children:"Double-click the .bat file and choose from menu options"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-slate-700 dark:text-slate-300",children:"⚡ Advanced Way:"}),(0,a.jsx)("p",{className:"ml-4",children:'Right-click the .ps1 file → "Run with PowerShell"'})]})]})]})]})]})}let x=o}}]);