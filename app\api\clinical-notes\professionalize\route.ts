import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';
import { ClinicalPromptsManager } from '@/lib/prompts/clinical-prompts';

export async function POST(request: NextRequest) {
  // Initialize OpenAI client only when needed (runtime, not build time)
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
  try {
    const body = await request.json();
    const { 
      voiceNotes, 
      dentrixNotes, 
      patientInfo, 
      appointmentInfo, 
      promptId, 
      customPrompt, 
      openaiModel = 'gpt-4o' 
    } = body;

    if (!voiceNotes || voiceNotes.length === 0) {
      return NextResponse.json(
        { error: 'Voice notes are required' },
        { status: 400 }
      );
    }

    console.log(`API: Professionalizing notes with model: ${openaiModel}`);

    let promptText = '';
    
    if (customPrompt) {
      promptText = customPrompt;
    } else if (promptId) {
      const prompt = ClinicalPromptsManager.getPromptById(promptId);
      if (!prompt) {
        return NextResponse.json(
          { error: 'Invalid prompt ID' },
          { status: 400 }
        );
      }
      promptText = prompt.prompt;
    } else {
      // Use default professionalization prompt
      const defaultPrompt = ClinicalPromptsManager.getDefaultPrompt('professionalization');
      if (!defaultPrompt) {
        return NextResponse.json(
          { error: 'No default professionalization prompt found' },
          { status: 500 }
        );
      }
      promptText = defaultPrompt.prompt;
    }

    // Process prompt variables
    const processedPrompt = ClinicalPromptsManager.processPromptVariables(promptText, {
      voiceNotes,
      dentrixNotes: dentrixNotes || [],
      patientInfo,
      appointmentInfo
    });

    const startTime = Date.now();

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: openaiModel,
      messages: [
        {
          role: 'system',
          content: 'You are a professional dental clinical documentation specialist. Create accurate, professional clinical notes based on the provided information.'
        },
        {
          role: 'user',
          content: processedPrompt
        }
      ],
      temperature: 0.3,
      max_tokens: 2000,
    });

    const endTime = Date.now();
    const processingTime = endTime - startTime;

    const professionalizedNote = completion.choices[0]?.message?.content || '';
    
    if (!professionalizedNote) {
      return NextResponse.json(
        { error: 'Failed to generate professionalized note' },
        { status: 500 }
      );
    }

    // Prepare metadata
    const metadata = {
      modelUsed: openaiModel,
      processingTimeMs: processingTime,
      tokensUsed: completion.usage?.total_tokens || 0,
      promptTokens: completion.usage?.prompt_tokens || 0,
      completionTokens: completion.usage?.completion_tokens || 0,
      outputLength: professionalizedNote.length,
      timestamp: new Date().toISOString(),
      promptId: promptId || 'custom',
      voiceNotesCount: voiceNotes.length,
      dentrixNotesCount: dentrixNotes?.length || 0
    };

    console.log(`API: Successfully professionalized notes in ${processingTime}ms using ${metadata.tokensUsed} tokens`);

    return NextResponse.json({
      professionalizedNote,
      metadata,
      success: true
    });

  } catch (error) {
    console.error('Error professionalizing notes:', error);
    
    if (error instanceof Error && error.message.includes('API key')) {
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to professionalize notes',
        success: false
      },
      { status: 500 }
    );
  }
}
