(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6983],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(5155),s=r(2115),n=r(4624),c=r(2085),i=r(9434);let o=(0,c.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef((e,t)=>{let{className:r,variant:s,size:c,asChild:l=!1,...d}=e,m=l?n.DX:"button";return(0,a.jsx)(m,{className:(0,i.cn)(o({variant:s,size:c,className:r})),ref:t,...d})});l.displayName="Button"},1386:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(5155),s=r(2115),n=r(285),c=r(6695),i=r(4944),o=r(2085),l=r(9434);let d=(0,o.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function m(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)(d({variant:r}),t),...s})}var x=r(6496),u=r(3904),g=r(1007),h=r(9074),f=r(1362);function p(){let{theme:e,resolvedTheme:t}=(0,f.D)(),r="dark"===t,[o,l]=(0,s.useState)([]),[d,p]=(0,s.useState)(!0),[y,b]=(0,s.useState)(null),[N,j]=(0,s.useState)(null),[v,w]=(0,s.useState)(!1),[S,k]=(0,s.useState)({}),[C,R]=(0,s.useState)(null),[M,D]=(0,s.useState)(!1),z=(0,s.useMemo)(()=>{let e=new Set;return o.forEach(t=>{try{let r=new Date(t.date).toISOString().split("T")[0];e.add(r)}catch(e){console.error("Invalid date for recording ".concat(t.fileName,": ").concat(t.date))}}),Array.from(e)},[o]),F=async e=>{w(!0),b(null);let t=o.filter(t=>{try{return new Date(t.date).toISOString().startsWith(e)}catch(e){return!1}}).map(e=>({filename:e.fileName,recordingDate:new Date(e.date).toISOString(),size:e.size}));try{let r=await fetch("/api/voice/smart-sort",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({audioFiles:t,uploadDate:e})});if(!r.ok){let e=await r.json();throw Error(e.error||"Smart sort failed")}let a=await r.json();j(a),l(e=>e.map(e=>{let t=a.suggestions.find(t=>t.audioFile.filename===e.fileName);return t?{...e,status:"Matching",suggestedMatch:t.recommendedMatch}:e}))}catch(e){b("Failed to match appointments: ".concat(e.message))}finally{w(!1)}},O=async e=>{k(t=>({...t,[e.id]:"generating"}));try{let t=await fetch("/api/dental-notes/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({transcription:e.transcription,summary:e.summary,patient:e.patient})});if(!t.ok)throw Error("Note generation failed");let r=await t.json();l(t=>t.map(t=>t.id===e.id?{...t,clinicalNote:r.clinicalNote,status:"Complete"}:t))}catch(e){console.error(e)}finally{k(t=>({...t,[e.id]:null}))}},T=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];p(!0),b(null);try{let t=await fetch(e?"/api/voice/recordings?refresh=true":"/api/voice/recordings");if(!t.ok)throw Error("Failed to fetch recordings");let r=await t.json();l(r.recordings),E()}catch(e){b("Failed to fetch voice recordings.")}finally{p(!1)}},E=async()=>{try{let e=await fetch("/api/voice/cache-status");if(e.ok){let t=await e.json();R(t)}}catch(e){console.error("Error loading cache status:",e)}},A=async()=>{D(!0);try{(await fetch("/api/voice/recordings-cache",{method:"POST"})).ok&&await T(!0)}catch(e){console.error("Error refreshing cache:",e),b("Failed to refresh cache. Please try again.")}finally{D(!1)}};return(0,s.useEffect)(()=>{T()},[]),(0,a.jsxs)("div",{className:"container mx-auto p-4",children:[(0,a.jsx)(x.z,{title:"Voice Recording Workflow"}),(0,a.jsx)("p",{className:"p-4 ".concat(r?"text-gray-300":"text-muted-foreground"),children:"Manage voice recordings from upload to clinical note generation."}),(d||v||Object.values(S).some(e=>e))&&(0,a.jsx)(i.k,{value:50,className:"w-full my-4"}),y&&(0,a.jsxs)("div",{className:"p-4 mb-4 text-sm rounded-lg ".concat(r?"text-red-300 bg-red-900/30 border border-red-700":"text-red-700 bg-red-100 border border-red-200"),role:"alert",children:[(0,a.jsx)("span",{className:"font-medium",children:"Error:"})," ",y]}),(0,a.jsxs)("div",{className:"p-4 space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-lg ".concat(r?"bg-gray-800 border border-gray-700":"bg-gray-50"),children:[(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium ".concat(r?"text-white":"text-gray-900"),children:"Performance Cache"}),C&&(0,a.jsx)("p",{className:"text-sm ".concat(r?"text-gray-400":"text-gray-600"),children:C.exists?(0,a.jsxs)(a.Fragment,{children:[C.recordingsCount," recordings cached •",C.cacheAge.display," old •",C.cacheSize.display," •",C.recommendation]}):"No cache found - first load will be slower"})]})}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(n.$,{onClick:()=>T(),disabled:d,variant:"outline",size:"sm",children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 ".concat(d?"animate-spin":"")}),"Quick Refresh"]}),(0,a.jsxs)(n.$,{onClick:A,disabled:M,size:"sm",children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 ".concat(M?"animate-spin":"")}),M?"Rebuilding...":"Rebuild Cache"]})]})]}),(0,a.jsx)("div",{className:"flex space-x-2",children:z.map(e=>(0,a.jsx)(n.$,{onClick:()=>F(e),disabled:v,children:v?"Matching...":"Match Appointments for ".concat(e)},e))}),N&&(0,a.jsxs)(c.Zp,{className:"mb-4 ".concat(r?"bg-gray-800 border-gray-700":""),children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{className:r?"text-white":"",children:["Smart Sort Results for ",N.uploadDate]})}),(0,a.jsxs)(c.Wu,{children:[(0,a.jsxs)("p",{className:r?"text-gray-300":"text-gray-700",children:[N.totalFiles," files processed. ",N.autoMatches," auto-matched, ",N.manualReviewNeeded," need manual review."]}),(0,a.jsxs)("p",{className:r?"text-gray-300":"text-gray-700",children:["Found ",N.appointmentsOnDate.length," appointments on this date."]})]})]}),(0,a.jsxs)(c.Zp,{className:r?"bg-gray-800 border-gray-700":"",children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{className:r?"text-white":"",children:"All Recordings"})}),(0,a.jsx)(c.Wu,{children:d?(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:r?"text-gray-300":"text-gray-600",children:"Loading recordings..."})}):y?(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-red-500",children:y})}):(0,a.jsx)("div",{className:"space-y-4",children:o.map(e=>(0,a.jsxs)("div",{className:"p-4 border rounded-lg ".concat(r?"bg-gray-800 border-gray-700":"bg-white border-gray-200"),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold ".concat(r?"text-white":"text-gray-900"),children:e.fileName}),(0,a.jsxs)("p",{className:"text-sm ".concat(r?"text-gray-400":"text-muted-foreground"),children:[(e.size/1e6).toFixed(2)," MB - ","Invalid Date"===new Date(e.date).toString()?"Invalid Date":new Date(e.date).toLocaleString()]})]}),(0,a.jsx)(m,{className:r?"bg-gray-700 text-gray-200":"",children:e.status})]}),e.patient&&(0,a.jsxs)("div",{className:"mt-2 text-sm flex items-center ".concat(r?"text-gray-400":"text-muted-foreground"),children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"})," ",e.patient.name]}),e.appointment&&(0,a.jsxs)("div",{className:"mt-1 text-sm flex items-center ".concat(r?"text-gray-400":"text-muted-foreground"),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"})," ",new Date(e.appointment.date).toLocaleDateString()]}),e.suggestedMatch&&(0,a.jsxs)("div",{className:"mt-2 p-2 border rounded-lg ".concat(r?"bg-green-900/20 border-green-700":"bg-green-50 border-green-200"),children:[(0,a.jsx)("p",{className:"text-sm font-semibold ".concat(r?"text-green-300":"text-green-800"),children:"✓ Recommended Match:"}),(0,a.jsxs)("p",{className:"text-sm ".concat(r?"text-gray-300":"text-muted-foreground"),children:[e.suggestedMatch.patientName," (",e.suggestedMatch.startTime," - ",e.suggestedMatch.endTime,")"]}),(0,a.jsxs)("p",{className:"text-xs ".concat(r?"text-gray-400":"text-muted-foreground"),children:["Confidence: ",(100*e.suggestedMatch.confidence).toFixed(0),"%"]}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)(n.$,{size:"sm",variant:"secondary",children:"Accept Match"}),(0,a.jsx)(n.$,{size:"sm",variant:"link",children:"View other options"})]})]}),e.transcription&&e.transcription.trim()?(0,a.jsxs)("div",{className:"mt-2 p-2 border rounded-lg ".concat(r?"bg-gray-700 border-gray-600":"bg-gray-50"),children:[(0,a.jsx)("p",{className:"text-sm font-semibold ".concat(r?"text-white":"text-gray-900"),children:"Transcription:"}),(0,a.jsx)("p",{className:"text-sm whitespace-pre-wrap ".concat(r?"text-gray-300":"text-muted-foreground"),children:e.transcription})]}):(0,a.jsxs)("div",{className:"mt-2 p-2 border rounded-lg ".concat(r?"bg-yellow-900/20 border-yellow-700":"bg-yellow-50 border-yellow-200"),children:[(0,a.jsx)("p",{className:"text-sm font-semibold ".concat(r?"text-yellow-300":"text-yellow-800"),children:"Transcription:"}),(0,a.jsx)("p",{className:"text-sm ".concat(r?"text-yellow-200":"text-yellow-600"),children:(0,a.jsx)("em",{children:'Not yet transcribed - click "Generate Note" to process'})})]}),e.summary&&e.summary.trim()?(0,a.jsxs)("div",{className:"mt-2 p-2 border rounded-lg ".concat(r?"bg-blue-900/20 border-blue-700":"bg-blue-50"),children:[(0,a.jsx)("p",{className:"text-sm font-semibold ".concat(r?"text-blue-300":"text-blue-900"),children:"Summary:"}),(0,a.jsx)("p",{className:"text-sm ".concat(r?"text-gray-300":"text-muted-foreground"),children:e.summary})]}):(0,a.jsxs)("div",{className:"mt-2 p-2 border rounded-lg ".concat(r?"bg-yellow-900/20 border-yellow-700":"bg-yellow-50 border-yellow-200"),children:[(0,a.jsx)("p",{className:"text-sm font-semibold ".concat(r?"text-yellow-300":"text-yellow-800"),children:"Summary:"}),(0,a.jsx)("p",{className:"text-sm ".concat(r?"text-yellow-200":"text-yellow-600"),children:(0,a.jsx)("em",{children:'Not yet summarized - click "Generate Note" to process'})})]}),e.clinicalNote&&(0,a.jsxs)("div",{className:"mt-2 p-2 border rounded-lg ".concat(r?"bg-purple-900/20 border-purple-700":"bg-purple-50"),children:[(0,a.jsx)("p",{className:"text-sm font-semibold ".concat(r?"text-purple-300":"text-purple-900"),children:"Clinical Note:"}),(0,a.jsx)("p",{className:"text-sm whitespace-pre-wrap ".concat(r?"text-gray-300":"text-muted-foreground"),children:e.clinicalNote})]}),(0,a.jsx)("div",{className:"mt-4 flex space-x-2",children:(0,a.jsx)(n.$,{size:"sm",variant:"default",onClick:()=>O(e),disabled:"generating"===S[e.id],children:"generating"===S[e.id]?"Processing...":e.transcription&&e.transcription.trim()?e.summary&&e.summary.trim()?"Generate Clinical Note":"Generate Summary":"Transcribe & Summarize"})})]},e.id))})})]})]})]})}},3154:(e,t,r)=>{Promise.resolve().then(r.bind(r,1386))},4944:(e,t,r)=>{"use strict";r.d(t,{k:()=>i});var a=r(5155),s=r(2115),n=r(1838),c=r(9434);let i=s.forwardRef((e,t)=>{let{className:r,value:s,...i}=e;return(0,a.jsx)(n.bL,{ref:t,className:(0,c.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",r),...i,children:(0,a.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})});i.displayName=n.bL.displayName},6695:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>l,ZB:()=>o,Zp:()=>c,aR:()=>i});var a=r(5155),s=r(2115),n=r(9434);let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});c.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});i.displayName="CardHeader";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});o.displayName="CardTitle",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})}).displayName="CardDescription";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});l.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2596),s=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[8096,6496,7358],()=>t(3154)),_N_E=e.O()}]);