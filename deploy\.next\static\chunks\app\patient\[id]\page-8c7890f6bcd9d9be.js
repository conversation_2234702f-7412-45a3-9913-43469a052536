(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5510],{3926:(e,t,s)=>{Promise.resolve().then(s.bind(s,8327))},8327:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(5155),r=s(2115),l=s(5695),i=s(1362),d=s(6496),n=s(4822),c=s(1007),o=s(3786),x=s(9074),m=s(7434);function h(){let e=(0,l.useRouter)(),t=(0,l.useParams)(),{theme:s}=(0,i.D)(),h=t.id,[p,u]=(0,r.useState)(null),[b,j]=(0,r.useState)([]),[g,k]=(0,r.useState)(null),[N,f]=(0,r.useState)(!0),[w,v]=(0,r.useState)(!1),[y,B]=(0,r.useState)(null);(0,r.useEffect)(()=>{h&&D()},[h]);let D=async()=>{f(!0),B(null);try{var e,t;let s=await fetch("/api/patients/".concat(h));if(s.ok){let e=await s.json();if(e.patient){u(e.patient),P(h);return}}let a=await fetch("/api/patients/search?q=".concat(h));if(a.ok){let t=null==(e=(await a.json()).patients)?void 0:e.find(e=>e.id===h);if(t){u(t),P(h);return}}if(h.length>4){let e=await fetch("/api/patients/search?q=".concat(h.slice(-4)));if(e.ok){let s=null==(t=(await e.json()).patients)?void 0:t.find(e=>e.id===h);if(s){u(s),P(h);return}}}throw Error("Patient not found")}catch(e){console.error("Error loading patient:",e),B("Failed to load patient data for ID: ".concat(h,". The patient may not exist or the search API may be unavailable."))}finally{f(!1)}},P=async e=>{v(!0);try{let t=await fetch("/api/patients/".concat(e,"/visits"));if(t.ok){let e=await t.json();j(e.visits||[])}}catch(e){console.error("Error loading visits:",e)}finally{v(!1)}},C=e=>{k(e)},S=e=>{try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch(t){return e}},E=e=>{window.open("https://clinic.overjet.ai/app/fmx/dailypatients/".concat(e),"_blank")};return N?(0,a.jsxs)("div",{className:"min-h-screen bg-slate-50 dark:bg-slate-900",children:[(0,a.jsx)(d.z,{title:"Dentalapp",showBackButton:!0,onBackClick:()=>e.back()}),(0,a.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-300",children:"Loading patient data..."})]})})]}):y||!p?(0,a.jsxs)("div",{className:"min-h-screen bg-slate-50 dark:bg-slate-900",children:[(0,a.jsx)(d.z,{title:"Dentalapp",showBackButton:!0,onBackClick:()=>e.back()}),(0,a.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-red-900 dark:text-red-100 mb-2",children:"Error Loading Patient"}),(0,a.jsx)("p",{className:"text-red-800 dark:text-red-200",children:y||"Patient not found"}),(0,a.jsx)("button",{onClick:()=>e.back(),className:"mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",children:"Go Back"})]})})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-slate-50 dark:bg-slate-900",children:[(0,a.jsx)(d.z,{title:"Dentalapp",showBackButton:!0,onBackClick:()=>{g?k(null):e.back()}}),(0,a.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:g?(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-6 p-6 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-sm",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-slate-900 dark:text-white mb-4",children:["Visit Details - ",S(g.date)]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-slate-600 dark:text-slate-400",children:[(0,a.jsx)("strong",{children:"Type:"})," ",g.appointmentType]}),g.provider&&(0,a.jsxs)("p",{className:"text-slate-600 dark:text-slate-400",children:[(0,a.jsx)("strong",{children:"Provider:"})," ",g.provider]}),g.procedures&&g.procedures.length>0&&(0,a.jsxs)("p",{className:"text-slate-600 dark:text-slate-400",children:[(0,a.jsx)("strong",{children:"Procedures:"})," ",g.procedures.join(", ")]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-slate-600 dark:text-slate-400",children:[(0,a.jsx)("strong",{children:"Status:"})," ",g.status]}),g.operatory&&(0,a.jsxs)("p",{className:"text-slate-600 dark:text-slate-400",children:[(0,a.jsx)("strong",{children:"Operatory:"})," ",g.operatory]})]})]})]}),(0,a.jsx)(n.r,{patientId:p.id,appointmentDate:g.date,appointmentId:g.id,title:"Clinical Notes for this Visit",showPagination:!1,showFilters:!1,isDarkMode:"dark"===s})]}):(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)("div",{className:"mb-6 p-6 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full",children:(0,a.jsx)(c.A,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-slate-900 dark:text-white",children:[p.firstName,p.middleName,p.lastName].filter(Boolean).join(" ")}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-sm text-slate-600 dark:text-slate-400",children:[p.age&&p.genderInitial&&(0,a.jsxs)("span",{children:[p.age,p.genderInitial]}),p.dateOfBirth&&(0,a.jsxs)("span",{children:["DOB: ",S(p.dateOfBirth)]}),p.phone&&(0,a.jsx)("span",{children:p.phone})]})]})]}),p.chartNumber&&(0,a.jsxs)("button",{onClick:()=>E(p.chartNumber),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"View in Overjet"})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-sm p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-slate-900 dark:text-white",children:"Recent Visits"})]}),w?(0,a.jsxs)("div",{className:"text-center py-4",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-slate-600 dark:text-slate-400",children:"Loading visits..."})]}):b.length>0?(0,a.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:b.slice(0,10).map(e=>(0,a.jsx)("button",{onClick:()=>C(e),className:"w-full text-left p-3 bg-slate-50 dark:bg-slate-700 hover:bg-slate-100 dark:hover:bg-slate-600 rounded-md transition-colors",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-slate-900 dark:text-white",children:S(e.date)}),(0,a.jsxs)("p",{className:"text-sm text-slate-600 dark:text-slate-400",children:[e.appointmentType,e.provider&&" - ".concat(e.provider),e.procedures&&e.procedures.length>0&&(0,a.jsxs)("span",{className:"block text-xs text-slate-500 dark:text-slate-500 mt-1",children:["Procedures: ",e.procedures.join(", ")]})]})]}),(0,a.jsx)("span",{className:"text-xs px-2 py-1 rounded ".concat("Completed"===e.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"),children:e.status})]})},e.id))}):(0,a.jsx)("p",{className:"text-slate-600 dark:text-slate-400",children:"No visits found"})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg shadow-sm p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(m.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-slate-900 dark:text-white",children:"Clinical Notes (Past 2 Years)"})]}),(0,a.jsx)(n.r,{patientId:p.id,title:"",showPagination:!0,showFilters:!0,isDarkMode:"dark"===s,maxHeight:"400px"})]})]})]})})]})}function p(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-300",children:"Loading patient..."})]})}),children:(0,a.jsx)(h,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8096,6496,4822,7358],()=>t(3926)),_N_E=e.O()}]);