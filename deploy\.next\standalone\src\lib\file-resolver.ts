/**
 * SIMPLE FILE RESOLVER FOR AZURE TRANSCRIPTION
 * 
 * This module provides a straightforward way to find and resolve audio files
 * in Azure Blob Storage for transcription. No complex logic - just works.
 * 
 * PROBLEM: The transcription system was failing because it couldn't find files
 * due to inconsistent naming patterns (e.g., "azure-250626_1630.mp3-24194529-23627")
 * 
 * SOLUTION: This resolver tries multiple strategies to find files:
 * 1. Exact filename match
 * 2. Filename without numeric suffixes
 * 3. Search across all folders
 * 4. Fuzzy matching for similar names
 */

import { AzureStorageService } from './azure-storage';

export interface FileResolutionResult {
  found: boolean;
  actualPath?: string;
  originalName?: string;
  size?: number;
  searchStrategies?: string[];
  error?: string;
}

export class FileResolver {
  /**
   * MAIN FUNCTION: Find an audio file in Azure Storage
   * 
   * @param requestedFileName - The filename requested for transcription
   * @returns FileResolutionResult with actual path if found
   */
  static async findAudioFile(requestedFileName: string): Promise<FileResolutionResult> {
    console.log(`🔍 FileResolver: Looking for "${requestedFileName}"`);
    
    const strategies: string[] = [];
    
    try {
      // STRATEGY 1: Exact filename match
      strategies.push('exact-match');
      let foundPath = await AzureStorageService.findFileByName(requestedFileName);
      if (foundPath) {
        console.log(`✅ FileResolver: Found via exact match - ${foundPath}`);
        return {
          found: true,
          actualPath: foundPath,
          originalName: requestedFileName,
          searchStrategies: strategies
        };
      }

      // STRATEGY 2: Remove numeric suffixes (e.g., "file.mp3-12345-67890" -> "file.mp3")
      strategies.push('suffix-removal');
      const baseFileName = this.removeNumericSuffixes(requestedFileName);
      if (baseFileName !== requestedFileName) {
        console.log(`🔍 FileResolver: Trying base filename "${baseFileName}"`);
        foundPath = await AzureStorageService.findFileByName(baseFileName);
        if (foundPath) {
          console.log(`✅ FileResolver: Found via suffix removal - ${foundPath}`);
          return {
            found: true,
            actualPath: foundPath,
            originalName: baseFileName,
            searchStrategies: strategies
          };
        }
      }

      // STRATEGY 3: Search for similar filenames (fuzzy match)
      strategies.push('fuzzy-match');
      const fuzzyMatch = await this.findSimilarFile(requestedFileName);
      if (fuzzyMatch) {
        console.log(`✅ FileResolver: Found via fuzzy match - ${fuzzyMatch}`);
        return {
          found: true,
          actualPath: fuzzyMatch,
          originalName: requestedFileName,
          searchStrategies: strategies
        };
      }

      // STRATEGY 4: List all files for debugging
      strategies.push('list-all-files');
      const allFiles = await AzureStorageService.listFiles('');
      console.log(`📂 FileResolver: Available files in storage:`, allFiles.map(f => f.name));
      
      console.log(`❌ FileResolver: File not found after ${strategies.length} strategies`);
      return {
        found: false,
        error: `File "${requestedFileName}" not found in Azure Storage after trying: ${strategies.join(', ')}`,
        searchStrategies: strategies
      };

    } catch (error) {
      console.error(`💥 FileResolver: Error during search:`, error);
      return {
        found: false,
        error: `Search error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        searchStrategies: strategies
      };
    }
  }

  /**
   * Remove numeric suffixes from filenames
   * Examples:
   * - "azure-250626_1630.mp3-24194529-23627" -> "azure-250626_1630.mp3"
   * - "file.mp3-12345" -> "file.mp3"
   * - "normal.mp3" -> "normal.mp3" (unchanged)
   */
  private static removeNumericSuffixes(filename: string): string {
    // Remove patterns like "-12345-67890" or "-12345" from the end
    return filename.replace(/-\d+(-\d+)*$/, '');
  }

  /**
   * Find files with similar names (fuzzy matching)
   * This helps when there are slight variations in naming
   */
  private static async findSimilarFile(targetName: string): Promise<string | null> {
    try {
      const allFiles = await AzureStorageService.listFiles('');
      const targetBase = this.removeNumericSuffixes(targetName.toLowerCase());
      
      // Look for files that contain the base name
      for (const file of allFiles) {
        const fileBase = this.removeNumericSuffixes(file.name.toLowerCase());
        if (fileBase.includes(targetBase) || targetBase.includes(fileBase)) {
          console.log(`🎯 FileResolver: Fuzzy match found - "${file.name}" for "${targetName}"`);
          return file.path;
        }
      }
      
      return null;
    } catch (error) {
      console.error(`FileResolver: Fuzzy match error:`, error);
      return null;
    }
  }

  /**
   * DIAGNOSTIC FUNCTION: List all available files
   * Use this to debug what's actually in storage
   */
  static async listAllFiles(): Promise<{ name: string; path: string; size: number }[]> {
    try {
      const files = await AzureStorageService.listFiles('');
      return files.map(f => ({
        name: f.name,
        path: f.path,
        size: f.size
      }));
    } catch (error) {
      console.error('FileResolver: Error listing files:', error);
      return [];
    }
  }
}