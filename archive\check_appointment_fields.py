import requests
import json
from datetime import datetime, timedelta
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 60

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def check_appointment_fields(request_key, date):
    """Check all fields in appointment data to find date-related fields."""
    headers = {"Request-Key": request_key}
    
    print(f"Checking appointment fields for {date}...")
    
    # Try v2 endpoint
    try:
        resp = requests.get(
            f"{API_BASE_V2}/appointments", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            
            # Extract items from v2 response
            if isinstance(data, list) and len(data) > 0:
                items = data[0].get("items", [])
                print(f"Found {len(items)} appointments from v2 endpoint")
                
                if items:
                    # Get all field names from the first appointment
                    first_appt = items[0]
                    print("\nFields in v2 appointment data:")
                    for field, value in first_appt.items():
                        print(f"  {field}: {value}")
                    
                    # Check for date-related fields in all appointments
                    date_fields = set()
                    for appt in items:
                        for field, value in appt.items():
                            if isinstance(value, str) and (
                                "date" in field.lower() or 
                                "time" in field.lower() or
                                "created" in field.lower() or
                                "modified" in field.lower() or
                                "updated" in field.lower() or
                                "scheduled" in field.lower()
                            ):
                                date_fields.add(field)
                    
                    print("\nDate-related fields found:")
                    for field in sorted(date_fields):
                        print(f"  {field}")
                    
                    # Print values of date-related fields for a few appointments
                    print("\nValues of date-related fields for sample appointments:")
                    for i, appt in enumerate(items[:5]):
                        print(f"\nAppointment {i+1}:")
                        for field in sorted(date_fields):
                            print(f"  {field}: {appt.get(field, 'N/A')}")
                        
                        # Also print a few key fields for context
                        print(f"  patient_name: {appt.get('patient_name', 'N/A')}")
                        print(f"  operatory: {appt.get('operatory', 'N/A')}")
                        print(f"  provider_id: {appt.get('provider_id', 'N/A')}")
                        print(f"  description: {appt.get('description', 'N/A')}")
                else:
                    print("No appointments found")
            else:
                print("No appointments found")
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v4 endpoint
    print("\nChecking v4 endpoint...")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/appointments", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            items = data.get("items", [])
            print(f"Found {len(items)} appointments from v4 endpoint")
            
            if items:
                # Get all field names from the first appointment
                first_appt = items[0]
                print("\nFields in v4 appointment data:")
                for field, value in first_appt.items():
                    print(f"  {field}: {value}")
                
                # Check for date-related fields in all appointments
                date_fields = set()
                for appt in items:
                    for field, value in appt.items():
                        if isinstance(value, str) and (
                            "date" in field.lower() or 
                            "time" in field.lower() or
                            "created" in field.lower() or
                            "modified" in field.lower() or
                            "updated" in field.lower() or
                            "scheduled" in field.lower()
                        ):
                            date_fields.add(field)
                
                print("\nDate-related fields found:")
                for field in sorted(date_fields):
                    print(f"  {field}")
                
                # Print values of date-related fields for a few appointments
                print("\nValues of date-related fields for sample appointments:")
                for i, appt in enumerate(items[:5]):
                    print(f"\nAppointment {i+1}:")
                    for field in sorted(date_fields):
                        print(f"  {field}: {appt.get(field, 'N/A')}")
                    
                    # Also print a few key fields for context
                    print(f"  patient_name: {appt.get('patient_name', 'N/A')}")
                    print(f"  provider_id: {appt.get('provider_id', 'N/A')}")
                    print(f"  description: {appt.get('description', 'N/A')}")
            else:
                print("No appointments found")
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")

def main():
    # Get date from command line or use default
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        date = "2025-05-16"  # Default date
    
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Check appointment fields
    check_appointment_fields(request_key, date)
    
    print("\nCheck complete.")

if __name__ == "__main__":
    main()
