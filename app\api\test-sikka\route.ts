import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing Sikka API connection...');
    
    const credentials = loadCredentials();
    console.log('Loaded credentials:', {
      app_id: credentials.app_id ? 'SET' : 'MISSING',
      app_key: credentials.app_key ? 'SET' : 'MISSING',
      office_id: credentials.office_id ? 'SET' : 'MISSING',
      secret_key: credentials.secret_key ? 'SET' : 'MISSING'
    });
    
    const client = new SikkaApiClient(credentials);
    
    // Test authentication
    console.log('Attempting authentication...');
    const requestKey = await client.authenticate();
    console.log('Authentication successful, request key:', requestKey ? 'RECEIVED' : 'MISSING');
    
    // Test a simple API call
    console.log('Testing appointments API call...');
    const appointments = await client.getAppointments('2025-06-15');
    console.log('Appointments API call successful, found:', appointments.length, 'appointments');
    
    return NextResponse.json({
      success: true,
      message: 'Sikka API connection test successful',
      results: {
        authentication: 'SUCCESS',
        appointmentsCount: appointments.length,
        requestKey: requestKey ? 'RECEIVED' : 'MISSING'
      }
    });
    
  } catch (error) {
    console.error('Sikka API test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : 'No stack trace available'
    }, { status: 500 });
  }
}
