<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dental Appointment Viewer</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Dental Appointment Viewer</h1>
            <div class="date-selector">
                <button id="prev-date">&lt;</button>
                <input type="date" id="date-picker">
                <button id="next-date">&gt;</button>
            </div>
        </header>
        
        <main>
            <div class="schedule-container">
                <div class="time-column">
                    <div class="time-header">Time</div>
                    <div class="time-slots">
                        <!-- Time slots will be generated by JavaScript -->
                    </div>
                </div>
                
                <div class="operatories-container" id="operatories-container">
                    <!-- Operatory columns will be generated by JavaScript -->
                </div>
            </div>
        </main>
        
        <div id="appointment-details" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2>Appointment Details</h2>
                <div id="appointment-info"></div>
            </div>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Loading appointments...</p>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
