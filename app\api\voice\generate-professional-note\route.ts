import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

// Lazy initialization of OpenAI client
function getOpenAIClient() {
  return new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
}

interface ProcedureCode {
  code: string;
  description: string;
}

interface AppointmentDetails {
  patientName: string;
  date: string;
  provider: string;
  operatory: string;
  appointmentType: string;
}

interface Transcription {
  recordingName: string;
  transcription: string;
}

interface NoteGenerationRequest {
  appointmentDetails: AppointmentDetails;
  procedureCodes: ProcedureCode[];
  originalNotes: string;
  transcriptions: Transcription[];
  additionalNotes: string;
  rejectionFeedback?: string[];
  customFeedback?: string;
}

export async function POST(request: NextRequest) {
  try {
    const data: NoteGenerationRequest = await request.json();

    if (!data.appointmentDetails) {
      return NextResponse.json(
        { error: 'Appointment details are required' },
        { status: 400 }
      );
    }

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        {
          error: 'OpenAI API key not configured',
          details: 'Please configure OPENAI_API_KEY environment variable'
        },
        { status: 500 }
      );
    }

    console.log('Generating professional note for:', data.appointmentDetails.patientName);

    // Build the comprehensive prompt
    const prompt = buildProfessionalNotePrompt(data);

    // Generate the professional note using OpenAI
    const openai = getOpenAIClient();
    const response = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: `You are a professional dental clinical documentation assistant. Your role is to create comprehensive, accurate, and well-formatted clinical notes that meet professional dental standards.

Key requirements:
- Use proper dental terminology and professional language
- Organize information logically with clear sections
- Include specific details about procedures, findings, and patient responses
- Maintain HIPAA compliance - focus on clinical facts
- Create notes suitable for legal documentation
- Follow standard dental charting conventions
- Be thorough but concise (typically 2-4 paragraphs)

Format the note as a single, well-structured clinical entry suitable for copying into dental practice management software like Dentrix.`
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 1500,
      temperature: 0.3
    });

    const professionalNote = response.choices[0]?.message?.content;

    if (!professionalNote) {
      throw new Error('Failed to generate professional note');
    }

    return NextResponse.json({
      success: true,
      professionalNote,
      appointmentId: data.appointmentDetails.patientName + '_' + data.appointmentDetails.date,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error generating professional note:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate professional note',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

function buildProfessionalNotePrompt(data: NoteGenerationRequest): string {
  let prompt = `Create a comprehensive, professional dental clinical note using the following information:

APPOINTMENT DETAILS:
Patient: ${data.appointmentDetails.patientName}
Date: ${data.appointmentDetails.date}
Provider: ${data.appointmentDetails.provider}
Operatory: ${data.appointmentDetails.operatory}
Appointment Type: ${data.appointmentDetails.appointmentType}
`;

  // Add procedure codes if available
  if (data.procedureCodes && data.procedureCodes.length > 0) {
    prompt += `\nPROCEDURES COMPLETED:\n`;
    data.procedureCodes.forEach(proc => {
      prompt += `- ${proc.code}: ${proc.description}\n`;
    });
  }

  // Add original clinical notes if available
  if (data.originalNotes && data.originalNotes.trim()) {
    prompt += `\nORIGINAL CLINICAL NOTES FROM DENTRIX:\n${data.originalNotes}\n`;
  }

  // Add voice transcriptions if available
  if (data.transcriptions && data.transcriptions.length > 0) {
    prompt += `\nVOICE TRANSCRIPTIONS:\n`;
    data.transcriptions.forEach((trans, index) => {
      if (trans.transcription && trans.transcription.trim()) {
        prompt += `Recording ${index + 1} (${trans.recordingName}):\n${trans.transcription}\n\n`;
      }
    });
  }

  // Add additional notes if available
  if (data.additionalNotes && data.additionalNotes.trim()) {
    prompt += `\nADDITIONAL NOTES:\n${data.additionalNotes}\n`;
  }

  // Add rejection feedback if this is a rework
  if (data.rejectionFeedback && data.rejectionFeedback.length > 0) {
    prompt += `\nPREVIOUS FEEDBACK FOR IMPROVEMENT:\n`;
    data.rejectionFeedback.forEach(feedback => {
      prompt += `- ${feedback}\n`;
    });
  }

  if (data.customFeedback && data.customFeedback.trim()) {
    prompt += `\nSPECIFIC FEEDBACK:\n${data.customFeedback}\n`;
  }

  // Add instructions
  prompt += `\nINSTRUCTIONS:
1. Create a comprehensive, professional clinical note that integrates all the above information
2. If there are original notes, enhance them with information from transcriptions and additional notes
3. If no original notes exist, create a complete note based on transcriptions and additional notes
4. Reference the completed procedures appropriately throughout the note
5. Use proper dental terminology and maintain professional tone
6. Organize the note logically with clear flow between sections
7. Include specific clinical findings, treatment provided, and patient responses
8. Address any feedback provided to improve the note quality
9. Ensure the note is suitable for legal documentation and insurance purposes
10. Keep the note comprehensive but concise (typically 2-4 well-structured paragraphs)

Format the note as a single clinical entry ready for copying into Dentrix or similar dental practice management software.`;

  return prompt;
}

// Helper function to validate OpenAI API key
export async function GET() {
  try {
    const hasApiKey = !!process.env.OPENAI_API_KEY;
    
    if (!hasApiKey) {
      return NextResponse.json({
        configured: false,
        error: 'OpenAI API key not configured'
      });
    }

    // Test the API key with a simple request
    const openai = getOpenAIClient();
    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'user',
          content: 'Test message for API validation'
        }
      ],
      max_tokens: 10
    });

    return NextResponse.json({
      configured: true,
      model: 'gpt-4o',
      status: 'API key is valid and working'
    });

  } catch (error) {
    console.error('OpenAI API validation error:', error);
    return NextResponse.json({
      configured: false,
      error: 'OpenAI API key is invalid or API is not accessible',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
