"use strict";exports.id=8248,exports.ids=[8248],exports.modules={8248:(e,r,t)=>{t.d(r,{zc:()=>i});class a extends Error{constructor(e,r,t=!1,a){super(e),this.name="ProcessingError",this.code=r,this.retryable=t,this.details=a}}class o extends a{constructor(e,r){super(`${e} service is currently unavailable`,"SERVICE_UNAVAILABLE",!0,{serviceName:e,...r})}}class s extends a{constructor(e,r){super(`${e} quota exceeded${r?`, resets at ${r.toISOString()}`:""}`,"QUOTA_EXCEEDED",!0,{serviceName:e,resetTime:r})}}class i{static classifyError(e){return e instanceof a?{type:e.constructor.name,message:e.message,retryable:e.retryable,retryDelay:e.retryable?this.calculateRetryDelay(e.code):void 0,code:e.code}:401===e.status?{type:"AuthenticationError",message:"Invalid API key or authentication failed",retryable:!1,code:"AUTH_FAILED"}:429===e.status?{type:"QuotaExceededError",message:"API rate limit exceeded",retryable:!0,retryDelay:6e4,code:"RATE_LIMIT_EXCEEDED"}:413===e.status?{type:"FileTooLargeError",message:"File size exceeds API limits",retryable:!1,code:"FILE_TOO_LARGE"}:e.message?.includes("The specified blob does not exist")?{type:"FileNotFoundError",message:"File not found in Azure storage",retryable:!1,code:"BLOB_NOT_FOUND"}:"BlobNotFound"===e.code?{type:"FileNotFoundError",message:"Blob not found in Azure storage",retryable:!1,code:"BLOB_NOT_FOUND"}:"ContainerNotFound"===e.code?{type:"ConfigurationError",message:"Azure storage container not found",retryable:!1,code:"CONTAINER_NOT_FOUND"}:e.message?.includes("recognition canceled")?{type:"ServiceUnavailableError",message:"Speech recognition service unavailable",retryable:!0,retryDelay:5e3,code:"SPEECH_SERVICE_UNAVAILABLE"}:e.message?.toLowerCase().includes("rate limit")||e.message?.toLowerCase().includes("quota")||e.message?.toLowerCase().includes("too many requests")?{type:"QuotaExceededError",message:"Service quota exceeded",retryable:!0,retryDelay:6e4,code:"QUOTA_EXCEEDED"}:"ECONNRESET"===e.code||"ETIMEDOUT"===e.code||"ENOTFOUND"===e.code||"ECONNREFUSED"===e.code?{type:"NetworkError",message:`Network connection error: ${e.code}`,retryable:!0,retryDelay:1e4,code:"NETWORK_ERROR"}:"AbortError"===e.name||e.message?.includes("timeout")?{type:"TimeoutError",message:"Operation timed out",retryable:!0,retryDelay:15e3,code:"TIMEOUT"}:"ENOENT"===e.code?{type:"FileNotFoundError",message:"File not found on local filesystem",retryable:!1,code:"FILE_NOT_FOUND"}:"EACCES"===e.code||"EPERM"===e.code?{type:"PermissionError",message:"Insufficient permissions to access file",retryable:!1,code:"PERMISSION_DENIED"}:{type:"UnknownError",message:e.message||"An unknown error occurred",retryable:!1,code:"UNKNOWN"}}static calculateRetryDelay(e){switch(e){case"QUOTA_EXCEEDED":case"RATE_LIMIT_EXCEEDED":return 6e4;case"SERVICE_UNAVAILABLE":case"SPEECH_SERVICE_UNAVAILABLE":case"API_ERROR":return 5e3;case"NETWORK_ERROR":return 1e4;case"TIMEOUT":case"API_TIMEOUT":return 15e3;case"BLOB_NOT_FOUND":case"CONTAINER_NOT_FOUND":case"FILE_NOT_FOUND":case"AUTH_FAILED":case"FILE_TOO_LARGE":case"INVALID_FILE_FORMAT":case"PERMISSION_DENIED":case"MAX_RETRIES_EXHAUSTED":case"NON_RETRYABLE":return 0;case"STATE_CORRUPTION":return 3e4;default:return 3e3}}static async withRetry(e,r=3,t="operation"){let a;for(let o=1;o<=r;o++)try{return await e()}catch(s){a=s;let e=this.classifyError(s);if(console.error(`❌ Attempt ${o} failed for ${t}:`,{type:e.type,message:e.message,retryable:e.retryable}),!e.retryable)throw s;o<r&&e.retryDelay&&await new Promise(r=>setTimeout(r,e.retryDelay))}throw a}static logError(e,r={}){console.error("❌ Processing Error:",{...this.classifyError(e),context:r,timestamp:new Date().toISOString(),stack:e.stack})}static getUserMessage(e){switch(this.classifyError(e).code){case"INVALID_FILE_FORMAT":return"The audio file format is not supported. Please use MP3, WAV, M4A, or other supported formats.";case"FILE_NOT_FOUND":return"The audio file could not be found. Please check that the file exists and try again.";case"SERVICE_UNAVAILABLE":return"The transcription service is temporarily unavailable. Please try again in a few minutes.";case"QUOTA_EXCEEDED":return"Service usage limit reached. Please wait a few minutes before trying again.";case"NETWORK_ERROR":return"Network connection error. Please check your internet connection and try again.";case"TIMEOUT":return"The operation took too long to complete. Please try again with a smaller file.";default:return"An unexpected error occurred. Please try again or contact support if the problem persists."}}static shouldRetryLater(e){return this.classifyError(e).retryable}static getRetryDelay(e){return this.classifyError(e).retryDelay||3e3}static async withExponentialBackoff(e,r=3,t=1e3,o=3e4,s="operation",i){let n;for(let c=1;c<=r;c++)try{return i?.jobId,await e()}catch(E){n=E;let e=this.classifyError(E),l={attempt:c,maxRetries:r,operationName:s,...i,error:{type:e.type,message:e.message,code:e.code,retryable:e.retryable}};if(i?.jobId?console.error(`❌ [Job ${i.jobId}] Attempt ${c} failed:`,l):console.error(`❌ Attempt ${c} failed for ${s}:`,l),!e.retryable)throw new a(`Non-retryable error in ${s}: ${e.message}`,e.code||"NON_RETRYABLE",!1,{originalError:E,context:l});if(c<r){let e=Math.min(Math.min(t*Math.pow(2,c-1),o)+1e3*Math.random(),o);i?.jobId,await new Promise(r=>setTimeout(r,e))}}throw new a(`Max retries (${r}) exhausted for ${s}: ${n.message}`,"MAX_RETRIES_EXHAUSTED",!1,{originalError:n,context:i})}static async wrapApiCall(e,r,t){let i=`${r} API call`;return this.withExponentialBackoff(async()=>{try{Date.now();let r=await e();return Date.now(),t?.jobId,r}catch(e){if(429===e.status||e.message?.includes("rate limit"))throw new s(r,this.extractResetTime(e));if(e.status>=500&&e.status<600)throw new o(r,{status:e.status,statusText:e.statusText});if("ECONNRESET"===e.code||"ETIMEDOUT"===e.code||"ENOTFOUND"===e.code)throw new a(`Network error calling ${r}: ${e.message}`,"NETWORK_ERROR",!0,{originalError:e});if("AbortError"===e.name||e.message?.includes("timeout"))throw new a(`Timeout calling ${r}: ${e.message}`,"API_TIMEOUT",!0,{originalError:e});if(e instanceof a)throw e;throw new a(`API error from ${r}: ${e.message}`,"API_ERROR",!1,{originalError:e,status:e.status})}},3,1e3,3e4,i,t)}static createJobError(e,r,t){let a=this.classifyError(e);return{type:this.mapErrorTypeToJobError(a.type),code:a.code||"UNKNOWN",message:a.message,details:e.details||e,timestamp:new Date().toISOString(),stage:r,retryable:a.retryable,context:{jobId:t.jobId,filename:t.filename,operation:t.operation,stackTrace:e.stack}}}static mapErrorTypeToJobError(e){return({FileNotFoundError:"file_not_found",FileFormatError:"validation_error",ServiceUnavailableError:"api_error",QuotaExceededError:"quota_exceeded",NetworkError:"network_error",TimeoutError:"timeout_error",ProcessingError:"api_error",UnknownError:"unknown_error"})[e]||"unknown_error"}static extractResetTime(e){if(e.headers&&e.headers["x-ratelimit-reset"])return new Date(1e3*parseInt(e.headers["x-ratelimit-reset"]));if(e.headers&&e.headers["retry-after"]){let r=parseInt(e.headers["retry-after"]);return new Date(Date.now()+1e3*r)}return new Date(Date.now()+6e4)}static async processBatchWithErrorHandling(e,r,t={}){let{batchSize:a=10,continueOnError:o=!0,maxConcurrent:s=3,operationName:i="batch operation",context:n}=t,c=[],l=[];for(let t=0;t<e.length;t+=a){let s=Math.min(t+a,e.length),i=e.slice(t,s).map(async(a,s)=>{let i=t+s;try{let e=await r(a,i);return c[i]=e,n?.jobId,{index:i,result:e,error:null}}catch(t){let r=this.classifyError(t);if(n?.jobId?console.error(`❌ [Job ${n.jobId}] Item ${i+1}/${e.length} failed:`,r):console.error(`❌ Item ${i+1}/${e.length} failed:`,r),c[i]=null,l.push({index:i,item:a,error:t}),!o)throw t;return{index:i,result:null,error:t}}});(await Promise.allSettled(i)).forEach((e,r)=>{if("rejected"===e.status){let a=t+r;if(console.error(`💥 Unexpected batch error for item ${a+1}:`,e.reason),!o)throw e.reason}})}let E={total:e.length,successful:c.filter(e=>null!==e).length,failed:l.length};return{results:c,errors:l,summary:E}}}}};