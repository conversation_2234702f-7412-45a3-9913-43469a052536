import requests
import json
import sys
from datetime import datetime

# API base URLs
API_BASE_V2 = "https://api.sikkasoft.com/v2"
API_BASE_V4 = "https://api.sikkasoft.com/v4"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        }
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def test_date_filters(request_key, date):
    """Test different date filter parameters to get only appointments for a specific date."""
    headers = {"Request-Key": request_key}
    
    print(f"\nTesting different date filter parameters for {date}:")
    
    # List of parameter combinations to try
    param_combinations = [
        {"date": date},
        {"date": date, "filter": "date"},
        {"date": date, "filter_by": "date"},
        {"date": date, "filter_type": "scheduled"},
        {"scheduled_date": date},
        {"appointment_date": date},
        {"occurrence_date": date},
        {"actual_date": date},
        {"date_filter": date},
        {"date_type": "scheduled", "date": date},
        {"date_equals": date},
        {"date_is": date},
        {"date_on": date},
        {"on_date": date},
        {"for_date": date},
        {"exact_date": date},
        {"specific_date": date},
        {"appointment_on": date},
        {"scheduled_on": date},
        {"date_only": date},
        {"date_eq": date},
        {"date": f"eq:{date}"},
        {"date": f"equals:{date}"},
        {"date": f"is:{date}"},
        {"date": f"=:{date}"},
        {"date": date, "include_only": "scheduled"},
        {"date": date, "include": "scheduled"},
        {"date": date, "type": "scheduled"},
        {"date": date, "appointment_type": "scheduled"},
        {"date": date, "date_type": "scheduled"},
    ]
    
    # Try each parameter combination
    for i, params in enumerate(param_combinations):
        param_str = ", ".join([f"{k}={v}" for k, v in params.items()])
        print(f"\n{i+1}. Testing parameters: {param_str}")
        
        try:
            resp = requests.get(
                f"{API_BASE_V2}/appointments",
                headers=headers,
                params=params
            )
            
            if resp.status_code == 200:
                data = resp.json()
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    print(f"  Found {len(items)} appointments in response")
                    
                    # Check how many appointments are actually for the target date
                    target_appointments = [a for a in items if a.get("date") == date]
                    print(f"  Of these, {len(target_appointments)} are scheduled for {date}")
                    
                    # Check if all appointments are for the target date
                    if len(items) > 0 and len(target_appointments) == len(items):
                        print(f"  SUCCESS: All {len(items)} appointments are for {date}")
                        
                        # Check for operatory information
                        has_operatory = [a for a in target_appointments if a.get("operatory")]
                        print(f"  Of these, {len(has_operatory)} have operatory information")
                        
                        # Get unique operatories
                        operatories = set()
                        for appt in target_appointments:
                            operatory = appt.get("operatory")
                            if operatory and operatory not in ["", "N/A"]:
                                operatories.add(operatory)
                        
                        print(f"  Operatories found: {', '.join(sorted(operatories))}")
                        
                        # Print sample appointments
                        if target_appointments:
                            print("\n  Sample appointments:")
                            for i, appt in enumerate(sorted(target_appointments, key=lambda a: a.get("time", ""))[:3]):
                                time = appt.get("time", "")
                                patient = appt.get("patient_name", "Unknown")
                                operatory = appt.get("operatory", "N/A")
                                provider = appt.get("provider_id", "N/A")
                                print(f"    {i+1}. {time} - {patient} - Op: {operatory} - Provider: {provider}")
                    elif len(items) > 0:
                        print(f"  PARTIAL: Only {len(target_appointments)} out of {len(items)} appointments are for {date}")
                    else:
                        print("  No appointments found")
                else:
                    print("  No appointments found in response")
            else:
                print(f"  Error: {resp.status_code}")
                print(f"  {resp.text}")
        except Exception as e:
            print(f"  Error: {e}")
    
    # Try with date format variations
    date_formats = [
        date,  # YYYY-MM-DD
        date.replace("-", "/"),  # YYYY/MM/DD
        f"{date.split('-')[1]}/{date.split('-')[2]}/{date.split('-')[0]}",  # MM/DD/YYYY
    ]
    
    print("\nTesting date format variations:")
    for i, date_format in enumerate(date_formats):
        print(f"\n{i+1}. Testing date format: {date_format}")
        
        try:
            resp = requests.get(
                f"{API_BASE_V2}/appointments",
                headers=headers,
                params={"date": date_format}
            )
            
            if resp.status_code == 200:
                data = resp.json()
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    print(f"  Found {len(items)} appointments in response")
                    
                    # Check how many appointments are actually for the target date
                    target_appointments = [a for a in items if a.get("date") == date]
                    print(f"  Of these, {len(target_appointments)} are scheduled for {date}")
                else:
                    print("  No appointments found in response")
            else:
                print(f"  Error: {resp.status_code}")
                print(f"  {resp.text}")
        except Exception as e:
            print(f"  Error: {e}")

def main():
    # Get date from command line or use default
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        # Use a date that we know has appointments in the test data
        date = "2025-05-16"
    
    # Validate date format
    try:
        datetime.strptime(date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        return
    
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Test date filters
    test_date_filters(request_key, date)
    
    print("\nTest complete.")

if __name__ == "__main__":
    main()
