import requests
import json
from datetime import datetime

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Target date and provider
TARGET_DATE = "2025-05-16"
TARGET_PROVIDER = "LL01"

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        }
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None

    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def fetch_practice_schedule(request_key):
    """Fetch practice schedule for operatories on a specific date."""
    print(f"Fetching practice schedule for {TARGET_DATE}...")
    headers = {"Request-Key": request_key}

    # Try different parameter combinations
    parameter_sets = [
        # Try 1: Basic parameters
        {
            "date": TARGET_DATE,
            "resource_type": "operatory"
        },
        # Try 2: With practice_id
        {
            "practice_id": OFFICE_ID,
            "date": TARGET_DATE,
            "resource_type": "operatory"
        },
        # Try 3: With resource_id as provider
        {
            "date": TARGET_DATE,
            "resource_id": TARGET_PROVIDER
        },
        # Try 4: Just date
        {
            "date": TARGET_DATE
        },
        # Try 5: No parameters
        {}
    ]

    for i, params in enumerate(parameter_sets):
        print(f"\nTry {i+1}: Using parameters: {params}")
        resp = requests.get(f"{API_BASE_V2}/practice_schedule", headers=headers, params=params)

        print(f"Response status: {resp.status_code}")
        if resp.status_code == 200:
            data = resp.json()
            print(f"Success! Got data with {len(data)} items")
            return data
        elif resp.status_code != 204:  # No content is a valid response
            print("Error response:")
            print(resp.text[:500] + "..." if len(resp.text) > 500 else resp.text)

    # Try with request_key as URL parameter
    print("\nTry 6: Using request_key as URL parameter")
    url = f"{API_BASE_V2}/practice_schedule?request_key={request_key}"
    print(f"URL: {url}")
    resp = requests.get(url)

    print(f"Response status: {resp.status_code}")
    if resp.status_code == 200:
        data = resp.json()
        print(f"Success! Got data with {len(data) if isinstance(data, list) else 'non-list'} items")
        return data
    elif resp.status_code != 204:
        print("Error response:")
        print(resp.text[:500] + "..." if len(resp.text) > 500 else resp.text)

    # If we get here, none of the attempts worked
    print("\nAll attempts failed to get practice schedule data.")
    return {}

def fetch_appointments(request_key):
    """Fetch appointments for a specific date and provider."""
    print(f"Fetching appointments for {TARGET_DATE}...")
    headers = {"Request-Key": request_key}
    params = {"date": TARGET_DATE}

    resp = requests.get(f"{API_BASE_V4}/appointments", headers=headers, params=params)

    if resp.status_code != 200:
        print(f"Error: {resp.status_code}")
        print(resp.text)
        return []

    data = resp.json()
    items = data.get("items", [])

    # Filter to only include appointments for the specified date and provider
    filtered_items = [a for a in items
                     if a.get("date", "").startswith(TARGET_DATE)
                     and a.get("provider_id") == TARGET_PROVIDER]

    return filtered_items

def main():
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return

    # Fetch practice schedule for operatories
    schedule_data = fetch_practice_schedule(request_key)

    # Check if we got any data
    if not schedule_data:
        print("No practice schedule data found.")
        return

    # Extract operatory information
    items = schedule_data.get("items", [])
    print(f"Found {len(items)} items in practice schedule")

    # Create a mapping of operatory IDs to appointment serial numbers
    operatory_map = {}
    for item in items:
        operatory_id = item.get("resource_id")
        events = item.get("events", [])
        appt_sr_nos = [event.get("appointment_sr_no") for event in events if event.get("appointment_sr_no")]
        if operatory_id and appt_sr_nos:
            operatory_map[operatory_id] = appt_sr_nos

    print(f"Found {len(operatory_map)} operatories with scheduled appointments")
    for operatory_id, appt_sr_nos in operatory_map.items():
        print(f"Operatory {operatory_id}: {len(appt_sr_nos)} appointments")

    # Fetch appointments
    appointments = fetch_appointments(request_key)
    print(f"Found {len(appointments)} appointments for provider {TARGET_PROVIDER} on {TARGET_DATE}")

    # Create a reverse mapping of appointment serial numbers to operatory IDs
    appt_to_operatory = {}
    for operatory_id, appt_sr_nos in operatory_map.items():
        for sr_no in appt_sr_nos:
            appt_to_operatory[sr_no] = operatory_id

    # Display appointments with operatory information
    print("\nAppointments with operatory information:")
    print(f"{'TIME':<8}{'PATIENT':<25}{'PROCEDURE':<35}{'OPERATORY':<10}")
    print(f"{'-' * 8}{'-' * 25}{'-' * 35}{'-' * 10}")

    # Sort appointments by time
    sorted_appointments = sorted(appointments, key=lambda a: a.get("time", ""))

    for appt in sorted_appointments:
        time = appt.get("time", "")
        patient_name = appt.get("patient_name", "Unknown Patient")
        description = appt.get("description", "No description")
        appt_sr_no = appt.get("appointment_sr_no")
        operatory_id = appt_to_operatory.get(appt_sr_no, "N/A")

        # Truncate long descriptions
        if len(description) > 32:
            description = description[:29] + "..."

        # Truncate long patient names
        if len(patient_name) > 22:
            patient_name = patient_name[:19] + "..."

        print(f"{time:<8}{patient_name:<25}{description:<35}{operatory_id:<10}")

if __name__ == "__main__":
    main()
