"use strict";exports.id=1369,exports.ids=[1369],exports.modules={71369:(e,t,r)=>{r.r(t),r.d(t,{GET:()=>n,POST:()=>a,updateTranscriptionProgress:()=>i});var s=r(32190);let o=new Map;async function n(e){try{let{searchParams:t}=new URL(e.url),r=t.get("recordingId");if(!r)return s.NextResponse.json({error:"Recording ID is required"},{status:400});let n=o.get(r);if(!n)return s.NextResponse.json({status:"not_found",progress:0,message:"Transcription not started"});return s.NextResponse.json(n)}catch(e){return console.error("Get transcription progress error:",e),s.NextResponse.json({error:`Failed to get progress: ${e.message}`},{status:500})}}async function a(e){try{let{recordingId:t,status:r,progress:n,currentChunk:a,totalChunks:i,message:p}=await e.json();if(!t)return s.NextResponse.json({error:"Recording ID is required"},{status:400});let u={status:r,progress:Math.min(100,Math.max(0,n||0)),currentChunk:a,totalChunks:i,message:p,startTime:o.get(t)?.startTime||Date.now()};return o.set(t,u),s.NextResponse.json({success:!0})}catch(e){return console.error("Update transcription progress error:",e),s.NextResponse.json({error:`Failed to update progress: ${e.message}`},{status:500})}}function i(e,t,r,s,n,a){let i={status:t,progress:Math.min(100,Math.max(0,r)),currentChunk:s,totalChunks:n,message:a,startTime:o.get(e)?.startTime||Date.now()};o.set(e,i)}setInterval(()=>{let e=Date.now();for(let[t,r]of o.entries())("completed"===r.status||"error"===r.status)&&e-(r.startTime||0)>3e5&&o.delete(t)},6e4)}};