name: Validate Deployment Health

on:
  workflow_run:
    workflows: ["Optimized Build and Deploy - Dental Practice Management App"]
    types:
      - completed
  workflow_dispatch:
    inputs:
      app_url:
        description: 'App URL to validate (default: production)'
        required: false
        default: 'https://dentalapp.azurewebsites.net'

jobs:
  validate-deployment:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}
    
    steps:
      - name: Set App URL
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "APP_URL=${{ github.event.inputs.app_url }}" >> $GITHUB_ENV
          else
            echo "APP_URL=https://dentalapp.azurewebsites.net" >> $GITHUB_ENV
          fi

      - name: Comprehensive Application Health Check
        run: |
          echo "🏥 Comprehensive Application Health Check"
          echo "========================================"
          echo "Target URL: $APP_URL"
          echo ""
          
          # Extended timeout for thorough validation
          HEALTH_CHECK_TIMEOUT=600  # 10 minutes
          CHECK_INTERVAL=15         # 15 seconds
          FAILED_CHECKS=()
          
          echo "⏱️  Waiting for application to fully initialize (60 seconds)..."
          sleep 60

      - name: HTTP Response Validation
        run: |
          echo "🔍 HTTP Response Validation"
          echo "============================"
          
          for i in $(seq 1 20); do
            echo "Attempt $i/20: Testing primary endpoint"
            
            # Get detailed response information
            RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code};TIME:%{time_total};SIZE:%{size_download}" \
              --max-time 30 "$APP_URL" || echo "HTTPSTATUS:000;TIME:30;SIZE:0")
            
            HTTP_STATUS=$(echo "$RESPONSE" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d: -f2)
            RESPONSE_TIME=$(echo "$RESPONSE" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
            RESPONSE_SIZE=$(echo "$RESPONSE" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
            
            echo "  HTTP Status: $HTTP_STATUS"
            echo "  Response Time: ${RESPONSE_TIME}s"
            echo "  Response Size: ${RESPONSE_SIZE} bytes"
            
            if [ "$HTTP_STATUS" = "200" ] && [ "$RESPONSE_SIZE" -gt "1000" ]; then
              echo "✅ Application responding correctly with substantial content"
              break
            elif [ "$HTTP_STATUS" = "000" ]; then
              echo "❌ HTTP 000 detected - serious deployment issue"
            elif [ "$HTTP_STATUS" = "503" ] || [ "$HTTP_STATUS" = "502" ]; then
              echo "⚠️  Service unavailable (HTTP $HTTP_STATUS) - may still be starting"
            elif [ "$RESPONSE_SIZE" -lt "1000" ]; then
              echo "⚠️  Response too small (${RESPONSE_SIZE} bytes) - may be error page"
            else
              echo "⚠️  HTTP $HTTP_STATUS - unexpected response"
            fi
            
            if [ $i -eq 20 ]; then
              echo "❌ HTTP validation failed after 20 attempts"
              echo "Final status: HTTP $HTTP_STATUS, Size: $RESPONSE_SIZE bytes"
              exit 1
            fi
            
            sleep 15
          done

      - name: API Endpoints Validation
        run: |
          echo "🧪 API Endpoints Validation"
          echo "============================"
          
          API_ENDPOINTS=(
            "/api/deployment-info:Deployment information"
            "/api/manifest:Application manifest"
            "/api/openai/config:OpenAI configuration status"
            "/api/settings:Application settings"
          )
          
          FAILED_ENDPOINTS=()
          
          for endpoint_info in "${API_ENDPOINTS[@]}"; do
            endpoint=$(echo "$endpoint_info" | cut -d: -f1)
            description=$(echo "$endpoint_info" | cut -d: -f2)
            
            echo "Testing: $APP_URL$endpoint ($description)"
            
            RESPONSE=$(curl -s -w "HTTPSTATUS:%{http_code};TIME:%{time_total}" \
              --max-time 20 "$APP_URL$endpoint" || echo "HTTPSTATUS:000;TIME:20")
            
            STATUS=$(echo "$RESPONSE" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d: -f2)
            TIME=$(echo "$RESPONSE" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
            
            if [ "$STATUS" = "200" ]; then
              echo "✅ $endpoint responding correctly (${TIME}s)"
            elif [ "$STATUS" = "404" ]; then
              echo "ℹ️  $endpoint not found (may be optional)"
            else
              echo "❌ $endpoint failed with HTTP $STATUS"
              FAILED_ENDPOINTS+=("$endpoint:$STATUS")
            fi
          done
          
          if [ ${#FAILED_ENDPOINTS[@]} -gt 0 ]; then
            echo ""
            echo "⚠️  Some API endpoints failed:"
            for failed in "${FAILED_ENDPOINTS[@]}"; do
              echo "  - $failed"
            done
          else
            echo "✅ All critical API endpoints are functional"
          fi

      - name: Static Assets Validation
        run: |
          echo "📁 Static Assets Validation"
          echo "=========================="
          
          STATIC_ASSETS=(
            "/favicon.svg:Favicon"
            "/manifest.json:PWA Manifest"
            "/_next/static:Next.js static assets"
          )
          
          for asset_info in "${STATIC_ASSETS[@]}"; do
            asset=$(echo "$asset_info" | cut -d: -f1)
            description=$(echo "$asset_info" | cut -d: -f2)
            
            echo "Testing: $APP_URL$asset ($description)"
            
            STATUS=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$APP_URL$asset" || echo "000")
            
            if [ "$STATUS" = "200" ]; then
              echo "✅ $asset serving correctly"
            elif [ "$STATUS" = "404" ]; then
              echo "ℹ️  $asset not found (may be optional)"
            else
              echo "⚠️  $asset returned HTTP $STATUS"
            fi
          done

      - name: Performance and Security Headers Check
        run: |
          echo "🛡️  Performance and Security Headers Check"
          echo "=========================================="
          
          echo "Checking HTTP headers..."
          HEADERS=$(curl -s -I --max-time 15 "$APP_URL" || echo "")
          
          # Check for important headers
          if echo "$HEADERS" | grep -i "content-encoding: gzip" > /dev/null; then
            echo "✅ GZIP compression enabled"
          else
            echo "⚠️  GZIP compression not detected"
          fi
          
          if echo "$HEADERS" | grep -i "cache-control" > /dev/null; then
            echo "✅ Cache-Control headers present"
          else
            echo "⚠️  Cache-Control headers missing"
          fi
          
          if echo "$HEADERS" | grep -i "x-frame-options\|content-security-policy" > /dev/null; then
            echo "✅ Security headers detected"
          else
            echo "ℹ️  Basic security headers not detected (may be handled by app)"
          fi

      - name: Environment Configuration Validation
        run: |
          echo "🔧 Environment Configuration Validation"
          echo "======================================"
          
          echo "Testing deployment info for environment validation..."
          DEPLOYMENT_INFO=$(curl -s --max-time 20 "$APP_URL/api/deployment-info" || echo '{"error":"unavailable"}')
          
          if echo "$DEPLOYMENT_INFO" | grep -q '"timestamp"'; then
            echo "✅ Deployment info endpoint working"
            
            # Extract and display key information
            if command -v jq > /dev/null; then
              echo "📋 Deployment Details:"
              echo "$DEPLOYMENT_INFO" | jq -r '. | to_entries[] | "  \(.key): \(.value)"' 2>/dev/null || echo "  Raw: $DEPLOYMENT_INFO"
            else
              echo "📋 Deployment Details (raw):"
              echo "  $DEPLOYMENT_INFO" | head -5
            fi
            
            # Check for build information
            if echo "$DEPLOYMENT_INFO" | grep -q '"commit_hash"'; then
              echo "✅ Build information present"
            fi
          else
            echo "⚠️  Deployment info endpoint not responding as expected"
            echo "Response: $DEPLOYMENT_INFO"
          fi
          
          echo ""
          echo "Testing OpenAI configuration..."
          OPENAI_CONFIG=$(curl -s --max-time 15 "$APP_URL/api/openai/config" || echo '{"error":"unavailable"}')
          
          if echo "$OPENAI_CONFIG" | grep -q '"configured"'; then
            echo "✅ OpenAI configuration endpoint accessible"
          else
            echo "ℹ️  OpenAI configuration endpoint not available (may be protected)"
          fi

      - name: Load Test (Basic)
        run: |
          echo "⚡ Basic Load Test"
          echo "=================="
          
          echo "Performing basic load test (10 concurrent requests)..."
          
          # Create a simple load test
          for i in $(seq 1 10); do
            curl -s -o /dev/null -w "Request $i: %{http_code} (%{time_total}s)\n" \
              --max-time 30 "$APP_URL" &
          done
          
          wait  # Wait for all background jobs to complete
          
          echo "✅ Basic load test completed"

      - name: Final Health Assessment
        run: |
          echo "📊 Final Health Assessment"
          echo "========================="
          
          # Final validation call
          FINAL_CHECK=$(curl -s -w "HTTPSTATUS:%{http_code};TIME:%{time_total};SIZE:%{size_download}" \
            --max-time 30 "$APP_URL" || echo "HTTPSTATUS:000;TIME:30;SIZE:0")
          
          FINAL_STATUS=$(echo "$FINAL_CHECK" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d: -f2)
          FINAL_TIME=$(echo "$FINAL_CHECK" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
          FINAL_SIZE=$(echo "$FINAL_CHECK" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
          
          echo "Final Check Results:"
          echo "  HTTP Status: $FINAL_STATUS"
          echo "  Response Time: ${FINAL_TIME}s"
          echo "  Response Size: ${FINAL_SIZE} bytes"
          echo ""
          
          if [ "$FINAL_STATUS" = "200" ] && [ "$FINAL_SIZE" -gt "1000" ]; then
            echo "🎉 DEPLOYMENT VALIDATION SUCCESSFUL!"
            echo ""
            echo "✅ Application is healthy and responding correctly"
            echo "✅ API endpoints are functional"
            echo "✅ Static assets are serving"
            echo "✅ Performance is acceptable"
            echo ""
            echo "🌐 Application URL: $APP_URL"
            echo "📅 Validation completed: $(date -u)"
          else
            echo "❌ DEPLOYMENT VALIDATION FAILED!"
            echo ""
            echo "The application may have serious issues:"
            echo "  - HTTP Status: $FINAL_STATUS (expected: 200)"
            echo "  - Response Size: $FINAL_SIZE bytes (expected: >1000)"
            echo ""
            echo "Please check:"
            echo "  1. Azure App Service logs"
            echo "  2. Application configuration"
            echo "  3. Environment variables"
            echo "  4. GitHub Secrets configuration"
            
            exit 1
          fi

      - name: Cleanup and Notification
        if: always()
        run: |
          echo ""
          echo "📋 Validation Summary"
          echo "===================="
          echo "Timestamp: $(date -u)"
          echo "Target URL: $APP_URL"
          echo "Workflow: ${{ github.workflow }}"
          echo "Run ID: ${{ github.run_id }}"
          echo ""
          
          if [ "$?" = "0" ]; then
            echo "🟢 Overall Status: PASSED"
            echo "The application is ready for production use."
          else
            echo "🔴 Overall Status: FAILED"
            echo "The application requires attention before production use."
          fi