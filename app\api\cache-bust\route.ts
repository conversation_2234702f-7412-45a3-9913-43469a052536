import { NextResponse } from 'next/server';

export async function GET() {
  const timestamp = new Date().toISOString();
  
  return NextResponse.json({
    message: "🚨 NEW VERSION 2.0.1 IS LIVE! 🚨",
    version: "2.0.1-FORCE-REFRESH",
    timestamp,
    features: {
      fileResolver: "ACTIVE - Handles naming mismatches",
      webUSBOnly: "ACTIVE - No more regular upload",
      debugConsole: "AVAILABLE at /debug-transcription",
      directCalls: "ACTIVE - No more HTTP timeouts"
    },
    deploymentInfo: {
      buildDate: process.env.NEXT_PUBLIC_BUILD_DATE,
      buildTime: process.env.NEXT_PUBLIC_BUILD_TIME,
      buildNumber: process.env.NEXT_PUBLIC_BUILD_NUMBER,
      commitHash: process.env.NEXT_PUBLIC_COMMIT_HASH,
    }
  }, {
    headers: {
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Surrogate-Control': 'no-store'
    }
  });
}