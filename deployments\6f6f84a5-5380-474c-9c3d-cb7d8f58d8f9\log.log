2025-07-14T14:06:18.5466298Z,Updating submodules.,8c7bfa09-1cbb-4da1-a733-3df599f67982,0
2025-07-14T14:06:19.6901964Z,Preparing deployment for commit id '6f6f84a5-5'.,fb36e4ac-5d37-4485-9097-71dac0eb0379,0
2025-07-14T14:06:19.9644154Z,PreDeployment: context.CleanOutputPath False,11955b21-00f3-4f57-bea6-7e9e4fcb0954,0
2025-07-14T14:06:20.0852365Z,PreDeployment: context.OutputPath /home/<USER>/wwwroot,020590ce-a4e6-4e00-a8df-d87001e12f70,0
2025-07-14T14:06:20.2719336Z,Repository path is /tmp/zipdeploy/extracted,b91ffe4f-36e3-4589-96f7-cb5a037909e0,0
2025-07-14T14:06:20.4207485Z,Running oryx build...,9230a261-eaa8-4fa2-a2e9-3c9d5def9368,0
	2025-07-14T14:06:20.4388725Z,Command: oryx build /tmp/zipdeploy/extracted -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log  -i /tmp/8ddc2df9b786f1f -p compress_node_modules=tar-gz | tee /tmp/oryx-build.log,,0
	2025-07-14T14:06:23.0640312Z,Operation performed by Microsoft Oryx&comma; https://github.com/Microsoft/Oryx,,0
	2025-07-14T14:06:23.1234823Z,You can report issues at https://github.com/Microsoft/Oryx/issues,,0
	2025-07-14T14:06:23.2245666Z,,,0
	2025-07-14T14:06:23.2631372Z,Oryx Version: 0.2.20250611.1+0649de32f1279969c9023dd41b389cce4bb94493&comma; Commit: 0649de32f1279969c9023dd41b389cce4bb94493&comma; ReleaseTagName: 20250611.1,,0
	2025-07-14T14:06:23.2845284Z,,,0
	2025-07-14T14:06:23.3231351Z,Build Operation ID: 7d76e097b39784c1,,0
	2025-07-14T14:06:23.3438215Z,Repository Commit : 6f6f84a5-5380-474c-9c3d-cb7d8f58d8f9,,0
	2025-07-14T14:06:23.3750932Z,OS Type           : bookworm,,0
	2025-07-14T14:06:23.4262158Z,Image Type        : githubactions,,0
	2025-07-14T14:06:23.4940951Z,,,0
	2025-07-14T14:06:23.5240484Z,Primary SDK Storage URL: https://oryx-cdn.microsoft.io,,0
	2025-07-14T14:06:23.5458031Z,Backup SDK Storage URL: https://oryxsdks-cdn.azureedge.net,,0
	2025-07-14T14:06:23.5759643Z,Detecting platforms...,,0
	2025-07-14T14:06:27.1100072Z,Detected following platforms:,,0
	2025-07-14T14:06:27.1429075Z,  nodejs: 20.19.3,,0
	2025-07-14T14:06:27.1638106Z,  python: 3.8.18,,0
	2025-07-14T14:06:27.1815761Z,Version '20.19.3' of platform 'nodejs' is not installed. Generating script to install it...,,0
	2025-07-14T14:06:27.2316321Z,Version '3.8.18' of platform 'python' is not installed. Generating script to install it...,,0
	2025-07-14T14:06:27.4330116Z,Detected the following frameworks: Typescript&comma;Next.js,,0
	2025-07-14T14:06:27.8438784Z,Warning: An outdated version of python was detected (3.8.18). Consider updating.\nVersions supported by Oryx: https://github.com/microsoft/Oryx,,0
	2025-07-14T14:06:27.8727601Z,,,0
	2025-07-14T14:06:27.9239733Z,,,0
	2025-07-14T14:06:28.0804708Z,Using intermediate directory '/tmp/8ddc2df9b786f1f'.,,0
	2025-07-14T14:06:28.1011557Z,,,0
	2025-07-14T14:06:28.1415613Z,Copying files to the intermediate directory...,,0
	2025-07-14T14:06:32.6709005Z,Done in 4 sec(s).,,0
	2025-07-14T14:06:32.7677488Z,,,0
	2025-07-14T14:06:32.7997276Z,Source directory     : /tmp/8ddc2df9b786f1f,,0
	2025-07-14T14:06:32.8295261Z,Destination directory: /home/<USER>/wwwroot,,0
	2025-07-14T14:06:32.8779624Z,,,0
	2025-07-14T14:06:32.9079290Z,,,0
	2025-07-14T14:06:32.9303036Z,Downloading and extracting 'nodejs' version '20.19.3' to '/tmp/oryx/platforms/nodejs/20.19.3'...,,0
	2025-07-14T14:06:32.9800240Z,Detected image debian flavor: bookworm.,,0
	2025-07-14T14:06:35.3137292Z,Downloaded in 3 sec(s).,,0
	2025-07-14T14:06:35.3942339Z,Verifying checksum...,,0
	2025-07-14T14:06:35.4999376Z,Extracting contents...,,0
	2025-07-14T14:06:49.0174640Z,performing sha512 checksum for: nodejs...,,0
	2025-07-14T14:06:49.9596412Z,Done in 17 sec(s).,,0
	2025-07-14T14:06:49.9798026Z,,,0
	2025-07-14T14:06:50.0507568Z,,,0
	2025-07-14T14:06:50.0803700Z,Downloading and extracting 'python' version '3.8.18' to '/tmp/oryx/platforms/python/3.8.18'...,,0
	2025-07-14T14:06:50.1831313Z,Detected image debian flavor: bookworm.,,0
	2025-07-14T14:06:53.8914989Z,Downloaded in 3 sec(s).,,0
	2025-07-14T14:06:53.9908331Z,Verifying checksum...,,0
	2025-07-14T14:06:54.0809124Z,Extracting contents...,,0
	2025-07-14T14:07:07.9705426Z,performing sha512 checksum for: python...,,0
	2025-07-14T14:07:10.7747335Z,Done in 20 sec(s).,,0
	2025-07-14T14:07:10.9945660Z,,,0
	2025-07-14T14:07:11.1331450Z,Removing existing manifest file,,0
	2025-07-14T14:07:11.1640818Z,Creating directory for command manifest file if it does not exist,,0
	2025-07-14T14:07:11.2456840Z,Creating a manifest file...,,0
	2025-07-14T14:07:11.2741214Z,Node Build Command Manifest file created.,,0
	2025-07-14T14:07:11.3042066Z,,,0
	2025-07-14T14:07:11.3269604Z,Using Node version:,,0
	2025-07-14T14:07:11.6279459Z,v20.19.3,,0
	2025-07-14T14:07:11.6657028Z,,,0
	2025-07-14T14:07:11.7404673Z,Using Npm version:,,0
	2025-07-14T14:07:13.4643741Z,10.8.2,,0
	2025-07-14T14:07:13.5672265Z,,,0
	2025-07-14T14:07:13.6159888Z,Running 'npm install'...,,0
	2025-07-14T14:07:13.6531870Z,,,0
	2025-07-14T14:08:30.8334674Z,npm warn deprecated node-domexception@1.0.0: Use your platform's native DOMException instead,,1
	2025-07-14T14:10:10.2807941Z,,,0
	2025-07-14T14:10:10.3472609Z,added 444 packages&comma; and audited 445 packages in 3m,,0
	2025-07-14T14:10:10.4282942Z,,,0
	2025-07-14T14:10:10.5171181Z,163 packages are looking for funding,,0
	2025-07-14T14:10:10.6698553Z,  run `npm fund` for details,,0
	2025-07-14T14:10:10.9035156Z,,,0
	2025-07-14T14:10:10.9451539Z,1 low severity vulnerability,,0
	2025-07-14T14:10:11.0843555Z,,,0
	2025-07-14T14:10:11.1225790Z,To address all issues&comma; run:,,0
	2025-07-14T14:10:11.3937040Z,  npm audit fix,,0
	2025-07-14T14:10:11.4435598Z,,,0
	2025-07-14T14:10:11.4746548Z,Run `npm audit` for details.,,0
	2025-07-14T14:10:11.5043394Z,,,0
	2025-07-14T14:10:11.5345453Z,Running 'npm run build'...,,0
	2025-07-14T14:10:11.5733332Z,,,0
	2025-07-14T14:10:12.6270447Z,,,0
	2025-07-14T14:10:12.6677162Z,> dental-schedule-next@0.1.0 build,,0
	2025-07-14T14:10:12.6960532Z,> next build,,0
	2025-07-14T14:10:12.7782048Z,,,0
	2025-07-14T14:10:19.5807789Z,Attention: Next.js now collects completely anonymous telemetry regarding usage.,,0
	2025-07-14T14:10:19.6589140Z,This information is used to shape Next.js' roadmap and prioritize features.,,0
	2025-07-14T14:10:19.7273068Z,You can learn more&comma; including how to opt-out if you'd not like to participate in this anonymous program&comma; by visiting the following URL:,,0
	2025-07-14T14:10:19.7966583Z,https://nextjs.org/telemetry,,0
	2025-07-14T14:10:19.8363405Z,,,0
	2025-07-14T14:10:20.2124175Z,   ▲ Next.js 15.3.3,,0
	2025-07-14T14:10:20.4735133Z,,,0
	2025-07-14T14:10:20.6127801Z,   Creating an optimized production build ...,,0
	2025-07-14T14:13:03.7332700Z, ✓ Compiled successfully in 2.4min,,0
	2025-07-14T14:13:04.0149020Z,   Skipping validation of types,,0
	2025-07-14T14:13:04.1235076Z,   Skipping linting,,0
	2025-07-14T14:13:05.7739379Z,   Collecting page data ...,,0
	2025-07-14T14:13:08.9270306Z,Error: The OPENAI_API_KEY environment variable is missing or empty; either provide it&comma; or instantiate the OpenAI client with an apiKey option&comma; like new OpenAI({ apiKey: 'My API Key' }).,,1
	2025-07-14T14:13:08.9664306Z,    at new d0 (.next/server/chunks/630.js:5:30671),,1
	2025-07-14T14:13:08.9974413Z,    at 5169 (.next/server/app/api/ai/chat/route.js:1:974),,1
	2025-07-14T14:13:09.0301453Z,    at t (.next/server/webpack-runtime.js:1:127),,1
	2025-07-14T14:13:09.1172455Z,    at a (.next/server/app/api/ai/chat/route.js:86:7912),,1
	2025-07-14T14:13:09.1685730Z,    at <unknown> (.next/server/app/api/ai/chat/route.js:86:7947),,1
	2025-07-14T14:13:09.2093330Z,    at t.X (.next/server/webpack-runtime.js:1:1191),,1
	2025-07-14T14:13:09.2595899Z,    at <unknown> (.next/server/app/api/ai/chat/route.js:86:7925),,1
	2025-07-14T14:13:09.3197052Z,    at Object.<anonymous> (.next/server/app/api/ai/chat/route.js:86:7974),,1
	2025-07-14T14:13:09.3482710Z,,,1
	2025-07-14T14:13:09.3715732Z,> Build error occurred,,1
	2025-07-14T14:13:09.4186233Z,[Error: Failed to collect page data for /api/ai/chat] { type: 'Error' },,1
	2025-07-14T14:13:10.4895659Z,npm warn deprecated node-domexception@1.0.0: Use your platform's native DOMException instead\nError: The OPENAI_API_KEY environment variable is missing or empty; either provide it&comma; or instantiate the OpenAI client with an apiKey option&comma; like new OpenAI({ apiKey:[Redacted] [Redacted] API Key' }).\n    at new d0 (.next/server/chunks/630.js:5:30671)\n    at 5169 (.next/server/app/api/ai/chat/route.js:1:974)\n    at t (.next/server/webpack-runtime.js:1:127)\n    at a (.next/server/app/api/ai/chat/route.js:86:7912)\n    at <unknown> (.next/server/app/api/ai/chat/route.js:86:7947)\n    at t.X (.next/server/webpack-runtime.js:1:1191)\n    at <unknown> (.next/server/app/api/ai/chat/route.js:86:7925)\n    at Object.<anonymous> (.next/server/app/api/ai/chat/route.js:86:7974)\n\n> Build error occurred\n[Error: Failed to collect page data for /api/ai/chat] { type: 'Error' }\n/bin/bash -c "oryx build /tmp/zipdeploy/extracted -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log  -i /tmp/8ddc2df9b786f1f -p compress_node_modules=tar-gz | tee /tmp/oryx-build.log ; exit $PIPESTATUS ",,2
2025-07-14T14:13:10.8971145Z,,05a9cddb-db8a-4caa-a481-877988923f82,0
2025-07-14T14:13:11.0660140Z,Generating summary of Oryx build,2ce86e1a-7646-4c99-bba7-bb0c42f97491,0
2025-07-14T14:13:11.2653270Z,Parsing the build logs,cd5b89f6-e431-4200-a426-750ce4efd010,0
2025-07-14T14:13:11.4157765Z,Found 0 issue(s),c9b5a3e2-86df-41e7-a1c1-837f4896da5c,0
2025-07-14T14:13:11.5445757Z,,6471e6fc-4d7b-49c3-8170-4034c7b0df3f,0
2025-07-14T14:13:11.7374981Z,Build Summary :,1827a5ee-40e0-46bb-b01c-c4d28ffb3b1d,0
2025-07-14T14:13:11.8630417Z,===============,4e4762d9-9de2-4450-8773-4ce281b26fc6,0
2025-07-14T14:13:11.9681356Z,Errors (0),1b21bfb7-645d-4cbb-a397-1a1e482f1d4f,0
2025-07-14T14:13:12.0864547Z,Warnings (0),4a254c8f-87e9-4311-a6d3-4d7fb0de8cf1,0
2025-07-14T14:13:12.2292204Z,,c2f311e8-c989-41f2-957a-f77364f454a4,0
2025-07-14T14:13:13.4389743Z,Deployment Failed. deployer = Push-Deployer deploymentPath = ZipDeploy. Extract zip. Remote build.,236aa3ba-43c8-4606-9d02-a2b502b5d586,0
