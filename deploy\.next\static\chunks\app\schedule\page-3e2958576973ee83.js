(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7826],{3287:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var n=r(5155),i=r(2115),a=r(5695),o=r(9836),s=r(838),l=r(6496),d=r(4679);function c(e){if(!e)return 0;try{let t=0,r=0,n=e.match(/(\d+):(\d+)\s*(AM|PM|am|pm)?/i),i=e.match(/^(\d+):(\d+)$/),a=e.match(/(\d+)\s*(AM|PM|am|pm)/i),o=e.match(/^(\d{2})(\d{2})$/);if(n){t=parseInt(n[1],10),r=parseInt(n[2],10);let e=n[3]?n[3].toUpperCase():null;"PM"===e&&t<12?t+=12:"AM"===e&&12===t&&(t=0)}else if(i)t=parseInt(i[1],10),r=parseInt(i[2],10);else if(a){t=parseInt(a[1],10);let e=a[2].toUpperCase();"PM"===e&&t<12?t+=12:"AM"===e&&12===t&&(t=0)}else if(o)t=parseInt(o[1],10),r=parseInt(o[2],10);else if("noon"===e.toLowerCase())t=12,r=0;else{if("midnight"!==e.toLowerCase())return console.error("Unrecognized time format:",e),0;t=0,r=0}return 60*t+r}catch(t){return console.error("Time parsing error:",t,e),0}}function p(e){if(!e)return"";try{var t;let r,n;return t=c(e),r=Math.floor(t/60),n="AM",r>=12&&(n="PM",r>12&&(r-=12)),0===r&&(r=12),"".concat(r,":").concat((t%60).toString().padStart(2,"0")," ").concat(n)}catch(t){return console.error("Time formatting error:",t,e),e}}function m(e){let{operatory:t,appointments:r,onAppointmentClick:i,onPatientClick:a,displayStartHour:o,displayEndHour:s}=e,l=r.filter(e=>e&&e.operatory===t).map(e=>(function(e,t){try{if(!e||"object"!=typeof e)return console.error("Invalid appointment object:",e),null;return{id:String(e.id||"".concat(t,"-").concat(Math.random().toString(36).substring(2,9))),appointment_sr_no:e.appointment_sr_no?String(e.appointment_sr_no):void 0,patient_name:String(e.patient_name||e.patientName||"No Patient"),patientName:String(e.patient_name||e.patientName||"No Patient"),startTime:String(e.startTime||"8:00 AM"),endTime:String(e.endTime||"9:00 AM"),length:Number(e.length||60),type:String(e.type||e.description||"Unknown"),description:String(e.description||""),notes:String(e.notes||""),operatory:String(e.operatory||t),patient_id:e.patient_id?String(e.patient_id):e.patientId?String(e.patientId):"",provider:(()=>{try{if(e.provider)if("object"==typeof e.provider)return e.provider.name||JSON.stringify(e.provider);else return String(e.provider);if(e.providerName)if("object"==typeof e.providerName)return e.providerName.name||JSON.stringify(e.providerName);else return String(e.providerName);return""}catch(e){return console.error("Error processing provider in sanitizeAppointment:",e),""}})(),status:e.status?String(e.status):"",isBlocked:!!e.isBlocked}}catch(e){return console.error("Error sanitizing appointment:",e),null}})(e,t)).filter(Boolean),m=[];for(let e=o;e<=s;e++)for(let t=0;t<60;t+=10)m.push({isHour:0===t,slotIndex:(e-o)*6+t/10});let x=l.map((e,t)=>{let r=c(e.startTime),n=Number(e.length)||60,i=!1;for(let e=0;e<t;e++){let t=l[e];if(!h(t))continue;let a=c(t.startTime),o=a+(Number(t.length)||60),s=r+n;if(r<o&&s>a){i=!0;break}}return{...e,topPosition:(r-60*o)/10*24,height:n/10*24,hasOverlap:i,index:t}});return(0,n.jsx)("div",{className:"min-w-0 h-full bg-gray-300 dark:bg-gray-700",children:(0,n.jsxs)("div",{className:"relative",style:{height:"".concat(24*m.length,"px")},children:[m.map(e=>(0,n.jsx)("div",{className:"absolute left-0 right-0 pointer-events-none",style:{top:"".concat(24*e.slotIndex,"px"),height:"24px",borderTop:e.isHour?"2px solid #4b5563":"1px solid #6b7280"}},"grid-".concat(e.slotIndex))),x.map(e=>h(e)?(0,n.jsx)("div",{style:{position:"absolute",top:"".concat(e.topPosition,"px"),left:e.hasOverlap?"50%":"0",width:e.hasOverlap?"50%":"100%",height:"".concat(e.height,"px"),zIndex:10+e.index},children:(0,n.jsxs)("div",{className:"h-full w-full bg-gray-50 dark:bg-gray-600 rounded cursor-pointer border-l-4 text-xs border border-gray-700 dark:border-gray-300 ".concat(function(e){let t="";if(e.provider&&"object"==typeof e.provider){let r=String(e.provider.href||"").split("/");t=r[r.length-1].replace(/[\"'\s]+$/,"")}else e.provider&&(t=String(e.provider));if(t.startsWith("LL")||t.startsWith("DL"))return"border-l-purple-400";if(t.startsWith("GO")||t.includes("GO"))return"border-l-green-500";if(t.startsWith("DAZ")||t.includes("DAZ")||t.includes("DZ"))return"border-l-blue-300";if(t.startsWith("NS")||t.includes("NS"))return"border-l-orange-300";if("XOFF"===t)return"border-l-gray-400";return"border-l-blue-500"}(e)),onClick:()=>i(e),style:{padding:"2px 4px",display:"flex",flexDirection:"column",justifyContent:"flex-start",overflow:"hidden",boxSizing:"border-box"},children:[(0,n.jsxs)("div",{className:"w-full",children:[(0,n.jsxs)("div",{className:"sm:hidden",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,n.jsx)("div",{className:"font-semibold text-blue-600 dark:text-blue-400 text-xs leading-tight cursor-pointer hover:text-blue-800 dark:hover:text-blue-300 hover:underline flex-1 transition-colors duration-200",onClick:t=>null==a?void 0:a(e,t),title:"View patient details",children:(0,d.Qr)(e.patient_name)}),(0,n.jsx)("button",{className:"text-xs text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 cursor-pointer ml-2 px-2 py-1 rounded font-medium shadow-sm hover:shadow-md transition-all duration-200 border border-blue-700 dark:border-blue-400",onClick:t=>{t.stopPropagation(),i(e)},title:"View appointment details",children:"Appt"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[u(e)&&(0,n.jsx)("span",{className:"text-gray-600 dark:text-gray-400 text-xs",children:u(e)}),(0,n.jsx)("div",{className:"text-gray-600 dark:text-gray-400 font-medium text-xs",children:p(e.startTime)})]}),e.type&&(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("div",{className:"text-gray-600 dark:text-gray-300 text-xs truncate flex-1 mr-2",children:e.type}),e.patient_id&&"No Patient"!==e.patient_name&&"Unknown Patient"!==e.patient_name&&(0,n.jsx)("a",{href:"https://clinic.overjet.ai/app/fmx/dailypatients/".concat(e.patient_id),target:"_blank",rel:"noopener noreferrer",className:"text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 text-xs font-bold tracking-wide transition-colors flex-shrink-0 bg-purple-50 hover:bg-purple-100 dark:bg-purple-900/20 dark:hover:bg-purple-900/30 px-1 py-0.5 rounded",onClick:e=>e.stopPropagation(),title:"View patient X-rays in Overjet AI",children:"Overjet"})]})]}),(0,n.jsxs)("div",{className:"hidden sm:flex items-center justify-between w-full",children:[(0,n.jsxs)("div",{className:"flex items-center gap-1 flex-1 min-w-0",children:[(0,n.jsx)("span",{className:"font-semibold text-blue-600 dark:text-blue-400 truncate cursor-pointer hover:text-blue-800 dark:hover:text-blue-300 hover:underline transition-colors duration-200",onClick:t=>null==a?void 0:a(e,t),title:"View patient details",children:(0,d.Qr)(e.patient_name)}),u(e)&&(0,n.jsx)("span",{className:"text-gray-600 dark:text-gray-400 text-xs",children:u(e)})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"text-gray-600 dark:text-gray-400 font-medium text-xs",children:p(e.startTime)}),(0,n.jsx)("button",{className:"text-xs text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 cursor-pointer px-2 py-1 rounded font-medium shadow-sm hover:shadow-md transition-all duration-200 border border-blue-700 dark:border-blue-400",onClick:t=>{t.stopPropagation(),i(e)},title:"View appointment details",children:"Appt"})]})]})]}),e.type&&(0,n.jsxs)("div",{className:"hidden sm:flex mt-1 items-center justify-between",children:[(0,n.jsx)("div",{className:"text-gray-600 dark:text-gray-300 text-xs truncate flex-1 mr-2",children:e.type}),e.patient_id&&"No Patient"!==e.patient_name&&"Unknown Patient"!==e.patient_name&&(0,n.jsx)("a",{href:"https://clinic.overjet.ai/app/fmx/dailypatients/".concat(e.patient_id),target:"_blank",rel:"noopener noreferrer",className:"text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 text-xs font-bold tracking-wide transition-colors flex-shrink-0 bg-purple-50 hover:bg-purple-100 dark:bg-purple-900/20 dark:hover:bg-purple-900/30 px-2 py-1 rounded",onClick:e=>e.stopPropagation(),title:"View patient X-rays in Overjet AI",children:"Overjet"})]})]})},e.id):(0,n.jsx)("div",{style:{position:"absolute",top:"".concat(e.topPosition,"px"),left:"0",width:"100%",height:"".concat(e.height,"px"),zIndex:10+e.index},children:(0,n.jsx)(b,{note:e,onClick:i})},e.id))]})})}function u(e){let t=e.age||"",r=(e.gender||"").charAt(0).toUpperCase()||"";return t&&r?"".concat(t).concat(r):""}function x(e){let{operatories:t,appointments:r,onAppointmentClick:a,onPatientClick:o}=e,[s,l]=(0,i.useState)(!1),[d,u]=(0,i.useState)(!1),[x,b]=(0,i.useState)(0);(0,i.useEffect)(()=>{u(!0);let e=()=>{l(document.documentElement.classList.contains("dark"))};e();let t=new MutationObserver(e);return t.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),()=>t.disconnect()},[]);let g=(0,i.useMemo)(()=>t.filter(e=>r.some(t=>t&&t.operatory===e)),[t,r]),f=Math.ceil(g.length/2),y=(0,i.useMemo)(()=>{if(window.innerWidth<640){let e=2*x;return g.slice(e,e+2)}return g},[g,x]),{displayStartHour:v,displayEndHour:k}=(0,i.useMemo)(()=>{let e=8,t=17;if(r&&r.length>0){r.forEach(r=>{if(h(r)){let n=c(r.startTime),i=c(r.endTime);n>0&&(e=Math.min(e,Math.floor(n/60))),i>0&&(t=Math.max(t,Math.ceil(i/60)))}}),e=Math.max(0,e-1);let n=(t=Math.min(23,t+1))-e;if(n<8){let r=8-n,i=Math.floor(r/2),a=Math.ceil(r/2);e=Math.max(0,e-i),t=Math.min(23,t+a)}}return e>=t&&(e=8,t=17),{displayStartHour:e,displayEndHour:t}},[r]),j=(0,i.useMemo)(()=>{let e=[];for(let t=v;t<=k;t++)for(let r=0;r<60;r+=10){let n=0===r,i=n?p("".concat(t,":00")):"";e.push({time:i,isHour:n,slotIndex:(t-v)*6+r/10})}return e},[v,k]);return(0,n.jsxs)("div",{className:"space-y-4",children:[f>1&&(0,n.jsxs)("div",{className:"sm:hidden flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3 shadow",children:[(0,n.jsxs)("button",{onClick:()=>{b(e=>Math.max(0,e-1))},disabled:0===x,className:"flex items-center px-3 py-2 rounded-md text-sm font-medium ".concat(0===x?"text-gray-400 dark:text-gray-600 cursor-not-allowed":"text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"),children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Previous"]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:y.map(e=>e).join(", ")}),(0,n.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-500",children:["(",x+1," of ",f,")"]})]}),(0,n.jsxs)("button",{onClick:()=>{b(e=>Math.min(f-1,e+1))},disabled:x===f-1,className:"flex items-center px-3 py-2 rounded-md text-sm font-medium ".concat(x===f-1?"text-gray-400 dark:text-gray-600 cursor-not-allowed":"text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20"),children:["Next",(0,n.jsx)("svg",{className:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]}),(0,n.jsxs)("div",{className:"flex border-b-2 border-gray-600 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-t-lg",children:[(0,n.jsx)("div",{className:"flex items-center justify-center",style:{width:"4rem",borderRight:"2px solid #4b5563"},children:(0,n.jsx)("span",{className:"text-xs font-bold text-gray-700 dark:text-gray-300",children:"Time"})}),y.map((e,t)=>(0,n.jsx)("div",{className:"flex-1 flex items-center justify-center py-2 px-1",style:{borderRight:t<y.length-1?"1px solid #6b7280":"none"},children:(0,n.jsx)("span",{className:"text-sm font-bold text-gray-900 dark:text-white truncate",children:e})},"header-".concat(e)))]}),(0,n.jsxs)("div",{className:"schedule-timeline flex h-full border-2 border-gray-600 dark:border-gray-600 border-t-0 rounded-b-lg overflow-hidden shadow-lg bg-gray-300 dark:bg-gray-700",children:[(0,n.jsxs)("div",{className:"relative",style:{width:"4rem",borderRight:"2px solid #4b5563"},children:[j.map(e=>(0,n.jsx)("div",{className:"schedule-grid-line absolute left-0 right-0 h-6",style:{top:"".concat(24*e.slotIndex,"px"),width:"100%",borderTop:e.isHour?"2px solid #4b5563":"1px solid #6b7280"}},"time-line-".concat(e.slotIndex))),j.map(e=>e.isHour&&(0,n.jsx)("div",{className:"h-6 flex items-center justify-end pr-2",style:{position:"absolute",top:"".concat(24*e.slotIndex,"px"),width:"100%",fontSize:"0.75rem"},children:(0,n.jsx)("span",{style:{color:s?"#e5e7eb":"#374151",fontWeight:"bold",opacity:1},children:e.time})},"time-label-div-".concat(e.slotIndex)))]}),y.map((e,t)=>(0,n.jsx)("div",{className:"flex-1 min-w-0 bg-gray-300 dark:bg-gray-700",style:{borderRight:t<y.length-1?"1px solid #6b7280":"none"},children:(0,n.jsx)(m,{operatory:e,appointments:r,onAppointmentClick:a,onPatientClick:o,displayStartHour:v,displayEndHour:k})},e))]})]})}function h(e){return!(!e||"object"!=typeof e||e.isBlocked||e.type&&e.type.toLowerCase().includes("note")||e.description&&e.description.toLowerCase().includes("note")||e.patient_name&&e.patient_name.toLowerCase().includes("note"))&&e.patient_name&&"No Patient"!==e.patient_name&&""!==e.patient_name.trim()&&!e.patient_name.toLowerCase().includes("no patient")}function b(e){let{note:t,onClick:r}=e;try{if(!t||"object"!=typeof t)return console.error("Invalid note object:",t),null;let e="";return e=t.description&&""!==t.description.trim()?String(t.description).trim():t.type&&""!==t.type.trim()?String(t.type).trim():t.patient_name&&""!==t.patient_name.trim()&&"No Patient"!==t.patient_name&&!t.patient_name.toLowerCase().includes("no patient")?String(t.patient_name).trim():"Schedule Note",(0,n.jsx)("div",{className:"schedule-note-card bg-yellow-300 dark:bg-sky-900 rounded cursor-pointer border-l-3 border-yellow-700 dark:border-sky-400 shadow-sm overflow-hidden",onClick:e=>{e.preventDefault(),"function"==typeof r&&r(t)},style:{minHeight:"24px",padding:"0.5rem"},children:(0,n.jsx)("div",{className:"text-yellow-900 dark:text-sky-100 w-full text-xs font-medium leading-snug break-words",style:{display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden",textOverflow:"ellipsis",maxHeight:"100%"},children:e})})}catch(e){return console.error("Error rendering schedule note:",e),null}}function g(){let e,t=(0,a.useRouter)(),r=(0,a.useSearchParams)(),[d,c]=(0,i.useState)(!0);(0,i.useEffect)(()=>{c(!document.referrer.includes("operatories"))},[]);let p=(null==r?void 0:r.get("date"))||"";if(p&&p.match(/^\d{4}-\d{2}-\d{2}$/))e=p;else{let t=new Date;e=(0,o.GP)(t,"yyyy-MM-dd")}let m=[];try{m=(m=(null==r?void 0:r.has("operatories[]"))?r.getAll("operatories[]"):(null==r?void 0:r.has("operatories"))?(r.get("operatories")||"").split(",").filter(Boolean):["DL01","DL02"]).map(e=>String(e).trim()).filter(e=>e&&"DAZ1"!==e&&"DAZ2"!==e),0===m.length&&(m=["DL01","DL02"])}catch(e){console.error("Error processing operatories:",e),m=["DL01","DL02"]}let u=[...m].sort((e,t)=>{var r,n;let i=(null==(r=e.match(/^[A-Z]+/))?void 0:r[0])||"",a=(null==(n=t.match(/^[A-Z]+/))?void 0:n[0])||"",o=parseInt(e.replace(/^[A-Z]+/,"")||"0",10),s=parseInt(t.replace(/^[A-Z]+/,"")||"0",10);return"DL"===i&&"DL"!==a?-1:"DL"!==i&&"DL"===a?1:i===a?o-s:i.localeCompare(a)}),[h,b]=(0,i.useState)(null),[g,f,y]=e.split("-").map(Number),v=new Date(g,f-1,y,12,0,0),k=()=>{let e=new Date(g,f-1,y);e.setDate(e.getDate()-1);let r=(0,o.GP)(e,"yyyy-MM-dd");t.push("/schedule?date=".concat(r))},j=()=>{let e=new Date(g,f-1,y);e.setDate(e.getDate()+1);let r=(0,o.GP)(e,"yyyy-MM-dd");t.push("/schedule?date=".concat(r))};(0,i.useEffect)(()=>{if(!p){let e=(0,o.GP)(new Date,"yyyy-MM-dd");t.push("/operatories?date=".concat(e))}},[p,t]);let N=new URLSearchParams;N.append("date",e),u.forEach(e=>N.append("operatories[]",e));let w=N.toString(),C=e&&u.length>0?"/api/appointments?".concat(w):null,S=(0,i.useCallback)(async e=>{let t=await fetch(e);if(!t.ok)throw Error("Failed to fetch appointments: ".concat(t.status));return t.json()},[]),{data:_=[],error:M,isLoading:P,mutate:L}=(0,s.Ay)(C,S,{revalidateOnFocus:!0,revalidateOnReconnect:!0,dedupingInterval:6e4,refreshInterval:0,errorRetryCount:2,shouldRetryOnError:!1});return(0,n.jsxs)("div",{className:"schedule-page min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,n.jsx)(l.z,{title:"Dentalapp",showBackButton:!0,onBackClick:()=>{d?t.push("/"):t.back()},backButtonLabel:d?"Back to Home":"Back to Operatories",activeTab:"schedule"}),(0,n.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsxs)("div",{className:"sm:hidden space-y-3 mb-2",children:[(0,n.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white text-center",children:["Schedule for ",v.toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric",year:"numeric",timeZone:"UTC"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-center space-x-3",children:[(0,n.jsx)("button",{onClick:k,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors min-w-[80px]",title:"Previous Day",children:"← Prev"}),(0,n.jsx)("button",{onClick:j,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors min-w-[80px]",title:"Next Day",children:"Next →"})]})]}),(0,n.jsxs)("div",{className:"hidden sm:flex items-center justify-between mb-2",children:[(0,n.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:["Schedule for ",v.toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric",timeZone:"UTC"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("button",{onClick:k,className:"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors",title:"Previous Day",children:"← Prev"}),(0,n.jsx)("button",{onClick:j,className:"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors",title:"Next Day",children:"Next →"})]})]}),(0,n.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Operatories: ",u.join(", ")]})]}),P?(0,n.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):M?(0,n.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative",role:"alert",children:[(0,n.jsx)("strong",{className:"font-bold",children:"Error: "}),(0,n.jsx)("span",{className:"block sm:inline",children:(null==M?void 0:M.message)||"Failed to fetch appointments"})]}):_&&0!==_.length?(0,n.jsx)(x,{operatories:u,appointments:_,onAppointmentClick:e=>{e.appointment_sr_no?t.push("/appointment/".concat(e.appointment_sr_no)):b(e)},onPatientClick:(e,r)=>{r.stopPropagation(),e.patient_id&&"No Patient"!==e.patient_name&&"Unknown Patient"!==e.patient_name&&t.push("/patient/".concat(e.patient_id))}}):(0,n.jsxs)("div",{className:"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative",role:"alert",children:[(0,n.jsx)("strong",{className:"font-bold",children:"No appointments found "}),(0,n.jsx)("span",{className:"block sm:inline",children:"for the selected date and operatories."})]})]})]})}function f(){return(0,n.jsx)(i.Suspense,{fallback:(0,n.jsx)("div",{children:"Loading schedule..."}),children:(0,n.jsx)(g,{})})}},4679:(e,t,r)=>{"use strict";function n(e){if(!e||""===e.trim())return"No Patient";let t=String(e).trim();if(t.includes(",")){let e=t.split(",").map(e=>e.trim());if(e.length>=2){var r;let t=e[0].trim(),n=null==(r=e[1].split(" ")[0])?void 0:r.trim();if(n&&t){let e=n.charAt(0).toUpperCase()+n.slice(1).toLowerCase(),r=t.toUpperCase();return"".concat(e," ").concat(r)}}}let n=t.split(" ").filter(e=>e.trim());if(0===n.length)return"No Patient";if(1===n.length)return n[0].charAt(0).toUpperCase()+n[0].slice(1).toLowerCase();if(n.length>=2){let e="",t="",r=n[0],i=n[n.length-1];2===n.length||r===r.toUpperCase()&&i!==i.toUpperCase()?(e=i,t=r):(i.toUpperCase(),e=r,t=i);let a=e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),o=t.toUpperCase();return"".concat(a," ").concat(o)}return t}function i(e){var t,r;let i=null==(t=e.firstName)?void 0:t.trim(),a=null==(r=e.lastName)?void 0:r.trim();if(i&&a){let e=i.charAt(0).toUpperCase()+i.slice(1).toLowerCase(),t=a.toUpperCase();return"".concat(e," ").concat(t)}if(i)return i.charAt(0).toUpperCase()+i.slice(1).toLowerCase();if(a)return a.toUpperCase();let o=[e.firstName,e.middleName,e.lastName].filter(Boolean);return o.length>0?n(o.join(" ")):"No Patient"}r.d(t,{Qr:()=>n,hG:()=>i})},8203:(e,t,r)=>{Promise.resolve().then(r.bind(r,3287))}},e=>{var t=t=>e(e.s=t);e.O(0,[8096,6496,7358],()=>t(8203)),_N_E=e.O()}]);