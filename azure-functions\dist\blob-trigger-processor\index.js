"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.blobTriggerProcessor = blobTriggerProcessor;
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
// Import our enhanced validation and processing logic
const AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING;
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
const CONTAINER_NAME = 'recordings';
/**
 * Azure Function triggered when a new audio file is uploaded to blob storage
 * Automatically processes the file for transcription and summarization
 */
function blobTriggerProcessor(blob, context) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, _b, _c, _d;
        const blobName = (_a = context.triggerMetadata) === null || _a === void 0 ? void 0 : _a.name;
        // Only process audio files, ignore JSON metadata files
        if (!isAudioFile(blobName)) {
            context.log(`⏭️ Skipping non-audio file: ${blobName}`);
            return;
        }
        context.log(`🎵 NEW AUDIO FILE DETECTED: ${blobName}`);
        context.log(`📊 File size: ${blob.length} bytes`);
        try {
            // Initialize Azure Blob Service
            const blobServiceClient = storage_blob_1.BlobServiceClient.fromConnectionString(AZURE_STORAGE_CONNECTION_STRING);
            const containerClient = blobServiceClient.getContainerClient(CONTAINER_NAME);
            // Check if file is already processed
            const baseName = blobName.replace(/\.[^/.]+$/, '');
            const transcriptionJsonPath = `${baseName}.json`;
            const failureJsonPath = `${baseName}.failure.json`;
            // Check if already processed (success or permanent failure)
            const transcriptionExists = yield checkFileExists(containerClient, transcriptionJsonPath);
            const failureExists = yield checkFileExists(containerClient, failureJsonPath);
            if (transcriptionExists) {
                context.log(`✅ File already processed: ${blobName}`);
                return;
            }
            if (failureExists) {
                context.log(`❌ File previously failed (permanent): ${blobName}`);
                return;
            }
            // Download and validate the audio file
            context.log(`📥 Downloading file for validation: ${blobName}`);
            const blobClient = containerClient.getBlobClient(blobName);
            const downloadResponse = yield blobClient.download(0);
            if (!downloadResponse.readableStreamBody) {
                throw new Error('Failed to download blob content');
            }
            const audioBuffer = yield streamToBuffer(downloadResponse.readableStreamBody);
            context.log(`📁 Downloaded ${audioBuffer.length} bytes`);
            // Enhanced audio validation
            const validationResult = yield validateAudioFile(audioBuffer, blobName);
            if (!validationResult.isValid) {
                context.log(`❌ Audio validation failed: ${validationResult.error}`);
                // Save permanent failure status
                const failureData = {
                    fileName: blobName,
                    error: validationResult.error,
                    failureType: 'validation_failed',
                    recommendations: (_b = validationResult.details) === null || _b === void 0 ? void 0 : _b.recommendations,
                    failedAt: new Date().toISOString(),
                    isPermanentFailure: true,
                    processedBy: 'azure-function-blob-trigger',
                    validationDetails: {
                        fileSize: audioBuffer.length,
                        entropy: (_c = validationResult.details) === null || _c === void 0 ? void 0 : _c.entropy,
                        dataRatio: (_d = validationResult.details) === null || _d === void 0 ? void 0 : _d.dataRatio
                    }
                };
                yield uploadJsonToBlob(containerClient, failureJsonPath, failureData);
                context.log(`💾 Permanent failure saved: ${failureJsonPath}`);
                return;
            }
            context.log(`✅ Audio validation passed for: ${blobName}`);
            // Process the file for transcription
            yield processAudioFile(context, containerClient, blobName, audioBuffer);
        }
        catch (error) {
            context.log(`❌ Error processing ${blobName}:`, error);
            // Don't create failure file for infrastructure errors - these should be retryable
            // Azure Functions will automatically retry based on the retry policy
        }
    });
}
;
/**
 * Check if file is an audio file based on extension
 */
function isAudioFile(fileName) {
    const audioExtensions = ['.mp3', '.wav', '.m4a', '.mp4', '.webm', '.ogg', '.aac', '.flac'];
    const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return audioExtensions.includes(ext);
}
/**
 * Check if a file exists in blob storage
 */
function checkFileExists(containerClient, fileName) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const blobClient = containerClient.getBlobClient(fileName);
            return yield blobClient.exists();
        }
        catch (_a) {
            return false;
        }
    });
}
/**
 * Convert readable stream to buffer
 */
function streamToBuffer(readableStream) {
    return __awaiter(this, void 0, void 0, function* () {
        return new Promise((resolve, reject) => {
            const chunks = [];
            readableStream.on('data', (data) => {
                chunks.push(data instanceof Buffer ? data : Buffer.from(data));
            });
            readableStream.on('end', () => {
                resolve(Buffer.concat(chunks));
            });
            readableStream.on('error', reject);
        });
    });
}
/**
 * Enhanced audio file validation (simplified version for Azure Function)
 */
function validateAudioFile(buffer, fileName) {
    return __awaiter(this, void 0, void 0, function* () {
        const MIN_FILE_SIZE = 1024; // 1KB minimum
        const MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB maximum
        const MIN_DATA_RATIO = 0.1;
        const MIN_ENTROPY = 2.0;
        // Check file size
        if (buffer.length < MIN_FILE_SIZE) {
            return {
                isValid: false,
                error: `File too small (${buffer.length} bytes). Minimum size is ${MIN_FILE_SIZE} bytes.`,
                details: {
                    recommendations: ['Check if file was properly recorded', 'Re-upload if corrupted'],
                    fileSize: buffer.length
                }
            };
        }
        if (buffer.length > MAX_FILE_SIZE) {
            return {
                isValid: false,
                error: `File too large (${(buffer.length / 1024 / 1024).toFixed(1)}MB). Maximum size is ${MAX_FILE_SIZE / 1024 / 1024}MB.`,
                details: {
                    recommendations: ['Split audio into smaller segments', 'Compress the audio file'],
                    fileSize: buffer.length
                }
            };
        }
        // Check for all-zero content
        const hasContent = buffer.some(byte => byte !== 0);
        if (!hasContent) {
            return {
                isValid: false,
                error: 'File contains only null bytes - likely corrupted',
                details: {
                    recommendations: ['Re-upload the file', 'Check source recording device']
                }
            };
        }
        // Check data ratio
        const nonZeroBytes = buffer.filter(byte => byte !== 0).length;
        const dataRatio = nonZeroBytes / buffer.length;
        if (dataRatio < MIN_DATA_RATIO) {
            return {
                isValid: false,
                error: `File appears to be mostly empty (${Math.round(dataRatio * 100)}% data)`,
                details: {
                    recommendations: ['File may be corrupted during upload', 'Try re-recording'],
                    dataRatio
                }
            };
        }
        // Check for HTML content
        const beginning = buffer.subarray(0, 100).toString('utf8', 0, 100);
        if (beginning.includes('<!DOCTYPE') || beginning.includes('<html>')) {
            return {
                isValid: false,
                error: 'File contains HTML content instead of audio data',
                details: {
                    recommendations: ['File download may have failed', 'Re-download the original file']
                }
            };
        }
        // Calculate entropy
        const entropy = calculateEntropy(buffer.subarray(0, Math.min(4096, buffer.length)));
        if (entropy < MIN_ENTROPY) {
            return {
                isValid: false,
                error: `File has very low entropy (${entropy.toFixed(2)}) - likely not valid audio`,
                details: {
                    recommendations: ['File may not contain valid audio data', 'Convert to supported format'],
                    entropy
                }
            };
        }
        return {
            isValid: true,
            details: {
                fileSize: buffer.length,
                dataRatio: Math.round(dataRatio * 100) / 100,
                entropy: Math.round(entropy * 100) / 100
            }
        };
    });
}
/**
 * Calculate Shannon entropy
 */
function calculateEntropy(buffer) {
    const frequencies = new Array(256).fill(0);
    for (const byte of buffer) {
        frequencies[byte]++;
    }
    let entropy = 0;
    const length = buffer.length;
    for (const freq of frequencies) {
        if (freq > 0) {
            const probability = freq / length;
            entropy -= probability * Math.log2(probability);
        }
    }
    return entropy;
}
/**
 * Process audio file for transcription using your existing API
 */
function processAudioFile(context, containerClient, blobName, audioBuffer) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        context.log(`🎵 Starting transcription for: ${blobName}`);
        try {
            // Call your existing transcription API
            const transcriptionUrl = process.env.TRANSCRIPTION_API_URL || 'https://dentalapp-5va08j5gu-suncoastdcs-projects.vercel.app/api/voice/transcribe';
            const response = yield fetch(transcriptionUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Internal-Call': 'true' // Skip auth for internal calls
                },
                body: JSON.stringify({
                    fileName: blobName,
                    forceRetranscribe: false
                })
            });
            if (!response.ok) {
                const errorText = yield response.text();
                throw new Error(`Transcription API failed: ${response.status} ${errorText}`);
            }
            const result = yield response.json();
            context.log(`✅ Transcription completed for: ${blobName} (${((_a = result.transcription) === null || _a === void 0 ? void 0 : _a.length) || 0} chars)`);
            // Generate summary if transcription was successful
            if (result.transcription && result.transcription.length > 50) {
                yield generateSummary(context, containerClient, blobName, result.transcription);
            }
        }
        catch (error) {
            context.log(`❌ Failed to process ${blobName}:`, error);
            throw error; // Let Azure Functions handle the retry
        }
    });
}
/**
 * Generate summary using your existing API
 */
function generateSummary(context, containerClient, blobName, transcription) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        context.log(`📝 Starting summarization for: ${blobName}`);
        try {
            const summaryUrl = process.env.SUMMARY_API_URL || 'https://dentalapp-5va08j5gu-suncoastdcs-projects.vercel.app/api/voice/summarize';
            const response = yield fetch(summaryUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Internal-Call': 'true'
                },
                body: JSON.stringify({
                    fileName: blobName,
                    transcription: transcription
                })
            });
            if (!response.ok) {
                const errorText = yield response.text();
                context.log(`⚠️ Summarization failed for ${blobName}: ${response.status} ${errorText}`);
                return; // Don't fail the whole process if summarization fails
            }
            const result = yield response.json();
            context.log(`✅ Summarization completed for: ${blobName} (${((_a = result.summary) === null || _a === void 0 ? void 0 : _a.length) || 0} chars)`);
        }
        catch (error) {
            context.log(`⚠️ Failed to generate summary for ${blobName}:`, error);
            // Don't throw - summarization is not critical
        }
    });
}
/**
 * Upload JSON data to blob storage
 */
function uploadJsonToBlob(containerClient, fileName, data) {
    return __awaiter(this, void 0, void 0, function* () {
        const blobClient = containerClient.getBlockBlobClient(fileName);
        const jsonContent = JSON.stringify(data, null, 2);
        yield blobClient.upload(jsonContent, jsonContent.length, {
            blobHTTPHeaders: { blobContentType: 'application/json' }
        });
    });
}
// Register the function with Azure Functions v4
functions_1.app.storageBlob('blobTriggerProcessor', {
    path: 'recordings/{name}',
    connection: 'AzureWebJobsStorage',
    handler: blobTriggerProcessor
});
//# sourceMappingURL=index.js.map