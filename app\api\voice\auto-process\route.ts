import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  const deprecationHeaders = {
    'X-Deprecated': 'true',
    'X-Replacement': '/api/voice/cloud-process',
    'X-Deprecation-Date': '2025-01-01'
  };
  
  try {
    console.log('🔄 Auto-process: Checking for unprocessed files and starting processing if needed...');
    
    // Check if processing is already running (using cloud-process)
    const statusResponse = await fetch(`${getBaseUrl()}/api/voice/cloud-process`, {
      headers: { 'X-Internal-Call': 'true' }
    });
    
    if (statusResponse.ok) {
      const statusData = await statusResponse.json();
      if (statusData.success && statusData.activeJobs > 0) {
        console.log('✅ Auto-process: Processing already running, no action needed');
        return NextResponse.json({
          success: true,
          message: 'Processing already in progress',
          alreadyRunning: true,
          activeJobs: statusData.activeJobs
        }, { headers: deprecationHeaders });
      }
    }
    
    // Check for unprocessed files (using cloud-first SQL endpoint)
    const recordingsResponse = await fetch(`${getBaseUrl()}/api/voice/recordings-sql`, {
      headers: { 'X-Internal-Call': 'true' }
    });
    
    if (!recordingsResponse.ok) {
      throw new Error(`Failed to fetch recordings: ${recordingsResponse.status}`);
    }
    
    const recordingsData = await recordingsResponse.json();
    const recordings = recordingsData.recordings || recordingsData;
    
    // Count unprocessed files
    const unprocessedCount = recordings.filter((r: any) => 
      !r.transcription || r.transcription.length === 0 || 
      (r.transcription && (!r.clinical_summary || r.clinical_summary.length === 0))
    ).length;
    
    console.log(`📊 Auto-process: Found ${unprocessedCount} unprocessed files out of ${recordings.length} total`);
    
    if (unprocessedCount === 0) {
      console.log('✅ Auto-process: All files are already processed');
      return NextResponse.json({
        success: true,
        message: 'All files are already processed',
        unprocessedCount: 0,
        totalCount: recordings.length
      }, { headers: deprecationHeaders });
    }
    
    // Start processing (using cloud-process for persistent jobs)
    console.log(`🚀 Auto-process: Starting background processing for ${unprocessedCount} files...`);
    const startResponse = await fetch(`${getBaseUrl()}/api/voice/cloud-process`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'X-Internal-Call': 'true' 
      },
      body: JSON.stringify({ action: 'start_batch' })
    });
    
    if (!startResponse.ok) {
      const errorText = await startResponse.text();
      throw new Error(`Failed to start processing: ${startResponse.status} - ${errorText}`);
    }
    
    const startData = await startResponse.json();
    console.log('✅ Auto-process: Background processing started successfully');
    
    return NextResponse.json({
      success: true,
      message: `Started processing ${unprocessedCount} unprocessed files`,
      unprocessedCount,
      totalCount: recordings.length,
      processingStarted: true,
      startResponse: startData
    }, { headers: deprecationHeaders });
    
  } catch (error) {
    console.error('❌ Auto-process error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500, headers: deprecationHeaders });
  }
}

export async function GET() {
  const deprecationHeaders = {
    'X-Deprecated': 'true',
    'X-Replacement': '/api/voice/cloud-process',
    'X-Deprecation-Date': '2025-01-01'
  };
  
  try {
    // Return current auto-processing status (using cloud-process)
    const statusResponse = await fetch(`${getBaseUrl()}/api/voice/cloud-process`, {
      headers: { 'X-Internal-Call': 'true' }
    });
    
    const recordingsResponse = await fetch(`${getBaseUrl()}/api/voice/recordings-sql`, {
      headers: { 'X-Internal-Call': 'true' }
    });
    
    let currentStatus = null;
    let unprocessedCount = 0;
    let totalCount = 0;
    
    if (statusResponse.ok) {
      const statusData = await statusResponse.json();
      if (statusData.success) {
        currentStatus = statusData;
      }
    }
    
    if (recordingsResponse.ok) {
      const recordingsData = await recordingsResponse.json();
      const recordings = recordingsData.recordings || recordingsData;
      totalCount = recordings.length;
      unprocessedCount = recordings.filter((r: any) => 
        !r.transcription || r.transcription.length === 0 || 
        (r.transcription && (!r.clinical_summary || r.clinical_summary.length === 0))
      ).length;
    }
    
    return NextResponse.json({
      success: true,
      currentStatus,
      unprocessedCount,
      totalCount,
      isProcessing: currentStatus?.activeJobs > 0 || false,
      autoProcessingRecommended: unprocessedCount > 0 && !(currentStatus?.activeJobs > 0)
    }, { headers: deprecationHeaders });
    
  } catch (error) {
    console.error('❌ Auto-process status error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500, headers: deprecationHeaders });
  }
}

function getBaseUrl(): string {
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  if (process.env.NEXT_PUBLIC_API_URL) {
    return process.env.NEXT_PUBLIC_API_URL;
  }
  return 'http://localhost:3000';
}