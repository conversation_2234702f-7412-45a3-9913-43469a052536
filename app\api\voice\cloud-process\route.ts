import { NextRequest, NextResponse } from 'next/server';
import { TranscriptionService } from '@/lib/transcription-service';
import { transcriptionQueue } from '@/lib/transcription-queue';
import { VercelDB } from '@/lib/vercel-db';
import { sql } from '@vercel/postgres';
import { featureFlags } from '@/lib/feature-flags';

interface CloudProcessingRequest {
  files?: Array<{
    filename: string;
    audioData: string; // base64 encoded audio
    metadata?: {
      patientId?: string;
      deviceId?: string;
      recordingDate?: string;
      notes?: string;
    };
  }>;
  transcriptionIds?: string[];
  options?: {
    priority?: 'low' | 'normal' | 'high';
    generateSummary?: boolean;
    summaryType?: 'clinical' | 'brief' | 'detailed' | 'patient-friendly';
    enableDiarization?: boolean;
    language?: string;
  };
}

interface CloudProcessingResponse {
  success: boolean;
  message: string;
  results: {
    totalRequested: number;
    successfullyQueued: number;
    failed: number;
    jobIds: string[];
    failedFiles?: Array<{
      filename: string;
      error: string;
    }>;
  };
  estimatedProcessingTime: number;
  queueStatus: {
    position: number;
    totalQueued: number;
    avgProcessingTimeMs: number;
  };
}

/**
 * CLOUD PROCESSING ENDPOINT - Transcription-Only Architecture
 * High-performance cloud-based audio processing using OpenAI Whisper
 * Direct file processing without storage - transcription-only workflow
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Check if new architecture is enabled
    if (!featureFlags.enableTranscriptionOnlyWorkflow) {
      return NextResponse.json({
        error: 'Transcription-only workflow not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const body: CloudProcessingRequest = await request.json();
    const { files = [], transcriptionIds = [], options = {} } = body;

    // Validate request
    if (files.length === 0 && transcriptionIds.length === 0) {
      return NextResponse.json({
        error: 'No files or transcription IDs provided for processing',
        code: 'NO_INPUT'
      }, { status: 400 });
    }

    console.log(`🌩️ Cloud processing request: ${files.length} files, ${transcriptionIds.length} existing transcriptions`);

    const jobIds: string[] = [];
    const failedFiles: Array<{ filename: string; error: string }> = [];
    let successfullyQueued = 0;

    // Process new audio files
    for (const file of files) {
      try {
        const result = await processNewAudioFile(file, options);
        jobIds.push(result.jobId);
        successfullyQueued++;
        
        console.log(`✅ Queued cloud processing for: ${file.filename} (Job: ${result.jobId})`);
      } catch (error) {
        console.error(`❌ Failed to queue ${file.filename}:`, error);
        failedFiles.push({
          filename: file.filename,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Process existing transcription IDs (for reprocessing)
    for (const transcriptionId of transcriptionIds) {
      try {
        const result = await processExistingTranscription(transcriptionId, options);
        if (result.jobId) {
          jobIds.push(result.jobId);
          successfullyQueued++;
          
          console.log(`✅ Queued reprocessing for transcription: ${transcriptionId} (Job: ${result.jobId})`);
        }
      } catch (error) {
        console.error(`❌ Failed to queue transcription ${transcriptionId}:`, error);
        failedFiles.push({
          filename: `transcription-${transcriptionId}`,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Get queue statistics
    const queueStats = transcriptionQueue.getStats();
    const estimatedProcessingTime = queueStats.avgProcessingTimeMs * jobIds.length;

    const response: CloudProcessingResponse = {
      success: successfullyQueued > 0,
      message: successfullyQueued > 0 
        ? `Successfully queued ${successfullyQueued} items for cloud processing`
        : 'No items were successfully queued for processing',
      results: {
        totalRequested: files.length + transcriptionIds.length,
        successfullyQueued,
        failed: failedFiles.length,
        jobIds,
        ...(failedFiles.length > 0 && { failedFiles })
      },
      estimatedProcessingTime,
      queueStatus: {
        position: queueStats.pending + queueStats.processing,
        totalQueued: queueStats.pending + queueStats.processing + jobIds.length,
        avgProcessingTimeMs: queueStats.avgProcessingTimeMs
      }
    };

    console.log(`🎉 Cloud processing queued: ${successfullyQueued} success, ${failedFiles.length} failed`);

    return NextResponse.json(response, { 
      status: response.success ? 200 : 207 // 207 Multi-Status for partial success
    });

  } catch (error) {
    console.error('❌ Cloud processing error:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Cloud processing failed',
      code: 'CLOUD_PROCESSING_ERROR',
      processingTimeMs: Date.now() - startTime
    }, { status: 500 });
  }
}

/**
 * GET endpoint for cloud processing status and job monitoring
 */
export async function GET(request: NextRequest) {
  try {
    if (!featureFlags.enableTranscriptionOnlyWorkflow) {
      return NextResponse.json({
        error: 'Transcription-only workflow not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const jobIds = searchParams.get('jobIds')?.split(',') || [];
    const statusType = searchParams.get('type') || 'queue';

    if (jobIds.length > 0) {
      // Get status for specific jobs
      const jobStatuses = await Promise.all(
        jobIds.map(async (jobId) => {
          const status = transcriptionQueue.getJobStatus(jobId);
          return {
            jobId,
            found: !!status,
            status: status?.status || 'not_found',
            result: status?.result,
            error: status?.error,
            createdAt: status?.createdAt,
            completedAt: status?.completedAt,
            progress: getProgressFromStatus(status?.status)
          };
        })
      );

      return NextResponse.json({
        success: true,
        jobs: jobStatuses,
        summary: {
          total: jobIds.length,
          found: jobStatuses.filter(j => j.found).length,
          completed: jobStatuses.filter(j => j.status === 'completed').length,
          failed: jobStatuses.filter(j => j.status === 'failed').length,
          processing: jobStatuses.filter(j => j.status === 'processing').length
        }
      });
    }

    // Return overall cloud processing status
    const queueStats = transcriptionQueue.getStats();
    
    return NextResponse.json({
      success: true,
      cloudStatus: {
        service: 'operational',
        queueHealth: queueStats.pending + queueStats.processing < 100 ? 'healthy' : 'busy',
        averageProcessingTime: `${Math.round(queueStats.avgProcessingTimeMs / 1000)}s`,
        capacity: {
          current: queueStats.processing,
          maximum: 10, // Based on OpenAI rate limits
          utilization: Math.round((queueStats.processing / 10) * 100)
        }
      },
      queue: {
        pending: queueStats.pending,
        processing: queueStats.processing,
        completed: queueStats.completed,
        failed: queueStats.failed,
        totalProcessed: queueStats.completed + queueStats.failed
      },
      performance: {
        avgProcessingTimeMs: queueStats.avgProcessingTimeMs,
        successRate: queueStats.completed + queueStats.failed > 0 
          ? Math.round((queueStats.completed / (queueStats.completed + queueStats.failed)) * 100)
          : 0,
        throughputPerHour: Math.round(3600000 / Math.max(queueStats.avgProcessingTimeMs, 1000))
      }
    });

  } catch (error) {
    console.error('❌ Error getting cloud processing status:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Status check failed',
      code: 'STATUS_ERROR'
    }, { status: 500 });
  }
}

async function processNewAudioFile(
  file: CloudProcessingRequest['files'][0],
  options: CloudProcessingRequest['options'] = {}
): Promise<{ jobId: string; transcriptionId: string }> {
  
  // Validate audio data
  if (!file.audioData) {
    throw new Error('No audio data provided');
  }

  // Decode base64 audio data
  const audioBuffer = Buffer.from(file.audioData, 'base64');
  
  // Validate file size (OpenAI limit)
  if (audioBuffer.length > 25 * 1024 * 1024) {
    throw new Error('Audio file too large (>25MB). Use chunking for larger files.');
  }

  // Validate audio format
  const isValidFormat = await TranscriptionService.validateAudioFormat(audioBuffer, file.filename);
  if (!isValidFormat) {
    throw new Error(`Unsupported audio format. Supported: ${TranscriptionService.getSupportedFormats().join(', ')}`);
  }

  // Create transcription record first
  const transcriptionData = {
    filename: file.filename,
    deviceId: file.metadata?.deviceId || 'cloud-upload',
    patientId: file.metadata?.patientId,
    metadata: {
      cloudProcessing: true,
      recordingDate: file.metadata?.recordingDate,
      notes: file.metadata?.notes,
      fileSize: audioBuffer.length,
      uploadedAt: new Date().toISOString()
    }
  };

  const transcription = await VercelDB.createTranscription(transcriptionData);

  // Queue transcription job
  const jobId = await transcriptionQueue.enqueueJob(audioBuffer, {
    filename: file.filename,
    transcriptionId: transcription.id,
    deviceId: transcriptionData.deviceId,
    patientId: transcriptionData.patientId,
    options: {
      generateSummary: options.generateSummary !== false,
      summaryType: options.summaryType || 'clinical',
      enableDiarization: options.enableDiarization || false,
      language: options.language || 'en'
    }
  }, options.priority || 'normal');

  return {
    jobId,
    transcriptionId: transcription.id
  };
}

async function processExistingTranscription(
  transcriptionId: string,
  options: CloudProcessingRequest['options'] = {}
): Promise<{ jobId?: string; message: string }> {
  
  // Get existing transcription
  const result = await sql`
    SELECT id, filename, transcription_text, summary_text, device_id, patient_id
    FROM transcriptions 
    WHERE id = ${transcriptionId}
  `;

  if (result.rows.length === 0) {
    throw new Error('Transcription not found');
  }

  const transcription = result.rows[0];

  // For existing transcriptions, we can only regenerate summaries
  // (no original audio file available in transcription-only architecture)
  if (transcription.transcription_text && (options.generateSummary !== false)) {
    // Use the summarize endpoint to regenerate summary
    const summaryResponse = await fetch('/api/voice/summarize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        transcriptionIds: [transcriptionId],
        summaryType: options.summaryType || 'clinical'
      })
    });

    if (!summaryResponse.ok) {
      throw new Error('Failed to regenerate summary');
    }

    return {
      message: 'Summary regeneration completed'
    };
  }

  // If no transcription text exists, we cannot reprocess without original audio
  if (!transcription.transcription_text) {
    throw new Error('Cannot reprocess transcription without original audio file');
  }

  return {
    message: 'Transcription already exists and no reprocessing was requested'
  };
}

function getProgressFromStatus(status?: string): number {
  switch (status) {
    case 'queued': return 10;
    case 'processing': return 50;
    case 'completed': return 100;
    case 'failed': return 100;
    default: return 0;
  }
}

/**
 * DELETE endpoint for cancelling cloud processing jobs
 */
export async function DELETE(request: NextRequest) {
  try {
    if (!featureFlags.enableTranscriptionOnlyWorkflow) {
      return NextResponse.json({
        error: 'Transcription-only workflow not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const jobIds = searchParams.get('jobIds')?.split(',') || [];

    if (jobIds.length === 0) {
      return NextResponse.json({
        error: 'No job IDs provided for cancellation',
        code: 'NO_JOB_IDS'
      }, { status: 400 });
    }

    let cancelledCount = 0;
    let notFoundCount = 0;
    let alreadyCompletedCount = 0;

    for (const jobId of jobIds) {
      const status = transcriptionQueue.getJobStatus(jobId);
      
      if (!status) {
        notFoundCount++;
        continue;
      }

      if (status.status === 'completed' || status.status === 'failed') {
        alreadyCompletedCount++;
        continue;
      }

      // Cancel the job (implementation depends on queue system)
      try {
        transcriptionQueue.cancelJob(jobId);
        cancelledCount++;
        console.log(`❌ Cancelled cloud processing job: ${jobId}`);
      } catch (error) {
        console.error(`❌ Failed to cancel job ${jobId}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Processed ${jobIds.length} cancellation requests`,
      results: {
        cancelled: cancelledCount,
        notFound: notFoundCount,
        alreadyCompleted: alreadyCompletedCount,
        total: jobIds.length
      }
    });

  } catch (error) {
    console.error('❌ Error cancelling cloud processing jobs:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Cancellation failed',
      code: 'CANCELLATION_ERROR'
    }, { status: 500 });
  }
}

/**
 * PUT endpoint for batch status updates and management
 */
export async function PUT(request: NextRequest) {
  try {
    if (!featureFlags.enableTranscriptionOnlyWorkflow) {
      return NextResponse.json({
        error: 'Transcription-only workflow not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { action, jobIds = [], options = {} } = await request.json();

    switch (action) {
      case 'retry_failed':
        return await retryFailedJobs(jobIds, options);
      
      case 'priority_boost':
        return await boostJobPriority(jobIds);
      
      case 'batch_status':
        return await getBatchJobStatus(jobIds);
      
      default:
        return NextResponse.json({
          error: `Unknown action: ${action}`,
          code: 'INVALID_ACTION'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Error in cloud processing management:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Management operation failed',
      code: 'MANAGEMENT_ERROR'
    }, { status: 500 });
  }
}

async function retryFailedJobs(jobIds: string[], options: any): Promise<NextResponse> {
  let retriedCount = 0;
  const newJobIds: string[] = [];

  for (const jobId of jobIds) {
    const status = transcriptionQueue.getJobStatus(jobId);
    
    if (status && status.status === 'failed') {
      // Create new job with same parameters
      try {
        const newJobId = await transcriptionQueue.retryJob(jobId);
        newJobIds.push(newJobId);
        retriedCount++;
        console.log(`🔄 Retried failed job ${jobId} as ${newJobId}`);
      } catch (error) {
        console.error(`❌ Failed to retry job ${jobId}:`, error);
      }
    }
  }

  return NextResponse.json({
    success: true,
    message: `Retried ${retriedCount} failed jobs`,
    results: {
      retriedCount,
      newJobIds,
      requestedCount: jobIds.length
    }
  });
}

async function boostJobPriority(jobIds: string[]): Promise<NextResponse> {
  let boostedCount = 0;

  for (const jobId of jobIds) {
    try {
      transcriptionQueue.updateJobPriority(jobId, 'high');
      boostedCount++;
      console.log(`⚡ Boosted priority for job: ${jobId}`);
    } catch (error) {
      console.error(`❌ Failed to boost priority for job ${jobId}:`, error);
    }
  }

  return NextResponse.json({
    success: true,
    message: `Boosted priority for ${boostedCount} jobs`,
    boostedCount,
    requestedCount: jobIds.length
  });
}

async function getBatchJobStatus(jobIds: string[]): Promise<NextResponse> {
  const jobStatuses = jobIds.map(jobId => {
    const status = transcriptionQueue.getJobStatus(jobId);
    return {
      jobId,
      status: status?.status || 'not_found',
      progress: getProgressFromStatus(status?.status),
      result: status?.result,
      error: status?.error,
      processingTimeMs: status?.completedAt && status?.createdAt 
        ? new Date(status.completedAt).getTime() - new Date(status.createdAt).getTime()
        : undefined
    };
  });

  const summary = {
    total: jobIds.length,
    completed: jobStatuses.filter(j => j.status === 'completed').length,
    failed: jobStatuses.filter(j => j.status === 'failed').length,
    processing: jobStatuses.filter(j => j.status === 'processing').length,
    queued: jobStatuses.filter(j => j.status === 'queued').length,
    notFound: jobStatuses.filter(j => j.status === 'not_found').length
  };

  return NextResponse.json({
    success: true,
    jobs: jobStatuses,
    summary
  });
}