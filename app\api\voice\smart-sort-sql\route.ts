import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { date } = await request.json();

    if (!date) {
      return NextResponse.json({
        error: 'Date parameter is required'
      }, { status: 400 });
    }

    console.log(`🤖 Starting smart sort for recordings on ${date}`);

    // Step 1: Fetch unmatched recordings for the specified date
    const recordingsResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/voice/recordings-sql?date=${date}&unmatched=true`);
    
    if (!recordingsResponse.ok) {
      throw new Error('Failed to fetch recordings');
    }

    const recordingsData = await recordingsResponse.json();
    const recordings = recordingsData.recordings || [];

    if (recordings.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No unmatched recordings found for the specified date',
        uploadDate: date,
        totalFiles: 0,
        autoMatches: 0,
        manualReviewNeeded: 0,
        results: []
      });
    }

    console.log(`Found ${recordings.length} unmatched recordings for ${date}`);

    let autoMatches = 0;
    let manualReviewNeeded = 0;
    const results = [];

    // Step 2: Analyze each recording using smart-match with robust error handling
    const performanceMetrics = {
      startTime: Date.now(),
      totalAttempts: 0,
      successfulAnalyses: 0,
      autoMatchAttempts: 0,
      confidenceDistribution: { high: 0, medium: 0, low: 0 }
    };

    for (const recording of recordings) {
      console.log(`🔍 Analyzing recording: ${recording.fileName}`);
      performanceMetrics.totalAttempts++;
      
      let retryCount = 0;
      const maxRetries = 3;
      let analysisSuccess = false;

      while (retryCount < maxRetries && !analysisSuccess) {
        try {
          // Call smart-match for this single recording with retry logic
          const smartMatchResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/voice/smart-match`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              recordingIds: [recording.id || recording.name]
            })
          });

          if (!smartMatchResponse.ok) {
            const errorText = await smartMatchResponse.text();
            console.error(`Smart match failed for ${recording.fileName} (attempt ${retryCount + 1}): ${errorText}`);
            
            if (retryCount < maxRetries - 1) {
              retryCount++;
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // Exponential backoff
              continue;
            } else {
              manualReviewNeeded++;
              results.push({
                recordingId: recording.id,
                filename: recording.fileName,
                status: 'failed_analysis',
                error: `Smart match analysis failed after ${maxRetries} attempts: ${errorText}`
              });
              break;
            }
          }

          analysisSuccess = true;
          performanceMetrics.successfulAnalyses++;

          const smartMatchData = await smartMatchResponse.json();
          const analysis = smartMatchData.results?.[0];

          if (!analysis) {
            manualReviewNeeded++;
            results.push({
              recordingId: recording.id,
              filename: recording.fileName,
              status: 'no_analysis',
              error: 'No analysis results returned'
            });
            break;
          }

          // Track confidence distribution for analytics
          if (analysis.confidence >= 0.8) {
            performanceMetrics.confidenceDistribution.high++;
          } else if (analysis.confidence >= 0.6) {
            performanceMetrics.confidenceDistribution.medium++;
          } else {
            performanceMetrics.confidenceDistribution.low++;
          }

          console.log(`📊 Analysis for ${recording.fileName}: confidence=${analysis.confidence.toFixed(3)}, suggestions=${analysis.suggestedAppointments?.length || 0}`);
          if (analysis.reasoning) {
            console.log(`   Reasoning: ${analysis.reasoning}`);
          }

          // Step 3: Auto-match if confidence is high enough
          if (analysis.confidence >= 0.8 && analysis.suggestedAppointments?.length === 1) {
            const appointment = analysis.suggestedAppointments[0];
            performanceMetrics.autoMatchAttempts++;
            
            console.log(`🎯 Auto-matching ${recording.fileName} to appointment ${appointment.sikka_id} (confidence: ${analysis.confidence.toFixed(3)})`);
            
            // Attempt to auto-match
            const matchResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/voice/match-recording-sql`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                recording_id: recording.id,
                appointment_id: appointment.sikka_id
              })
            });

            if (matchResponse.ok) {
              autoMatches++;
              results.push({
                recordingId: recording.id,
                filename: recording.fileName,
                status: 'auto_matched',
                confidence: analysis.confidence,
                matchedAppointment: {
                  ...appointment,
                  matchMethod: 'auto',
                  matchTimestamp: new Date().toISOString(),
                  algorithmVersion: '2.0'
                },
                reasoning: analysis.reasoning,
                metadata: {
                  retryCount,
                  processingTime: Date.now() - performanceMetrics.startTime
                }
              });
            } else {
              const errorText = await matchResponse.text();
              console.error(`Failed to create auto-match for ${recording.fileName}: ${errorText}`);
              manualReviewNeeded++;
              results.push({
                recordingId: recording.id,
                filename: recording.fileName,
                status: 'match_failed',
                confidence: analysis.confidence,
                suggestedAppointment: appointment,
                reasoning: analysis.reasoning,
                error: `Failed to create match in database: ${errorText}`,
                metadata: { retryCount }
              });
            }
          } else {
            // Needs manual review
            manualReviewNeeded++;
            results.push({
              recordingId: recording.id,
              filename: recording.fileName,
              status: 'needs_review',
              confidence: analysis.confidence,
              suggestedAppointments: analysis.suggestedAppointments || [],
              reasoning: analysis.reasoning,
              extractedDate: analysis.extractedDate,
              patientName: analysis.patientName,
              visitType: analysis.visitType,
              metadata: {
                retryCount,
                reasonForManualReview: analysis.confidence < 0.8 ? 'Low confidence' : 'Multiple suggestions'
              }
            });
          }

        } catch (error) {
          console.error(`Error processing recording ${recording.fileName} (attempt ${retryCount + 1}):`, error);
          
          if (retryCount < maxRetries - 1) {
            retryCount++;
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            continue;
          } else {
            manualReviewNeeded++;
            results.push({
              recordingId: recording.id,
              filename: recording.fileName,
              status: 'processing_error',
              error: error instanceof Error ? error.message : 'Unknown error',
              metadata: { retryCount, finalAttempt: true }
            });
            break;
          }
        }
      }
    }

    // Calculate final performance metrics
    const totalProcessingTime = Date.now() - performanceMetrics.startTime;
    const successRate = performanceMetrics.totalAttempts > 0 ? 
      (performanceMetrics.successfulAnalyses / performanceMetrics.totalAttempts) * 100 : 0;
    const autoMatchRate = performanceMetrics.autoMatchAttempts > 0 ? 
      (autoMatches / performanceMetrics.autoMatchAttempts) * 100 : 0;

    console.log(`📈 Performance metrics for ${date}:`);
    console.log(`   Total processing time: ${totalProcessingTime}ms`);
    console.log(`   Analysis success rate: ${successRate.toFixed(1)}% (${performanceMetrics.successfulAnalyses}/${performanceMetrics.totalAttempts})`);
    console.log(`   Auto-match success rate: ${autoMatchRate.toFixed(1)}% (${autoMatches}/${performanceMetrics.autoMatchAttempts})`);
    console.log(`   Confidence distribution: High=${performanceMetrics.confidenceDistribution.high}, Medium=${performanceMetrics.confidenceDistribution.medium}, Low=${performanceMetrics.confidenceDistribution.low}`);

    const response = {
      success: true,
      message: `Smart sort completed for ${date}`,
      uploadDate: date,
      totalFiles: recordings.length,
      autoMatches,
      manualReviewNeeded,
      results,
      summary: {
        autoMatchRate: recordings.length > 0 ? Math.round((autoMatches / recordings.length) * 100) : 0,
        needsReviewRate: recordings.length > 0 ? Math.round((manualReviewNeeded / recordings.length) * 100) : 0
      },
      performanceMetrics: {
        totalProcessingTime,
        successRate: Math.round(successRate),
        autoMatchSuccessRate: Math.round(autoMatchRate),
        confidenceDistribution: performanceMetrics.confidenceDistribution,
        averageProcessingTimePerFile: recordings.length > 0 ? Math.round(totalProcessingTime / recordings.length) : 0
      },
      algorithmMetadata: {
        version: '2.0',
        confidenceThreshold: 0.8,
        retryLogic: true,
        maxRetries: 3
      }
    };

    console.log(`✅ Smart sort complete: ${autoMatches} auto-matched, ${manualReviewNeeded} need review`);
    
    return NextResponse.json(response);

  } catch (error) {
    console.error('Smart sort error:', error);
    return NextResponse.json({
      error: 'Failed to perform smart sort',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}