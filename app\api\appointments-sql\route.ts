/**
 * SQL-based API route for fetching appointments from Vercel Postgres database
 */

import { NextRequest, NextResponse } from 'next/server';
import { VercelDB } from '@/lib/vercel-db';
import { featureFlags } from '@/lib/feature-flags';

export async function GET(request: NextRequest) {
  try {
    // Check if Vercel Postgres is enabled
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        message: 'Enable FEATURE_FLAG_VERCEL_POSTGRES=true to use this endpoint',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const searchParams = request.nextUrl.searchParams;
    const dateParam = searchParams.get('date');
    const operatories = searchParams.getAll('operatories[]');
    const providers = searchParams.getAll('providers[]');

    if (!dateParam) {
      return NextResponse.json(
        { error: 'Date parameter is required' },
        { status: 400 }
      );
    }

    // Ensure date is in the correct format (YYYY-MM-DD)
    let date = dateParam;
    try {
      // Parse the date string to ensure it's valid
      const [year, month, day] = dateParam.split('-').map(Number);
      const parsedDate = new Date(Date.UTC(year, month - 1, day, 0, 0, 0));

      // Format it back to YYYY-MM-DD to ensure consistency
      date = `${parsedDate.getUTCFullYear()}-${String(parsedDate.getUTCMonth() + 1).padStart(2, '0')}-${String(parsedDate.getUTCDate()).padStart(2, '0')}`;
    } catch (error) {
      console.error('Error parsing date:', error);
      return NextResponse.json(
        { error: 'Invalid date format. Use YYYY-MM-DD' },
        { status: 400 }
      );
    }

    console.log(`API: Fetching appointments from Vercel Postgres for date: ${date}, operatories: ${operatories.join(', ') || 'none'}, providers: ${providers.join(', ') || 'none'}`);

    const appointments = await VercelDB.getAppointmentsByDate(date, {
      operatory: operatories.length > 0 ? operatories[0] : undefined,
      provider: providers.length > 0 ? providers[0] : undefined,
    });

    console.log(`API: Successfully fetched ${appointments.length} appointments from Vercel Postgres`);

    // Transform database results to match existing API format
    const transformedAppointments = appointments.map(appt => ({
      id: appt.sikka_id,
      sikka_id: appt.sikka_id,
      patient_name: appt.patient_name,
      patientName: appt.patient_name, // alias for compatibility
      appointment_date: appt.appointment_date,
      appointmentDate: appt.appointment_date, // alias for compatibility
      appointment_time: appt.appointment_time,
      appointmentTime: appt.appointment_time, // alias for compatibility
      operatory: appt.operatory,
      provider: appt.provider,
      appointment_type: appt.appointment_type,
      appointmentType: appt.appointment_type, // alias for compatibility
      status: appt.status,
      notes: appt.notes,
      patient_phone: appt.patient_phone,
      patientPhone: appt.patient_phone, // alias for compatibility
      patient_email: appt.patient_email,
      patientEmail: appt.patient_email, // alias for compatibility
      last_sync: appt.last_sync,
      lastSync: appt.last_sync, // alias for compatibility
      created_at: appt.created_at,
      updated_at: appt.updated_at
    }));

    // Log appointments by operatory and provider for debugging
    const operatoryCounts: { [key: string]: number } = {};
    const providerCounts: { [key: string]: number } = {};
    transformedAppointments.forEach(appt => {
      const op = appt.operatory || 'unknown';
      const provider = appt.provider || 'unknown';
      operatoryCounts[op] = (operatoryCounts[op] || 0) + 1;
      providerCounts[provider] = (providerCounts[provider] || 0) + 1;
    });
    console.log('API: Appointments by operatory:', JSON.stringify(operatoryCounts));
    console.log('API: Appointments by provider:', JSON.stringify(providerCounts));

    // Return response with data freshness information
    const response = {
      appointments: transformedAppointments,
      total_count: transformedAppointments.length,
      date: date,
      data_source: 'vercel_postgres'
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Vercel Postgres error fetching appointments:', error);
    
    // Provide specific error messages for common issues
    let errorMessage = 'Failed to fetch appointments from Vercel Postgres';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('connect')) {
        errorMessage = 'Vercel Postgres connection failed';
        statusCode = 503;
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Database query timeout';
        statusCode = 504;
      } else if (error.message.includes('relation') && error.message.includes('does not exist')) {
        errorMessage = 'Appointments table not found - run database initialization';
        statusCode = 500;
      }
    }

    return NextResponse.json(
      { 
        error: errorMessage,
        details: error instanceof Error ? error.message : 'Unknown database error',
        solution: 'Ensure VERCEL_POSTGRES_URL is configured and appointments table exists',
        data_source: 'vercel_postgres'
      },
      { status: statusCode }
    );
  }
}