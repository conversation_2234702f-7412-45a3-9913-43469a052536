@echo off
title USB Dental Recordings Upload Tool
color 0B
cls

echo.
echo ========================================
echo   USB Dental Recordings Upload Tool v1.1.0
echo ========================================
echo.

:: Get the drive letter where this script is running from
set "USB_DRIVE=%~d0"
set "SCRIPT_DIR=%~dp0"
set "SCRIPT_NAME=%~nx0"

echo 📍 Running from: %USB_DRIVE%
echo 📁 Script location: %SCRIPT_DIR%
echo.

:: Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell check...'" >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ PowerShell is not available on this system
    echo    This tool requires PowerShell to run
    echo.
    pause
    exit /b 1
)

echo ✅ PowerShell detected
echo.

:: Check for PowerShell script (using simple version for testing)
set "PS_SCRIPT=%SCRIPT_DIR%upload-recordings-simple.ps1"
if not exist "%PS_SCRIPT%" (
    echo ❌ PowerShell script not found: upload-recordings-simple.ps1
    echo    Please ensure upload-recordings-simple.ps1 is in the same directory
    echo.
    pause
    exit /b 1
)

echo ✅ Upload script found
echo.

:: Run the PowerShell script with the USB drive parameter
echo 🚀 Starting test process...
echo   PowerShell Script: %PS_SCRIPT%
echo   USB Drive: %USB_DRIVE%
echo   Script Directory: %SCRIPT_DIR%
echo.

:: Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell test successful'" 2>nul
if %errorLevel% neq 0 (
    echo ❌ ERROR: PowerShell is not available or accessible
    echo    Please ensure PowerShell is installed and accessible
    echo.
    echo Press any key to close...
    pause > nul
    exit /b 1
)

echo ✅ PowerShell is available
echo 🔄 Executing upload script...
echo.

echo 🔄 About to execute PowerShell script...
echo Command: powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -USBDrive "%USB_DRIVE%" -ScriptDir "%SCRIPT_DIR%"
echo.
echo Press any key to start the PowerShell script...
pause > nul

echo.
echo ========================================
echo POWERSHELL SCRIPT OUTPUT:
echo ========================================
powershell -ExecutionPolicy Bypass -NoExit -File "%PS_SCRIPT%" -USBDrive "%USB_DRIVE%" -ScriptDir "%SCRIPT_DIR%"
set "PS_EXIT_CODE=%errorLevel%"

echo.
echo ========================================
echo POWERSHELL SCRIPT FINISHED
echo ========================================
echo Exit code: %PS_EXIT_CODE%
echo.

:: Check the exit code from PowerShell
if %PS_EXIT_CODE% equ 0 (
    echo ✅ Upload process completed successfully!
) else (
    echo ⚠️  Upload process completed with exit code %PS_EXIT_CODE%
    echo    This might indicate an error or warning
)

echo.
echo BATCH FILE COMPLETED - Window will stay open
echo Press any key to close this window...
pause > nul
exit /b %PS_EXIT_CODE%
