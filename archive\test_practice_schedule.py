import requests
import json
from datetime import datetime

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

def get_request_key(office_id, secret_key, app_id, app_key):
    """Obtain a request key for API authentication."""
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": office_id,
            "secret_key": secret_key,
            "app_id": app_id,
            "app_key": app_key
        }
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None

    data = auth_resp.json()
    return data.get("request_key")

def fetch_practice_schedule(request_key, date=None, resource_id=None):
    """Fetch practice schedule data."""
    headers = {"Request-Key": request_key}
    params = {}

    if date:
        params["date"] = date

    if resource_id:
        params["resource_id"] = resource_id

    print(f"Fetching practice schedule with params: {params}")

    resp = requests.get(f"{API_BASE_V2}/practice_schedule", headers=headers, params=params)

    if resp.status_code != 200:
        print(f"Error: {resp.status_code}")
        print(resp.text)
        return None

    return resp.json()

def main():
    # API credentials
    office_id = "D43989"
    secret_key = "35442814D4396E20C222"
    app_id = "fdd52aaffb0c1bead647874ba551db0c"
    app_key = "88254bfa2224607ef425646aafe5f722"

    # Get request key
    print("Getting request key...")
    request_key = get_request_key(office_id, secret_key, app_id, app_key)
    if not request_key:
        print("Failed to get request key. Exiting.")
        return

    print(f"Request key obtained: {request_key}")

    # Test: Fetch operatories
    print("\nTEST: Fetch operatories")
    # First, let's try to find operatory information
    schedule_data = fetch_practice_schedule(request_key)
    if schedule_data:
        # Look for operatory information
        operatories = []
        for item in schedule_data[0].get("items", []):
            resource_type = item.get("resource_type")
            resource_id = item.get("resource_id")

            # Resource type 2 is operatory
            if resource_type == "2":
                operatories.append(resource_id)

        print(f"Found operatories: {operatories}")

        # If we found operatories, try to get schedule for one
        if operatories:
            print(f"\nTEST: Fetch schedule for operatory {operatories[0]}")
            operatory_schedule = fetch_practice_schedule(request_key, resource_id=operatories[0])
            if operatory_schedule:
                print(json.dumps(operatory_schedule, indent=2))

    # Test: Fetch schedule for a specific date
    print("\nTEST: Fetch schedule for a specific date")
    date = "2025-05-16"
    schedule_data = fetch_practice_schedule(request_key, date=date)
    if schedule_data:
        print(json.dumps(schedule_data, indent=2))

    # Test: Try to find appointments for a specific date
    print("\nTEST: Try to find appointments for a specific date")
    date = "2025-05-16"

    # Try different endpoints that might contain appointment-operatory mapping
    try:
        headers = {"Request-Key": request_key}
        params = {"date": date}

        # Try the appointments endpoint with operatory parameter
        print("Trying appointments endpoint with operatory parameter...")
        resp = requests.get(f"{API_BASE_V4}/appointments", headers=headers, params=params)
        print(f"Status: {resp.status_code}")
        if resp.status_code == 200:
            data = resp.json()
            print(json.dumps(data, indent=2))

        # Try the schedule endpoint
        print("\nTrying schedule endpoint...")
        resp = requests.get(f"{API_BASE_V2}/schedule", headers=headers, params=params)
        print(f"Status: {resp.status_code}")
        if resp.status_code == 200:
            data = resp.json()
            print(json.dumps(data, indent=2))

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
