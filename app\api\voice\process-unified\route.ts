import { NextRequest, NextResponse } from 'next/server';
import { TranscriptionService } from '@/lib/transcription-service';
import { transcriptionQueue } from '@/lib/transcription-queue';
import { sql } from '@vercel/postgres';
import { featureFlags } from '@/lib/feature-flags';

interface UnifiedProcessingRequest {
  operation: 'batch' | 'status' | 'resume' | 'cancel';
  transcriptionIds?: string[];
  options?: {
    forceReprocess?: boolean;
    summaryType?: 'clinical' | 'brief' | 'detailed' | 'patient-friendly';
    priority?: 'low' | 'normal' | 'high';
    batchSize?: number;
  };
}

interface ProcessingResult {
  transcriptionId: string;
  filename: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
  transcriptionText?: string;
  summaryText?: string;
  confidence?: number;
  error?: string;
  processingTimeMs?: number;
  skipReason?: string;
}

interface BatchProcessingStatus {
  batchId: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  progress: {
    total: number;
    completed: number;
    failed: number;
    pending: number;
  };
  results: ProcessingResult[];
  startedAt: string;
  completedAt?: string;
  estimatedTimeRemaining?: number;
}

// In-memory batch job tracking (production would use persistent storage)
const batchJobs = new Map<string, BatchProcessingStatus>();

/**
 * UNIFIED PROCESSING ENDPOINT - Transcription-Only Architecture
 * Centralized processing for batch transcription operations
 * Uses TranscriptionService and queue for processing coordination
 */
export async function POST(request: NextRequest) {
  try {
    // Check if new architecture is enabled
    if (!featureFlags.enableTranscriptionOnlyWorkflow) {
      return NextResponse.json({
        error: 'Transcription-only workflow not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const body: UnifiedProcessingRequest = await request.json();
    const { operation, transcriptionIds = [], options = {} } = body;

    console.log(`🚀 Unified processing request: ${operation} for ${transcriptionIds.length} transcriptions`);

    switch (operation) {
      case 'batch':
        return await startBatchProcessing(transcriptionIds, options);
      
      case 'status':
        return await getBatchStatus();
      
      case 'resume':
        return await resumeBatchProcessing(options);
      
      case 'cancel':
        return await cancelBatchProcessing(transcriptionIds);
      
      default:
        return NextResponse.json({
          error: `Unknown operation: ${operation}`,
          code: 'INVALID_OPERATION'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Unified processing error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Processing failed',
      code: 'UNIFIED_PROCESSING_ERROR'
    }, { status: 500 });
  }
}

/**
 * GET endpoint for batch processing status
 */
export async function GET(request: NextRequest) {
  try {
    if (!featureFlags.enableTranscriptionOnlyWorkflow) {
      return NextResponse.json({
        error: 'Transcription-only workflow not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const batchId = searchParams.get('batchId');

    if (batchId) {
      const batchStatus = batchJobs.get(batchId);
      if (!batchStatus) {
        return NextResponse.json({
          error: 'Batch job not found',
          code: 'BATCH_NOT_FOUND'
        }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        batch: batchStatus
      });
    }

    // Return all active batches
    const activeBatches = Array.from(batchJobs.values()).filter(
      batch => batch.status === 'running'
    );

    return NextResponse.json({
      success: true,
      activeBatches: activeBatches.length,
      batches: activeBatches.map(batch => ({
        batchId: batch.batchId,
        status: batch.status,
        progress: batch.progress,
        startedAt: batch.startedAt
      }))
    });

  } catch (error) {
    console.error('❌ Error getting batch status:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Status check failed',
      code: 'STATUS_ERROR'
    }, { status: 500 });
  }
}

async function startBatchProcessing(
  transcriptionIds: string[],
  options: UnifiedProcessingRequest['options'] = {}
): Promise<NextResponse> {
  
  // If no specific IDs provided, get all pending transcriptions
  let targetIds = transcriptionIds;
  
  if (targetIds.length === 0) {
    console.log('🔍 No specific IDs provided, finding transcriptions that need processing...');
    
    const result = await sql`
      SELECT id
      FROM transcriptions 
      WHERE transcription_text IS NULL 
      OR transcription_text = ''
      OR (${options.forceReprocess} AND transcription_text IS NOT NULL)
      ORDER BY created_at DESC
      LIMIT 100
    `;
    
    targetIds = result.rows.map(row => row.id);
    
    if (targetIds.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No transcriptions need processing',
        batchId: null,
        totalTranscriptions: 0
      });
    }
  }

  console.log(`📦 Starting batch processing for ${targetIds.length} transcriptions`);

  // Create batch job
  const batchId = `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const batchStatus: BatchProcessingStatus = {
    batchId,
    status: 'running',
    progress: {
      total: targetIds.length,
      completed: 0,
      failed: 0,
      pending: targetIds.length
    },
    results: [],
    startedAt: new Date().toISOString()
  };

  batchJobs.set(batchId, batchStatus);

  // Start processing in background
  processBatchInBackground(batchId, targetIds, options)
    .catch(error => {
      console.error(`❌ Batch processing ${batchId} failed:`, error);
      const batch = batchJobs.get(batchId);
      if (batch) {
        batch.status = 'failed';
        batch.completedAt = new Date().toISOString();
        batchJobs.set(batchId, batch);
      }
    });

  return NextResponse.json({
    success: true,
    message: `Started batch processing of ${targetIds.length} transcriptions`,
    batchId,
    totalTranscriptions: targetIds.length,
    statusUrl: `/api/voice/process-unified?batchId=${batchId}`
  });
}

async function getBatchStatus(): Promise<NextResponse> {
  const queueStats = transcriptionQueue.getStats();
  const activeBatches = Array.from(batchJobs.values()).filter(
    batch => batch.status === 'running'
  );

  return NextResponse.json({
    success: true,
    queue: {
      pending: queueStats.pending,
      processing: queueStats.processing,
      completed: queueStats.completed,
      failed: queueStats.failed,
      avgProcessingTimeMs: queueStats.avgProcessingTimeMs
    },
    batches: {
      active: activeBatches.length,
      total: batchJobs.size,
      details: activeBatches.map(batch => ({
        batchId: batch.batchId,
        progress: batch.progress,
        startedAt: batch.startedAt
      }))
    }
  });
}

async function resumeBatchProcessing(
  options: UnifiedProcessingRequest['options'] = {}
): Promise<NextResponse> {
  
  console.log('🔄 Resuming interrupted batch processing...');

  // Find transcriptions that were being processed but failed or are incomplete
  const result = await sql`
    SELECT id 
    FROM transcriptions 
    WHERE (transcription_text IS NULL OR transcription_text = '')
    AND created_at > NOW() - INTERVAL '24 hours'
    ORDER BY created_at DESC
    LIMIT 50
  `;

  const transcriptionIds = result.rows.map(row => row.id);

  if (transcriptionIds.length === 0) {
    return NextResponse.json({
      success: true,
      message: 'No transcriptions need resuming',
      batchId: null
    });
  }

  // Start new batch processing for resumed items
  return await startBatchProcessing(transcriptionIds, {
    ...options,
    forceReprocess: false // Don't reprocess successfully completed items
  });
}

async function cancelBatchProcessing(batchIds: string[]): Promise<NextResponse> {
  let cancelledCount = 0;

  for (const batchId of batchIds) {
    const batch = batchJobs.get(batchId);
    if (batch && batch.status === 'running') {
      batch.status = 'cancelled';
      batch.completedAt = new Date().toISOString();
      batchJobs.set(batchId, batch);
      cancelledCount++;
      
      console.log(`❌ Cancelled batch processing: ${batchId}`);
    }
  }

  return NextResponse.json({
    success: true,
    message: `Cancelled ${cancelledCount} batch jobs`,
    cancelledBatches: cancelledCount,
    requestedBatches: batchIds.length
  });
}

async function processBatchInBackground(
  batchId: string,
  transcriptionIds: string[],
  options: UnifiedProcessingRequest['options'] = {}
): Promise<void> {
  
  const batch = batchJobs.get(batchId);
  if (!batch) return;

  console.log(`🔄 Background processing batch ${batchId} with ${transcriptionIds.length} transcriptions`);

  const batchSize = options.batchSize || 5; // Process in smaller chunks
  const priority = options.priority || 'normal';

  try {
    // Process transcriptions in batches to avoid overwhelming the queue
    for (let i = 0; i < transcriptionIds.length; i += batchSize) {
      const chunk = transcriptionIds.slice(i, i + batchSize);
      
      // Check if batch was cancelled
      const currentBatch = batchJobs.get(batchId);
      if (!currentBatch || currentBatch.status === 'cancelled') {
        console.log(`⏹️ Batch ${batchId} was cancelled, stopping processing`);
        return;
      }

      console.log(`📦 Processing chunk ${Math.floor(i / batchSize) + 1}/${Math.ceil(transcriptionIds.length / batchSize)}`);

      // Process chunk in parallel
      const chunkPromises = chunk.map(transcriptionId => 
        processTranscriptionForBatch(batchId, transcriptionId, options)
      );

      await Promise.all(chunkPromises);

      // Add delay between chunks to prevent overwhelming
      if (i + batchSize < transcriptionIds.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Mark batch as completed
    const finalBatch = batchJobs.get(batchId);
    if (finalBatch && finalBatch.status === 'running') {
      finalBatch.status = 'completed';
      finalBatch.completedAt = new Date().toISOString();
      batchJobs.set(batchId, finalBatch);
      
      console.log(`🎉 Batch processing ${batchId} completed: ${finalBatch.progress.completed} success, ${finalBatch.progress.failed} failed`);
    }

  } catch (error) {
    console.error(`❌ Batch processing ${batchId} failed:`, error);
    
    const errorBatch = batchJobs.get(batchId);
    if (errorBatch) {
      errorBatch.status = 'failed';
      errorBatch.completedAt = new Date().toISOString();
      batchJobs.set(batchId, errorBatch);
    }
  }
}

async function processTranscriptionForBatch(
  batchId: string,
  transcriptionId: string,
  options: UnifiedProcessingRequest['options'] = {}
): Promise<void> {
  
  const batch = batchJobs.get(batchId);
  if (!batch) return;

  const result: ProcessingResult = {
    transcriptionId,
    filename: 'unknown',
    status: 'pending'
  };

  try {
    // Get transcription data
    const transcriptionResult = await sql`
      SELECT id, filename, transcription_text, summary_text
      FROM transcriptions 
      WHERE id = ${transcriptionId}
    `;

    if (transcriptionResult.rows.length === 0) {
      result.status = 'skipped';
      result.skipReason = 'Transcription not found';
      updateBatchResult(batchId, result);
      return;
    }

    const transcription = transcriptionResult.rows[0];
    result.filename = transcription.filename;

    // Check if already processed and not forcing reprocess
    if (transcription.transcription_text && !options.forceReprocess) {
      result.status = 'skipped';
      result.skipReason = 'Already processed';
      result.transcriptionText = transcription.transcription_text;
      result.summaryText = transcription.summary_text;
      updateBatchResult(batchId, result);
      return;
    }

    result.status = 'processing';
    updateBatchResult(batchId, result);

    // For existing transcriptions, we'll use the transcription service
    // to regenerate summaries or reprocess if needed
    if (transcription.transcription_text && options.forceReprocess) {
      // Regenerate summary
      const summaryResponse = await fetch('/api/voice/summarize', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          transcriptionIds: [transcriptionId],
          summaryType: options.summaryType || 'clinical'
        })
      });

      if (summaryResponse.ok) {
        const summaryData = await summaryResponse.json();
        result.status = 'completed';
        result.transcriptionText = transcription.transcription_text;
        result.summaryText = summaryData.summary?.text;
      } else {
        result.status = 'failed';
        result.error = 'Summary generation failed';
      }
    } else {
      // This is a placeholder - in the real implementation, this would trigger
      // transcription if the audio file is available
      result.status = 'skipped';
      result.skipReason = 'No audio file available for transcription';
    }

  } catch (error) {
    console.error(`❌ Failed to process transcription ${transcriptionId} in batch ${batchId}:`, error);
    result.status = 'failed';
    result.error = error instanceof Error ? error.message : 'Unknown error';
  }

  updateBatchResult(batchId, result);
}

function updateBatchResult(batchId: string, result: ProcessingResult): void {
  const batch = batchJobs.get(batchId);
  if (!batch) return;

  // Update or add result
  const existingIndex = batch.results.findIndex(r => r.transcriptionId === result.transcriptionId);
  if (existingIndex >= 0) {
    batch.results[existingIndex] = result;
  } else {
    batch.results.push(result);
  }

  // Update progress counters
  const completed = batch.results.filter(r => r.status === 'completed').length;
  const failed = batch.results.filter(r => r.status === 'failed').length;
  const pending = batch.progress.total - completed - failed;

  batch.progress = {
    total: batch.progress.total,
    completed,
    failed,
    pending: Math.max(0, pending)
  };

  // Calculate estimated time remaining
  if (completed > 0) {
    const elapsed = Date.now() - new Date(batch.startedAt).getTime();
    const avgTimePerItem = elapsed / completed;
    batch.estimatedTimeRemaining = Math.round(avgTimePerItem * pending);
  }

  batchJobs.set(batchId, batch);
}

/**
 * DELETE endpoint to clean up completed batch jobs
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const batchId = searchParams.get('batchId');
    const cleanupType = searchParams.get('type') || 'completed';

    let removedCount = 0;

    if (batchId) {
      // Remove specific batch
      if (batchJobs.delete(batchId)) {
        removedCount = 1;
      }
    } else {
      // Clean up based on type
      const batchesToRemove: string[] = [];
      
      for (const [id, batch] of batchJobs.entries()) {
        const shouldRemove = 
          (cleanupType === 'completed' && batch.status === 'completed') ||
          (cleanupType === 'failed' && batch.status === 'failed') ||
          (cleanupType === 'old' && batch.completedAt && 
           new Date(batch.completedAt).getTime() < Date.now() - 24 * 60 * 60 * 1000);
        
        if (shouldRemove) {
          batchesToRemove.push(id);
        }
      }

      for (const id of batchesToRemove) {
        batchJobs.delete(id);
        removedCount++;
      }
    }

    return NextResponse.json({
      success: true,
      message: `Removed ${removedCount} batch jobs`,
      removedCount,
      remainingBatches: batchJobs.size
    });

  } catch (error) {
    console.error('❌ Error cleaning up batch jobs:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Cleanup failed',
      code: 'CLEANUP_ERROR'
    }, { status: 500 });
  }
}