{"version": "2.0.0", "options": {"cwd": "c:\\temp-dental"}, "tasks": [{"label": "Launch Audio Transfer GUI", "type": "shell", "command": "python ${workspaceFolder}/archive/audio_transfer_gui.py", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": []}, {"label": "Start Next.js Server", "type": "shell", "command": "npm run dev", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": []}, {"label": "Open Schedule in Simple Browser", "type": "shell", "command": "cmd /c start vscode://simpleBrowser/open?url=http://localhost:3001", "presentation": {"reveal": "silent", "panel": "shared"}, "problemMatcher": []}, {"type": "func", "label": "func: host start", "command": "host start", "problemMatcher": "$func-node-watch", "isBackground": true, "dependsOn": "npm watch (functions)", "options": {"cwd": "${workspaceFolder}/azure-functions"}}, {"type": "shell", "label": "npm build (functions)", "command": "npm run build", "dependsOn": "npm install (functions)", "problemMatcher": "$tsc", "options": {"cwd": "${workspaceFolder}/azure-functions"}}, {"type": "shell", "label": "npm watch (functions)", "command": "npm run watch", "dependsOn": "npm install (functions)", "problemMatcher": "$tsc-watch", "group": {"kind": "build", "isDefault": true}, "isBackground": true, "options": {"cwd": "${workspaceFolder}/azure-functions"}}, {"type": "shell", "label": "npm install (functions)", "command": "npm install", "options": {"cwd": "${workspaceFolder}/azure-functions"}}, {"type": "shell", "label": "npm prune (functions)", "command": "npm prune --production", "dependsOn": "npm build (functions)", "problemMatcher": [], "options": {"cwd": "${workspaceFolder}/azure-functions"}}]}