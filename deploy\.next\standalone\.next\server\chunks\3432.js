"use strict";exports.id=3432,exports.ids=[3432],exports.modules={13432:(e,r,t)=>{t.r(r),t.d(r,{VoiceRecordingsTab:()=>o});var s=t(60687);t(43210);var l=t(42624);function o({isDarkMode:e=!1}){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:`text-center p-6 rounded-lg border ${e?"bg-gradient-to-r from-blue-900 to-green-900 border-slate-700":"bg-gradient-to-r from-blue-50 to-green-50 border-gray-300"}`,children:[(0,s.jsx)("h2",{className:`text-2xl font-bold mb-2 ${e?"text-blue-300":"text-gray-800"}`,children:"\uD83D\uDE80 Real-Time Voice Transcription"}),(0,s.jsx)("p",{className:`${e?"text-blue-200":"text-gray-600"}`,children:"Live streaming transcription with real-time progress updates - no more mystery failures!"})]}),(0,s.jsx)(l.o,{}),(0,s.jsxs)("div",{className:`rounded p-4 ${e?"bg-yellow-900 border-yellow-700":"bg-yellow-50 border-yellow-200"} border`,children:[(0,s.jsx)("h4",{className:`font-semibold mb-2 ${e?"text-yellow-300":"text-yellow-800"}`,children:"✨ System Upgraded!"}),(0,s.jsx)("p",{className:`text-sm ${e?"text-yellow-200":"text-yellow-700"}`,children:'This tab now uses the new real-time streaming transcription system. No more "Started processing 93 files" with nothing happening - you\'ll see live updates for every step!'})]})]})}}};