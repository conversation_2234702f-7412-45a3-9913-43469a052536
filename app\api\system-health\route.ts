import { NextRequest, NextResponse } from 'next/server';
import { checkDatabaseHealth } from '@/lib/database/index';

export async function GET(request: NextRequest) {
  // Check for internal call header to bypass authentication
  const isInternalCall = request.headers.get('X-Internal-Call') === 'true';
  try {
    // Try to connect to the database
    console.log('🔍 Testing database connection...');
    
    const health = await checkDatabaseHealth();

    if (health.connected) {
      console.log('✅ Database connection successful');
      return NextResponse.json({
        database_connected: true,
        message: 'Database connection successful',
        status: 'healthy',
        timestamp: new Date().toISOString(),
        server: process.env.AZURE_SQL_SERVER,
        database: process.env.AZURE_SQL_DATABASE
      });
    } else {
      console.error('❌ Database connection failed:', health.error);
      return NextResponse.json({
        database_connected: false,
        message: `Database connection failed: ${health.error}`,
        status: 'connection_error',
        timestamp: new Date().toISOString(),
        error_details: health.error
      });
    }

  } catch (error) {
    console.error('❌ System health check failed:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json({
      database_connected: false,
      message: `System health check failed: ${errorMessage}`,
      status: 'system_error',
      timestamp: new Date().toISOString(),
      error_details: errorMessage
    });
  }
}

// Also support POST for consistency
export async function POST(request: NextRequest) {
  return GET(request);
}