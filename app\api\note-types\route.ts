import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function GET(request: NextRequest) {
  try {
    console.log('Note Types API: Fetching available note types');

    const credentials = loadCredentials();
    const sikkaClient = new SikkaApiClient(credentials);
    await sikkaClient.authenticate();

    // Get note types from Sikka API
    const noteTypes = await sikkaClient.getNoteTypes();

    console.log(`Note Types API: Found ${noteTypes.length} note types`);

    return NextResponse.json({
      success: true,
      noteTypes,
      total: noteTypes.length
    });

  } catch (error) {
    console.error('Note Types API error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to fetch note types',
        noteTypes: []
      },
      { status: 500 }
    );
  }
}
