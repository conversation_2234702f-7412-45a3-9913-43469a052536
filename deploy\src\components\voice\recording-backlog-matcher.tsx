"use client";

import React, { useState, useEffect } from "react";
import { Play, Pause, FileText, User, Calendar, Search, ArrowRight, CheckCircle, Clock, Filter, RefreshCw } from "lucide-react";

interface VoiceRecording {
  id: string;
  name: string;
  duration: number;
  size: number;
  createdAt: string;
  transcription?: string;
  summary?: string;
  matchedAppointmentId?: string;
  patientName?: string;
  appointmentDate?: string;
  provider?: string;
}

interface Appointment {
  id: string;
  patientName: string;
  provider: string;
  operatory: string;
  date: string;
  startTime: string;
  endTime: string;
  description: string;
  type: string;
}

interface RecordingBacklogMatcherProps {
  isDarkMode: boolean;
}

export function RecordingBacklogMatcher({ isDarkMode }: RecordingBacklogMatcherProps) {
  const [recordings, setRecordings] = useState<VoiceRecording[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [currentRecordingIndex, setCurrentRecordingIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDateRange, setSelectedDateRange] = useState("all");
  const [isLoading, setIsLoading] = useState(true);
  const [isMatching, setIsMatching] = useState(false);

  // Get unmatched recordings only
  const unmatchedRecordings = recordings.filter(r => !r.matchedAppointmentId);
  const currentRecording = unmatchedRecordings[currentRecordingIndex];

  // Filter appointments based on search and date
  const filteredAppointments = appointments.filter(apt => {
    const matchesSearch = searchQuery === "" || 
      apt.patientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      apt.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      apt.provider.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesDate = selectedDateRange === "all" || (() => {
      const aptDate = new Date(apt.date);
      const recordingDate = currentRecording ? new Date(currentRecording.createdAt) : new Date();
      const daysDiff = Math.abs((aptDate.getTime() - recordingDate.getTime()) / (1000 * 60 * 60 * 24));
      
      switch (selectedDateRange) {
        case "same-day": return daysDiff < 1;
        case "week": return daysDiff <= 7;
        case "month": return daysDiff <= 30;
        default: return true;
      }
    })();

    return matchesSearch && matchesDate;
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Load recordings
      const recordingsResponse = await fetch('/api/voice/recordings-fast');
      if (recordingsResponse.ok) {
        const recordingsData = await recordingsResponse.json();
        setRecordings(recordingsData.recordings || []);
      }

      // Load appointments for a wider date range
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30); // 30 days ago
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + 30); // 30 days ahead

      const appointmentsResponse = await fetch(
        `/api/appointments?startDate=${startDate.toISOString().split('T')[0]}&endDate=${endDate.toISOString().split('T')[0]}`
      );
      if (appointmentsResponse.ok) {
        const appointmentsData = await appointmentsResponse.json();
        setAppointments(appointmentsData.appointments || []);
      }
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const matchRecording = async (appointment: Appointment) => {
    if (!currentRecording) return;

    setIsMatching(true);
    try {
      const response = await fetch('/api/voice/match-recording', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          recordingId: currentRecording.id,
          appointmentId: appointment.id,
          patientName: appointment.patientName,
          appointmentDate: appointment.date,
          provider: appointment.provider
        })
      });

      if (response.ok) {
        // Update local state
        setRecordings(prev => prev.map(r =>
          r.id === currentRecording.id
            ? {
                ...r,
                matchedAppointmentId: appointment.id,
                patientName: appointment.patientName,
                appointmentDate: appointment.date,
                provider: appointment.provider
              }
            : r
        ));

        // Move to next recording
        if (currentRecordingIndex < unmatchedRecordings.length - 1) {
          setCurrentRecordingIndex(prev => prev + 1);
        } else {
          setCurrentRecordingIndex(0); // Loop back or handle completion
        }
      }
    } catch (error) {
      console.error('Failed to match recording:', error);
    } finally {
      setIsMatching(false);
    }
  };

  const skipRecording = () => {
    if (currentRecordingIndex < unmatchedRecordings.length - 1) {
      setCurrentRecordingIndex(prev => prev + 1);
    } else {
      setCurrentRecordingIndex(0);
    }
  };

  const goToPrevious = () => {
    if (currentRecordingIndex > 0) {
      setCurrentRecordingIndex(prev => prev - 1);
    } else {
      setCurrentRecordingIndex(unmatchedRecordings.length - 1);
    }
  };

  const formatFileSize = (bytes: number): string => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Loading recordings...</span>
      </div>
    );
  }

  if (unmatchedRecordings.length === 0) {
    return (
      <div className="text-center py-12">
        <CheckCircle className="h-16 w-16 mx-auto text-green-600 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          All recordings matched!
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Great job! All voice recordings have been matched to appointments.
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Recording Backlog Matcher
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Match recordings one-by-one to appointments. {unmatchedRecordings.length} recordings remaining.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Recording Panel */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Current Recording ({currentRecordingIndex + 1} of {unmatchedRecordings.length})
            </h3>
            <div className="flex space-x-2">
              <button
                onClick={goToPrevious}
                className="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-600"
              >
                Previous
              </button>
              <button
                onClick={skipRecording}
                className="px-3 py-1 bg-yellow-200 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 rounded text-sm hover:bg-yellow-300 dark:hover:bg-yellow-700"
              >
                Skip
              </button>
            </div>
          </div>

          {currentRecording && (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">
                  {currentRecording.name}
                </h4>
                <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
                  <span>{formatDuration(currentRecording.duration)}</span>
                  <span>•</span>
                  <span>{formatFileSize(currentRecording.size)}</span>
                  <span>•</span>
                  <span>{new Date(currentRecording.createdAt).toLocaleDateString()}</span>
                </div>
              </div>

              {currentRecording.summary && (
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="text-xs font-medium text-blue-800 dark:text-blue-300 mb-1">
                        Visit Summary
                      </div>
                      <div className="text-sm text-blue-900 dark:text-blue-100 leading-relaxed">
                        {currentRecording.summary}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {!currentRecording.summary && currentRecording.transcription && (
                <div className="p-3 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Transcription Preview
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
                    {currentRecording.transcription.substring(0, 200)}...
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Appointment Search Panel */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Find Matching Appointment
          </h3>

          {/* Search and Filters */}
          <div className="space-y-4 mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search patients, procedures, providers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            <select
              value={selectedDateRange}
              onChange={(e) => setSelectedDateRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">All dates</option>
              <option value="same-day">Same day</option>
              <option value="week">Within 1 week</option>
              <option value="month">Within 1 month</option>
            </select>
          </div>

          {/* Appointments List */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {filteredAppointments.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No appointments found</p>
                <p className="text-xs">Try adjusting your search or date filter</p>
              </div>
            ) : (
              filteredAppointments.map((appointment) => (
                <button
                  key={appointment.id}
                  onClick={() => matchRecording(appointment)}
                  disabled={isMatching}
                  className="w-full p-3 text-left border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {appointment.patientName}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {appointment.description}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                        {new Date(appointment.date).toLocaleDateString()} • {appointment.startTime} • {appointment.provider}
                      </div>
                    </div>
                    <ArrowRight className="h-4 w-4 text-gray-400" />
                  </div>
                </button>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
