import requests
import json
from datetime import datetime, timedelta
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 60

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def fetch_paginated_appointments(request_key, date):
    """Fetch appointments for a specific date using pagination."""
    headers = {"Request-Key": request_key}
    
    print(f"Fetching appointments for {date} with pagination...")
    
    # Try v2 endpoint with pagination
    all_items = []
    offset = 0
    limit = 50
    max_pages = 20  # Increased to get more appointments
    
    for page in range(max_pages):
        try:
            params = {
                "date": date,
                "offset": offset,
                "limit": limit
            }
            
            resp = requests.get(
                f"{API_BASE_V2}/appointments", 
                headers=headers, 
                params=params,
                timeout=API_TIMEOUT
            )
            
            if resp.status_code == 200:
                data = resp.json()
                
                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    all_items.extend(items)
                    
                    print(f"  Fetched {len(items)} appointments (page {page+1}, offset {offset})")
                    
                    if len(items) < limit:
                        break
                    
                    offset += limit
                else:
                    print("  No appointments found")
                    break
            else:
                print(f"Error fetching appointments: {resp.status_code}")
                print(resp.text)
                break
        except Exception as e:
            print(f"Error: {e}")
            break
    
    print(f"Total appointments fetched: {len(all_items)}")
    
    # Check if appointments have operatory information
    has_operatory = any(a.get("operatory") for a in all_items)
    if has_operatory:
        print("Appointments have operatory information")
    else:
        print("Appointments do NOT have operatory information")
    
    # Count appointments by operatory
    operatory_counts = {}
    for appt in all_items:
        operatory = appt.get("operatory", "N/A")
        if operatory not in operatory_counts:
            operatory_counts[operatory] = 0
        operatory_counts[operatory] += 1
    
    print("\nAppointments by operatory:")
    for op, count in sorted(operatory_counts.items()):
        print(f"  {op}: {count} appointments")
    
    # Count appointments by provider
    provider_counts = {}
    for appt in all_items:
        provider = appt.get("provider_id", "N/A")
        if provider not in provider_counts:
            provider_counts[provider] = 0
        provider_counts[provider] += 1
    
    print("\nAppointments by provider:")
    for provider, count in sorted(provider_counts.items()):
        print(f"  {provider}: {count} appointments")
    
    return all_items

def main():
    # Get date from command line or use default
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        date = "2023-05-16"  # Default date
    
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Fetch appointments with pagination
    appointments = fetch_paginated_appointments(request_key, date)
    
    # Print sample appointments
    print("\nSample appointments:")
    for i, appt in enumerate(sorted(appointments, key=lambda a: a.get("time", ""))[:10]):
        print(f"  {i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('operatory', 'N/A')} - {appt.get('provider_id', 'N/A')} - {appt.get('description', '')}")
    
    print("\nFetch complete.")

if __name__ == "__main__":
    main()
