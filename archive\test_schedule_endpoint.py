import requests
import json
from datetime import datetime, timedelta
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 60

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def test_schedule_endpoint(request_key, date):
    """Test the schedule endpoint."""
    headers = {"Request-Key": request_key}
    
    print(f"Testing schedule endpoint for {date}...")
    
    # Try v2 schedule endpoint
    print("\nTrying v2 schedule endpoint:")
    try:
        resp = requests.get(
            f"{API_BASE_V2}/schedule", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v4 schedule endpoint
    print("\nTrying v4 schedule endpoint:")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/schedule", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v2 daily_schedule endpoint
    print("\nTrying v2 daily_schedule endpoint:")
    try:
        resp = requests.get(
            f"{API_BASE_V2}/daily_schedule", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v4 daily_schedule endpoint
    print("\nTrying v4 daily_schedule endpoint:")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/daily_schedule", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v2 practice_schedule endpoint
    print("\nTrying v2 practice_schedule endpoint:")
    try:
        resp = requests.get(
            f"{API_BASE_V2}/practice_schedule", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")
    
    # Try v4 practice_schedule endpoint
    print("\nTrying v4 practice_schedule endpoint:")
    try:
        resp = requests.get(
            f"{API_BASE_V4}/practice_schedule", 
            headers=headers, 
            params={"date": date},
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            print("Response:")
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {resp.status_code}")
            print(resp.text)
    except Exception as e:
        print(f"Error: {e}")

def main():
    # Get date from command line or use default
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        # Use a date that we know has appointments in the test data
        date = "2025-05-16"
    
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Test schedule endpoint
    test_schedule_endpoint(request_key, date)
    
    print("\nTest complete.")

if __name__ == "__main__":
    main()
