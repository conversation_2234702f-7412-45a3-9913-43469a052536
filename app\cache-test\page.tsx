'use client';

export default function CacheTestPage() {
  // Restrict access in production
  if (process.env.NODE_ENV === 'production') {
    return <div className="p-8 text-center text-gray-500">Access denied in production</div>;
  }
  const timestamp = new Date().toISOString();
  
  return (
    <div className="container mx-auto p-6">
      <div className="bg-red-100 border-2 border-red-500 rounded-lg p-8 text-center">
        <h1 className="text-4xl font-bold text-red-800 mb-4">
          🚨 CACHE TEST - VERSION 2.0.1 🚨
        </h1>
        <div className="text-2xl text-red-700 mb-4">
          IF YOU SEE THIS, THE NEW VERSION IS DEPLOYED!
        </div>
        <div className="text-lg text-red-600">
          FileResolver System: ACTIVE<br/>
          WebUSB-Only Interface: ACTIVE<br/>
          Debug Console: AVAILABLE at /debug-transcription<br/>
          Timestamp: {timestamp}
        </div>
        <div className="mt-6 text-sm text-red-500">
          This page proves the new deployment is live.
        </div>
      </div>
    </div>
  );
}