/**
 * Centralized patient name formatting utility
 * Ensures consistent "First LAST" format across the entire application
 */

export interface Patient {
  firstName?: string;
  lastName?: string;
  middleName?: string;
}

/**
 * Format patient name consistently as "First LAST"
 * @param name - Patient name string in various formats
 * @returns Formatted name as "First LAST"
 */
export function formatPatientName(name: string): string {
  if (!name || name.trim() === '') return 'No Patient';

  const patientName = String(name).trim();

  // Handle "Last, First" format (with comma) - convert to "First LAST"
  if (patientName.includes(',')) {
    const parts = patientName.split(',').map(part => part.trim());
    if (parts.length >= 2) {
      const lastName = parts[0].trim();
      const firstName = parts[1].split(' ')[0]?.trim(); // Take first word after comma
      if (firstName && lastName) {
        const formattedFirst = firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase();
        const formattedLast = lastName.toUpperCase();
        return `${formattedFirst} ${formattedLast}`;
      }
    }
  }

  // Handle space-separated names
  const nameParts = patientName.split(' ').filter(part => part.trim());
  if (nameParts.length === 0) return 'No Patient';

  if (nameParts.length === 1) {
    // Only one name part - treat as first name
    const formatted = nameParts[0].charAt(0).toUpperCase() + nameParts[0].slice(1).toLowerCase();
    return formatted;
  }

  // For multiple parts, always format as "First LAST"
  if (nameParts.length >= 2) {
    let firstName = '';
    let lastName = '';

    // Detect format and extract first and last names
    const firstPart = nameParts[0];
    const lastPart = nameParts[nameParts.length - 1];

    // Special case: Common dental software pattern where last name comes first
    // Examples: "Elidor Benoit" should be "Benoit ELIDOR"
    // Heuristic: If the first part looks like a last name (longer, more formal)
    // and the second part looks like a first name (shorter, more common)
    if (nameParts.length === 2) {
      // Check if this might be "LastName FirstName" format
      // This is common in dental software where last names are listed first

      // For now, let's assume the Sikka API returns "LastName FirstName" format
      // based on the pattern we're seeing with "Elidor Benoit"
      firstName = lastPart;  // Second part is first name
      lastName = firstPart;  // First part is last name
    } else {
      // For more than 2 parts, use the original logic
      // If first part is all uppercase and last part is mixed case, it's likely "LAST First"
      if (firstPart === firstPart.toUpperCase() && lastPart !== lastPart.toUpperCase()) {
        firstName = lastPart;
        lastName = firstPart;
      }
      // If last part is all uppercase, it's likely "First LAST" or "First Middle LAST"
      else if (lastPart === lastPart.toUpperCase()) {
        firstName = firstPart;
        lastName = lastPart;
      }
      // Default: treat first part as first name, last part as last name
      else {
        firstName = firstPart;
        lastName = lastPart;
      }
    }

    // Format consistently as "First LAST"
    const formattedFirst = firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase();
    const formattedLast = lastName.toUpperCase();

    return `${formattedFirst} ${formattedLast}`;
  }

  return patientName;
}

/**
 * Format patient name from structured data (firstName, lastName fields)
 * @param patient - Patient object with firstName, lastName, etc.
 * @returns Formatted name as "First LAST"
 */
export function formatPatientNameFromFields(patient: Patient): string {
  const firstName = patient.firstName?.trim();
  const lastName = patient.lastName?.trim();

  if (firstName && lastName) {
    const formattedFirst = firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase();
    const formattedLast = lastName.toUpperCase();
    return `${formattedFirst} ${formattedLast}`;
  } else if (firstName) {
    return firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase();
  } else if (lastName) {
    return lastName.toUpperCase();
  }

  // Fallback to joining all parts if structured data isn't complete
  const parts = [patient.firstName, patient.middleName, patient.lastName].filter(Boolean);
  if (parts.length > 0) {
    return formatPatientName(parts.join(' '));
  }

  return 'No Patient';
}

/**
 * Extract first name from formatted patient name
 * @param formattedName - Name in "First LAST" format
 * @returns First name only
 */
export function getFirstName(formattedName: string): string {
  return formattedName.split(' ')[0] || formattedName;
}

/**
 * Extract last name from formatted patient name
 * @param formattedName - Name in "First LAST" format
 * @returns Last name only
 */
export function getLastName(formattedName: string): string {
  const parts = formattedName.split(' ');
  return parts[parts.length - 1] || '';
}
