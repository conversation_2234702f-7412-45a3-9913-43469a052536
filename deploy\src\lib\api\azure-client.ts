/**
 * Azure API Client
 * Handles API requests to the Azure backend, including the X-Client-ID header.
 */

const AZURE_BASE_URL = process.env.NEXT_PUBLIC_AZURE_BASE_URL;
const AZURE_CLIENT_ID = process.env.NEXT_PUBLIC_AZURE_CLIENT_ID;

/**
 * Helper function to make API requests to the Azure backend
 */
async function fetchFromAzure(path: string, options: RequestInit = {}): Promise<Response> {
    if (!AZURE_BASE_URL || !AZURE_CLIENT_ID) {
        throw new Error('Azure API environment variables are not set.');
    }

    const headers = {
        ...options.headers,
        'X-Client-ID': AZURE_CLIENT_ID,
        'Content-Type': 'application/json',
    };

    const response = await fetch(`${AZURE_BASE_URL}${path}`, {
        ...options,
        headers,
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Azure API request failed with status ${response.status}: ${errorText}`);
    }

    return response;
}

export default fetchFromAzure;