@echo off
echo ========================================
echo    Starting Dental App Service
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

cd /d "C:\DentalApp"

echo Checking PM2 installation...
pm2 --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: PM2 not found. Please run INSTALL.bat first.
    pause
    exit /b 1
)

echo Starting Dental App service...
call pm2 start config\ecosystem.config.js

:: Wait for service to start
echo Waiting for service to start...
timeout /t 3 /nobreak >nul

:: Check service status
call pm2 list | findstr "dental-app" | findstr "online" >nul
if %errorLevel%==0 (
    echo.
    echo ========================================
    echo    Service Started Successfully!
    echo ========================================
    echo.
    echo Dental App is now running.
    echo Access the app at: http://localhost:3000
    echo.
    echo Service will automatically restart if it crashes.
    echo Service will start automatically when Windows boots.
    echo.
) else (
    echo.
    echo ========================================
    echo    Service Start Failed
    echo ========================================
    echo.
    echo The service could not be started.
    echo Please check the logs for more information.
    echo.
    echo Run: C:\DentalApp\scripts\logs.bat
    echo.
)

echo Press any key to continue...
pause >nul
