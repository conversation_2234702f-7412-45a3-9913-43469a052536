import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import JSZip from 'jszip';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fileType = searchParams.get('file'); // 'bat', 'ps1', 'zip', or 'gui'
    const raw = searchParams.get('raw') === 'true'; // Return raw content instead of file download

    // Handle ZIP package download
    if (fileType === 'zip' || fileType === 'usb-portable-package.zip') {
      try {
        const batPath = path.join(process.cwd(), 'usb-portable', 'UPLOAD-RECORDINGS.bat');
        const ps1Path = path.join(process.cwd(), 'usb-portable', 'upload-recordings.ps1');

        // Check if both files exist
        if (!existsSync(batPath) || !existsSync(ps1Path)) {
          return NextResponse.json({
            error: 'USB Portable Tool files not found',
            message: 'Both .bat and .ps1 files are required for the package',
            missing: [
              !existsSync(batPath) ? 'UPLOAD-RECORDINGS.bat' : null,
              !existsSync(ps1Path) ? 'upload-recordings.ps1' : null
            ].filter(Boolean)
          }, { status: 404 });
        }

        // Read both files
        const batFile = await readFile(batPath);
        const ps1File = await readFile(ps1Path);

        // Create ZIP package
        const zip = new JSZip();
        zip.file('UPLOAD-RECORDINGS.bat', batFile);
        zip.file('upload-recordings.ps1', ps1File);
        zip.file('README.txt', `USB Portable Dental Recordings Upload Tool Package

Version: 1.0.0
Release Date: ${new Date().toISOString().split('T')[0]}

INSTALLATION:
1. Extract both files to a folder on your USB drive
2. Run UPLOAD-RECORDINGS.bat to start the upload process

FEATURES:
- Auto-detects USB drive location
- Scans multiple recorder folder paths (FOLDER_A through FOLDER_E)
- Uploads to Azure Blob Storage with date-organized structure
- Archives uploaded files locally after successful upload
- Smart update checking and auto-update functionality
- Progress tracking and detailed error handling
- Cache invalidation to notify dental app of new files

REQUIREMENTS:
- Windows PowerShell
- Internet connection for uploads
- Azure authentication configured

For support, contact your dental office IT administrator.`);

        const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

        return new NextResponse(zipBuffer, {
          headers: {
            'Content-Type': 'application/zip',
            'Content-Disposition': 'attachment; filename="USB-Portable-Tool-Package.zip"',
            'Content-Length': zipBuffer.length.toString(),
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

      } catch (zipError) {
        return NextResponse.json({
          error: 'Failed to create ZIP package',
          details: zipError instanceof Error ? zipError.message : 'Unknown error'
        }, { status: 500 });
      }
    }

    if (!fileType || !['bat', 'ps1', 'gui'].includes(fileType)) {
      return NextResponse.json({
        error: 'Invalid file type. Use ?file=bat, ?file=ps1, ?file=gui, or ?file=zip'
      }, { status: 400 });
    }
    
    // Determine file path based on type
    let filePath: string;
    let fileName: string;
    let contentType: string;

    if (fileType === 'bat') {
      filePath = path.join(process.cwd(), 'usb-portable', 'UPLOAD-RECORDINGS.bat');
      fileName = 'UPLOAD-RECORDINGS.bat';
      contentType = 'application/octet-stream';
    } else if (fileType === 'gui') {
      filePath = path.join(process.cwd(), 'usb-portable', 'dental-usb-uploader-gui.ps1');
      fileName = 'dental-usb-uploader-gui.ps1';
      contentType = 'application/octet-stream';
    } else {
      filePath = path.join(process.cwd(), 'usb-portable', 'upload-recordings.ps1');
      fileName = 'upload-recordings.ps1';
      contentType = 'application/octet-stream';
    }
    
    // Check if file exists
    if (!existsSync(filePath)) {
      return NextResponse.json({
        error: `USB Portable Tool ${fileType.toUpperCase()} file not found`,
        message: `The file ${fileName} needs to be built first.`,
        path: filePath
      }, { status: 404 });
    }

    // Read file content
    const fileContent = await readFile(filePath, 'utf-8');

    // If raw content is requested, return as plain text
    if (raw) {
      console.log(`USB Portable Tool ${fileType.toUpperCase()} raw content requested: ${fileName}`);

      return new NextResponse(fileContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
    }

    // Otherwise, serve as file download
    const fileBuffer = Buffer.from(fileContent, 'utf-8');

    // Log download
    console.log(`USB Portable Tool ${fileType.toUpperCase()} file downloaded: ${fileName}`);

    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    
  } catch (error) {
    console.error('Error serving USB portable tool file:', error);
    
    return NextResponse.json({
      error: 'Failed to download file',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Track download statistics
export async function POST(request: NextRequest) {
  try {
    const { fileType, version } = await request.json();
    
    // Log download for analytics
    console.log(`USB Portable Tool download tracked:`, {
      fileType,
      version,
      timestamp: new Date().toISOString(),
      userAgent: request.headers.get('user-agent')
    });
    
    return NextResponse.json({
      success: true,
      message: 'Download tracked successfully'
    });
    
  } catch (error) {
    console.error('Error tracking download:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to track download'
    }, { status: 500 });
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
