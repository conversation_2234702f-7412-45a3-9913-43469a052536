/**
 * Sikka API Client for AI Office Manager
 * Simplified version focused on data retrieval for AI processing
 */

interface SikkaCredentials {
  office_id: string;
  secret_key: string;
  app_id: string;
  app_key: string;
}

interface SikkaAppointment {
  appointment_id: string;
  patient_id: string;
  appointment_date: string;
  appointment_time: string;
  operatory: string;
  provider_code: string;
  procedure_codes?: string[];
  status: string;
  notes?: string;
}

export class SikkaClient {
  private credentials: SikkaCredentials;
  private requestKey: string | null = null;

  constructor() {
    // Load credentials from environment variables
    this.credentials = {
      office_id: process.env.SIKKA_OFFICE_ID || 'D43989',
      secret_key: process.env.SIKKA_SECRET_KEY || '35442814D4396E20C222',
      app_id: process.env.SIKKA_APP_ID || 'fdd52aaffb0c1bead647874ba551db0c',
      app_key: process.env.SIKKA_APP_KEY || '88254bfa2224607ef425646aafe5f722',
    };
  }

  /**
   * Authenticate with Sikka API and get request key
   */
  async authenticate(): Promise<string> {
    try {
      const response = await fetch('https://api.sikkasoft.com/v4/request_key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          grant_type: 'request_key',
          office_id: this.credentials.office_id,
          secret_key: this.credentials.secret_key,
          app_id: this.credentials.app_id,
          app_key: this.credentials.app_key,
        }),
      });

      if (!response.ok) {
        throw new Error(`Authentication failed: ${response.status}`);
      }

      const data = await response.json();
      this.requestKey = data.request_key;

      if (!this.requestKey) {
        throw new Error('No request key in response');
      }

      return this.requestKey;
    } catch (error) {
      console.error('Sikka authentication error:', error);
      throw error;
    }
  }

  /**
   * Get operatory information
   */
  async getOperatories(): Promise<any[]> {
    try {
      if (!this.requestKey) {
        await this.authenticate();
      }

      const response = await fetch('https://api.sikkasoft.com/v2/operatories', {
        headers: {
          'Request-Key': this.requestKey!,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch operatories: ${response.status}`);
      }

      const data = await response.json();
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Error fetching operatories:', error);
      return [];
    }
  }

  /**
   * Get patient information (anonymized for AI processing)
   */
  async getPatients(limit: number = 100): Promise<any[]> {
    try {
      if (!this.requestKey) {
        await this.authenticate();
      }

      const response = await fetch(`https://api.sikkasoft.com/v2/patients?limit=${limit}`, {
        headers: {
          'Request-Key': this.requestKey!,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch patients: ${response.status}`);
      }

      const data = await response.json();
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Error fetching patients:', error);
      return [];
    }
  }

  /**
   * Get procedure information
   */
  async getProcedures(startDate: string, endDate?: string): Promise<any[]> {
    try {
      if (!this.requestKey) {
        await this.authenticate();
      }

      const actualEndDate = endDate || startDate;
      const response = await fetch(
        `https://api.sikkasoft.com/v4/procedures?startdate=${startDate}&enddate=${actualEndDate}`,
        {
          headers: {
            'Request-Key': this.requestKey!,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch procedures: ${response.status}`);
      }

      const data = await response.json();
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error('Error fetching procedures:', error);
      return [];
    }
  }

  /**
   * Get appointments for a date range
   */
  async getAppointments(startDate: string, endDate?: string): Promise<SikkaAppointment[]> {
    try {
      if (!this.requestKey) {
        await this.authenticate();
      }

      const actualEndDate = endDate || startDate;
      
      const response = await fetch(
        `https://api.sikkasoft.com/v2/appointments?startdate=${startDate}&enddate=${actualEndDate}&date_filter_on=appointment_date`,
        {
          headers: {
            'Request-Key': this.requestKey!,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch appointments: ${response.status}`);
      }

      const data = await response.json();
      const appointments: SikkaAppointment[] = [];

      // Process the response data
      if (Array.isArray(data) && data.length > 0 && data[0].items) {
        for (const item of data[0].items) {
          // Skip blocked appointments with no useful data
          if (item.description?.toLowerCase().trim() === 'blocked' && !item.patient_name) {
            continue;
          }

          appointments.push({
            appointment_id: item.appointment_sr_no || `${item.date}-${item.operatory}-${item.time}`,
            patient_id: item.patient_id || 'unknown',
            appointment_date: item.date || startDate,
            appointment_time: item.time || '8:00 AM',
            operatory: item.operatory || 'unknown',
            provider_code: item.provider || 'unknown',
            procedure_codes: item.procedure_codes ? [item.procedure_codes] : [],
            status: item.status || 'unknown',
            notes: item.description || '',
          });
        }
      }

      return appointments;
    } catch (error) {
      console.error('Error fetching appointments:', error);
      throw error;
    }
  }
}
