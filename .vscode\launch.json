{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Run Dental Schedule", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev", "--", "-p", "3001"], "cwd": "${workspaceFolder}/dental-schedule-next", "console": "integratedTerminal", "serverReadyAction": {"pattern": "- Local:\\s+(https?://\\S+)", "uriFormat": "%s", "action": "debugWithChrome"}}, {"name": "Attach to Node Functions", "type": "node", "request": "attach", "restart": true, "port": 9229, "preLaunchTask": "func: host start"}]}