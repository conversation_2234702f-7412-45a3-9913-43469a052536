{"app\\page.tsx -> ../src/components/ai/agent-chat": {"id": 4227, "files": ["static/chunks/vendors-298176f3d5a9c490.js", "static/chunks/4227.c9fc51c80e520d22.js"]}, "app\\page.tsx -> ../src/components/clinical-notes/simple-clinical-notes": {"id": 4418, "files": ["static/chunks/vendors-298176f3d5a9c490.js", "static/chunks/4418.45e6c6fbc1380b41.js"]}, "app\\page.tsx -> ../src/components/tools/tools-and-settings": {"id": 9233, "files": ["static/chunks/vendors-298176f3d5a9c490.js", "static/chunks/9233.b3eb30f9426a179a.js"]}, "app\\page.tsx -> ../src/components/tools/usb-transfer-download": {"id": 9335, "files": ["static/chunks/vendors-298176f3d5a9c490.js", "static/chunks/9335.b0398f30c50b9251.js"]}, "app\\page.tsx -> ../src/components/voice/voice-recordings-tab": {"id": 6455, "files": ["static/chunks/vendors-298176f3d5a9c490.js", "static/chunks/4854-f19009e4b94f83df.js", "static/chunks/8836.65808582652bff6b.js"]}, "app\\page.tsx -> ../src/components/voice/voice-upload-wrapper": {"id": 9715, "files": ["static/chunks/vendors-298176f3d5a9c490.js", "static/chunks/3235-f831b160f9835c64.js", "static/chunks/9715.8879c8f9fcf5975f.js"]}, "node_modules\\next\\dist\\client\\index.js -> ../pages/_app": {"id": 472, "files": ["static/chunks/vendors-298176f3d5a9c490.js"]}, "node_modules\\next\\dist\\client\\index.js -> ../pages/_error": {"id": 9341, "files": ["static/chunks/vendors-298176f3d5a9c490.js"]}, "src\\components\\usb\\webusb-integration-test.tsx -> ../../lib/usb-file-utils": {"id": 9346, "files": ["static/chunks/9346.508f375eeb769813.js"]}}