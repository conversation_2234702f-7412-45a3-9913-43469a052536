import { NextRequest, NextResponse } from 'next/server';
import { AzureStorageService } from '../../../../src/lib/azure-storage';
import { getCurrentSettings } from '../../settings/route';

interface TranscriptionLog {
  recordingId: string;
  filename: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  modelUsed: string;
  promptUsed: string;
  promptId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
  error?: string;
  transcriptionLength?: number;
  speakerSeparationEnabled: boolean;
  speakersDetected?: number;
  audioSource: string;
  fileSizeMB: number;
  chunkCount?: number;
  isChunked: boolean;
}

interface BatchTranscriptionResult {
  success: boolean;
  totalFiles: number;
  processed: number;
  skipped: number;
  failed: number;
  startTime: string;
  endTime: string;
  totalDuration: number;
  logs: TranscriptionLog[];
  errors: string[];
}

export async function POST(request: NextRequest) {
  console.warn(`Deprecated endpoint called: ${request.url}`);
  console.warn('This endpoint is deprecated. Use /api/voice/auto-process instead.');
  
  const batchStartTime = new Date().toISOString();
  const startTimestamp = Date.now();
  
  console.log(`🎙️ Starting batch transcription of all Azure audio files at ${batchStartTime}`);

  try {
    const { 
      forceRetranscribe = false,
      enableSpeakerSeparation = true,
      promptId = 'dental-transcription-context',
      customPrompt,
      openaiModel
    } = await request.json();

    // Load user settings
    const userSettings = await getCurrentSettings();
    const selectedModel = openaiModel || userSettings.transcriptionModel || 'whisper-1';

    console.log(`📋 Batch transcription settings:
    - Model: ${selectedModel}
    - Speaker separation: ${enableSpeakerSeparation}
    - Force retranscribe: ${forceRetranscribe}
    - Prompt ID: ${promptId}`);

    if (!AzureStorageService.isConfigured()) {
      return NextResponse.json(
        { error: 'Azure Storage is not configured' },
        { status: 400 }
      );
    }

    // Get all audio files from Azure
    console.log('🔍 Scanning Azure Blob Storage for audio files...');
    const allFiles = await AzureStorageService.listFiles('');
    
    // Filter for actual audio files (exclude metadata files)
    const audioFiles = allFiles.filter(file => {
      const ext = file.name.toLowerCase().split('.').pop();
      const isAudioExtension = ['mp3', 'wav', 'm4a', 'webm', 'ogg', 'flac', 'aac'].includes(ext || '');
      const isMetadataPath = file.path.includes('metadata/');
      const isJsonFile = ext === 'json';
      const isSystemFile = file.name.startsWith('.') || file.name.startsWith('_');

      // Only include files that are audio extensions and not system/metadata files
      return isAudioExtension &&
             !isMetadataPath &&
             !isJsonFile &&
             !isSystemFile &&
             file.size > 1024; // Exclude very small files (likely corrupted or empty)
    });

    console.log(`📁 Found ${audioFiles.length} audio files in Azure Storage`);

    if (audioFiles.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No audio files found to transcribe',
        totalFiles: 0,
        processed: 0,
        skipped: 0,
        failed: 0
      });
    }

    // Load existing transcriptions to check what's already done
    let existingTranscriptions = {};
    try {
      existingTranscriptions = await AzureStorageService.downloadJson('metadata/transcriptions.json');
      console.log(`📚 Loaded ${Object.keys(existingTranscriptions).length} existing transcriptions`);
    } catch (error) {
      console.log('📝 No existing transcriptions found, starting fresh');
    }

    const logs: TranscriptionLog[] = [];
    const errors: string[] = [];
    let processed = 0;
    let skipped = 0;
    let failed = 0;

    // Process each audio file
    for (let i = 0; i < audioFiles.length; i++) {
      const file = audioFiles[i];
      const recordingId = `azure-${file.name}-${file.size}-${Math.floor(file.size / 1024)}`;
      const logEntry: TranscriptionLog = {
        recordingId,
        filename: file.name,
        startTime: new Date().toISOString(),
        modelUsed: selectedModel,
        promptUsed: customPrompt || promptId,
        promptId: promptId,
        status: 'pending',
        speakerSeparationEnabled: enableSpeakerSeparation,
        audioSource: 'Azure Blob Storage',
        fileSizeMB: parseFloat((file.size / (1024 * 1024)).toFixed(2)),
        isChunked: false
      };

      console.log(`\n🎵 Processing ${i + 1}/${audioFiles.length}: ${file.name} (${logEntry.fileSizeMB} MB)`);

      try {
        // Check if already transcribed and not forcing retranscription
        if (!forceRetranscribe && existingTranscriptions[recordingId]) {
          console.log(`⏭️  Skipping ${file.name} - already transcribed`);
          logEntry.status = 'skipped';
          logEntry.endTime = new Date().toISOString();
          logs.push(logEntry);
          skipped++;
          continue;
        }

        logEntry.status = 'processing';
        
        // Call the individual transcription API
        // For server-side calls, construct the full URL properly
        const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
        const host = process.env.VERCEL_URL || process.env.WEBSITE_HOSTNAME || 'localhost:3000';
        const baseUrl = process.env.NEXT_PUBLIC_API_URL || `${protocol}://${host}`;

        console.log(`🔗 Calling transcription API: ${baseUrl}/api/voice/transcribe`);
        const transcribeResponse = await fetch(`${baseUrl}/api/voice/transcribe`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Internal-Call': 'true'
          },
          body: JSON.stringify({
            fileName: file.name,
            recordingId,
            forceRetranscribe: forceRetranscribe
          }),
        });

        const transcribeResult = await transcribeResponse.json();
        logEntry.endTime = new Date().toISOString();
        logEntry.duration = Date.now() - new Date(logEntry.startTime).getTime();

        console.log(`📊 Response for ${file.name}: status=${transcribeResponse.status}, ok=${transcribeResponse.ok}, has_transcription=${!!transcribeResult.transcription}`);

        if (transcribeResponse.ok && transcribeResult.transcription) {
          logEntry.status = 'completed';
          logEntry.transcriptionLength = transcribeResult.transcription?.length || 0;
          logEntry.isChunked = transcribeResult.method === 'chunked';
          logEntry.chunkCount = transcribeResult.chunkInfo?.totalChunks || 1;

          console.log(`✅ Successfully transcribed ${file.name} (${logEntry.transcriptionLength} chars, method: ${transcribeResult.method || 'direct'})`);
          processed++;
        } else {
          logEntry.status = 'failed';
          logEntry.error = transcribeResult.error || `HTTP ${transcribeResponse.status}: ${transcribeResponse.statusText}`;
          console.log(`❌ Failed to transcribe ${file.name}: ${logEntry.error}`);
          console.log(`📋 Full response:`, transcribeResult);
          errors.push(`${file.name}: ${logEntry.error}`);
          failed++;
        }

      } catch (error: any) {
        logEntry.status = 'failed';
        logEntry.error = error.message || 'Processing failed';
        logEntry.endTime = new Date().toISOString();
        logEntry.duration = Date.now() - new Date(logEntry.startTime).getTime();
        
        console.error(`💥 Error processing ${file.name}:`, error);
        errors.push(`${file.name}: ${error.message}`);
        failed++;
      }

      logs.push(logEntry);

      // Add delay between requests to be respectful to OpenAI API
      if (i < audioFiles.length - 1) {
        console.log('⏱️  Waiting 2 seconds before next transcription...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    const batchEndTime = new Date().toISOString();
    const totalDuration = Date.now() - startTimestamp;

    // Save detailed logs to Azure
    const logData = {
      batchId: `batch-${Date.now()}`,
      startTime: batchStartTime,
      endTime: batchEndTime,
      totalDuration,
      settings: {
        model: selectedModel,
        promptId,
        speakerSeparationEnabled: enableSpeakerSeparation,
        forceRetranscribe
      },
      summary: {
        totalFiles: audioFiles.length,
        processed,
        skipped,
        failed,
        successRate: audioFiles.length > 0 ? ((processed / audioFiles.length) * 100).toFixed(1) : '0'
      },
      logs,
      errors
    };

    try {
      await AzureStorageService.uploadJson(`metadata/batch-transcription-${Date.now()}.json`, logData);
      console.log('📊 Batch transcription logs saved to Azure Storage');
    } catch (error) {
      console.error('Failed to save batch logs:', error);
    }

    const result: BatchTranscriptionResult = {
      success: true,
      totalFiles: audioFiles.length,
      processed,
      skipped,
      failed,
      startTime: batchStartTime,
      endTime: batchEndTime,
      totalDuration,
      logs,
      errors
    };

    console.log(`\n🎉 Batch transcription completed!
    📊 Summary:
    - Total files: ${audioFiles.length}
    - Processed: ${processed}
    - Skipped: ${skipped}
    - Failed: ${failed}
    - Success rate: ${logData.summary.successRate}%
    - Total time: ${(totalDuration / 1000 / 60).toFixed(1)} minutes`);

    return NextResponse.json(
      {
        ...result,
        deprecated: true,
        message: 'This endpoint is deprecated. Use /api/voice/auto-process instead.'
      },
      {
        headers: {
          'X-Deprecated': 'true',
          'X-Replacement': '/api/voice/auto-process'
        }
      }
    );

  } catch (error: any) {
    console.error('💥 Batch transcription failed:', error);
    
    const batchEndTime = new Date().toISOString();
    const totalDuration = Date.now() - startTimestamp;

    return NextResponse.json({
      success: false,
      error: `Batch transcription failed: ${error.message}`,
      startTime: batchStartTime,
      endTime: batchEndTime,
      totalDuration,
      logs: [],
      errors: [error.message],
      deprecated: true,
      message: 'This endpoint is deprecated. Use /api/voice/auto-process instead.'
    }, { 
      status: 500,
      headers: {
        'X-Deprecated': 'true',
        'X-Replacement': '/api/voice/auto-process'
      }
    });
  }
}
