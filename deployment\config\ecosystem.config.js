// PM2 Configuration for Dental App Production
// 
// IMPORTANT: This configuration requires critical environment variables
// Reference .env.sample for complete documentation of all required variables
// 
// STARTUP VALIDATION: The application will validate required environment
// variables at startup and exit if critical variables are missing
//
// SECURITY: All sensitive credentials should be set as environment variables
// in the deployment environment, not hardcoded in this file
module.exports = {
  apps: [{
    name: 'dental-app',
    script: 'npm',
    args: 'start',
    cwd: './app',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      HOST: '0.0.0.0',
      
      // ============================================
      // OPENAI API CONFIGURATION
      // ============================================
      // CRITICAL: Update with actual OpenAI API key for production
      // SECURITY NOTE: Set OPENAI_API_KEY as environment variable in production
      OPENAI_API_KEY: process.env.OPENAI_API_KEY || 'sk-your_openai_api_key_here', // MUST be updated
      OPENAI_ORGANIZATION: '', // Optional: Organization ID
      OPENAI_TRANSCRIPTION_MODEL: 'whisper-1',
      OPENAI_CHAT_MODEL: 'gpt-4',
      OPENAI_MAX_TOKENS: 4000,
      OPENAI_TEMPERATURE: 0.3,
      
      // ============================================
      // AZURE STORAGE CONFIGURATION
      // ============================================
      // CRITICAL: Required for production voice processing
      // SECURITY NOTE: Set AZURE_STORAGE_CONNECTION_STRING as environment variable in production
      // This contains sensitive credentials and should never be hardcoded
      AZURE_STORAGE_CONNECTION_STRING: process.env.AZURE_STORAGE_CONNECTION_STRING || 'DefaultEndpointsProtocol=https;AccountName=yourstorageaccount;AccountKey=youraccountkey==;EndpointSuffix=core.windows.net', // MUST be updated
      AZURE_STORAGE_CONTAINER_NAME: 'dentalrecordings',
      AZURE_JOBS_CONTAINER_NAME: 'processingjobs',
      
      // Azure Functions Configuration
      // SECURITY NOTE: Set AzureWebJobsStorage as environment variable in production
      AzureWebJobsStorage: process.env.AzureWebJobsStorage || 'DefaultEndpointsProtocol=https;AccountName=yourstorageaccount;AccountKey=youraccountkey==;EndpointSuffix=core.windows.net', // MUST be updated
      FUNCTIONS_WORKER_RUNTIME: 'node',
      FUNCTIONS_EXTENSION_VERSION: '~4',
      
      // Optional: Azure Speech Service
      AZURE_SPEECH_KEY: '', // Optional backup transcription service
      AZURE_SPEECH_REGION: 'eastus',
      
      // ============================================
      // SIKKA API CONFIGURATION
      // ============================================
      // CRITICAL: Required for dental practice integration
      // SECURITY NOTE: Set these as environment variables in production deployment
      // Never hardcode actual credentials in this file
      SIKKA_API_KEY: process.env.SIKKA_API_KEY || 'your_sikka_api_key_here', // MUST be updated
      SIKKA_PRACTICE_ID: process.env.SIKKA_PRACTICE_ID || 'your_practice_id_here', // MUST be updated
      SIKKA_API_BASE_URL: 'https://api.sikka.com/v1',
      SIKKA_API_TIMEOUT: 30000,
      
      // ============================================
      // JOB PROCESSING CONFIGURATION
      // ============================================
      JOB_RETENTION_DAYS: 30,
      MAX_RETRY_ATTEMPTS: 3,
      RETRY_DELAY_BASE_MS: 1000,
      BATCH_SIZE: 10,
      PROCESSING_TIMEOUT_MS: 300000,
      QUEUE_POLL_INTERVAL_MS: 5000,
      STATUS_UPDATE_INTERVAL_MS: 2000,
      
      // ============================================
      // ERROR HANDLING & MONITORING
      // ============================================
      // HIGHLY RECOMMENDED: Application monitoring
      APPINSIGHTS_INSTRUMENTATIONKEY: 'your_app_insights_key_here', // RECOMMENDED
      SENTRY_DSN: 'your_sentry_dsn_here', // RECOMMENDED
      
      // Logging configuration
      LOG_LEVEL: 'info',
      LOG_FORMAT: 'json',
      LOG_TO_FILE: true,
      LOG_FILE_PATH: 'C:\\DentalApp\\logs\\voice-processing.log',
      
      // Alert configuration
      ALERT_EMAIL: '<EMAIL>',
      ALERT_WEBHOOK_URL: 'https://hooks.slack.com/your_webhook_url',
      
      // ============================================
      // NETWORK SYNC CONFIGURATION (Legacy Support)
      // ============================================
      // Legacy network share for voice recordings (Dentrix server)
      NETWORK_SHARE_PATH: '\\\\192.168.0.2\\share\\RECORDINGS',
      NETWORK_SHARE_USERNAME: '', // Set if needed for authentication
      NETWORK_SHARE_PASSWORD: '', // Set if needed for authentication
      NETWORK_SHARE_DOMAIN: '',   // Set if needed for domain authentication
      
      // ============================================
      // LOCAL STORAGE CONFIGURATION
      // ============================================
      // Local backup and database paths
      LOCAL_BACKUP_PATH: 'C:\\DentalApp\\data\\voice-recordings',
      LOCAL_DB_PATH: 'C:\\DentalApp\\data\\voice-jobs.db',
      DATABASE_PATH: 'C:\\DentalApp\\data\\database.sqlite', // Legacy
      LOGS_PATH: 'C:\\DentalApp\\logs',
      
      // ============================================
      // APPLICATION CONFIGURATION
      // ============================================
      // Application identity
      APP_NAME: 'Dentalapp',
      APP_VERSION: '1.0.0',
      
      // Security settings
      NEXTAUTH_SECRET: 'your_secret_key_here', // MUST be updated for NextAuth
      SESSION_SECRET: 'dental-app-secret-key-change-in-production', // Legacy
      NEXTAUTH_URL: 'https://yourdomain.com',
      
      // Performance settings
      REQUEST_TIMEOUT_MS: 30000,
      MAX_FILE_SIZE_MB: 100,
      CORS_ORIGINS: 'https://yourdomain.com',
      
      // Rate limiting
      RATE_LIMIT_REQUESTS_PER_MINUTE: 100,
      RATE_LIMIT_BURST_SIZE: 20,
      
      // File upload security
      ALLOWED_FILE_EXTENSIONS: 'mp3,wav,m4a,webm,ogg,flac',
      VIRUS_SCANNING_ENABLED: false,
      
      // ============================================
      // USB RECORDING CONFIGURATION
      // ============================================
      USB_DEFAULT_PATH: 'D:\\REC_FILE\\FOLDER01',
      
      // ============================================
      // BACKUP CONFIGURATION
      // ============================================
      BACKUP_RETENTION_DAYS: 30,
      AUTO_BACKUP_ENABLED: true,
      
      // ============================================
      // HEALTH CHECK CONFIGURATION
      // ============================================
      HEALTH_CHECK_PATH: '/health',
      READY_CHECK_PATH: '/ready',
      
      // ============================================
      // PRODUCTION SETTINGS
      // ============================================
      DEBUG_MODE: false,
      VERBOSE_LOGGING: false,
      NODE_TLS_REJECT_UNAUTHORIZED: 1,
      
      // Development overrides (disabled in production)
      DEV_MOCK_AZURE: false,
      DEV_MOCK_OPENAI: false,
      DEV_SKIP_VALIDATION: false
    },
    
    // Logging configuration
    error_file: 'C:\\DentalApp\\logs\\error.log',
    out_file: 'C:\\DentalApp\\logs\\output.log',
    log_file: 'C:\\DentalApp\\logs\\combined.log',
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss',
    
    // Process management
    max_restarts: 10,
    min_uptime: '10s',
    restart_delay: 4000,
    
    // Memory and CPU limits
    max_memory_restart: '1G',
    
    // Health monitoring
    health_check_grace_period: 3000,
    health_check_fatal_exceptions: true,
    
    // Windows service specific settings
    windowsHide: true,
    
    // Environment specific overrides
    env_production: {
      NODE_ENV: 'production',
      DEBUG: false,
      VERBOSE_LOGGING: false
    },
    
    env_development: {
      NODE_ENV: 'development',
      DEBUG: true,
      VERBOSE_LOGGING: true,
      watch: true
    }
  }],
  
  // Deployment configuration
  deploy: {
    production: {
      user: 'administrator',
      host: 'localhost',
      ref: 'origin/main',
      repo: 'https://github.com/suncoastdc/temp-dental.git',
      path: 'C:\\DentalApp\\app',
      'post-deploy': 'npm install --production && pm2 reload ecosystem.config.js --env production'
    }
  }
};
