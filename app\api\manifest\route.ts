import { NextResponse } from 'next/server';

// Serve manifest.json through API route to bypass authentication issues
export async function GET() {
  const manifest = {
    "name": "Dental Schedule App",
    "short_name": "DentalApp",
    "description": "Dental practice scheduling and voice notes application",
    "start_url": "/",
    "display": "standalone",
    "orientation": "portrait-primary",
    "background_color": "#ffffff",
    "theme_color": "#3b82f6",
    "scope": "/",
    "categories": ["medical", "productivity"],
    "icons": [
      {
        "src": "/favicon.svg",
        "sizes": "any",
        "type": "image/svg+xml"
      },
      {
        "src": "/favicon.ico",
        "sizes": "16x16 32x32 48x48",
        "type": "image/x-icon"
      }
    ]
  };

  return NextResponse.json(manifest, {
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=3600',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}
