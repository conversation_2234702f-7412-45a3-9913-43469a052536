"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { PageHeader } from '@/components/ui/page-header';
import { AVAILABLE_OPENAI_MODELS, ClinicalPromptsManager } from '@/lib/prompts/clinical-prompts';
import { Settings, Save, RotateCcw, Mic, FileText, Brain, LogOut } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface UserSettings {
  transcriptionModel: string;
  transcriptionPrompt: string;
  summarizationModel: string;
  summarizationPrompt: string;
  customTranscriptionPrompt: string;
  customSummarizationPrompt: string;
}

const DEFAULT_SETTINGS: UserSettings = {
  transcriptionModel: 'whisper-1',
  transcriptionPrompt: 'dental-transcription-context',
  summarizationModel: 'gpt-4o-mini-2024-07-18',
  summarizationPrompt: 'brief-summary',
  customTranscriptionPrompt: '',
  customSummarizationPrompt: ''
};

export default function SettingsPage() {
  const router = useRouter();
  const { logout } = useAuth();
  const [settings, setSettings] = useState<UserSettings>(DEFAULT_SETTINGS);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings({ ...DEFAULT_SETTINGS, ...data.settings });
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ settings })
      });

      if (response.ok) {
        setMessage({ type: 'success', text: 'Settings saved successfully!' });
        setTimeout(() => setMessage(null), 3000);
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      setMessage({ type: 'error', text: 'Failed to save settings. Please try again.' });
      setTimeout(() => setMessage(null), 3000);
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = () => {
    setSettings(DEFAULT_SETTINGS);
    setMessage({ type: 'success', text: 'Settings reset to defaults' });
    setTimeout(() => setMessage(null), 3000);
  };

  const handleBack = () => {
    router.push('/');
  };

  // Filter models by type
  const transcriptionModels = AVAILABLE_OPENAI_MODELS.filter(model =>
    model.id === 'whisper-1' || model.id.includes('audio')
  );

  const chatModels = AVAILABLE_OPENAI_MODELS.filter(model =>
    !model.id.includes('whisper') && !model.id.includes('audio')
  );

  const transcriptionPrompts = ClinicalPromptsManager.getPromptsByType('transcription');
  const summarizationPrompts = ClinicalPromptsManager.getPromptsByType('summarization');

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
        <PageHeader
          title="Settings"
          showBackButton={true}
          onBackClick={handleBack}
        />
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      <PageHeader
        title="Settings"
        showBackButton={true}
        onBackClick={handleBack}
        activeTab="settings"
      />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Settings className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
              AI Transcription & Summarization Settings
            </h1>
          </div>
          <p className="text-slate-600 dark:text-slate-400">
            Configure OpenAI models and prompts for audio transcription and summarization
          </p>
        </div>

        {/* Success/Error Messages */}
        {message && (
          <div className={`mb-6 p-4 rounded-md ${
            message.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200'
              : 'bg-red-50 border border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200'
          }`}>
            {message.text}
          </div>
        )}

        <div className="space-y-8">
          {/* Transcription Settings */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <Mic className="h-6 w-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
                Audio Transcription Settings
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Transcription Model */}
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Transcription Model
                </label>
                <select
                  value={settings.transcriptionModel}
                  onChange={(e) => setSettings(prev => ({ ...prev, transcriptionModel: e.target.value }))}
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {transcriptionModels.map(model => (
                    <option key={model.id} value={model.id}>
                      {model.name} - ${model.costPer1kTokens}/1k tokens
                    </option>
                  ))}
                </select>
                <p className="mt-1 text-xs text-slate-500 dark:text-slate-400">
                  Model used for converting audio to text
                </p>
              </div>

              {/* Transcription Prompt */}
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Transcription Prompt
                </label>
                <select
                  value={settings.transcriptionPrompt}
                  onChange={(e) => setSettings(prev => ({ ...prev, transcriptionPrompt: e.target.value }))}
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {transcriptionPrompts.map(prompt => (
                    <option key={prompt.id} value={prompt.id}>
                      {prompt.name}
                    </option>
                  ))}
                  <option value="custom">Custom Prompt</option>
                </select>
                <p className="mt-1 text-xs text-slate-500 dark:text-slate-400">
                  Context and instructions for transcription
                </p>
              </div>
            </div>

            {/* Custom Transcription Prompt */}
            {settings.transcriptionPrompt === 'custom' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Custom Transcription Prompt
                </label>
                <textarea
                  value={settings.customTranscriptionPrompt}
                  onChange={(e) => setSettings(prev => ({ ...prev, customTranscriptionPrompt: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your custom transcription prompt..."
                />
              </div>
            )}
          </div>

          {/* Summarization Settings */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <FileText className="h-6 w-6 text-purple-600" />
              <h2 className="text-xl font-semibold text-slate-900 dark:text-white">
                Summarization Settings
              </h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Summarization Model */}
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Summarization Model
                </label>
                <select
                  value={settings.summarizationModel}
                  onChange={(e) => setSettings(prev => ({ ...prev, summarizationModel: e.target.value }))}
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {chatModels.map(model => (
                    <option key={model.id} value={model.id}>
                      {model.name} - ${model.costPer1kTokens}/1k tokens
                      {model.recommended ? ' (Recommended)' : ''}
                    </option>
                  ))}
                </select>
                <p className="mt-1 text-xs text-slate-500 dark:text-slate-400">
                  Model used for generating 2-3 sentence summaries
                </p>
              </div>

              {/* Summarization Prompt */}
              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Summarization Prompt
                </label>
                <select
                  value={settings.summarizationPrompt}
                  onChange={(e) => setSettings(prev => ({ ...prev, summarizationPrompt: e.target.value }))}
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {summarizationPrompts.map(prompt => (
                    <option key={prompt.id} value={prompt.id}>
                      {prompt.name}
                    </option>
                  ))}
                  <option value="custom">Custom Prompt</option>
                </select>
                <p className="mt-1 text-xs text-slate-500 dark:text-slate-400">
                  Instructions for generating summaries
                </p>
              </div>
            </div>

            {/* Custom Summarization Prompt */}
            {settings.summarizationPrompt === 'custom' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Custom Summarization Prompt
                </label>
                <textarea
                  value={settings.customSummarizationPrompt}
                  onChange={(e) => setSettings(prev => ({ ...prev, customSummarizationPrompt: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your custom summarization prompt..."
                />
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center pt-6">
            <button
              onClick={resetToDefaults}
              className="flex items-center space-x-2 px-4 py-2 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors"
            >
              <RotateCcw className="h-4 w-4" />
              <span>Reset to Defaults</span>
            </button>

            <button
              onClick={saveSettings}
              disabled={saving}
              className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Save className="h-4 w-4" />
              <span>{saving ? 'Saving...' : 'Save Settings'}</span>
            </button>
          </div>

          {/* Logout Section */}
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-slate-200 dark:border-slate-700 p-6 mt-8">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                  Account
                </h3>
                <p className="text-sm text-slate-600 dark:text-slate-400">
                  Sign out of the dental practice management system
                </p>
              </div>
              <button
                onClick={logout}
                className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                <LogOut className="h-4 w-4" />
                <span>Sign Out</span>
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
