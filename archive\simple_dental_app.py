#!/usr/bin/env python3
"""
Simple Dental Appointment Viewer
A clean, straightforward GUI for viewing dental appointments by operatory
"""

import PySimpleG<PERSON> as sg
import json
import os
import sys
import requests
from datetime import datetime

# API configuration
API_BASE_V2 = "https://api.sikkasoft.com/v2"
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_TIMEOUT = 30  # seconds

# Define operatory names - these are the actual operatories from your system
OPERATORIES = [
    'DAZ1', 'DAZ2', 'DL01', 'DL02', 'NS02', 'X-PS', 'Y-10', 'ZZ01', 'ZZ02'
]

def load_credentials():
    """Load API credentials from credentials.json file."""
    try:
        with open("credentials.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        sg.popup_error("Error: credentials.json file not found.\nPlease create a credentials.json file with your Sikka API credentials.")
        sys.exit(1)
    except json.JSONDecodeError:
        sg.popup_error("Error: Invalid JSON in credentials.json file.")
        sys.exit(1)

def authenticate(app_id, app_key, office_id, secret_key):
    """Authenticate with Sikka API and get request key."""
    try:
        auth_resp = requests.post(
            f"{API_BASE_V4}/request_key",
            headers={"Content-Type": "application/json"},
            json={
                "grant_type": "request_key",
                "office_id": office_id,
                "secret_key": secret_key,
                "app_id": app_id,
                "app_key": app_key
            },
            timeout=API_TIMEOUT
        )

        if auth_resp.status_code == 200:
            data = auth_resp.json()
            request_key = data.get("request_key")
            if request_key:
                return request_key
            else:
                sg.popup_error("Error: No request key in response.")
        else:
            sg.popup_error(f"Authentication failed with status code: {auth_resp.status_code}")
    except Exception as e:
        sg.popup_error(f"Error during authentication: {e}")
    
    sys.exit(1)

def fetch_appointments(request_key, target_date, selected_operatories):
    """Fetch appointments for a specific date and filter by operatories."""
    headers = {"Request-Key": request_key}
    
    # Set up parameters with the correct date filtering parameters
    params = {
        "startdate": target_date,
        "enddate": target_date,
        "date_filter_on": "appointment_date"
    }
    
    try:
        resp = requests.get(
            f"{API_BASE_V2}/appointments",
            headers=headers,
            params=params,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            
            # Extract items from v2 response
            if isinstance(data, list) and len(data) > 0:
                all_items = data[0].get("items", [])
                
                # Filter by selected operatories
                if selected_operatories:
                    filtered_items = [a for a in all_items if a.get("operatory") in selected_operatories]
                    return filtered_items
                else:
                    return all_items
            else:
                return []
        else:
            sg.popup_error(f"Error fetching appointments: {resp.status_code}")
            return []
    except Exception as e:
        sg.popup_error(f"Error: {e}")
        return []

def format_appointments(appointments):
    """Format appointments into a readable text format."""
    if not appointments:
        return "No appointments found for the selected date and operatories."
    
    # Sort appointments by operatory and time
    sorted_appointments = sorted(
        appointments,
        key=lambda a: (a.get("operatory", ""), a.get("time", ""))
    )
    
    # Format the output
    output = []
    current_operatory = None
    
    for appt in sorted_appointments:
        operatory = appt.get("operatory", "N/A")
        
        # Print operatory header when it changes
        if operatory != current_operatory:
            output.append("\n" + "=" * 50)
            output.append(f"OPERATORY: {operatory}")
            output.append("=" * 50)
            current_operatory = operatory
        
        # Get appointment details
        time = appt.get("time", "")
        patient_name = appt.get("patient_name", "Unknown Patient")
        appt_sr_no = appt.get("appointment_sr_no", "")
        length = appt.get("length", "")
        provider_id = appt.get("provider_id", "")
        description = appt.get("description", "")
        
        # Check if this is a real patient appointment or just blocked time
        is_blocked = "Blocked" in str(appt_sr_no) or not patient_name or patient_name.strip() == ""
        
        # Format appointment details
        output.append(f"\nTime: {time} | Length: {length} min | Provider: {provider_id}")
        output.append(f"Patient: {patient_name}")
        output.append(f"Appt Serial #: {appt_sr_no}")
        output.append(f"Description: {description}")
        output.append("-" * 50)
    
    return "\n".join(output)

def main():
    # Set theme
    sg.theme('SystemDefault')
    
    # Get today's date in YYYY-MM-DD format
    today = datetime.now().strftime("%Y-%m-%d")
    
    # Define the layout
    layout = [
        [sg.Text('Date (YYYY-MM-DD):', size=(15, 1)), 
         sg.Input(today, key='-DATE-', size=(15, 1))],
        [sg.Text('Select Operatories:', size=(15, 1))],
        [sg.Listbox(values=OPERATORIES, select_mode=sg.LISTBOX_SELECT_MODE_MULTIPLE, 
                   size=(30, 6), key='-OPERATORIES-')],
        [sg.Button('Fetch Appointments', key='-FETCH-'), sg.Button('Exit')]
    ]
    
    # Create the window
    window = sg.Window('Dental Appointment Viewer', layout)
    
    # Event loop
    while True:
        event, values = window.read()
        
        if event == sg.WIN_CLOSED or event == 'Exit':
            break
        
        if event == '-FETCH-':
            selected_operatories = values['-OPERATORIES-']
            date_str = values['-DATE-']
            
            # Validate date format
            try:
                datetime.strptime(date_str, "%Y-%m-%d")
            except ValueError:
                sg.popup_error("Invalid date format. Please use YYYY-MM-DD format.")
                continue
            
            # Check if at least one operatory is selected
            if not selected_operatories:
                sg.popup_error("Please select at least one operatory.")
                continue
            
            # Show a "please wait" popup
            sg.popup_no_wait("Fetching appointments...", auto_close=True, auto_close_duration=2)
            
            try:
                # Load credentials
                credentials = load_credentials()
                app_id = credentials.get("app_id")
                app_key = credentials.get("app_key")
                office_id = credentials.get("office_id")
                secret_key = credentials.get("secret_key")
                
                # Authenticate and get request key
                request_key = authenticate(app_id, app_key, office_id, secret_key)
                
                # Fetch appointments
                appointments = fetch_appointments(request_key, date_str, selected_operatories)
                
                # Format and display appointments
                result = format_appointments(appointments)
                
                # Show the result in a scrollable popup
                sg.popup_scrolled(result, title=f"Appointments for {date_str}", size=(80, 25))
                
            except Exception as e:
                sg.popup_error(f"Error fetching appointments: {str(e)}")
    
    # Close the window
    window.close()

if __name__ == "__main__":
    main()
