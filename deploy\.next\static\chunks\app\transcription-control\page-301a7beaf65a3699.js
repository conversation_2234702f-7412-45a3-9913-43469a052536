(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7537],{776:(e,s,r)=>{Promise.resolve().then(r.bind(r,9454))},9454:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var i=r(5155),n=r(4854);function a(){return(0,i.jsx)("div",{className:"container mx-auto p-6 max-w-4xl",children:(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"\uD83C\uDFAF Real-Time Transcription Control"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Live streaming transcription with real-time progress updates"})]}),(0,i.jsx)(n.o,{}),(0,i.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded p-4",children:[(0,i.jsx)("h4",{className:"font-semibold text-green-800 mb-2",children:"✨ New Features:"}),(0,i.jsxs)("ul",{className:"text-sm text-green-700 space-y-1",children:[(0,i.jsxs)("li",{children:["• ",(0,i.jsx)("strong",{children:"Real-time streaming"})," - See exactly what's happening as it happens"]}),(0,i.jsxs)("li",{children:["• ",(0,i.jsx)("strong",{children:"Live progress bars"})," - Visual feedback for every step"]}),(0,i.jsxs)("li",{children:["• ",(0,i.jsx)("strong",{children:"Instant error reporting"})," - Know immediately if something fails"]}),(0,i.jsxs)("li",{children:["• ",(0,i.jsx)("strong",{children:"File-by-file updates"})," - Track each transcription individually"]}),(0,i.jsxs)("li",{children:["• ",(0,i.jsx)("strong",{children:"No more mystery failures"})," - Everything is visible and logged"]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8096,4854,7358],()=>s(776)),_N_E=e.O()}]);