import requests
import json
from datetime import datetime, timedelta
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 60

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def test_appointment_filters(request_key, date):
    """Test different filter parameters for appointments."""
    headers = {"Request-Key": request_key}
    
    print(f"Testing different filter parameters for appointments on {date}...")
    
    # Try different filter parameters
    filter_params = [
        {"date": date},  # Standard date parameter
        {"appointment_date": date},  # Try appointment_date
        {"scheduled_date": date},  # Try scheduled_date
        {"start_date": date, "end_date": date},  # Try date range
        {"date_range": date},  # Try date_range
        {"date_filter": date},  # Try date_filter
        {"filter_date": date},  # Try filter_date
        {"actual_date": date},  # Try actual_date
        {"occurrence_date": date},  # Try occurrence_date
        {"appointment_occurrence_date": date},  # Try appointment_occurrence_date
    ]
    
    # Try different date formats
    date_formats = [
        date,  # YYYY-MM-DD
        date.replace("-", "/"),  # YYYY/MM/DD
        f"{date.split('-')[1]}/{date.split('-')[2]}/{date.split('-')[0]}",  # MM/DD/YYYY
    ]
    
    # Try v2 endpoint with different parameters
    print("\nTesting v2 endpoint with different parameters:")
    for params in filter_params:
        param_str = ", ".join([f"{k}={v}" for k, v in params.items()])
        print(f"\nTrying parameters: {param_str}")
        
        for date_format in date_formats:
            # Replace date values with the current format
            current_params = {k: date_format if v == date else v for k, v in params.items()}
            
            try:
                resp = requests.get(
                    f"{API_BASE_V2}/appointments", 
                    headers=headers, 
                    params=current_params,
                    timeout=API_TIMEOUT
                )
                
                if resp.status_code == 200:
                    data = resp.json()
                    
                    # Extract items from v2 response
                    if isinstance(data, list) and len(data) > 0:
                        items = data[0].get("items", [])
                        print(f"  Format {date_format}: Found {len(items)} appointments")
                        
                        # Check how many appointments are actually for the target date
                        target_appointments = [a for a in items if a.get("date") == date]
                        if target_appointments:
                            print(f"  Of these, {len(target_appointments)} are scheduled for {date}")
                            
                            # Print sample appointments
                            print("  Sample appointments:")
                            for i, appt in enumerate(sorted(target_appointments, key=lambda a: a.get("time", ""))[:3]):
                                print(f"    {i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('operatory', 'N/A')} - {appt.get('provider_id', 'N/A')} - {appt.get('description', '')}")
                    else:
                        print(f"  Format {date_format}: No appointments found")
                else:
                    print(f"  Format {date_format}: Error {resp.status_code}")
            except Exception as e:
                print(f"  Format {date_format}: Error - {e}")
    
    # Try v4 endpoint with different parameters
    print("\nTesting v4 endpoint with different parameters:")
    for params in filter_params:
        param_str = ", ".join([f"{k}={v}" for k, v in params.items()])
        print(f"\nTrying parameters: {param_str}")
        
        for date_format in date_formats:
            # Replace date values with the current format
            current_params = {k: date_format if v == date else v for k, v in params.items()}
            
            try:
                resp = requests.get(
                    f"{API_BASE_V4}/appointments", 
                    headers=headers, 
                    params=current_params,
                    timeout=API_TIMEOUT
                )
                
                if resp.status_code == 200:
                    data = resp.json()
                    items = data.get("items", [])
                    print(f"  Format {date_format}: Found {len(items)} appointments")
                    
                    # Check how many appointments are actually for the target date
                    target_appointments = [a for a in items if a.get("date") == date]
                    if target_appointments:
                        print(f"  Of these, {len(target_appointments)} are scheduled for {date}")
                        
                        # Print sample appointments
                        print("  Sample appointments:")
                        for i, appt in enumerate(sorted(target_appointments, key=lambda a: a.get("time", ""))[:3]):
                            print(f"    {i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('provider_id', 'N/A')} - {appt.get('description', '')}")
                else:
                    print(f"  Format {date_format}: Error {resp.status_code}")
            except Exception as e:
                print(f"  Format {date_format}: Error - {e}")
    
    # Try v2 endpoint with date_from and date_to parameters
    print("\nTrying date_from and date_to parameters:")
    try:
        params = {"date_from": date, "date_to": date}
        resp = requests.get(
            f"{API_BASE_V2}/appointments", 
            headers=headers, 
            params=params,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            
            # Extract items from v2 response
            if isinstance(data, list) and len(data) > 0:
                items = data[0].get("items", [])
                print(f"  Found {len(items)} appointments")
                
                # Check how many appointments are actually for the target date
                target_appointments = [a for a in items if a.get("date") == date]
                if target_appointments:
                    print(f"  Of these, {len(target_appointments)} are scheduled for {date}")
                    
                    # Print sample appointments
                    print("  Sample appointments:")
                    for i, appt in enumerate(sorted(target_appointments, key=lambda a: a.get("time", ""))[:3]):
                        print(f"    {i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('operatory', 'N/A')} - {appt.get('provider_id', 'N/A')} - {appt.get('description', '')}")
            else:
                print("  No appointments found")
        else:
            print(f"  Error {resp.status_code}")
    except Exception as e:
        print(f"  Error: {e}")
    
    # Try v2 endpoint with from_date and to_date parameters
    print("\nTrying from_date and to_date parameters:")
    try:
        params = {"from_date": date, "to_date": date}
        resp = requests.get(
            f"{API_BASE_V2}/appointments", 
            headers=headers, 
            params=params,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code == 200:
            data = resp.json()
            
            # Extract items from v2 response
            if isinstance(data, list) and len(data) > 0:
                items = data[0].get("items", [])
                print(f"  Found {len(items)} appointments")
                
                # Check how many appointments are actually for the target date
                target_appointments = [a for a in items if a.get("date") == date]
                if target_appointments:
                    print(f"  Of these, {len(target_appointments)} are scheduled for {date}")
                    
                    # Print sample appointments
                    print("  Sample appointments:")
                    for i, appt in enumerate(sorted(target_appointments, key=lambda a: a.get("time", ""))[:3]):
                        print(f"    {i+1}. {appt.get('time', '')} - {appt.get('patient_name', 'Unknown')} - {appt.get('operatory', 'N/A')} - {appt.get('provider_id', 'N/A')} - {appt.get('description', '')}")
            else:
                print("  No appointments found")
        else:
            print(f"  Error {resp.status_code}")
    except Exception as e:
        print(f"  Error: {e}")

def main():
    # Get date from command line or use default
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        # Use a date that we know has appointments in the test data
        date = "2025-05-16"
    
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Test appointment filters
    test_appointment_filters(request_key, date)
    
    print("\nTest complete.")

if __name__ == "__main__":
    main()
