"use strict";exports.id=5772,exports.ids=[5772],exports.modules={5772:(e,t,r)=>{r.d(t,{AzureStorageService:()=>s});var i=r(77583);let a=null,o=null,l=!1;function n(){if(l)return;let e=process.env.AZURE_STORAGE_CONNECTION_STRING;if(!e){console.warn("Azure Storage connection string not configured"),l=!0;return}try{o=i.BlobServiceClient.fromConnectionString(e).getContainerClient("recordings")}catch(e){console.error("Failed to initialize Azure Storage client:",e)}l=!0}class s{static isConfigured(){return n(),!!process.env.AZURE_STORAGE_CONNECTION_STRING&&!!o}static async uploadFile(e,t,r="audio/webm"){if(n(),!o)throw Error("Azure Storage not configured");let i=o.getBlockBlobClient(e);return await i.uploadData(t,{blobHTTPHeaders:{blobContentType:r}}),i.url}static async findFileByName(e){if(n(),!o)throw Error("Azure Storage not configured");for await(let t of o.listBlobsFlat())if(t.name.split("/").pop()===e)return t.name;return null}static async downloadFile(e){if(n(),!o)throw Error("Azure Storage not configured");if(!e.includes("/")){let t=await this.findFileByName(e);if(!t)throw Error(`The specified blob does not exist: ${e}`);e=t}let t=o.getBlockBlobClient(e),r=await t.download();if(!r.readableStreamBody)throw Error("Failed to download file");let i=[];for await(let e of r.readableStreamBody)i.push(e);return Buffer.concat(i)}static async listFiles(e=""){if(n(),!o)throw Error("Azure Storage not configured");let t=[];for await(let r of o.listBlobsFlat())if((!e||r.name.startsWith(e))&&r.name.match(/\.(webm|mp3|wav|m4a|ogg|json)$/i)){let e=r.name.split("/").pop()||r.name;t.push({name:e,size:r.properties.contentLength||0,lastModified:r.properties.lastModified||new Date,url:`${o.url}/${r.name}`,path:r.name})}return t}static async deleteFile(e){if(!o)throw Error("Azure Storage not configured");let t=o.getBlockBlobClient(e);await t.delete()}static async fileExists(e){if(!o)return!1;try{let t=o.getBlockBlobClient(e);return await t.getProperties(),!0}catch(e){return!1}}static async getFileMetadata(e){if(!o)return{exists:!1};try{let t=o.getBlockBlobClient(e),r=await t.getProperties();return{exists:!0,size:r.contentLength,lastModified:r.lastModified,etag:r.etag}}catch(e){return{exists:!1}}}static async findDuplicateAcrossAllFolders(e,t,r="webusb-upload"){if(n(),!o)return{exists:!1};try{for await(let i of o.listBlobsFlat({prefix:"recordings/"})){let a=i.name,o=a.split("/");if(o.length>=4){let l=o[2];if(o[o.length-1]===e&&l===r&&i.properties.contentLength===t)return{exists:!0,existingPath:a,existingMetadata:{size:i.properties.contentLength,lastModified:i.properties.lastModified,etag:i.properties.etag,contentType:i.properties.contentType}}}}return{exists:!1}}catch(e){return console.error("Error searching for duplicates:",e),{exists:!1}}}static async uploadFileWithDuplicateCheck(e,t,r="audio/webm",i={}){if(n(),!o)throw Error("Azure Storage not configured");let a=o.getBlockBlobClient(e),l=e.split("/"),s=l[l.length-1],c=l.length>=3?l[2]:"webusb-upload",u=await this.getFileMetadata(e);if(u?.exists){if(i.skipIfExists)return{url:a.url,wasSkipped:!0,reason:"File already exists at exact Azure path",existingPath:e};if(i.overwriteIfDifferentSize&&u.size!==t.length);else if(i.overwriteIfDifferentSize&&u.size===t.length)return{url:a.url,wasSkipped:!0,reason:"File already exists with same size at exact path",existingPath:e}}if(!1!==i.searchAllFolders){let e=await this.findDuplicateAcrossAllFolders(s,t.length,c);if(e.exists&&e.existingPath&&i.skipIfExists)return{url:o.getBlockBlobClient(e.existingPath).url,wasSkipped:!0,reason:`File already exists in Azure Storage at ${e.existingPath}`,existingPath:e.existingPath}}return await a.uploadData(t,{blobHTTPHeaders:{blobContentType:r}}),{url:a.url,wasSkipped:!1}}static async getFileUrl(e,t=60){if(!o)throw Error("Azure Storage not configured");return o.getBlockBlobClient(e).url}static async uploadJson(e,t){if(n(),!o)throw Error("Azure Storage not configured");let r=JSON.stringify(t,null,2),i=Buffer.from(r,"utf8"),a=o.getBlockBlobClient(e);await a.uploadData(i,{blobHTTPHeaders:{blobContentType:"application/json",blobCacheControl:"no-cache"}})}static async listAllFiles(){if(n(),!o)throw Error("Azure Storage not configured");let e=[];for await(let t of o.listBlobsFlat())e.push({path:t.name,size:t.properties.contentLength||0,lastModified:t.properties.lastModified||new Date});return e.forEach(e=>{}),e}static async downloadJson(e){try{let t=await this.downloadFile(e);return JSON.parse(t.toString())}catch(e){return{}}}}}};