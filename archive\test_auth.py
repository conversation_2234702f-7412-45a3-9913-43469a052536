import requests
import json
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Target date and provider
TARGET_DATE = "2025-05-16"
TARGET_PROVIDER = "LL01"

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        }
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def test_v4_endpoint(request_key):
    """Test a v4 endpoint to verify the request key works."""
    print("\nTesting v4 endpoint (appointments)...")
    headers = {"Request-Key": request_key}
    params = {"date": TARGET_DATE}
    
    resp = requests.get(f"{API_BASE_V4}/appointments", headers=headers, params=params)
    
    print(f"Response status: {resp.status_code}")
    if resp.status_code == 200:
        data = resp.json()
        print(f"Success! Got data with {len(data.get('items', []))} appointments")
        return True
    else:
        print("Error response:")
        print(resp.text[:500])
        return False

def test_v2_endpoint_with_header(request_key):
    """Test v2 endpoint with request key in header."""
    print("\nTesting v2 endpoint (practice_schedule) with request key in header...")
    headers = {"Request-Key": request_key}
    
    resp = requests.get(f"{API_BASE_V2}/practice_schedule", headers=headers)
    
    print(f"Response status: {resp.status_code}")
    if resp.status_code == 200:
        try:
            data = resp.json()
            print(f"Success! Got data")
            return True
        except:
            print("Error parsing JSON response")
            print(resp.text[:500])
            return False
    else:
        print("Error response:")
        print(resp.text[:500])
        return False

def test_v2_endpoint_with_param(request_key):
    """Test v2 endpoint with request key as URL parameter."""
    print("\nTesting v2 endpoint (practice_schedule) with request key as URL parameter...")
    url = f"{API_BASE_V2}/practice_schedule?request_key={request_key}"
    
    resp = requests.get(url)
    
    print(f"Response status: {resp.status_code}")
    if resp.status_code == 200:
        try:
            data = resp.json()
            print(f"Success! Got data")
            return True
        except:
            print("Error parsing JSON response")
            print(resp.text[:500])
            return False
    else:
        print("Error response:")
        print(resp.text[:500])
        return False

def test_v2_endpoint_with_both(request_key):
    """Test v2 endpoint with request key in both header and URL parameter."""
    print("\nTesting v2 endpoint (practice_schedule) with request key in both header and URL parameter...")
    headers = {"Request-Key": request_key}
    url = f"{API_BASE_V2}/practice_schedule?request_key={request_key}"
    
    resp = requests.get(url, headers=headers)
    
    print(f"Response status: {resp.status_code}")
    if resp.status_code == 200:
        try:
            data = resp.json()
            print(f"Success! Got data")
            return True
        except:
            print("Error parsing JSON response")
            print(resp.text[:500])
            return False
    else:
        print("Error response:")
        print(resp.text[:500])
        return False

def test_v2_endpoint_with_auth_header(request_key):
    """Test v2 endpoint with request key in Authorization header."""
    print("\nTesting v2 endpoint (practice_schedule) with request key in Authorization header...")
    headers = {"Authorization": f"Bearer {request_key}"}
    
    resp = requests.get(f"{API_BASE_V2}/practice_schedule", headers=headers)
    
    print(f"Response status: {resp.status_code}")
    if resp.status_code == 200:
        try:
            data = resp.json()
            print(f"Success! Got data")
            return True
        except:
            print("Error parsing JSON response")
            print(resp.text[:500])
            return False
    else:
        print("Error response:")
        print(resp.text[:500])
        return False

def test_v2_endpoint_with_direct_auth(request_key=None):
    """Test v2 endpoint with direct authentication parameters."""
    print("\nTesting v2 endpoint (practice_schedule) with direct authentication parameters...")
    params = {
        "office_id": OFFICE_ID,
        "secret_key": SECRET_KEY,
        "app_id": APP_ID,
        "app_key": APP_KEY
    }
    
    resp = requests.get(f"{API_BASE_V2}/practice_schedule", params=params)
    
    print(f"Response status: {resp.status_code}")
    if resp.status_code == 200:
        try:
            data = resp.json()
            print(f"Success! Got data")
            return True
        except:
            print("Error parsing JSON response")
            print(resp.text[:500])
            return False
    else:
        print("Error response:")
        print(resp.text[:500])
        return False

def main():
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Test v4 endpoint to verify request key works
    if not test_v4_endpoint(request_key):
        print("Failed to access v4 endpoint. Request key may be invalid.")
        return
    
    # Test v2 endpoint with different authentication methods
    test_v2_endpoint_with_header(request_key)
    test_v2_endpoint_with_param(request_key)
    test_v2_endpoint_with_both(request_key)
    test_v2_endpoint_with_auth_header(request_key)
    test_v2_endpoint_with_direct_auth()
    
    print("\nAll tests completed.")

if __name__ == "__main__":
    main()
