import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '../../../../src/lib/api/sikka-client';
import { loadCredentials } from '../../../../src/lib/api/credentials';

// GET /api/patients/[id] - Get patient by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      );
    }

    console.log(`API: Getting patient by ID: ${id}`);

    // Try to get patient directly by ID
    const credentials = loadCredentials();
    const sikkaClient = new SikkaApiClient(credentials);
    await sikkaClient.authenticate();
    const patient = await sikkaClient.getPatientById(id);

    if (patient) {
      return NextResponse.json({
        success: true,
        patient
      });
    } else {
      return NextResponse.json(
        {
          error: 'Patient not found',
          id
        },
        { status: 404 }
      );
    }
  } catch (error: any) {
    console.error('Get patient by ID error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get patient',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
