exports.id=982,exports.ids=[982,5772],exports.modules={5772:(t,e,r)=>{"use strict";r.d(e,{AzureStorageService:()=>l});var a=r(77583);let i=null,s=null,o=!1;function n(){if(o)return;let t=process.env.AZURE_STORAGE_CONNECTION_STRING;if(!t){console.warn("Azure Storage connection string not configured"),o=!0;return}try{s=a.BlobServiceClient.fromConnectionString(t).getContainerClient("recordings")}catch(t){console.error("Failed to initialize Azure Storage client:",t)}o=!0}class l{static isConfigured(){return n(),!!process.env.AZURE_STORAGE_CONNECTION_STRING&&!!s}static async uploadFile(t,e,r="audio/webm"){if(n(),!s)throw Error("Azure Storage not configured");let a=s.getBlockBlobClient(t);return await a.uploadData(e,{blobHTTPHeaders:{blobContentType:r}}),a.url}static async findFileByName(t){if(n(),!s)throw Error("Azure Storage not configured");for await(let e of s.listBlobsFlat())if(e.name.split("/").pop()===t)return e.name;return null}static async downloadFile(t){if(n(),!s)throw Error("Azure Storage not configured");if(!t.includes("/")){let e=await this.findFileByName(t);if(!e)throw Error(`The specified blob does not exist: ${t}`);t=e}let e=s.getBlockBlobClient(t),r=await e.download();if(!r.readableStreamBody)throw Error("Failed to download file");let a=[];for await(let t of r.readableStreamBody)a.push(t);return Buffer.concat(a)}static async listFiles(t=""){if(n(),!s)throw Error("Azure Storage not configured");let e=[];for await(let r of s.listBlobsFlat())if((!t||r.name.startsWith(t))&&r.name.match(/\.(webm|mp3|wav|m4a|ogg|json)$/i)){let t=r.name.split("/").pop()||r.name;e.push({name:t,size:r.properties.contentLength||0,lastModified:r.properties.lastModified||new Date,url:`${s.url}/${r.name}`,path:r.name})}return e}static async deleteFile(t){if(!s)throw Error("Azure Storage not configured");let e=s.getBlockBlobClient(t);await e.delete()}static async fileExists(t){if(!s)return!1;try{let e=s.getBlockBlobClient(t);return await e.getProperties(),!0}catch(t){return!1}}static async getFileMetadata(t){if(!s)return{exists:!1};try{let e=s.getBlockBlobClient(t),r=await e.getProperties();return{exists:!0,size:r.contentLength,lastModified:r.lastModified,etag:r.etag}}catch(t){return{exists:!1}}}static async findDuplicateAcrossAllFolders(t,e,r="webusb-upload"){if(n(),!s)return{exists:!1};try{for await(let a of s.listBlobsFlat({prefix:"recordings/"})){let i=a.name,s=i.split("/");if(s.length>=4){let o=s[2];if(s[s.length-1]===t&&o===r&&a.properties.contentLength===e)return{exists:!0,existingPath:i,existingMetadata:{size:a.properties.contentLength,lastModified:a.properties.lastModified,etag:a.properties.etag,contentType:a.properties.contentType}}}}return{exists:!1}}catch(t){return console.error("Error searching for duplicates:",t),{exists:!1}}}static async uploadFileWithDuplicateCheck(t,e,r="audio/webm",a={}){if(n(),!s)throw Error("Azure Storage not configured");let i=s.getBlockBlobClient(t),o=t.split("/"),l=o[o.length-1],c=o.length>=3?o[2]:"webusb-upload",u=await this.getFileMetadata(t);if(u?.exists){if(a.skipIfExists)return{url:i.url,wasSkipped:!0,reason:"File already exists at exact Azure path",existingPath:t};if(a.overwriteIfDifferentSize&&u.size!==e.length);else if(a.overwriteIfDifferentSize&&u.size===e.length)return{url:i.url,wasSkipped:!0,reason:"File already exists with same size at exact path",existingPath:t}}if(!1!==a.searchAllFolders){let t=await this.findDuplicateAcrossAllFolders(l,e.length,c);if(t.exists&&t.existingPath&&a.skipIfExists)return{url:s.getBlockBlobClient(t.existingPath).url,wasSkipped:!0,reason:`File already exists in Azure Storage at ${t.existingPath}`,existingPath:t.existingPath}}return await i.uploadData(e,{blobHTTPHeaders:{blobContentType:r}}),{url:i.url,wasSkipped:!1}}static async getFileUrl(t,e=60){if(!s)throw Error("Azure Storage not configured");return s.getBlockBlobClient(t).url}static async uploadJson(t,e){if(n(),!s)throw Error("Azure Storage not configured");let r=JSON.stringify(e,null,2),a=Buffer.from(r,"utf8"),i=s.getBlockBlobClient(t);await i.uploadData(a,{blobHTTPHeaders:{blobContentType:"application/json",blobCacheControl:"no-cache"}})}static async listAllFiles(){if(n(),!s)throw Error("Azure Storage not configured");let t=[];for await(let e of s.listBlobsFlat())t.push({path:e.name,size:e.properties.contentLength||0,lastModified:e.properties.lastModified||new Date});return t.forEach(t=>{}),t}static async downloadJson(t){try{let e=await this.downloadFile(t);return JSON.parse(e.toString())}catch(t){return{}}}}},10501:(t,e,r)=>{"use strict";r.d(e,{B:()=>a});class a{static{this.speechKey=process.env.AZURE_SPEECH_KEY}static{this.speechRegion=process.env.AZURE_SPEECH_REGION||"centralus"}static isConfigured(){return!!process.env.OPENAI_API_KEY}static getSupportedFormats(){return["wav","mp3","m4a","ogg","flac","webm"]}static async validateAudioFormat(t,e){let r=e.toLowerCase().split(".").pop();if(!r||!this.getSupportedFormats().includes(r)||t.length<1024)return!1;let a=t.slice(0,12).toString("hex");return!!(a.startsWith("52494646")||a.startsWith("494433")||a.startsWith("fff")||a.startsWith("4f676753"))||(a.includes("66747970"),!0)}static async transcribeAudio(t,e,a={}){if(!this.isConfigured())return{success:!1,error:"Transcription service not configured. Please set OPENAI_API_KEY environment variable."};try{if(!await this.validateAudioFormat(t,e))return{success:!1,error:`Invalid audio format for file: ${e}. Supported formats: ${this.getSupportedFormats().join(", ")}`};let{OpenAI:i}=await r.e(8096).then(r.bind(r,31630)),s=new i({apiKey:process.env.OPENAI_API_KEY}),o=Date.now(),n=new File([t],e,{type:this.getContentTypeFromFileName(e)}),l=await s.audio.transcriptions.create({file:n,model:"whisper-1",response_format:"json",temperature:.2,language:a.language?.split("-")[0]||"en"}),c=Date.now()-o,u=l.text;if(!u||0===u.trim().length)return{success:!1,error:"No speech recognized in audio file"};return{success:!0,transcription:u.trim(),confidence:.85,duration:c}}catch(t){if(console.error("❌ OpenAI Whisper transcription error:",t),413===t.status)return{success:!1,error:"Audio file too large for transcription service. Maximum size is 25MB."};if(429===t.status||t.message?.includes("rate limit"))return{success:!1,error:"Transcription service rate limit exceeded. Please try again in a few minutes."};return{success:!1,error:`Transcription failed: ${t.message||"Unknown error"}`}}}static getContentTypeFromFileName(t){return({mp3:"audio/mpeg",wav:"audio/wav",m4a:"audio/mp4",webm:"audio/webm",ogg:"audio/ogg",aac:"audio/aac",flac:"audio/flac"})[t.toLowerCase().split(".").pop()||""]||"audio/mpeg"}static async transcribeAudioWithFallback(t,e,r={}){let a=r.maxRetries||3,i="";for(let s=1;s<=a;s++)try{let o=await this.transcribeAudio(t,e,r);if(o.success)return{...o,method:`azure-speech-attempt-${s}`};if((i=o.error||"Unknown error").includes("Invalid audio format"))break;s<a&&await new Promise(t=>setTimeout(t,1e3*s))}catch(t){i=t instanceof Error?t.message:"Unknown error",s<a&&await new Promise(t=>setTimeout(t,1e3*s))}return{success:!1,error:`Failed after ${a} attempts. Last error: ${i}`,method:"azure-speech-failed"}}static async getServiceHealth(){if(!this.isConfigured())return{healthy:!1,region:"openai-fallback",error:"OpenAI API key not configured"};try{let{OpenAI:t}=await r.e(8096).then(r.bind(r,31630)),e=new t({apiKey:process.env.OPENAI_API_KEY});return await e.models.list(),{healthy:!0,region:"openai-fallback"}}catch(t){return{healthy:!1,region:"openai-fallback",error:t instanceof Error?t.message:"Unknown error"}}}}},50238:(t,e,r)=>{"use strict";r.d(e,{u:()=>i});var a=r(5772);class i{static getStateFilePath(t){let e=t.replace(/\.[^/.]+$/,"");return`${e}.state.json`}static async initializeState(t,e,r){let a={audioFile:t.split("/").pop()||t,status:"pending",transcription:{status:"pending",service:"azure-speech-to-text"},summarization:{status:"pending",service:"azure-openai"},metadata:{fileSize:e,format:r,processingAttempts:0,lastAttempt:new Date().toISOString(),createdAt:new Date().toISOString(),filePath:t}};return await this.saveState(t,a),a}static async loadState(t){try{let e=this.getStateFilePath(t),r=await a.AzureStorageService.downloadJson(e);if(!r||!r.audioFile)return null;return r}catch(t){return null}}static async saveState(t,e){let r=this.getStateFilePath(t);await a.AzureStorageService.uploadJson(r,e)}static async updateTranscriptionStatus(t,e,r,a){let i=await this.loadState(t);if(!i)throw Error(`No state found for ${t}`);i.transcription.status=e,"processing"===e?(i.transcription.startedAt=new Date().toISOString(),i.status="transcribing"):"complete"===e?(i.transcription.completedAt=new Date().toISOString(),i.transcription.result=r,i.status="summarizing"):"failed"===e&&(i.transcription.error=a,i.status="failed"),r&&(i.transcription.result=r),a&&(i.transcription.error=a),await this.saveState(t,i)}static async updateSummarizationStatus(t,e,r,a){let i=await this.loadState(t);if(!i)throw Error(`No state found for ${t}`);i.summarization.status=e,"processing"===e?i.summarization.startedAt=new Date().toISOString():"complete"===e?(i.summarization.completedAt=new Date().toISOString(),i.summarization.result=r,i.status="complete"):"failed"===e&&(i.summarization.error=a,i.status="failed"),r&&(i.summarization.result=r),a&&(i.summarization.error=a),await this.saveState(t,i)}static async markProcessingAttempt(t){let e=await this.loadState(t);if(!e)throw Error(`No state found for ${t}`);e.metadata.processingAttempts++,e.metadata.lastAttempt=new Date().toISOString(),await this.saveState(t,e)}static async getProcessingStats(){let t={total:0,pending:0,transcribing:0,summarizing:0,complete:0,failed:0};try{for(let e of(await a.AzureStorageService.listFiles(""))){if(!e.name.match(/\.(mp3|wav|m4a|webm|ogg|flac|aac)$/i))continue;t.total++;let r=await this.loadState(e.path);r?t[r.status]++:t.pending++}}catch(t){console.error("Error getting processing stats:",t)}return t}static async getFilesByStatus(t){let e=[];try{for(let r of(await a.AzureStorageService.listFiles(""))){if(!r.name.match(/\.(mp3|wav|m4a|webm|ogg|flac|aac)$/i))continue;let a=await this.loadState(r.path);a||"pending"!==t?a&&a.status===t&&e.push(r.path):e.push(r.path)}}catch(e){console.error(`Error getting files by status ${t}:`,e)}return e}static async resetFailedFiles(){let t=0;try{for(let e of(await this.getFilesByStatus("failed"))){let r=await this.loadState(e);r&&(r.status="pending",r.transcription.status="pending",r.summarization.status="pending",r.transcription.error=void 0,r.summarization.error=void 0,await this.saveState(e,r),t++)}}catch(t){console.error("Error resetting failed files:",t)}return t}static async cleanupOrphanedStates(){let t=0;try{for(let e of(await a.AzureStorageService.listFiles("")).filter(t=>t.name.endsWith(".state.json"))){let r=e.path.replace(".state.json",".mp3");!await a.AzureStorageService.fileExists(r)&&(await a.AzureStorageService.deleteFile(e.path),t++)}}catch(t){console.error("Error cleaning up orphaned states:",t)}return t}}},78335:()=>{},96487:()=>{}};