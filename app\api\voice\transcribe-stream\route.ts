import { NextRequest } from 'next/server';
import { AzureStorageService } from '@/lib/azure-storage';
import OpenAI from 'openai';
import { transcribeWithChunking, needsChunking } from '@/lib/audio-chunking';
import { getRecordingsByDate, createRecording, updateRecordingFields, createTranscription, createSummary, logMessage, getRecordingByFilename, getAllRecordings } from '@/lib/database';

export async function GET(request: NextRequest) {
  // Set up Server-Sent Events
  const encoder = new TextEncoder();
  
  const customReadable = new ReadableStream({
    start(controller) {
      // Send initial connection message
      const data = `data: ${JSON.stringify({ type: 'connected', message: 'Connected to transcription stream' })}\n\n`;
      controller.enqueue(encoder.encode(data));
      
      // Start transcription process
      startTranscriptionProcess(controller, encoder);
    }
  });

  return new Response(customReadable, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    },
  });
}

async function startTranscriptionProcess(controller: ReadableStreamDefaultController, encoder: TextEncoder) {
  try {
    // MIGRATION: Migration to SQL database completed
    console.log('✅ Using new SQL database system');
    // Send loading message
    controller.enqueue(encoder.encode(`data: ${JSON.stringify({ 
      type: 'status', 
      message: '🔍 Loading recordings from Azure...' 
    })}\n\n`));

    // Get recordings from Azure
    const recordings = await AzureStorageService.listFiles('');
    const audioFiles = recordings.filter(f => {
      const name = f.name.toLowerCase();
      // Skip Mac metadata files and other invalid files
      if (name.startsWith('._') || name.startsWith('.ds_store')) {
        return false;
      }
      return name.endsWith('.mp3') || name.endsWith('.wav') || name.endsWith('.m4a');
    });

    // Check which files need processing
    controller.enqueue(encoder.encode(`data: ${JSON.stringify({ 
      type: 'status',
      message: '🔍 Checking existing transcriptions and summaries...' 
    })}\n\n`));

    const needsTranscription: string[] = [];
    const needsSummary: string[] = [];
    const alreadyCompleted: string[] = [];
    
    // Check existing recordings database for transcriptions and summaries
    // Use new SQL-based recordings endpoint
    const existingRecordings = await getAllRecordings();
    console.log(`✅ [Transcribe Stream] Retrieved ${existingRecordings.length} recordings from database`);
    const recordingsByName = new Map(existingRecordings.map(r => [r.filename, r]));
    
    audioFiles.forEach(f => {
      const existing = recordingsByName.get(f.name);
      const hasTranscription = existing?.transcription && existing.transcription.length > 0;
      const hasSummary = existing?.clinical_summary && existing.clinical_summary.length > 0;
      
      if (!hasTranscription) {
        needsTranscription.push(f.name);
      }
      if (hasTranscription && !hasSummary) {
        needsSummary.push(f.name);
      }
      if (hasTranscription && hasSummary) {
        alreadyCompleted.push(f.name);
      }
    });

    const totalNeedingWork = needsTranscription.length + needsSummary.length;

    controller.enqueue(encoder.encode(`data: ${JSON.stringify({ 
      type: 'analysis_complete',
      totalFiles: audioFiles.length,
      needsTranscription: needsTranscription.length,
      needsSummary: needsSummary.length,
      completed: alreadyCompleted.length,
      skippedInvalid: recordings.length - audioFiles.length,
      message: `📊 Analysis: ${needsTranscription.length} need transcription, ${needsSummary.length} need summary, ${alreadyCompleted.length} complete, ${recordings.length - audioFiles.length} invalid files skipped`
    })}\n\n`));

    if (totalNeedingWork === 0) {
      controller.enqueue(encoder.encode(`data: ${JSON.stringify({ 
        type: 'completed', 
        message: '✨ All files are already processed!' 
      })}\n\n`));
      controller.close();
      return;
    }

    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY!
    });

    let completed = 0;
    let failed = 0;
    let currentlyProcessing = 0;
    let remainingNeedsTranscription = needsTranscription.length;
    let remainingNeedsSummary = needsSummary.length;

    // Separate files into transcription and summarization phases
    const filesToTranscribe = audioFiles.filter(f => needsTranscription.includes(f.name));
    const filesToSummarize = audioFiles.filter(f => needsSummary.includes(f.name));
    
    const totalFilesToProcess = filesToTranscribe.length + filesToSummarize.length;
    
    // Parallel processing with concurrency control
    const TRANSCRIPTION_CONCURRENCY = 5; // Transcriptions: 5 concurrent (audio processing - increased from 3)
    const SUMMARIZATION_CONCURRENCY = 8; // Summaries: 8 concurrent (text processing only - increased from 5)
    let processingPromises = new Map<string, Promise<void>>();
    
    const processTranscription = async (file: any, index: number, phase: string) => {
      currentlyProcessing++;
      
      const existing = recordingsByName.get(file.name);
      
      // Send progress update
      controller.enqueue(encoder.encode(`data: ${JSON.stringify({
        type: 'progress',
        current: index + 1,
        total: filesToTranscribe.length,
        fileName: file.name,
        completed,
        failed,
        currentlyProcessing,
        message: `🎵 ${phase} ${index + 1}/${filesToTranscribe.length}: ${file.name} (transcription) [${currentlyProcessing} active]`
      })}\n\n`));

      try {
        // Download file from Azure
        const audioBuffer = await AzureStorageService.downloadFile(file.path);
        
        // Check if file needs chunking due to size
        const isLargeFile = needsChunking(audioBuffer.length);
        
        let transcriptionText = '';
        if (isLargeFile) {
          // Use chunking for large files (no noise, just do it)
          const chunkResult = await transcribeWithChunking(audioBuffer, file.name, 'audio/mpeg');
          
          if (chunkResult.success) {
            transcriptionText = chunkResult.fullTranscription;
          } else {
            throw new Error(chunkResult.error || 'Large file processing failed');
          }
        } else {
          // Regular transcription for smaller files
          const transcription = await openai.audio.transcriptions.create({
            file: new File([audioBuffer], file.name, { type: 'audio/mpeg' }),
            model: 'whisper-1',
            response_format: 'text'
          });
          transcriptionText = transcription;
        }

        // Save transcription to database
        if (existing) {
          // Update existing recording
          const updates: any = {
            transcription: transcriptionText,
            transcribed_at: new Date(),
            status: 'transcribed'
          };
          
          console.log(`💾 Saving transcription for: ${file.name}`);
          await updateRecordingFields(existing.id, updates);
          console.log(`✅ Transcription saved for: ${file.name}`);
        } else {
          // Create new recording
          const newRecording = {
            filename: file.name,
            original_filename: file.name,
            blob_url: `https://storage.blob.core.windows.net/recordings/${file.name}`,
            blob_path: file.path,
            file_size: file.size,
            status: 'transcribed',
            metadata: {
              transcription_text: transcriptionText,
              transcribed_at: new Date().toISOString(),
              category: 'clinical',
              isUploaded: true,
            }
          };
          
          console.log(`💾 Creating new recording for: ${file.name}`);
          await createRecording(newRecording);
          console.log(`✅ New recording created for: ${file.name}`);
        }

        // Add small delay between requests to be respectful to API
        await new Promise(resolve => setTimeout(resolve, 1000));

        completed++;
        remainingNeedsTranscription--;
        
        // Send success update
        const wasChunked = needsChunking(audioBuffer.length);
        
        controller.enqueue(encoder.encode(`data: ${JSON.stringify({
          type: 'file_completed',
          fileName: file.name,
          transcription: transcriptionText.substring(0, 200) + '...',
          completed,
          failed,
          currentlyProcessing,
          remainingNeedsTranscription,
          remainingNeedsSummary,
          totalUnprocessed: remainingNeedsTranscription + remainingNeedsSummary,
          message: `✅ Transcribed: ${file.name} (${wasChunked ? 'chunked' : 'direct'}) - ${remainingNeedsTranscription + remainingNeedsSummary} remaining`
        })}\n\n`));

      } catch (error) {
        failed++;
        
        // Send error update
        controller.enqueue(encoder.encode(`data: ${JSON.stringify({
          type: 'file_failed',
          fileName: file.name,
          error: error instanceof Error ? error.message : 'Unknown error',
          completed,
          failed,
          currentlyProcessing,
          message: `❌ Transcription failed: ${file.name} - ${error instanceof Error ? error.message : 'Unknown error'}`
        })}\n\n`));
      } finally {
        currentlyProcessing--;
        processingPromises.delete(file.name);
      }
    };

    const processSummarization = async (file: any, index: number, phase: string) => {
      currentlyProcessing++;
      
      const existing = recordingsByName.get(file.name);
      
      // Send progress update
      controller.enqueue(encoder.encode(`data: ${JSON.stringify({
        type: 'progress',
        current: index + 1,
        total: filesToSummarize.length,
        fileName: file.name,
        completed,
        failed,
        currentlyProcessing,
        message: `📝 ${phase} ${index + 1}/${filesToSummarize.length}: ${file.name} (summary) [${currentlyProcessing} active]`
      })}\n\n`));

      try {
        // Get transcription text (should exist by now)
        const transcriptionText = existing?.transcription || '';
        
        if (!transcriptionText || transcriptionText.length < 50) {
          throw new Error('No transcription available for summarization');
        }

        // Generate summary
        const summaryResponse = await openai.chat.completions.create({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are a professional clinical assistant. Convert this voice transcription into a professional clinical note. Focus on clinical findings, patient symptoms, treatments, and recommendations. Use proper medical terminology and format.'
            },
            {
              role: 'user',
              content: `Please convert this voice memo into a professional clinical note:\n\n${transcriptionText}`
            }
          ],
          max_tokens: 1000,
          temperature: 0.3
        });
        
        const summaryText = summaryResponse.choices[0]?.message?.content || '';

        // Save summary to database
        if (existing) {
          // Update existing recording
          const updates: any = {
            clinical_summary: summaryText
          };
          
          console.log(`💾 Saving summary for: ${file.name}`);
          await updateRecordingFields(existing.id, updates);
          console.log(`✅ Summary saved for: ${file.name}`);
        } else {
          // This shouldn't happen for summaries since transcription should exist first
          console.warn(`⚠️ No existing recording found for summary: ${file.name}`);
        }

        // Add small delay between requests to be respectful to API
        await new Promise(resolve => setTimeout(resolve, 500));

        completed++;
        remainingNeedsSummary--;
        
        // Send success update
        controller.enqueue(encoder.encode(`data: ${JSON.stringify({
          type: 'file_completed',
          fileName: file.name,
          summary: summaryText.substring(0, 150) + '...',
          completed,
          failed,
          currentlyProcessing,
          remainingNeedsTranscription,
          remainingNeedsSummary,
          totalUnprocessed: remainingNeedsTranscription + remainingNeedsSummary,
          message: `✅ Summarized: ${file.name} - ${remainingNeedsTranscription + remainingNeedsSummary} remaining`
        })}\n\n`));

      } catch (error) {
        failed++;
        
        // Send error update
        controller.enqueue(encoder.encode(`data: ${JSON.stringify({
          type: 'file_failed',
          fileName: file.name,
          error: error instanceof Error ? error.message : 'Unknown error',
          completed,
          failed,
          currentlyProcessing,
          message: `❌ Summary failed: ${file.name} - ${error instanceof Error ? error.message : 'Unknown error'}`
        })}\n\n`));
      } finally {
        currentlyProcessing--;
        processingPromises.delete(file.name);
      }
    };

    // PHASE 1: Transcriptions (parallel processing)
    if (filesToTranscribe.length > 0) {
      controller.enqueue(encoder.encode(`data: ${JSON.stringify({
        type: 'status',
        message: `🎵 Phase 1: Starting transcriptions for ${filesToTranscribe.length} files...`
      })}\n\n`));

      for (let i = 0; i < filesToTranscribe.length; i++) {
        const file = filesToTranscribe[i];
        
        // Wait if we've reached the transcription concurrency limit
        while (processingPromises.size >= TRANSCRIPTION_CONCURRENCY) {
          await Promise.race(Array.from(processingPromises.values()));
        }
        
        // Start processing this file
        const promise = processTranscription(file, i, "Phase 1");
        processingPromises.set(file.name, promise);
      }
      
      // Wait for all transcriptions to complete
      await Promise.allSettled(Array.from(processingPromises.values()));
      processingPromises = new Map(); // Reset for next phase
      
      controller.enqueue(encoder.encode(`data: ${JSON.stringify({
        type: 'status',
        message: `✅ Phase 1 complete: All transcriptions finished`
      })}\n\n`));
    }

    // PHASE 2: Summarizations (parallel processing)
    if (filesToSummarize.length > 0) {
      controller.enqueue(encoder.encode(`data: ${JSON.stringify({
        type: 'status',
        message: `📝 Phase 2: Starting summaries for ${filesToSummarize.length} files...`
      })}\n\n`));

      
      
      for (let i = 0; i < filesToSummarize.length; i++) {
        const file = filesToSummarize[i];
        
        // Wait if we've reached the summarization concurrency limit
        while (processingPromises.size >= SUMMARIZATION_CONCURRENCY) {
          await Promise.race(Array.from(processingPromises.values()));
        }
        
        // Update the existing lookup for this file
        
        
        // Start processing this file
        const promise = processSummarization(file, i, "Phase 2");
        processingPromises.set(file.name, promise);
      }
      
      // Wait for all summarizations to complete
      await Promise.allSettled(Array.from(processingPromises.values()));
      
      controller.enqueue(encoder.encode(`data: ${JSON.stringify({
        type: 'status',
        message: `✅ Phase 2 complete: All summaries finished`
      })}\n\n`));
    }

    // Send final completion
    controller.enqueue(encoder.encode(`data: ${JSON.stringify({
      type: 'completed',
      completed,
      failed,
      total: totalFilesToProcess,
      totalFiles: audioFiles.length,
      alreadyCompleted: alreadyCompleted.length,
      transcriptionsCompleted: filesToTranscribe.length,
      summarizationsCompleted: filesToSummarize.length,
      message: `🎉 All phases complete! ✅ ${completed} processed (${filesToTranscribe.length} transcribed, ${filesToSummarize.length} summarized), ❌ ${failed} failed, 📋 ${alreadyCompleted.length} already complete`
    })}\n\n`));

  } catch (error) {
    controller.enqueue(encoder.encode(`data: ${JSON.stringify({
      type: 'error',
      message: `💥 Process error: ${error instanceof Error ? error.message : 'Unknown error'}`
    })}\n\n`));
  } finally {
    controller.close();
  }
}