name: Post Build Cleanup

on:
  workflow_run:
    workflows: ["Build and Release", "Deploy to Azure", "Azure Static Web Apps CI/CD", "Simple Release"]
    types:
      - completed

jobs:
  cleanup-old-artifacts:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    permissions:
      actions: write
      contents: read

    steps:
      - name: Cleanup old artifacts after successful build
        id: cleanup
        uses: actions/github-script@v7
        with:
          script: |
            const owner = context.repo.owner;
            const repo = context.repo.repo;

            console.log(`🧹 Post-build cleanup triggered by successful workflow: ${context.payload.workflow_run.name}`);

            try {
              // Get all artifacts
              const artifacts = await github.paginate(github.rest.actions.listArtifactsForRepo, {
                owner,
                repo,
                per_page: 100
              });

              console.log(`📦 Found ${artifacts.length} total artifacts`);

              if (artifacts.length === 0) {
                console.log('✅ No artifacts found, nothing to clean up');
                return;
              }

              // Calculate cutoff date (7 days ago for post-build cleanup)
              const cutoffDate = new Date();
              cutoffDate.setDate(cutoffDate.getDate() - 7);

              // Filter artifacts older than 7 days
              const oldArtifacts = artifacts.filter(artifact => {
                const createdAt = new Date(artifact.created_at);
                return createdAt < cutoffDate;
              });

              if (oldArtifacts.length === 0) {
                console.log('✅ No artifacts older than 7 days found');
                const totalSizeMB = artifacts.reduce((sum, artifact) => sum + (artifact.size_in_bytes / (1024 * 1024)), 0);
                console.log(`📊 Current storage: ${artifacts.length} artifacts, ${totalSizeMB.toFixed(1)} MB`);
                return;
              }

              console.log(`🗑️  Found ${oldArtifacts.length} artifacts older than 7 days to delete`);

              let deletedCount = 0;
              let freedSpaceMB = 0;

              for (const artifact of oldArtifacts) {
                try {
                  const sizeMB = artifact.size_in_bytes / (1024 * 1024);
                  const createdDate = new Date(artifact.created_at).toISOString().split('T')[0];

                  console.log(`  🗑️  Deleting: ${artifact.name} (${sizeMB.toFixed(1)}MB, ${createdDate})`);

                  await github.rest.actions.deleteArtifact({
                    owner,
                    repo,
                    artifact_id: artifact.id
                  });

                  deletedCount++;
                  freedSpaceMB += sizeMB;
                  console.log(`    ✅ Deleted successfully`);

                  // Small delay to avoid rate limiting
                  await new Promise(resolve => setTimeout(resolve, 200));

                } catch (error) {
                  console.error(`    ❌ Failed to delete artifact ${artifact.id}:`, error.message);
                }
              }

              // Calculate remaining storage
              const remainingArtifacts = artifacts.length - deletedCount;
              const totalSizeMB = artifacts.reduce((sum, artifact) => sum + (artifact.size_in_bytes / (1024 * 1024)), 0);
              const remainingStorageMB = totalSizeMB - freedSpaceMB;

              console.log(`🎉 Post-build cleanup complete!`);
              console.log(`   📊 Deleted: ${deletedCount} artifacts`);
              console.log(`   💾 Freed: ${freedSpaceMB.toFixed(1)} MB`);
              console.log(`   📦 Remaining: ${remainingArtifacts} artifacts (${remainingStorageMB.toFixed(1)} MB)`);

              // Set outputs for summary
              core.setOutput('deleted_count', deletedCount);
              core.setOutput('freed_space_mb', freedSpaceMB.toFixed(1));
              core.setOutput('remaining_artifacts', remainingArtifacts);
              core.setOutput('remaining_storage_mb', remainingStorageMB.toFixed(1));

            } catch (error) {
              console.error('❌ Error during post-build cleanup:', error);
              core.setFailed(`Post-build cleanup failed: ${error.message}`);
            }

      - name: Create cleanup summary
        if: always()
        run: |
          echo "## 🧹 Post-Build Cleanup Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Trigger:** ${{ github.event.workflow_run.name }} workflow completed successfully" >> $GITHUB_STEP_SUMMARY
          echo "- **Deleted:** ${{ steps.cleanup.outputs.deleted_count || 0 }} artifacts (older than 7 days)" >> $GITHUB_STEP_SUMMARY
          echo "- **Space Freed:** ${{ steps.cleanup.outputs.freed_space_mb || 0 }} MB" >> $GITHUB_STEP_SUMMARY
          echo "- **Remaining:** ${{ steps.cleanup.outputs.remaining_artifacts || 'Unknown' }} artifacts" >> $GITHUB_STEP_SUMMARY
          echo "- **Storage Used:** ${{ steps.cleanup.outputs.remaining_storage_mb || 'Unknown' }} MB" >> $GITHUB_STEP_SUMMARY
