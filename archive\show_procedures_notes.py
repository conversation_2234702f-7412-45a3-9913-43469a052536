#!/usr/bin/env python3
"""
Show Procedures and Notes

This script fetches and displays procedure codes and clinical notes for a specific date.
It's a simplified version of detailed_appointments.py focused on verifying the data.
"""

import json
import requests
import sys
from datetime import datetime

# API configuration
API_BASE = "https://api.sikkasoft.com/v1"
API_BASE_V2 = "https://api.sikkasoft.com/v2"
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_TIMEOUT = 30  # seconds

def load_credentials():
    """Load API credentials from credentials.json file."""
    try:
        with open("credentials.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: credentials.json file not found.")
        sys.exit(1)
    except json.JSONDecodeError:
        print("Error: Invalid JSON in credentials.json file.")
        sys.exit(1)

def authenticate(app_id, app_key, office_id, secret_key):
    """Authenticate with Sikka API and get request key."""
    print("Authenticating with Sikka API...")
    
    try:
        auth_resp = requests.post(
            f"{API_BASE_V4}/request_key",
            headers={"Content-Type": "application/json"},
            json={
                "grant_type": "request_key",
                "office_id": office_id,
                "secret_key": secret_key,
                "app_id": app_id,
                "app_key": app_key
            },
            timeout=API_TIMEOUT
        )
        
        if auth_resp.status_code == 200:
            data = auth_resp.json()
            request_key = data.get("request_key")
            if request_key:
                print("Authentication successful!")
                return request_key
            else:
                print("Error: No request key in response.")
                print(data)
        else:
            print(f"Authentication failed with status code: {auth_resp.status_code}")
            print(auth_resp.text)
    except Exception as e:
        print(f"Error during authentication: {e}")
    
    sys.exit(1)

def fetch_procedures(request_key, target_date):
    """Fetch procedure codes for a specific date."""
    headers = {"Request-Key": request_key}
    params = {"date": target_date, "transaction_type": "Procedure"}
    
    print(f"\nFetching procedure codes for {target_date}...")
    
    try:
        resp = requests.get(
            f"{API_BASE_V4}/transactions",
            headers=headers,
            params=params,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code != 200:
            print(f"Error fetching procedures: {resp.status_code}")
            print(resp.text)
            return []
        
        data = resp.json()
        items = data.get("items", [])
        
        # Filter for procedure transactions only
        procedure_items = [t for t in items if t.get("transaction_type") == "Procedure"]
        
        print(f"Found {len(procedure_items)} procedure codes")
        return procedure_items
    except Exception as e:
        print(f"Error: {e}")
        return []

def fetch_notes(request_key, target_date):
    """Fetch clinical notes for a specific date."""
    headers = {"Request-Key": request_key}
    params = {"date": target_date}
    
    print(f"\nFetching clinical notes for {target_date}...")
    
    try:
        resp = requests.get(
            f"{API_BASE_V4}/medical_notes",
            headers=headers,
            params=params,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code != 200:
            print(f"Error fetching notes: {resp.status_code}")
            print(resp.text)
            return []
        
        data = resp.json()
        items = data.get("items", [])
        
        # Filter for notes with content
        notes_with_content = []
        for item in items:
            content = None
            for field in ["note", "text", "description"]:
                if item.get(field) and item.get(field).strip():
                    content = item.get(field)
                    break
            
            if content:
                if "note" not in item or not item["note"]:
                    item["note"] = content
                notes_with_content.append(item)
        
        print(f"Found {len(notes_with_content)} notes with content")
        return notes_with_content
    except Exception as e:
        print(f"Error: {e}")
        return []

def display_procedures(procedures):
    """Display procedure codes in a readable format."""
    if not procedures:
        print("No procedure codes found.")
        return
    
    print("\n=== PROCEDURE CODES ===")
    print(f"{'CODE':<10}{'DESCRIPTION':<40}{'PATIENT ID':<15}{'AMOUNT':<10}{'TOOTH':<10}{'SURFACE':<10}")
    print("-" * 95)
    
    for proc in procedures:
        code = proc.get("procedure_code", "")
        desc = proc.get("procedure_description", "")
        patient_id = proc.get("patient_id", "")
        amount = float(proc.get("amount", "0"))
        tooth = proc.get("tooth_from", "") or "-"
        surface = proc.get("surface", "") or "-"
        
        # Truncate long descriptions
        if len(desc) > 37:
            desc = desc[:34] + "..."
        
        print(f"{code:<10}{desc:<40}{patient_id:<15}${amount:<9.2f}{tooth:<10}{surface:<10}")

def display_notes(notes):
    """Display clinical notes in a readable format."""
    if not notes:
        print("No clinical notes found.")
        return
    
    print("\n=== CLINICAL NOTES ===")
    
    for i, note in enumerate(notes):
        patient_id = note.get("patient_id", "")
        note_date = note.get("date", "")
        note_text = note.get("note", "")
        note_type = note.get("type", "")
        provider_id = note.get("provider_id", "")
        
        print(f"\nNote {i+1}:")
        print(f"Patient ID: {patient_id}")
        print(f"Date: {note_date}")
        print(f"Type: {note_type}")
        print(f"Provider: {provider_id}")
        print("Content:")
        
        # Format and truncate long notes
        if len(note_text) > 500:
            print(f"{note_text[:500]}...\n[Note truncated, total length: {len(note_text)} characters]")
        else:
            print(note_text)
        
        print("-" * 80)

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python show_procedures_notes.py YYYY-MM-DD")
        sys.exit(1)
    
    target_date = sys.argv[1]
    
    # Validate date format
    try:
        datetime.strptime(target_date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        sys.exit(1)
    
    # Load credentials
    credentials = load_credentials()
    app_id = credentials.get("app_id")
    app_key = credentials.get("app_key")
    office_id = credentials.get("office_id")
    secret_key = credentials.get("secret_key")
    
    # Authenticate and get request key
    request_key = authenticate(app_id, app_key, office_id, secret_key)
    
    # Fetch procedures and notes
    procedures = fetch_procedures(request_key, target_date)
    notes = fetch_notes(request_key, target_date)
    
    # Display the results
    display_procedures(procedures)
    display_notes(notes)

if __name__ == "__main__":
    main()
