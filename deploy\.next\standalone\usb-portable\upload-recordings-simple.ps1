# USB Dental Recordings Upload Tool - Simple Test Version
# Version 1.0.0

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true)]
    [string]$USBDrive,
    
    [Parameter(Mandatory=$true)]
    [string]$ScriptDir
)

# Configuration
$CURRENT_VERSION = "1.0.0"
$AZURE_BASE_URL = "https://dentalapp-cpcwgucdawhmb4hu.centralus-01.azurewebsites.net"

# Recording paths to scan (relative to USB drive)
$RECORDING_PATHS = @(
    "REC_FILE\FOLDER01",
    "RECORDER\FOLDER_A",
    "RECORDER\FOLDER_B",
    "RECORDER\FOLDER_C",
    "RECORDE<PERSON>\FOLDER_D",
    "RECORDER\FOLDER_E"
)

# Archive folder and device ID file
$ARCHIVE_FOLDER = "UPLOADED"
$DEVICE_ID_FILE = "device_id.txt"

# Statistics and device tracking
$script:TotalFiles = 0
$script:UploadedFiles = 0
$script:SkippedFiles = 0
$script:FailedFiles = 0
$script:DeviceId = "usb-portable"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Get-DeviceId {
    param([string]$USBDrive)

    Write-ColorOutput "[DEVICE] Checking device ID..." "Cyan"

    $deviceIdPath = Join-Path $USBDrive $DEVICE_ID_FILE

    if (Test-Path $deviceIdPath) {
        try {
            $existingId = Get-Content $deviceIdPath -Raw
            $existingId = $existingId.Trim()
            Write-ColorOutput "   [SUCCESS] Found existing device ID: $existingId" "Green"
            return $existingId
        } catch {
            Write-ColorOutput "   [WARNING] Could not read device ID file" "Yellow"
        }
    }

    # Generate new device ID based on USB drive letter and timestamp
    $driveLetter = $USBDrive.Substring(0, 1).ToLower()
    $timestamp = Get-Date -Format "yyyyMMdd"
    $newDeviceId = "usb-$driveLetter-$timestamp"

    Write-ColorOutput "   [INFO] No device ID found, creating new one: $newDeviceId" "Yellow"

    try {
        # Create device ID file
        $newDeviceId | Out-File -FilePath $deviceIdPath -Encoding UTF8 -NoNewline

        # Create setup info file
        $setupInfo = @"
USB Voice Recorder Setup
========================

Device ID: $newDeviceId
Setup Date: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Drive Letter: $USBDrive
Recordings Folders: REC_FILE\FOLDER01, RECORDER\FOLDER_A-E

This USB recorder is configured for the dental office voice recording system.
Do not delete the device_id.txt file as it identifies this recorder.

For support, contact your IT administrator.
"@

        $setupInfoPath = Join-Path $USBDrive "RECORDER_SETUP.txt"
        $setupInfo | Out-File -FilePath $setupInfoPath -Encoding UTF8

        Write-ColorOutput "   [SUCCESS] Device ID created and saved" "Green"
        return $newDeviceId

    } catch {
        Write-ColorOutput "   [ERROR] Could not create device ID file: $($_.Exception.Message)" "Red"
        return "usb-portable-fallback"
    }
}

function Test-UpdateCheck {
    Write-ColorOutput "[UPDATE CHECK] Testing update check..." "Cyan"

    try {
        $updateUrl = "$AZURE_BASE_URL/api/tools/usb-portable/version"
        Write-ColorOutput "   Checking: $updateUrl" "Gray"

        $response = Invoke-RestMethod -Uri $updateUrl -Method GET -TimeoutSec 10

        if ($response.version) {
            Write-ColorOutput "[SUCCESS] Update check successful!" "Green"
            Write-ColorOutput "   Current version: $CURRENT_VERSION" "White"
            Write-ColorOutput "   Server version: $($response.version)" "White"
            Write-ColorOutput "   Server says: $($response.description)" "White"
        } else {
            Write-ColorOutput "[WARNING] No version info received" "Yellow"
        }
    } catch {
        Write-ColorOutput "[ERROR] Update check failed: $($_.Exception.Message)" "Red"
    }
}

function Find-AudioFiles {
    Write-ColorOutput "[SCAN] Scanning for audio files..." "Cyan"
    $allFiles = @()

    foreach ($path in $RECORDING_PATHS) {
        $fullPath = Join-Path $USBDrive $path

        Write-ColorOutput "[SCAN] Checking: $fullPath" "Gray"

        if (Test-Path $fullPath) {
            $files = Get-ChildItem -Path $fullPath -Filter "*.mp3" -File | Where-Object {
                $_.Name -notmatch "^\..*" # Exclude hidden files
            }

            if ($files) {
                Write-ColorOutput "   [SUCCESS] Found $($files.Count) audio files" "Green"
                $allFiles += $files
            } else {
                Write-ColorOutput "   [INFO] No audio files found" "Yellow"
            }
        } else {
            Write-ColorOutput "   [INFO] Path not found" "Red"
        }
    }

    return $allFiles
}

# Main execution
Write-ColorOutput ""
Write-ColorOutput "USB Dental Recordings Upload Tool v$CURRENT_VERSION" "Cyan"
Write-ColorOutput "================================================" "Cyan"
Write-ColorOutput ""
Write-ColorOutput "USB Drive: $USBDrive" "White"
Write-ColorOutput "Script Dir: $ScriptDir" "White"
Write-ColorOutput ""

# Get or create device ID
$script:DeviceId = Get-DeviceId -USBDrive $USBDrive
Write-ColorOutput ""

# Test update check
Test-UpdateCheck
Write-ColorOutput ""

# Scan for files (automatically proceeding)
Write-ColorOutput "Proceeding with file scan..." "Green"
Write-ColorOutput ""
$audioFiles = Find-AudioFiles
$totalFiles = $audioFiles.Count

# Statistics tracking
$script:TotalFiles = $totalFiles
$script:UploadedFiles = 0
$script:SkippedFiles = 0
$script:FailedFiles = 0
$script:TotalSizeMB = 0

Write-ColorOutput ""
Write-ColorOutput "SCAN RESULTS" "Cyan"
Write-ColorOutput "===============" "Cyan"
Write-ColorOutput "Total files found: $totalFiles" "White"

if ($totalFiles -gt 0) {
    Write-ColorOutput ""
    Write-ColorOutput "Files found:" "Green"
    foreach ($file in $audioFiles) {
        $fileSize = [math]::Round($file.Length / 1MB, 2)
        $script:TotalSizeMB += $fileSize
        Write-ColorOutput "   File: $($file.Name) (Size: $fileSize MB)" "White"
    }

    Write-ColorOutput ""
    Write-ColorOutput "Total size: $([math]::Round($script:TotalSizeMB, 2)) MB" "Cyan"

    # Simulate upload process (replace with real upload logic later)
    Write-ColorOutput ""
    Write-ColorOutput "UPLOAD SIMULATION" "Cyan"
    Write-ColorOutput "=================" "Cyan"
    Write-ColorOutput "Device ID: $script:DeviceId" "White"
    Write-ColorOutput ""

    foreach ($file in $audioFiles) {
        Write-ColorOutput "Processing: $($file.Name)..." "White"

        # Show Azure path where file would be uploaded
        $now = Get-Date
        $dateFolder = $now.ToString("yyyy-MM-dd")
        $azurePath = "recordings/$dateFolder/$script:DeviceId/$($file.Name)"
        Write-ColorOutput "   Azure path: $azurePath" "Gray"

        # Simulate upload success/failure (90% success rate for demo)
        $random = Get-Random -Minimum 1 -Maximum 100
        if ($random -le 90) {
            Write-ColorOutput "   [SUCCESS] Uploaded successfully" "Green"
            $script:UploadedFiles++
        } else {
            Write-ColorOutput "   [ERROR] Upload failed" "Red"
            $script:FailedFiles++
        }

        Start-Sleep -Milliseconds 300  # Simulate processing time
    }
} else {
    Write-ColorOutput ""
    Write-ColorOutput "No audio files found to upload." "Yellow"
    Write-ColorOutput ""
    Write-ColorOutput "Checked paths:" "White"
    foreach ($path in $RECORDING_PATHS) {
        Write-ColorOutput "   $USBDrive\$path" "Gray"
    }
}

# Final summary
Write-ColorOutput ""
Write-ColorOutput "UPLOAD SUMMARY" "Cyan"
Write-ColorOutput "==============" "Cyan"
Write-ColorOutput "Device ID: $script:DeviceId" "White"
Write-ColorOutput "Total files found: $script:TotalFiles" "White"
Write-ColorOutput "Successfully uploaded: $script:UploadedFiles" "Green"
Write-ColorOutput "Failed uploads: $script:FailedFiles" "Red"
Write-ColorOutput "Total size processed: $([math]::Round($script:TotalSizeMB, 2)) MB" "White"

# Output results in a format the VBScript can parse (including device ID)
Write-Output "RESULTS:$script:TotalFiles:$script:UploadedFiles:$script:FailedFiles:$([math]::Round($script:TotalSizeMB, 2)):$script:DeviceId"

if ($script:FailedFiles -eq 0 -and $script:TotalFiles -gt 0) {
    Write-ColorOutput ""
    Write-ColorOutput "All files uploaded successfully!" "Green"
    exit 0
} elseif ($script:TotalFiles -eq 0) {
    Write-ColorOutput ""
    Write-ColorOutput "No files to process." "Yellow"
    exit 0
} else {
    Write-ColorOutput ""
    Write-ColorOutput "Some uploads failed. Check the output above for details." "Yellow"
    exit 1
}
