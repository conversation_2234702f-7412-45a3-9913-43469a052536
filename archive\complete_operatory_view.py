import requests
import json
from datetime import datetime, timedelta
from collections import defaultdict
import sys
import argparse

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 60

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None

    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def fetch_appointments_with_pagination(request_key, target_date):
    """Fetch appointments for a specific date using pagination from both v2 and v4 endpoints."""
    headers = {"Request-Key": request_key}

    print(f"Fetching appointments for {target_date}...")

    # Try v2 endpoint with pagination
    all_appointments = []
    offset = 0
    limit = 100  # Maximum number of appointments per page
    max_pages = 20  # Maximum number of pages to fetch (up to 2000 appointments)

    # First try v2 endpoint which may contain operatory information
    print("Using v2 endpoint to get appointments with operatory information...")
    for page in range(max_pages):
        print(f"  Fetching page {page+1} (offset: {offset}, limit: {limit})...")

        try:
            resp = requests.get(
                f"{API_BASE_V2}/appointments",
                headers=headers,
                params={"date": target_date, "offset": offset, "limit": limit},
                timeout=API_TIMEOUT
            )

            if resp.status_code == 200:
                data = resp.json()

                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    print(f"    Found {len(items)} appointments on page {page+1}")

                    # Filter for appointments scheduled on the target date
                    target_appointments = [a for a in items if a.get("date") == target_date]
                    print(f"    Of these, {len(target_appointments)} are scheduled for {target_date}")

                    all_appointments.extend(target_appointments)

                    # If we got fewer items than the limit, we've reached the end
                    if len(items) < limit:
                        print("    Reached the end of appointments")
                        break

                    # Move to the next page
                    offset += limit
                else:
                    print("    No appointments found on this page")
                    break
            else:
                print(f"    Error: {resp.status_code}")
                print(f"    {resp.text}")
                break
        except Exception as e:
            print(f"    Error: {e}")
            break

    # Now try v4 endpoint to get any additional appointments
    print("\nUsing v4 endpoint to get additional appointments...")
    v4_appointments = []
    offset = 0

    for page in range(max_pages):
        print(f"  Fetching page {page+1} (offset: {offset}, limit: {limit})...")

        try:
            resp = requests.get(
                f"{API_BASE_V4}/appointments",
                headers=headers,
                params={"date": target_date, "offset": offset, "limit": limit},
                timeout=API_TIMEOUT
            )

            if resp.status_code == 200:
                data = resp.json()
                items = data.get("items", [])
                print(f"    Found {len(items)} appointments on page {page+1}")

                # Filter for appointments scheduled on the target date
                # In v4, date is in format "2025-05-16T00:00:00", so we need to check if it starts with target_date
                target_appointments = [a for a in items if a.get("date", "").startswith(target_date)]
                print(f"    Of these, {len(target_appointments)} are scheduled for {target_date}")

                v4_appointments.extend(target_appointments)

                # If we got fewer items than the limit, we've reached the end
                if len(items) < limit:
                    print("    Reached the end of appointments")
                    break

                # Move to the next page
                offset += limit
            else:
                print(f"    Error: {resp.status_code}")
                print(f"    {resp.text}")
                break
        except Exception as e:
            print(f"    Error: {e}")
            break

    # Merge appointments from both endpoints
    print(f"\nFound {len(all_appointments)} appointments from v2 endpoint")
    print(f"Found {len(v4_appointments)} appointments from v4 endpoint")

    # Add operatory information from v4 to v2 appointments if missing
    appointment_map = {}

    # First, add all v2 appointments to the map
    for appt in all_appointments:
        appt_id = appt.get("appointment_sr_no")
        if appt_id:
            appointment_map[appt_id] = appt

    # Then, add v4 appointments or update v2 appointments with operatory info
    for appt in v4_appointments:
        appt_id = appt.get("appointment_sr_no")
        if appt_id:
            if appt_id in appointment_map:
                # If the appointment exists but doesn't have operatory info, add it
                if not appointment_map[appt_id].get("operatory") and appt.get("operatory"):
                    appointment_map[appt_id]["operatory"] = appt.get("operatory")
            else:
                # If the appointment doesn't exist in the map, add it
                appointment_map[appt_id] = appt

    # Convert the map back to a list
    merged_appointments = list(appointment_map.values())
    print(f"Total unique appointments after merging: {len(merged_appointments)}")

    # We're skipping the practice_schedule endpoint as it adds complexity
    # and the v2/v4 endpoints already provide the operatory information we need

    return merged_appointments

def get_operatories_from_appointments(appointments):
    """Extract all operatories from appointments."""
    operatories = set()
    for appt in appointments:
        operatory = appt.get("operatory")
        if operatory and operatory not in ["", "N/A"]:
            operatories.add(operatory)

    return sorted(operatories)

def display_operatory_schedule(appointments, selected_operatories=None, show_modno=False):
    """Display a side-by-side schedule for multiple operatories."""
    if not appointments:
        print("No appointments found.")
        return

    # Identify no-show and cancelled appointments
    modno_appointments = []
    regular_appointments = []

    for appt in appointments:
        description = appt.get("description", "").upper()
        if "MODNO" in description:
            modno_appointments.append(appt)
        else:
            regular_appointments.append(appt)

    # Get all operatories from appointments
    all_operatories = get_operatories_from_appointments(appointments)

    # Filter operatories if specified
    operatories = selected_operatories if selected_operatories else all_operatories

    # Filter appointments by operatory
    filtered_appointments = []
    for appt in (regular_appointments if not show_modno else appointments):
        if appt.get("operatory") in operatories:
            filtered_appointments.append(appt)

    if not filtered_appointments:
        print(f"No appointments found for operatories: {', '.join(operatories)}")
        return

    # Group appointments by operatory
    appts_by_operatory = {}
    for op in operatories:
        appts_by_operatory[op] = []

    for appt in filtered_appointments:
        operatory = appt.get("operatory")
        if operatory in operatories:
            appts_by_operatory[operatory].append(appt)

    # Get date from first appointment
    date_str = appointments[0].get("date")
    if "T" in date_str:
        date_str = date_str.split("T")[0]
    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
    formatted_date = date_obj.strftime("%A, %B %d, %Y")

    # Display the schedule header
    print(f"\n{'=' * 100}")
    print(f"{'DAILY SCHEDULE BY OPERATORY':^100}")
    print(f"{formatted_date:^100}")
    print(f"{'=' * 100}")

    # Create time slots from 8:00 to 17:00 in 10-minute increments
    start_time = datetime.strptime("08:00", "%H:%M")
    end_time = datetime.strptime("17:00", "%H:%M")

    # Create a dictionary of appointments by time for each operatory
    appts_by_time = {}
    for op in operatories:
        appts_by_time[op] = {}
        for appt in appts_by_operatory[op]:
            time_str = appt.get("time", "")
            if time_str:
                try:
                    time_obj = datetime.strptime(time_str, "%H:%M")
                    appts_by_time[op][time_str] = appt
                except ValueError:
                    # Skip appointments with invalid time format
                    continue

    # Calculate column width for each operatory
    col_width = (100 - 10) // len(operatories)

    # Print header with operatory names
    print(f"{'TIME':<10}", end="")
    for op in operatories:
        print(f"{op:^{col_width}}", end="")
    print()
    print("-" * 10 + "-" * (col_width * len(operatories)))

    # Track active appointments for each operatory
    active_appts = {}
    for op in operatories:
        active_appts[op] = None

    # Display the schedule
    current_time = start_time
    while current_time <= end_time:
        time_str = current_time.strftime("%H:%M")

        # Check if any appointment starts at this time in any operatory
        new_appt_started = False
        for op in operatories:
            if time_str in appts_by_time[op]:
                new_appt_started = True
                appt = appts_by_time[op][time_str]
                length_min = float(appt.get("length", "0"))
                end_time_obj = current_time + timedelta(minutes=length_min)

                active_appts[op] = {
                    "appt": appt,
                    "end_time": end_time_obj,
                    "start_time": current_time,
                    "is_modno": "MODNO" in appt.get("description", "").upper()
                }

        # Only show time slots at hour and half-hour marks, or when an appointment starts
        if time_str.endswith(":00") or time_str.endswith(":30") or new_appt_started:
            print(f"{time_str:<10}", end="")

            # Display appointment info for each operatory
            for op in operatories:
                if active_appts[op] and active_appts[op]["end_time"] > current_time:
                    appt = active_appts[op]["appt"]
                    patient_name = appt.get("patient_name", "Unknown")
                    description = appt.get("description", "")
                    provider_id = appt.get("provider_id", "N/A")
                    is_modno = active_appts[op]["is_modno"]

                    # Truncate long names/descriptions
                    display_text = f"{patient_name} - {provider_id}"
                    if is_modno:
                        display_text = f"[MODNO] {display_text}"

                    if len(display_text) > col_width - 4:
                        display_text = display_text[:col_width-7] + "..."

                    print(f"{display_text:<{col_width}}", end="")
                else:
                    print(f"{'[OPEN]':<{col_width}}", end="")
            print()

        # Update active appointments
        for op in operatories:
            if active_appts[op] and active_appts[op]["end_time"] <= current_time:
                active_appts[op] = None

        # Move to next time slot (10 minutes)
        current_time += timedelta(minutes=10)

    # Display MODNO appointments if not showing them in the main schedule
    if not show_modno and modno_appointments:
        modno_in_selected_ops = [a for a in modno_appointments if a.get("operatory") in operatories]
        if modno_in_selected_ops:
            print(f"\n{'=' * 100}")
            print(f"{'MODNO APPOINTMENTS':^100}")
            print(f"{'=' * 100}")
            print(f"\n{'TIME':<8}{'PATIENT':<25}{'OPERATORY':<10}{'PROVIDER':<10}")
            print(f"{'-' * 8}{'-' * 25}{'-' * 10}{'-' * 10}")

            for appt in sorted(modno_in_selected_ops, key=lambda a: a.get("time", "")):
                time = appt.get("time", "")
                patient_name = appt.get("patient_name", "Unknown Patient")
                operatory = appt.get("operatory", "N/A")
                provider_id = appt.get("provider_id", "N/A")

                # Truncate long patient names
                if len(patient_name) > 22:
                    patient_name = patient_name[:19] + "..."

                print(f"{time:<8}{patient_name:<25}{operatory:<10}{provider_id:<10}")

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Generate a complete daily schedule by operatory")
    parser.add_argument("date", help="Date in YYYY-MM-DD format")
    parser.add_argument("--operatories", help="Comma-separated list of operatories to display (e.g., DL01,DL02)")
    parser.add_argument("--show-modno", action="store_true", help="Show MODNO appointments in the main schedule")

    args = parser.parse_args()

    # Validate date format
    try:
        datetime.strptime(args.date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        return

    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return

    # Fetch appointments for the specified date with pagination
    appointments = fetch_appointments_with_pagination(request_key, args.date)

    if not appointments:
        print(f"No appointments found for {args.date}")
        return

    # Get all operatories from appointments
    all_operatories = get_operatories_from_appointments(appointments)
    print(f"\nOperatories found in appointments: {', '.join(all_operatories)}")

    # Parse operatories if provided
    selected_operatories = None
    if args.operatories:
        selected_operatories = [op.strip() for op in args.operatories.split(",")]
        print(f"Filtering by operatories: {', '.join(selected_operatories)}")

    # Display the operatory schedule
    display_operatory_schedule(appointments, selected_operatories, args.show_modno)

if __name__ == "__main__":
    main()
