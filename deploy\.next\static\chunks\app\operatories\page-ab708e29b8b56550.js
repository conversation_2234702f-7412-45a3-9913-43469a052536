(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6414],{271:(e,a,t)=>{Promise.resolve().then(t.bind(t,3871))},3871:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>m});var r=t(5155),s=t(2115),l=t(5695),o=t(6496),d=t(4423),n=t(79),c=t(2794),i=t(9107),g=t(714),x=t(9836),u=t(3898);function h(e){let{initialDate:a=new Date,onDateChange:t}=e,[l,o]=(0,s.useState)(a),[h,b]=(0,s.useState)((0,d.k)(a,{weekStartsOn:0})),[m,y]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=()=>{y(document.documentElement.classList.contains("dark"))};e();let a=new MutationObserver(e);return a.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),()=>a.disconnect()},[]),(0,s.useEffect)(()=>{o(()=>a),b(()=>(0,d.k)(a,{weekStartsOn:0}))},[a]);let p=(0,n.k)({start:h,end:(0,c.$)(h,{weekStartsOn:0})}),f=e=>{o(e),t(e)};return(0,r.jsxs)("div",{className:"rounded-lg p-4 border",style:m?{backgroundColor:"#374151",borderColor:"#4b5563",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)"}:{backgroundColor:"#e5e7eb",borderColor:"#d1d5db",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:(0,x.GP)(h,"MMMM yyyy")}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>{b(e=>(0,i.e)(e,7))},className:"p-2 rounded-md bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 shadow-sm","aria-label":"Previous week",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,r.jsx)("button",{onClick:()=>{let e=new Date;o(e),b((0,d.k)(e,{weekStartsOn:0})),t(e)},className:"px-3 py-2 rounded-md bg-blue-600 hover:bg-blue-700 text-white text-sm shadow-sm",children:"Today"}),(0,r.jsx)("button",{onClick:()=>{b(e=>(0,g.f)(e,7))},className:"p-2 rounded-md bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 shadow-sm","aria-label":"Next week",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-7 gap-1 mb-1",children:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map((e,a)=>(0,r.jsx)("div",{className:"text-center text-sm font-medium text-gray-500 dark:text-gray-400 py-1",children:e},a))}),(0,r.jsx)("div",{className:"grid grid-cols-7 gap-1",children:p.map((e,a)=>{let t=(0,u.r)(e,new Date),s=(0,u.r)(e,l),o=0===e.getDay()||6===e.getDay();return(0,r.jsxs)("button",{onClick:()=>f(e),className:"h-16 flex flex-col items-center justify-center rounded-md text-sm font-medium shadow-md transition-colors",style:s?{backgroundColor:"#2563eb",color:"white"}:t?m?{backgroundColor:"#1e3a8a",color:"#93c5fd"}:{backgroundColor:"#dbeafe",color:"#3b82f6"}:o?m?{backgroundColor:"#1f2937",color:"#9ca3af"}:{backgroundColor:"#f3f4f6",color:"#6b7280"}:m?{backgroundColor:"#374151",color:"#e5e7eb"}:{backgroundColor:"#d1d5db",color:"#374151"},children:[(0,r.jsx)("span",{className:"text-lg",children:(0,x.GP)(e,"d")}),(0,r.jsx)("span",{className:"text-xs",children:(0,x.GP)(e,"EEE")})]},a)})}),(0,r.jsxs)("div",{className:"mt-4 text-center text-gray-700 dark:text-gray-300",children:["Selected: ",(0,r.jsx)("span",{className:"font-semibold",children:(0,x.GP)(l,"EEEE, MMMM d, yyyy")})]})]})}function b(){let e=(0,l.useRouter)(),a=(0,l.useSearchParams)(),t=a.get("date"),d=new Date,n=t?new Date("".concat(t,"T12:00:00.000Z")):d,[c]=(0,s.useState)(t||(0,x.GP)(d,"yyyy-MM-dd")),[i,g]=(0,s.useState)(n),[u,b]=(0,s.useState)([]),[m,y]=(0,s.useState)(()=>{let e=a.getAll("operatory");return e.length>0?e:["DL01","DL02"]}),[p,f]=(0,s.useState)(!0),[k,w]=(0,s.useState)(null);(0,s.useEffect)(()=>{let e=a.getAll("operatory");e.length>0&&y(e),(async()=>{f(!0),w(null),b([]);try{let e=t||(0,x.GP)(new Date,"yyyy-MM-dd"),a=new Date().getTime(),r=await fetch("/api/operatories?date=".concat(e,"&_t=").concat(a));if(r.ok){let e=await r.json();if(!Array.isArray(e))throw Error("Invalid response format: expected an array of operatories");let a=e.filter(e=>e&&e.id&&"DAZ1"!==e.id&&"DAZ2"!==e.id);0===a.length&&(console.warn("No operatories with appointments found for the selected date"),w("No operatories with appointments found for the selected date")),b(a)}else{let e=await r.text();throw console.warn("API call failed with status: ".concat(r.status),e),Error("Failed to fetch operatories: ".concat(r.status," - ").concat(e))}}catch(e){console.error("Error fetching operatories:",e),w(e instanceof Error?e.message:"Failed to fetch operatories")}finally{f(!1)}})()},[t,a]);let v=e=>{y(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])};return(0,r.jsxs)("div",{className:"min-h-screen bg-white dark:bg-gray-900",children:[(0,r.jsx)(o.z,{title:"Dentalapp",showBackButton:!0,onBackClick:()=>e.push("/"),backButtonLabel:"Back to Home",activeTab:"schedule"}),(0,r.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)(h,{initialDate:i,onDateChange:a=>{g(a),y(["DL01","DL02"]);let t=(0,x.GP)(a,"yyyy-MM-dd");e.replace("/operatories?date=".concat(t))}})}),(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Select Operatories"}),p?(0,r.jsx)("div",{className:"animate-pulse flex space-x-4 mb-6",children:(0,r.jsxs)("div",{className:"flex-1 space-y-4 py-1",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"})]})]})}):k?(0,r.jsxs)("div",{className:"text-red-500 dark:text-red-400 mb-6",children:["Error: ",k]}):0===u.length?(0,r.jsx)("div",{className:"text-gray-500 dark:text-gray-400 mb-6",children:"No operatories found for this date."}):(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-end mb-2",children:[(0,r.jsx)("button",{onClick:()=>{y(u.map(e=>e.id))},className:"text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded mr-2",children:"Select All"}),(0,r.jsx)("button",{onClick:()=>{y([])},className:"text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 rounded",children:"Clear All"})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:u.map(e=>(0,r.jsx)("button",{type:"button",onClick:()=>v(e.id),className:"px-3 py-2 text-sm font-medium rounded-md transition-colors ".concat(m.includes(e.id)?"bg-blue-600 text-white":"bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700"),children:e.name},e.id))})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{onClick:()=>{if(0===m.length)return void alert("Please select at least one operatory");let a=(0,x.GP)(i,"yyyy-MM-dd"),t="/schedule?date=".concat(a);m.forEach(e=>{t+="&operatories[]=".concat(encodeURIComponent(e))}),e.push(t)},disabled:p||0===u.length||0===m.length,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50",children:"Next"})})]})})]})}function m(){return(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading operatories..."}),children:(0,r.jsx)(b,{})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[8096,6496,7358],()=>a(271)),_N_E=e.O()}]);