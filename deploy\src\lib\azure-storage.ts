import { BlobServiceClient, ContainerClient, BlockBlobClient } from '@azure/storage-blob';

// Azure Storage configuration
const CONTAINER_NAME = 'recordings';

// Lazy initialization - will be set when first accessed
let blobServiceClient: BlobServiceClient | null = null;
let containerClient: ContainerClient | null = null;
let initialized = false;

function initializeAzureStorage(): void {
  if (initialized) return;

  const AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING;

  if (!AZURE_STORAGE_CONNECTION_STRING) {
    console.warn('Azure Storage connection string not configured');
    initialized = true;
    return;
  }

  try {
    blobServiceClient = BlobServiceClient.fromConnectionString(AZURE_STORAGE_CONNECTION_STRING);
    containerClient = blobServiceClient.getContainerClient(CONTAINER_NAME);
    console.log('Azure Storage client initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Azure Storage client:', error);
  }

  initialized = true;
}

export interface AzureStorageFile {
  name: string;
  size: number;
  lastModified: Date;
  url: string;
  path: string;
}

export class AzureStorageService {
  static isConfigured(): boolean {
    initializeAzureStorage();
    return !!process.env.AZURE_STORAGE_CONNECTION_STRING && !!containerClient;
  }

  // Upload a file to Azure Blob Storage
  static async uploadFile(
    filePath: string,
    fileBuffer: Buffer,
    contentType: string = 'audio/webm'
  ): Promise<string> {
    initializeAzureStorage();
    if (!containerClient) {
      throw new Error('Azure Storage not configured');
    }

    const blockBlobClient = containerClient.getBlockBlobClient(filePath);
    
    await blockBlobClient.uploadData(fileBuffer, {
      blobHTTPHeaders: {
        blobContentType: contentType,
      },
    });

    return blockBlobClient.url;
  }

  // Find file by filename across all folders
  static async findFileByName(fileName: string): Promise<string | null> {
    initializeAzureStorage();
    if (!containerClient) {
      throw new Error('Azure Storage not configured');
    }

    console.log(`🔍 Searching for file: ${fileName} across all folders...`);

    for await (const blob of containerClient.listBlobsFlat()) {
      const blobFileName = blob.name.split('/').pop();
      if (blobFileName === fileName) {
        console.log(`✅ Found file: ${fileName} at path: ${blob.name}`);
        return blob.name;
      }
    }

    console.log(`❌ File not found: ${fileName}`);
    return null;
  }

  // Download a file from Azure Blob Storage
  static async downloadFile(filePath: string): Promise<Buffer> {
    initializeAzureStorage();
    if (!containerClient) {
      throw new Error('Azure Storage not configured');
    }

    // If the filePath doesn't contain a folder path, search for it
    if (!filePath.includes('/')) {
      const actualPath = await this.findFileByName(filePath);
      if (!actualPath) {
        throw new Error(`The specified blob does not exist: ${filePath}`);
      }
      filePath = actualPath;
    }

    const blockBlobClient = containerClient.getBlockBlobClient(filePath);
    const downloadResponse = await blockBlobClient.download();
    
    if (!downloadResponse.readableStreamBody) {
      throw new Error('Failed to download file');
    }

    const chunks: Buffer[] = [];
    for await (const chunk of downloadResponse.readableStreamBody) {
      chunks.push(chunk);
    }
    
    return Buffer.concat(chunks);
  }

  // List files in a directory (recursively searches all folders)
  static async listFiles(prefix: string = ''): Promise<AzureStorageFile[]> {
    initializeAzureStorage();
    if (!containerClient) {
      throw new Error('Azure Storage not configured');
    }

    const files: AzureStorageFile[] = [];

    // Use listBlobsFlat without prefix to get ALL files in the container
    // This will recursively find files in all folders
    console.log(`Searching for files in Azure Blob Storage with prefix: "${prefix}"`);

    for await (const blob of containerClient.listBlobsFlat()) {
      // Check if the blob path starts with our prefix (if any)
      if (prefix && !blob.name.startsWith(prefix)) {
        continue;
      }

      // Only include audio and JSON files
      if (blob.name.match(/\.(webm|mp3|wav|m4a|ogg|json)$/i)) {
        const fileName = blob.name.split('/').pop() || blob.name;

        files.push({
          name: fileName,
          size: blob.properties.contentLength || 0,
          lastModified: blob.properties.lastModified || new Date(),
          url: `${containerClient.url}/${blob.name}`,
          path: blob.name
        });

        console.log(`Found audio file: ${blob.name} (${blob.properties.contentLength} bytes)`);
      }
    }

    console.log(`Total audio files found in Azure: ${files.length}`);
    return files;
  }

  // Delete a file
  static async deleteFile(filePath: string): Promise<void> {
    if (!containerClient) {
      throw new Error('Azure Storage not configured');
    }

    const blockBlobClient = containerClient.getBlockBlobClient(filePath);
    await blockBlobClient.delete();
  }

  // Check if file exists
  static async fileExists(filePath: string): Promise<boolean> {
    if (!containerClient) {
      return false;
    }

    try {
      const blockBlobClient = containerClient.getBlockBlobClient(filePath);
      await blockBlobClient.getProperties();
      return true;
    } catch (error) {
      return false;
    }
  }

  // Get file metadata including size and last modified date
  static async getFileMetadata(filePath: string): Promise<{ exists: boolean; size?: number; lastModified?: Date; etag?: string } | null> {
    if (!containerClient) {
      return { exists: false };
    }

    try {
      const blockBlobClient = containerClient.getBlockBlobClient(filePath);
      const properties = await blockBlobClient.getProperties();
      return {
        exists: true,
        size: properties.contentLength,
        lastModified: properties.lastModified,
        etag: properties.etag
      };
    } catch (error) {
      return { exists: false };
    }
  }

  // Search for duplicate files across all date folders for a specific device
  static async findDuplicateAcrossAllFolders(
    fileName: string,
    fileSize: number,
    deviceId: string = 'webusb-upload'
  ): Promise<{ exists: boolean; existingPath?: string; existingMetadata?: any }> {
    initializeAzureStorage();
    if (!containerClient) {
      return { exists: false };
    }

    try {
      console.log(`🔍 Searching for duplicates of ${fileName} (${fileSize} bytes) across all date folders...`);

      // List all items in the recordings folder
      const recordingsPrefix = 'recordings/';
      const blobs = containerClient.listBlobsFlat({ prefix: recordingsPrefix });

      for await (const blob of blobs) {
        // Check if this blob matches our criteria
        const blobPath = blob.name;
        const pathParts = blobPath.split('/');

        // Expected structure: recordings/YYYY-MM-DD/deviceId/filename
        if (pathParts.length >= 4) {
          const blobDeviceId = pathParts[2];
          const blobFileName = pathParts[pathParts.length - 1];

          // Check if filename and device match
          if (blobFileName === fileName && blobDeviceId === deviceId) {
            // Check file size
            if (blob.properties.contentLength === fileSize) {
              console.log(`🎯 Found duplicate: ${blobPath} (size: ${blob.properties.contentLength} bytes)`);
              return {
                exists: true,
                existingPath: blobPath,
                existingMetadata: {
                  size: blob.properties.contentLength,
                  lastModified: blob.properties.lastModified,
                  etag: blob.properties.etag,
                  contentType: blob.properties.contentType
                }
              };
            } else {
              console.log(`📏 Found same filename but different size: ${blobPath} (Azure: ${blob.properties.contentLength} bytes, Local: ${fileSize} bytes)`);
            }
          }
        }
      }

      console.log(`✅ No duplicates found for ${fileName}`);
      return { exists: false };
    } catch (error) {
      console.error('Error searching for duplicates:', error);
      return { exists: false };
    }
  }

  // Upload file with comprehensive duplicate handling
  static async uploadFileWithDuplicateCheck(
    filePath: string,
    fileBuffer: Buffer,
    contentType: string = 'audio/webm',
    options: {
      skipIfExists?: boolean;
      overwriteIfDifferentSize?: boolean;
      searchAllFolders?: boolean;
    } = {}
  ): Promise<{ url: string; wasSkipped: boolean; reason?: string; existingPath?: string }> {
    initializeAzureStorage();
    if (!containerClient) {
      throw new Error('Azure Storage not configured');
    }

    const blockBlobClient = containerClient.getBlockBlobClient(filePath);

    // Extract filename and device info from path for cross-folder search
    const pathParts = filePath.split('/');
    const fileName = pathParts[pathParts.length - 1];
    const deviceId = pathParts.length >= 3 ? pathParts[2] : 'webusb-upload';

    // First check if file exists at exact path (fast check)
    const exactMetadata = await this.getFileMetadata(filePath);

    if (exactMetadata?.exists) {
      console.log(`📁 File exists at exact path: ${filePath}`);
      if (options.skipIfExists) {
        return {
          url: blockBlobClient.url,
          wasSkipped: true,
          reason: 'File already exists at exact Azure path',
          existingPath: filePath
        };
      }

      if (options.overwriteIfDifferentSize && exactMetadata.size !== fileBuffer.length) {
        console.log(`📝 File exists but size differs (Azure: ${exactMetadata.size} bytes, Local: ${fileBuffer.length} bytes) - overwriting`);
        // Continue with upload
      } else if (options.overwriteIfDifferentSize && exactMetadata.size === fileBuffer.length) {
        return {
          url: blockBlobClient.url,
          wasSkipped: true,
          reason: 'File already exists with same size at exact path',
          existingPath: filePath
        };
      }
    }

    // If enabled, search across all date folders for duplicates
    if (options.searchAllFolders !== false) { // Default to true
      const duplicateSearch = await this.findDuplicateAcrossAllFolders(fileName, fileBuffer.length, deviceId);

      if (duplicateSearch.exists && duplicateSearch.existingPath) {
        console.log(`🔍 Found duplicate in different folder: ${duplicateSearch.existingPath}`);

        if (options.skipIfExists) {
          return {
            url: containerClient.getBlockBlobClient(duplicateSearch.existingPath).url,
            wasSkipped: true,
            reason: `File already exists in Azure Storage at ${duplicateSearch.existingPath}`,
            existingPath: duplicateSearch.existingPath
          };
        }
      }
    }

    // If we get here, proceed with upload
    console.log(`📤 Uploading new file to: ${filePath}`);
    await blockBlobClient.uploadData(fileBuffer, {
      blobHTTPHeaders: {
        blobContentType: contentType,
      },
    });

    return {
      url: blockBlobClient.url,
      wasSkipped: false
    };
  }

  // Get file URL with SAS token for secure access
  static async getFileUrl(filePath: string, expiresInMinutes: number = 60): Promise<string> {
    if (!containerClient) {
      throw new Error('Azure Storage not configured');
    }

    const blockBlobClient = containerClient.getBlockBlobClient(filePath);
    
    // For now, return the direct URL
    // In production, you might want to generate SAS tokens for security
    return blockBlobClient.url;
  }

  // Upload JSON metadata
  static async uploadJson(filePath: string, data: any): Promise<void> {
    initializeAzureStorage();
    if (!containerClient) {
      throw new Error('Azure Storage not configured');
    }

    const jsonString = JSON.stringify(data, null, 2);
    const jsonBuffer = Buffer.from(jsonString, 'utf8');
    
    const blockBlobClient = containerClient.getBlockBlobClient(filePath);
    
    await blockBlobClient.uploadData(jsonBuffer, {
      blobHTTPHeaders: {
        blobContentType: 'application/json',
        blobCacheControl: 'no-cache'
      },
    });

    console.log(`💾 Uploaded JSON to: ${filePath} (${jsonBuffer.length} bytes)`);
  }

  // Debug method to list all files in container (for troubleshooting)
  static async listAllFiles(): Promise<{ path: string; size: number; lastModified: Date }[]> {
    initializeAzureStorage();
    if (!containerClient) {
      throw new Error('Azure Storage not configured');
    }

    const allFiles: { path: string; size: number; lastModified: Date }[] = [];

    console.log('Listing ALL files in Azure Blob Storage container...');

    for await (const blob of containerClient.listBlobsFlat()) {
      allFiles.push({
        path: blob.name,
        size: blob.properties.contentLength || 0,
        lastModified: blob.properties.lastModified || new Date()
      });
    }

    console.log(`Total files in container: ${allFiles.length}`);
    allFiles.forEach(file => {
      console.log(`  - ${file.path} (${file.size} bytes)`);
    });

    return allFiles;
  }

  // Download JSON metadata
  static async downloadJson(filePath: string): Promise<any> {
    try {
      const buffer = await this.downloadFile(filePath);
      return JSON.parse(buffer.toString());
    } catch (error) {
      console.log(`JSON file not found: ${filePath}`);
      return {};
    }
  }
}
