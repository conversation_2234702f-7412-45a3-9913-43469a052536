#!/usr/bin/env python3
"""
Test Display Script

This script fetches and displays procedure codes and clinical notes for a specific date
in an easy-to-read format for testing purposes.
"""

import json
import requests
import sys
from datetime import datetime
from collections import defaultdict

# API configuration
API_BASE = "https://api.sikkasoft.com/v1"
API_BASE_V2 = "https://api.sikkasoft.com/v2"
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_TIMEOUT = 30  # seconds

def load_credentials():
    """Load API credentials from credentials.json file."""
    try:
        with open("credentials.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: credentials.json file not found.")
        sys.exit(1)
    except json.JSONDecodeError:
        print("Error: Invalid JSON in credentials.json file.")
        sys.exit(1)

def authenticate(app_id, app_key, office_id, secret_key):
    """Authenticate with Sikka API and get request key."""
    print("Authenticating with Sikka API...")
    
    try:
        auth_resp = requests.post(
            f"{API_BASE_V4}/request_key",
            headers={"Content-Type": "application/json"},
            json={
                "grant_type": "request_key",
                "office_id": office_id,
                "secret_key": secret_key,
                "app_id": app_id,
                "app_key": app_key
            },
            timeout=API_TIMEOUT
        )
        
        if auth_resp.status_code == 200:
            data = auth_resp.json()
            request_key = data.get("request_key")
            if request_key:
                print("Authentication successful!")
                return request_key
            else:
                print("Error: No request key in response.")
                print(data)
        else:
            print(f"Authentication failed with status code: {auth_resp.status_code}")
            print(auth_resp.text)
    except Exception as e:
        print(f"Error during authentication: {e}")
    
    sys.exit(1)

def fetch_appointments(request_key, target_date):
    """Fetch appointments for a specific date."""
    headers = {"Request-Key": request_key}
    params = {
        "startdate": target_date,
        "enddate": target_date,
        "date_filter_on": "appointment_date"
    }
    
    print(f"\nFetching appointments for {target_date}...")
    
    try:
        resp = requests.get(
            f"{API_BASE_V2}/appointments",
            headers=headers,
            params=params,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code != 200:
            print(f"Error fetching appointments: {resp.status_code}")
            print(resp.text)
            return []
        
        data = resp.json()
        
        # Extract items from v2 response
        if isinstance(data, list) and len(data) > 0:
            items = data[0].get("items", [])
            print(f"Found {len(items)} appointments")
            return items
        else:
            print("No appointments found")
            return []
    except Exception as e:
        print(f"Error: {e}")
        return []

def fetch_procedures(request_key, target_date):
    """Fetch procedure codes for a specific date."""
    headers = {"Request-Key": request_key}
    params = {"date": target_date, "transaction_type": "Procedure"}
    
    print(f"\nFetching procedure codes for {target_date}...")
    
    try:
        resp = requests.get(
            f"{API_BASE_V4}/transactions",
            headers=headers,
            params=params,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code != 200:
            print(f"Error fetching procedures: {resp.status_code}")
            print(resp.text)
            return []
        
        data = resp.json()
        items = data.get("items", [])
        
        # Filter for procedure transactions only
        procedure_items = [t for t in items if t.get("transaction_type") == "Procedure"]
        
        print(f"Found {len(procedure_items)} procedure codes")
        return procedure_items
    except Exception as e:
        print(f"Error: {e}")
        return []

def fetch_notes(request_key, target_date):
    """Fetch clinical notes for a specific date."""
    headers = {"Request-Key": request_key}
    params = {"date": target_date}
    
    print(f"\nFetching clinical notes for {target_date}...")
    
    try:
        resp = requests.get(
            f"{API_BASE_V4}/medical_notes",
            headers=headers,
            params=params,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code != 200:
            print(f"Error fetching notes: {resp.status_code}")
            print(resp.text)
            return []
        
        data = resp.json()
        items = data.get("items", [])
        
        # Filter for notes with content
        notes_with_content = []
        for item in items:
            content = None
            for field in ["note", "text", "description"]:
                if item.get(field) and item.get(field).strip():
                    content = item.get(field)
                    break
            
            if content:
                if "note" not in item or not item["note"]:
                    item["note"] = content
                notes_with_content.append(item)
        
        print(f"Found {len(notes_with_content)} notes with content")
        return notes_with_content
    except Exception as e:
        print(f"Error: {e}")
        return []

def display_patient_data(appointments, procedures, notes):
    """Display patient data organized by patient."""
    if not appointments and not procedures and not notes:
        print("\nNo data found for the specified date.")
        return
    
    # Organize data by patient ID
    patients = defaultdict(lambda: {"appointments": [], "procedures": [], "notes": []})
    
    # Add appointments
    for appt in appointments:
        patient_id = appt.get("patient_id")
        if patient_id:
            patients[patient_id]["appointments"].append(appt)
            patients[patient_id]["name"] = appt.get("patient_name", "Unknown")
    
    # Add procedures
    for proc in procedures:
        patient_id = proc.get("patient_id")
        if patient_id:
            patients[patient_id]["procedures"].append(proc)
    
    # Add notes
    for note in notes:
        patient_id = note.get("patient_id")
        if patient_id:
            patients[patient_id]["notes"].append(note)
    
    # Display data for each patient
    print("\n" + "=" * 100)
    print(f"{'PATIENT DATA SUMMARY':^100}")
    print("=" * 100)
    
    for patient_id, data in sorted(patients.items(), key=lambda x: x[1].get("name", "")):
        patient_name = data.get("name", "Unknown")
        appointments = data.get("appointments", [])
        procedures = data.get("procedures", [])
        notes = data.get("notes", [])
        
        print(f"\n{'=' * 100}")
        print(f"PATIENT: {patient_name} (ID: {patient_id})")
        print(f"{'=' * 100}")
        
        # Display appointments
        if appointments:
            print("\nAPPOINTMENTS:")
            print(f"{'TIME':<10}{'LENGTH':<10}{'OPERATORY':<15}{'PROVIDER':<15}{'DESCRIPTION':<50}")
            print("-" * 100)
            
            for appt in sorted(appointments, key=lambda a: a.get("time", "")):
                time = appt.get("time", "")
                length = appt.get("length", "")
                operatory = appt.get("operatory", "")
                provider = appt.get("provider_id", "")
                description = appt.get("description", "")
                
                # Truncate long descriptions
                if len(description) > 47:
                    description = description[:44] + "..."
                
                print(f"{time:<10}{length:<10}{operatory:<15}{provider:<15}{description:<50}")
        else:
            print("\nNo appointments found for this patient.")
        
        # Display procedures
        if procedures:
            print("\nPROCEDURES:")
            print(f"{'CODE':<10}{'DESCRIPTION':<40}{'AMOUNT':<10}{'TOOTH':<10}{'SURFACE':<10}")
            print("-" * 80)
            
            for proc in procedures:
                code = proc.get("procedure_code", "")
                desc = proc.get("procedure_description", "")
                amount = float(proc.get("amount", "0"))
                tooth = proc.get("tooth_from", "") or "-"
                surface = proc.get("surface", "") or "-"
                
                # Truncate long descriptions
                if len(desc) > 37:
                    desc = desc[:34] + "..."
                
                print(f"{code:<10}{desc:<40}${amount:<9.2f}{tooth:<10}{surface:<10}")
        else:
            print("\nNo procedures found for this patient.")
        
        # Display notes
        if notes:
            print("\nCLINICAL NOTES:")
            
            for i, note in enumerate(notes):
                note_date = note.get("date", "")
                note_text = note.get("note", "")
                note_type = note.get("type", "")
                provider_id = note.get("provider_id", "")
                
                print(f"\nNote {i+1} (Date: {note_date}, Type: {note_type}, Provider: {provider_id}):")
                
                # Format and truncate long notes
                if len(note_text) > 300:
                    print(f"{note_text[:300]}...\n[Note truncated, total length: {len(note_text)} characters]")
                else:
                    print(note_text)
        else:
            print("\nNo clinical notes found for this patient.")

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python test_display.py YYYY-MM-DD")
        sys.exit(1)
    
    target_date = sys.argv[1]
    
    # Validate date format
    try:
        datetime.strptime(target_date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        sys.exit(1)
    
    # Load credentials
    credentials = load_credentials()
    app_id = credentials.get("app_id")
    app_key = credentials.get("app_key")
    office_id = credentials.get("office_id")
    secret_key = credentials.get("secret_key")
    
    # Authenticate and get request key
    request_key = authenticate(app_id, app_key, office_id, secret_key)
    
    # Fetch data
    appointments = fetch_appointments(request_key, target_date)
    procedures = fetch_procedures(request_key, target_date)
    notes = fetch_notes(request_key, target_date)
    
    # Display the results
    display_patient_data(appointments, procedures, notes)

if __name__ == "__main__":
    main()
