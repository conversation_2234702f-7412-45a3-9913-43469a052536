// Global type declarations

declare global {
  namespace NodeJS {
    interface Global {
      transcriptionState: {
        isRunning: boolean;
        currentFile: string | null;
        completedFiles: number;
        totalFiles: number;
        errors: string[];
        results: Record<string, {
          status: 'pending' | 'processing' | 'completed' | 'failed';
          transcription?: string;
          summary?: string;
          error?: string;
          duration?: number;
        }>;
      };
    }
  }

  var transcriptionState: {
    isRunning: boolean;
    currentFile: string | null;
    completedFiles: number;
    totalFiles: number;
    errors: string[];
    results: Record<string, {
      status: 'pending' | 'processing' | 'completed' | 'failed';
      transcription?: string;
      summary?: string;
      error?: string;
      duration?: number;
    }>;
  };
}

export {};