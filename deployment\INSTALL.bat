@echo off
echo ========================================
echo    Dental App Installation Script
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click INSTALL.bat and select "Run as administrator"
    pause
    exit /b 1
)

echo [1/8] Checking installation directory...
if not exist "C:\DentalApp" (
    echo Creating C:\DentalApp directory...
    mkdir "C:\DentalApp"
)

echo [2/8] Copying application files...
if exist "app" (
    echo Copying app files...
    xcopy /E /I /Y "app" "C:\DentalApp\app\"
) else (
    echo ERROR: App folder not found in current directory
    pause
    exit /b 1
)

echo [3/8] Copying scripts...
if exist "scripts" (
    xcopy /E /I /Y "scripts" "C:\DentalApp\scripts\"
) else (
    echo ERROR: Scripts folder not found
    pause
    exit /b 1
)

echo [4/8] Copying configuration...
if exist "config" (
    xcopy /E /I /Y "config" "C:\DentalApp\config\"
) else (
    echo ERROR: Config folder not found
    pause
    exit /b 1
)

echo [5/8] Creating data directories...
if not exist "C:\DentalApp\data" mkdir "C:\DentalApp\data"
if not exist "C:\DentalApp\data\voice-recordings" mkdir "C:\DentalApp\data\voice-recordings"
if not exist "C:\DentalApp\logs" mkdir "C:\DentalApp\logs"
if not exist "C:\DentalApp\backups" mkdir "C:\DentalApp\backups"

echo [6/8] Checking Node.js installation...
node --version >nul 2>&1
if %errorLevel% neq 0 (
    echo Node.js not found. Installing...
    if exist "tools\node-installer.msi" (
        echo Running Node.js installer...
        msiexec /i "tools\node-installer.msi" /quiet /norestart
        echo Waiting for Node.js installation to complete...
        timeout /t 30 /nobreak
    ) else (
        echo ERROR: Node.js installer not found in tools folder
        echo Please download Node.js from https://nodejs.org and install manually
        pause
        exit /b 1
    )
) else (
    echo Node.js already installed
)

echo [7/8] Installing PM2 and dependencies...
cd /d "C:\DentalApp\app"
call npm install --production
call npm install -g pm2
call npm install -g pm2-windows-service

echo [8/8] Setting up Windows service...
cd /d "C:\DentalApp"
call pm2-service-install -n "DentalApp"
call pm2 start config\ecosystem.config.js
call pm2 save
call pm2 startup

echo.
echo ========================================
echo    Installation Complete!
echo ========================================
echo.
echo The Dental App is now running as a Windows service.
echo.
echo Access the app at: http://localhost:3000
echo.
echo Management scripts are available in C:\DentalApp\scripts\
echo.
echo - start-service.bat    : Start the service
echo - stop-service.bat     : Stop the service  
echo - restart-service.bat  : Restart the service
echo - status.bat          : Check service status
echo - logs.bat            : View application logs
echo - backup.bat          : Backup application data
echo - update-remote.bat   : Update from GitHub
echo - update-usb.bat      : Update from USB
echo.
echo Installation log saved to: C:\DentalApp\logs\install.log
echo.

:: Create desktop shortcut
echo Creating desktop shortcut...
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%temp%\shortcut.vbs"
echo sLinkFile = "%USERPROFILE%\Desktop\Dental App.lnk" >> "%temp%\shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%temp%\shortcut.vbs"
echo oLink.TargetPath = "http://localhost:3000" >> "%temp%\shortcut.vbs"
echo oLink.IconLocation = "C:\DentalApp\app\public\favicon.ico" >> "%temp%\shortcut.vbs"
echo oLink.Save >> "%temp%\shortcut.vbs"
cscript /nologo "%temp%\shortcut.vbs"
del "%temp%\shortcut.vbs"

echo Desktop shortcut created: "Dental App"
echo.
echo Press any key to open the application...
pause >nul

:: Open the application in default browser
start http://localhost:3000

echo Installation completed successfully!
pause
