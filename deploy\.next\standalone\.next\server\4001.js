exports.id=4001,exports.ids=[4001,5772],exports.modules={5772:(e,t,r)=>{"use strict";r.d(t,{AzureStorageService:()=>d});var i=r(77583);let a=null,o=null,s=!1;function n(){if(s)return;let e=process.env.AZURE_STORAGE_CONNECTION_STRING;if(!e){console.warn("Azure Storage connection string not configured"),s=!0;return}try{o=i.BlobServiceClient.fromConnectionString(e).getContainerClient("recordings")}catch(e){console.error("Failed to initialize Azure Storage client:",e)}s=!0}class d{static isConfigured(){return n(),!!process.env.AZURE_STORAGE_CONNECTION_STRING&&!!o}static async uploadFile(e,t,r="audio/webm"){if(n(),!o)throw Error("Azure Storage not configured");let i=o.getBlockBlobClient(e);return await i.uploadData(t,{blobHTTPHeaders:{blobContentType:r}}),i.url}static async findFileByName(e){if(n(),!o)throw Error("Azure Storage not configured");for await(let t of o.listBlobsFlat())if(t.name.split("/").pop()===e)return t.name;return null}static async downloadFile(e){if(n(),!o)throw Error("Azure Storage not configured");if(!e.includes("/")){let t=await this.findFileByName(e);if(!t)throw Error(`The specified blob does not exist: ${e}`);e=t}let t=o.getBlockBlobClient(e),r=await t.download();if(!r.readableStreamBody)throw Error("Failed to download file");let i=[];for await(let e of r.readableStreamBody)i.push(e);return Buffer.concat(i)}static async listFiles(e=""){if(n(),!o)throw Error("Azure Storage not configured");let t=[];for await(let r of o.listBlobsFlat())if((!e||r.name.startsWith(e))&&r.name.match(/\.(webm|mp3|wav|m4a|ogg|json)$/i)){let e=r.name.split("/").pop()||r.name;t.push({name:e,size:r.properties.contentLength||0,lastModified:r.properties.lastModified||new Date,url:`${o.url}/${r.name}`,path:r.name})}return t}static async deleteFile(e){if(!o)throw Error("Azure Storage not configured");let t=o.getBlockBlobClient(e);await t.delete()}static async fileExists(e){if(!o)return!1;try{let t=o.getBlockBlobClient(e);return await t.getProperties(),!0}catch(e){return!1}}static async getFileMetadata(e){if(!o)return{exists:!1};try{let t=o.getBlockBlobClient(e),r=await t.getProperties();return{exists:!0,size:r.contentLength,lastModified:r.lastModified,etag:r.etag}}catch(e){return{exists:!1}}}static async findDuplicateAcrossAllFolders(e,t,r="webusb-upload"){if(n(),!o)return{exists:!1};try{for await(let i of o.listBlobsFlat({prefix:"recordings/"})){let a=i.name,o=a.split("/");if(o.length>=4){let s=o[2];if(o[o.length-1]===e&&s===r&&i.properties.contentLength===t)return{exists:!0,existingPath:a,existingMetadata:{size:i.properties.contentLength,lastModified:i.properties.lastModified,etag:i.properties.etag,contentType:i.properties.contentType}}}}return{exists:!1}}catch(e){return console.error("Error searching for duplicates:",e),{exists:!1}}}static async uploadFileWithDuplicateCheck(e,t,r="audio/webm",i={}){if(n(),!o)throw Error("Azure Storage not configured");let a=o.getBlockBlobClient(e),s=e.split("/"),d=s[s.length-1],l=s.length>=3?s[2]:"webusb-upload",c=await this.getFileMetadata(e);if(c?.exists){if(i.skipIfExists)return{url:a.url,wasSkipped:!0,reason:"File already exists at exact Azure path",existingPath:e};if(i.overwriteIfDifferentSize&&c.size!==t.length);else if(i.overwriteIfDifferentSize&&c.size===t.length)return{url:a.url,wasSkipped:!0,reason:"File already exists with same size at exact path",existingPath:e}}if(!1!==i.searchAllFolders){let e=await this.findDuplicateAcrossAllFolders(d,t.length,l);if(e.exists&&e.existingPath&&i.skipIfExists)return{url:o.getBlockBlobClient(e.existingPath).url,wasSkipped:!0,reason:`File already exists in Azure Storage at ${e.existingPath}`,existingPath:e.existingPath}}return await a.uploadData(t,{blobHTTPHeaders:{blobContentType:r}}),{url:a.url,wasSkipped:!1}}static async getFileUrl(e,t=60){if(!o)throw Error("Azure Storage not configured");return o.getBlockBlobClient(e).url}static async uploadJson(e,t){if(n(),!o)throw Error("Azure Storage not configured");let r=JSON.stringify(t,null,2),i=Buffer.from(r,"utf8"),a=o.getBlockBlobClient(e);await a.uploadData(i,{blobHTTPHeaders:{blobContentType:"application/json",blobCacheControl:"no-cache"}})}static async listAllFiles(){if(n(),!o)throw Error("Azure Storage not configured");let e=[];for await(let t of o.listBlobsFlat())e.push({path:t.name,size:t.properties.contentLength||0,lastModified:t.properties.lastModified||new Date});return e.forEach(e=>{}),e}static async downloadJson(e){try{let t=await this.downloadFile(e);return JSON.parse(t.toString())}catch(e){return{}}}}},78335:()=>{},86976:(e,t,r)=>{"use strict";r.d(t,{M:()=>c});var i=r(87550),a=r.n(i),o=r(33873),s=r.n(o),n=r(29021),d=r.n(n);class l{constructor(){let e=s().join(process.cwd(),"data");d().existsSync(e)||d().mkdirSync(e,{recursive:!0}),this.dbPath=s().join(e,"voice-recordings.db"),this.db=new(a())(this.dbPath),this.initializeDatabase()}initializeDatabase(){this.db.exec(`
      CREATE TABLE IF NOT EXISTS voice_recordings (
        id TEXT PRIMARY KEY,
        filename TEXT NOT NULL,
        device_id TEXT,
        file_path TEXT,
        file_size INTEGER,
        imported_at TEXT,
        recording_date TEXT,
        status TEXT DEFAULT 'pending',
        transcription TEXT,
        transcribed_at TEXT,
        clinical_summary TEXT,
        category TEXT DEFAULT 'clinical',
        posted_to_dentrix BOOLEAN DEFAULT FALSE,
        isUploaded BOOLEAN DEFAULT TRUE,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );

      CREATE INDEX IF NOT EXISTS idx_filename ON voice_recordings(filename);
      CREATE INDEX IF NOT EXISTS idx_status ON voice_recordings(status);
      CREATE INDEX IF NOT EXISTS idx_transcribed_at ON voice_recordings(transcribed_at);
      CREATE INDEX IF NOT EXISTS idx_created_at ON voice_recordings(created_at);
    `)}createRecording(e){let t=new Date().toISOString(),r={...e,created_at:t,updated_at:t};return this.db.prepare(`
      INSERT INTO voice_recordings (
        id, filename, device_id, file_path, file_size, imported_at, recording_date,
        status, transcription, transcribed_at, clinical_summary, category,
        posted_to_dentrix, isUploaded, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(r.id,r.filename,r.device_id,r.file_path,r.file_size,r.imported_at,r.recording_date,r.status,r.transcription,r.transcribed_at,r.clinical_summary,r.category,+!!r.posted_to_dentrix,+!!r.isUploaded,r.created_at,r.updated_at),r}getRecording(e){let t=this.db.prepare("SELECT * FROM voice_recordings WHERE id = ?").get(e);return t?{...t,posted_to_dentrix:!!t.posted_to_dentrix,isUploaded:!!t.isUploaded}:null}getRecordingByFilename(e){let t=this.db.prepare("SELECT * FROM voice_recordings WHERE filename = ?").get(e);return t?{...t,posted_to_dentrix:!!t.posted_to_dentrix,isUploaded:!!t.isUploaded}:null}updateRecording(e,t){let r=new Date().toISOString(),i=[],a=[];if(Object.entries(t).forEach(([e,t])=>{"id"!==e&&"created_at"!==e&&(i.push(`${e} = ?`),a.push(t))}),0===i.length)return!1;i.push("updated_at = ?"),a.push(r),a.push(e);let o=this.db.prepare(`
      UPDATE voice_recordings 
      SET ${i.join(", ")} 
      WHERE id = ?
    `).run(...a).changes>0;return o||console.warn(`⚠️ No recording found to update: ${e}`),o}getAllRecordings(){return this.db.prepare("SELECT * FROM voice_recordings ORDER BY created_at DESC").all().map(e=>({...e,posted_to_dentrix:!!e.posted_to_dentrix,isUploaded:!!e.isUploaded}))}getRecordingsByStatus(e){return this.db.prepare("SELECT * FROM voice_recordings WHERE status = ? ORDER BY created_at DESC").all(e).map(e=>({...e,posted_to_dentrix:!!e.posted_to_dentrix,isUploaded:!!e.isUploaded}))}getRecordingsNeedingTranscription(){return this.db.prepare(`
      SELECT * FROM voice_recordings 
      WHERE status IN ('pending', 'error') 
      OR (status = 'transcribed' AND transcription IS NULL)
      ORDER BY created_at ASC
    `).all().map(e=>({...e,posted_to_dentrix:!!e.posted_to_dentrix,isUploaded:!!e.isUploaded}))}getRecordingsNeedingSummarization(){return this.db.prepare(`
      SELECT * FROM voice_recordings 
      WHERE status = 'transcribed' 
      AND transcription IS NOT NULL 
      AND (clinical_summary IS NULL OR clinical_summary = '')
      ORDER BY transcribed_at ASC
    `).all().map(e=>({...e,posted_to_dentrix:!!e.posted_to_dentrix,isUploaded:!!e.isUploaded}))}deleteRecording(e){return this.db.prepare("DELETE FROM voice_recordings WHERE id = ?").run(e).changes>0}getStats(){let e={total:this.db.prepare("SELECT COUNT(*) as count FROM voice_recordings").get(),pending:this.db.prepare('SELECT COUNT(*) as count FROM voice_recordings WHERE status = "pending"').get(),transcribed:this.db.prepare('SELECT COUNT(*) as count FROM voice_recordings WHERE status = "transcribed"').get(),summarized:this.db.prepare('SELECT COUNT(*) as count FROM voice_recordings WHERE status = "summarized"').get(),error:this.db.prepare('SELECT COUNT(*) as count FROM voice_recordings WHERE status = "error"').get()};return{total:e.total.count,pending:e.pending.count,transcribed:e.transcribed.count,summarized:e.summarized.count,error:e.error.count}}close(){this.db.close()}backup(e){let t=new(a())(e);this.db.backup(t),t.close()}}let c=new l},96487:()=>{}};