# Azure Functions Deployment Guide

This guide walks you through deploying the automatic voice processing Azure Functions to handle continuous background processing.

## Overview

The Azure Functions provide:
1. **Blob Trigger** - Automatically processes new audio files as they're uploaded
2. **Scheduled Processor** - Runs every 10 minutes to catch missed files and update statistics
3. **Real-time Dashboard** - Updates processing statistics for the UI

## Prerequisites

- Azure subscription
- Azure CLI installed
- Azure Storage Account (same one used by your app)
- OpenAI API key

## Deployment Steps

### 1. Create Function App

```bash
# Set variables
RESOURCE_GROUP="your-resource-group"
STORAGE_ACCOUNT="your-storage-account"
FUNCTION_APP_NAME="dentalapp-voice-processor"
REGION="East US"

# Create Function App
az functionapp create \
  --resource-group $RESOURCE_GROUP \
  --consumption-plan-location $REGION \
  --runtime node \
  --runtime-version 18 \
  --functions-version 4 \
  --name $FUNCTION_APP_NAME \
  --storage-account $STORAGE_ACCOUNT
```

### 2. Configure Application Settings

```bash
# Set required environment variables
az functionapp config appsettings set \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --settings \
    "AZURE_STORAGE_CONNECTION_STRING=your-connection-string" \
    "OPENAI_API_KEY=your-openai-key" \
    "TRANSCRIPTION_API_URL=https://your-app.vercel.app/api/voice/transcribe" \
    "SUMMARY_API_URL=https://your-app.vercel.app/api/voice/summarize"
```

### 3. Deploy Functions

```bash
# Navigate to azure-functions directory
cd azure-functions

# Install dependencies
npm install

# Build TypeScript
npm run build

# Deploy to Azure
func azure functionapp publish $FUNCTION_APP_NAME
```

### 4. Verify Deployment

```bash
# Check function status
az functionapp function list \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --output table

# View logs
func azure functionapp logstream $FUNCTION_APP_NAME
```

## Configuration Details

### Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `AZURE_STORAGE_CONNECTION_STRING` | Connection string for Azure Storage | `DefaultEndpointsProtocol=https;AccountName=...` |
| `OPENAI_API_KEY` | OpenAI API key for transcription | `sk-...` |
| `TRANSCRIPTION_API_URL` | URL to your transcription API | `https://yourapp.vercel.app/api/voice/transcribe` |
| `SUMMARY_API_URL` | URL to your summary API | `https://yourapp.vercel.app/api/voice/summarize` |

### Function Triggers

#### Blob Trigger
- **Path**: `recordings/{name}`
- **Trigger**: New audio files uploaded to storage
- **Action**: Validates and processes audio files automatically

#### Scheduled Processor
- **Schedule**: `0 */10 * * * *` (every 10 minutes)
- **Action**: Scans for missed files, updates statistics, handles retries

## Testing

### 1. Test Blob Trigger

Upload an audio file to your storage container:

```bash
az storage blob upload \
  --account-name $STORAGE_ACCOUNT \
  --container-name recordings \
  --name test-audio.mp3 \
  --file ./test-audio.mp3
```

### 2. Monitor Processing

```bash
# View function logs
func azure functionapp logstream $FUNCTION_APP_NAME

# Check processing statistics
curl https://your-app.vercel.app/api/voice/processing-statistics
```

## Benefits

### Automatic Processing
- No need to manually start/stop processing
- Files are processed as soon as they're uploaded
- Background processing continues 24/7

### Improved Reliability
- Built-in retry mechanisms
- Error handling and logging
- Permanent failure detection for corrupted files

### Real-time Updates
- Live progress dashboard in UI
- Processing statistics updated every 10 minutes
- Clear status indicators for each file

### Cost Efficiency
- Only processes when needed
- Automatic scaling based on workload
- Pay-per-execution model

## Monitoring

### Azure Portal
1. Go to your Function App in Azure Portal
2. Navigate to "Functions" to see all deployed functions
3. Click on each function to view:
   - Execution count
   - Success/failure rates
   - Performance metrics
   - Logs

### Application Insights
- Automatic logging and telemetry
- Performance monitoring
- Error tracking and alerts

### UI Dashboard
- Real-time processing progress
- File status breakdown
- Processing rate metrics
- Estimated completion times

## Troubleshooting

### Common Issues

1. **Functions not triggering**
   - Check storage connection string
   - Verify container name is correct
   - Ensure function app has storage permissions

2. **Transcription failures**
   - Verify OpenAI API key is valid
   - Check API URL configuration
   - Review function logs for specific errors

3. **Permission errors**
   - Ensure function app identity has storage permissions
   - Check Azure RBAC settings
   - Verify connection string has correct access rights

### Logs and Debugging

```bash
# Stream live logs
func azure functionapp logstream $FUNCTION_APP_NAME

# Download logs
az functionapp log download \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP

# View specific function logs
az functionapp logs show \
  --name $FUNCTION_APP_NAME \
  --resource-group $RESOURCE_GROUP
```

## Scaling and Performance

### Default Configuration
- Consumption plan (automatic scaling)
- 10-minute timeout per function execution
- Maximum 2 parallel blob processors
- Exponential backoff retry strategy

### Optimization Tips
1. **For high volume**: Consider Premium plan for consistent performance
2. **For large files**: Increase function timeout if needed
3. **For many files**: Adjust `maxDegreeOfParallelism` in host.json
4. **For reliability**: Monitor retry counts and adjust strategy

## Security

### Best Practices
- Use Azure Key Vault for sensitive settings
- Enable managed identity for Azure services
- Restrict network access if needed
- Monitor function access logs

### Access Control
- Function app uses system-assigned managed identity
- Storage permissions granted through RBAC
- API calls use internal authentication headers

## Next Steps

After deployment:
1. Upload test files to verify processing
2. Monitor the real-time dashboard in your app
3. Set up alerts for failed processing
4. Consider backup and disaster recovery plans
5. Review and optimize based on usage patterns