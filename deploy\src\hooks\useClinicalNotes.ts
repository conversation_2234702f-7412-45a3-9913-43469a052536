import { useState, useEffect, useCallback } from 'react';

export interface ClinicalNote {
  id: string;
  date: string; // Date of the note
  provider: string;
  text: string; // Full note text
  type: string; // Note type (e.g., "Clinical Note")
  appointment_id?: string;
  appointment_date?: string; // Date of visit/appointment
  tooth_number?: string;
  surface?: string;
  procedures: string[]; // Procedure codes completed on visit date
  appointmentType?: string;
  noteLength?: number;
  wordCount?: number;
  preview?: string;
  relevanceScore?: number;
  daysFromAppointment?: number;
  rawData?: any; // Keep raw data for debugging
  // Legacy field for backward compatibility
  notes?: string;
}

export interface ClinicalNotesResponse {
  patientId: string;
  notes: ClinicalNote[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  debug?: any;
  summary?: {
    totalNotes: number;
    notesReturned: number;
    searchStrategy: string;
    hasAppointmentContext: boolean;
    dateRange: string;
  };
}

export interface UseClinicalNotesOptions {
  patientId?: string;
  appointmentId?: string;
  appointmentDate?: string;
  dateRange?: number; // days before/after appointment date
  limit?: number;
  page?: number;
  autoFetch?: boolean; // whether to fetch automatically when patientId changes
}

export interface UseClinicalNotesReturn {
  notes: ClinicalNote[];
  loading: boolean;
  error: string | null;
  pagination: ClinicalNotesResponse['pagination'] | null;
  summary: ClinicalNotesResponse['summary'] | null;
  debug: any;
  fetchNotes: () => Promise<void>;
  refetch: () => Promise<void>;
  nextPage: () => Promise<void>;
  prevPage: () => Promise<void>;
  hasNotes: boolean;
  isEmpty: boolean;
}

export function useClinicalNotes(options: UseClinicalNotesOptions = {}): UseClinicalNotesReturn {
  const {
    patientId,
    appointmentId,
    appointmentDate,
    dateRange = 7,
    limit = 20,
    page = 1,
    autoFetch = true
  } = options;

  const [notes, setNotes] = useState<ClinicalNote[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<ClinicalNotesResponse['pagination'] | null>(null);
  const [summary, setSummary] = useState<ClinicalNotesResponse['summary'] | null>(null);
  const [debug, setDebug] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(page);

  const fetchNotes = useCallback(async () => {
    if (!patientId) {
      setNotes([]);
      setPagination(null);
      setSummary(null);
      setDebug(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams({
        limit: limit.toString(),
        page: currentPage.toString()
      });

      if (appointmentId) {
        params.append('appointment_id', appointmentId);
      }

      if (appointmentDate) {
        params.append('appointment_date', appointmentDate);
        params.append('date_range', dateRange.toString());
      }

      console.log(`Fetching clinical notes for patient ${patientId}`, {
        appointmentId,
        appointmentDate,
        dateRange,
        limit,
        page: currentPage
      });

      const response = await fetch(`/api/patients/${patientId}/clinical-notes?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data: ClinicalNotesResponse = await response.json();
      
      setNotes(data.notes || []);
      setPagination(data.pagination);
      setSummary(data.summary);
      setDebug(data.debug);

      console.log(`Loaded ${data.notes?.length || 0} clinical notes for patient ${patientId}`);

    } catch (err: any) {
      console.error('Error fetching clinical notes:', err);
      setError(err.message || 'Failed to fetch clinical notes');
      setNotes([]);
      setPagination(null);
      setSummary(null);
    } finally {
      setLoading(false);
    }
  }, [patientId, appointmentId, appointmentDate, dateRange, limit, currentPage]);

  const refetch = useCallback(async () => {
    setCurrentPage(1); // Reset to first page
    await fetchNotes();
  }, [fetchNotes]);

  const nextPage = useCallback(async () => {
    if (pagination?.hasNext) {
      setCurrentPage(prev => prev + 1);
    }
  }, [pagination?.hasNext]);

  const prevPage = useCallback(async () => {
    if (pagination?.hasPrev) {
      setCurrentPage(prev => prev - 1);
    }
  }, [pagination?.hasPrev]);

  // Auto-fetch when dependencies change
  useEffect(() => {
    if (autoFetch && patientId) {
      fetchNotes();
    }
  }, [autoFetch, patientId, appointmentId, appointmentDate, dateRange, limit, currentPage, fetchNotes]);

  // Update current page when page prop changes
  useEffect(() => {
    setCurrentPage(page);
  }, [page]);

  const hasNotes = notes.length > 0;
  const isEmpty = !loading && !hasNotes && !error;

  return {
    notes,
    loading,
    error,
    pagination,
    summary,
    debug,
    fetchNotes,
    refetch,
    nextPage,
    prevPage,
    hasNotes,
    isEmpty
  };
}

// Utility function to format clinical notes for display
export function formatClinicalNote(note: ClinicalNote): {
  formattedDate: string;
  shortDate: string;
  providerInitials: string;
  procedureList: string;
  notePreview: string;
  isRecent: boolean;
} {
  const noteDate = new Date(note.date);
  const now = new Date();
  const daysDiff = Math.floor((now.getTime() - noteDate.getTime()) / (1000 * 60 * 60 * 24));

  return {
    formattedDate: noteDate.toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }),
    shortDate: noteDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    }),
    providerInitials: note.provider
      .split(' ')
      .map(name => name.charAt(0))
      .join('')
      .toUpperCase(),
    procedureList: note.procedures.length > 0 ? note.procedures.join(', ') : 'No procedures',
    notePreview: note.preview || (note.text || note.notes || '').substring(0, 200) + ((note.text || note.notes || '').length > 200 ? '...' : ''),
    isRecent: daysDiff <= 30 // Consider notes from last 30 days as recent
  };
}

// Utility function to group notes by date
export function groupNotesByDate(notes: ClinicalNote[]): Record<string, ClinicalNote[]> {
  return notes.reduce((groups, note) => {
    const date = note.date;
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(note);
    return groups;
  }, {} as Record<string, ClinicalNote[]>);
}

// Utility function to find notes relevant to an appointment
export function findRelevantNotes(
  notes: ClinicalNote[],
  appointmentDate: string,
  maxDays: number = 7
): ClinicalNote[] {
  const targetDate = new Date(appointmentDate);
  
  return notes
    .filter(note => {
      const noteDate = new Date(note.date);
      const daysDiff = Math.abs(noteDate.getTime() - targetDate.getTime()) / (1000 * 60 * 60 * 24);
      return daysDiff <= maxDays;
    })
    .sort((a, b) => {
      // Sort by proximity to appointment date
      const dateA = new Date(a.date);
      const dateB = new Date(b.date);
      const diffA = Math.abs(dateA.getTime() - targetDate.getTime());
      const diffB = Math.abs(dateB.getTime() - targetDate.getTime());
      return diffA - diffB;
    });
}
