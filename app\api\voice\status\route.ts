/**
 * REAL-TIME TRANSCRIPTION STATUS API
 * 
 * This endpoint provides live status updates for transcription jobs
 * so you can see what's happening without checking console logs
 * 
 * Usage:
 * GET /api/voice/status - Shows all active jobs and recent activity
 * GET /api/voice/status?jobId=xyz - Shows specific job status
 */

import { NextRequest, NextResponse } from 'next/server';

// In a real system, this would connect to your job storage
// For now, we'll create a simple status tracking system

interface JobStatus {
  id: string;
  type: 'transcribe-all' | 'auto-process' | 'batch-process';
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt: string;
  completedAt?: string;
  progress?: number;
  currentFile?: string;
  totalFiles?: number;
  processedFiles?: number;
  failedFiles?: number;
  errors?: string[];
  lastUpdate?: string;
}

// Simple in-memory storage for demonstration
// In production, this would be in Redis or a database
const jobStatuses: Map<string, JobStatus> = new Map();

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const jobId = searchParams.get('jobId');

  try {
    if (jobId) {
      // Get specific job status
      const job = jobStatuses.get(jobId);
      if (!job) {
        return NextResponse.json({
          error: `Job ${jobId} not found`,
          availableJobs: Array.from(jobStatuses.keys())
        }, { status: 404 });
      }

      return NextResponse.json({
        job,
        timestamp: new Date().toISOString()
      });
    }

    // Get all job statuses
    const allJobs = Array.from(jobStatuses.values());
    const activeJobs = allJobs.filter(j => j.status === 'running' || j.status === 'pending');
    const recentJobs = allJobs
      .sort((a, b) => new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime())
      .slice(0, 10);

    return NextResponse.json({
      summary: {
        totalJobs: allJobs.length,
        activeJobs: activeJobs.length,
        completedJobs: allJobs.filter(j => j.status === 'completed').length,
        failedJobs: allJobs.filter(j => j.status === 'failed').length
      },
      activeJobs,
      recentJobs,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, jobId, update } = await request.json();

    if (action === 'updateJob' && jobId && update) {
      // Update job status
      const existingJob = jobStatuses.get(jobId);
      if (existingJob) {
        const updatedJob = {
          ...existingJob,
          ...update,
          lastUpdate: new Date().toISOString()
        };
        jobStatuses.set(jobId, updatedJob);
        
        return NextResponse.json({
          success: true,
          job: updatedJob
        });
      } else {
        return NextResponse.json({
          error: `Job ${jobId} not found`
        }, { status: 404 });
      }
    }

    if (action === 'createJob' && jobId) {
      // Create new job
      const newJob: JobStatus = {
        id: jobId,
        type: update.type || 'transcribe-all',
        status: 'pending',
        startedAt: new Date().toISOString(),
        ...update
      };
      
      jobStatuses.set(jobId, newJob);
      
      return NextResponse.json({
        success: true,
        job: newJob
      });
    }

    return NextResponse.json({
      error: 'Invalid action'
    }, { status: 400 });

  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Helper function that other endpoints can use to update job status
export async function updateJobStatus(jobId: string, update: Partial<JobStatus>) {
  const existingJob = jobStatuses.get(jobId);
  if (existingJob) {
    const updatedJob = {
      ...existingJob,
      ...update,
      lastUpdate: new Date().toISOString()
    };
    jobStatuses.set(jobId, updatedJob);
    console.log(`📊 STATUS UPDATE: Job ${jobId} - ${JSON.stringify(update)}`);
  }
}