"use client";

import { useState, useRef, useEffect } from 'react';
import { Search, User, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import type { Patient } from '@/types/patient';

interface GlobalSearchProps {
  isDarkMode: boolean;
}

export function GlobalSearch({ isDarkMode }: GlobalSearchProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Patient[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fix hydration mismatch by only applying dark mode styles after mount
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  // Close search when clicking outside and cleanup timeout
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      // Cleanup timeout on unmount
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);
  
  // Debounced search function
  const performSearch = async (query: string) => {
    if (query.trim().length < 2) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);

    try {
      const response = await fetch(`/api/patients/search?q=${encodeURIComponent(query)}`);

      if (!response.ok) {
        throw new Error('Failed to search patients');
      }

      const data = await response.json();
      setSearchResults(data.patients || []);
    } catch (err) {
      console.error('Search error:', err);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle search input with debouncing
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Clear results immediately if search term is too short
    if (value.trim().length < 2) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    // Set loading state immediately for better UX
    setIsSearching(true);

    // Debounce the actual search by 300ms
    searchTimeoutRef.current = setTimeout(() => {
      performSearch(value);
    }, 300);
  };
  
  // Handle patient selection
  const handlePatientSelect = (patient: Patient) => {
    // Navigate directly to patient page
    router.push(`/patient/${patient.id}`);
    setIsOpen(false);
    setSearchTerm('');
    setSearchResults([]);
  };
  
  // Format patient name as "First LAST"
  const formatPatientName = (patient: Patient) => {
    // Format as "First LAST" - first name proper case, last name uppercase
    const firstName = patient.firstName?.charAt(0).toUpperCase() + patient.firstName?.slice(1).toLowerCase();
    const lastName = patient.lastName?.toUpperCase();

    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    } else if (firstName) {
      return firstName;
    } else if (lastName) {
      return lastName;
    }

    // Fallback to joining all parts if structured data isn't available
    const parts = [patient.firstName, patient.middleName, patient.lastName].filter(Boolean);
    return parts.join(' ');
  };
  
  return (
    <div ref={searchRef} className="relative">
      {/* Search button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center px-2 sm:px-3 py-2 rounded-md transition-colors ${
          !isMounted
            ? 'bg-gray-100 hover:bg-gray-200 text-gray-700' // Safe default for SSR
            : isOpen
              ? 'bg-blue-600 hover:bg-blue-700 text-white'
              : isDarkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-gray-200'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
        }`}
        title="Search patients"
      >
        <Search className="h-4 w-4 sm:mr-2" />
        <span className="hidden sm:inline">Patient Search</span>
        <span className="sm:hidden ml-1 text-xs">Search</span>
      </button>
      
      {/* Search popup */}
      {isOpen && isMounted && (
        <div className={`absolute right-0 mt-2 w-72 sm:w-80 md:w-96 rounded-md shadow-lg z-50 ${
          isDarkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
        } max-w-[calc(100vw-2rem)]`}>
          <div className="p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                Patient Search
              </h3>
              <button 
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                value={searchTerm}
                onChange={handleSearchChange}
                placeholder="Search patients..."
                autoFocus
                className={`w-full pl-10 pr-4 py-2 border rounded-md ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>
            
            {isSearching ? (
              <div className="py-4 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-400 mx-auto"></div>
              </div>
            ) : searchResults.length > 0 ? (
              <div className="max-h-80 overflow-y-auto">
                {searchResults.map((patient) => (
                  <div
                    key={patient.id}
                    onClick={() => handlePatientSelect(patient)}
                    className={`p-3 cursor-pointer rounded-md ${
                      isDarkMode 
                        ? 'hover:bg-gray-700' 
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center">
                      <User className="h-4 w-4 text-gray-400 mr-2" />
                      <div>
                        <p className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {formatPatientName(patient)}
                        </p>
                        {patient.dateOfBirth && (
                          <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            DOB: {new Date(patient.dateOfBirth).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                
                {searchResults.length > 5 && (
                  <div className="p-2 text-center">
                    <button
                      onClick={() => {
                        router.push(`/patient-search?q=${encodeURIComponent(searchTerm)}`);
                        setIsOpen(false);
                      }}
                      className="text-blue-500 hover:text-blue-600 text-sm"
                    >
                      View all results →
                    </button>
                  </div>
                )}
              </div>
            ) : searchTerm.length > 1 ? (
              <div className="py-4 text-center text-gray-500">
                No patients found
              </div>
            ) : null}
          </div>
        </div>
      )}
    </div>
  );
}
