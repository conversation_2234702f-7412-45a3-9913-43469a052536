import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import sys
import io
from pathlib import Path
import audio_transfer
import shutil
from datetime import datetime
import tkinter.simpledialog
import math
import re


class RedirectText(io.StringIO):
    def __init__(self, text_ctrl):
        super().__init__()
        self.text_ctrl = text_ctrl

    def write(self, s):
        # Add timestamp prefix to each new line
        lines = s.splitlines()
        for line in lines:
            if line.strip() == "":
                continue
            timestamp = datetime.now().strftime("%H:%M:%S")
            formatted_line = f"[{timestamp}] {line}\n"

            # Color coding for success and error
            if re.search(r"(error|failed|missing|invalid)", line, re.I):
                self.text_ctrl.tag_configure("error", foreground="red")
                self.text_ctrl.configure(state='normal')
                self.text_ctrl.insert(tk.END, formatted_line, "error")
                self.text_ctrl.configure(state='disabled')
            elif re.search(r"(success|verified|transferred|moved|complete)", line, re.I):
                self.text_ctrl.tag_configure("success", foreground="green")
                self.text_ctrl.configure(state='normal')
                self.text_ctrl.insert(tk.END, formatted_line, "success")
                self.text_ctrl.configure(state='disabled')
            else:
                self.text_ctrl.configure(state='normal')
                self.text_ctrl.insert(tk.END, formatted_line)
                self.text_ctrl.configure(state='disabled')

        self.text_ctrl.see(tk.END)
        return len(s)

    def flush(self):
        pass


class AudioTransferGUI(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Audio Transfer Tool")
        self.geometry("720x700")

        # Apply modern font and colors
        default_font = ("Segoe UI", 10)
        self.option_add("*Font", default_font)
        self.configure(bg="#f9fafb")  # light gray background

        self.usb_root = tk.StringVar(value="D:/")

        # USB root selection
        frame_top = ttk.Frame(self)
        frame_top.pack(fill=tk.X, padx=15, pady=10)

        ttk.Label(frame_top, text="USB Root Folder:", font=("Segoe UI", 11, "bold")).pack(side=tk.LEFT)
        self.entry_usb_root = ttk.Entry(frame_top, textvariable=self.usb_root, width=45)
        self.entry_usb_root.pack(side=tk.LEFT, padx=8)
        ttk.Button(frame_top, text="Browse...", command=self.browse_usb_root).pack(side=tk.LEFT)

        self.device_id_label = ttk.Label(frame_top, text="", foreground="#2563eb", font=("Segoe UI", 10, "italic"))
        self.device_id_label.pack(side=tk.LEFT, padx=15)

        self.btn_create_device_id = ttk.Button(frame_top, text="Create device_id.txt", command=self.create_device_id_file)
        self.btn_create_device_id.pack(side=tk.LEFT, padx=10)

        # Audio files list
        frame_files = ttk.LabelFrame(self, text="Audio Files in Recordings")
        frame_files.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        self.canvas = tk.Canvas(frame_files, bg="white", highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(frame_files, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(
                scrollregion=self.canvas.bbox("all")
            )
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)

        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")

        # Enable mouse wheel scrolling on canvas
        self.canvas.bind_all("<MouseWheel>", self._on_mousewheel)

        self.file_vars = []  # list of (filepath, tk.IntVar)
        self.file_progress_bars = {}  # filepath -> (ttk.Progressbar, ttk.Label)

        # Transfer button
        frame_bottom = ttk.Frame(self)
        frame_bottom.pack(fill=tk.X, padx=15, pady=10)

        self.btn_transfer = ttk.Button(frame_bottom, text="Transfer Selected", command=self.transfer_selected)
        self.btn_transfer.pack(side=tk.LEFT)

        # Overall progress bar with label
        self.overall_progress = ttk.Progressbar(frame_bottom, orient='horizontal', length=250, mode='determinate')
        self.overall_progress.pack(side=tk.LEFT, padx=15)
        self.overall_label = ttk.Label(frame_bottom, text="0%", width=5)
        self.overall_label.pack(side=tk.LEFT)

        # Log output
        frame_log = ttk.LabelFrame(self, text="Log Output")
        frame_log.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        self.text_log = tk.Text(frame_log, height=10, state='disabled', bg="#f3f4f6", fg="#111827")
        self.text_log.pack(fill=tk.BOTH, expand=True)

        self.load_audio_files()
        self.update_device_id_status()

    def _on_mousewheel(self, event):
        # Scroll canvas vertically
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def browse_usb_root(self):
        folder = filedialog.askdirectory()
        if folder:
            self.usb_root.set(folder)
            self.load_audio_files()
            self.update_device_id_status()

    def update_device_id_status(self):
        usb_root_path = Path(self.usb_root.get())
        device_id_file = usb_root_path / "device_id.txt"
        if device_id_file.exists():
            try:
                device_id = device_id_file.read_text().strip()
                self.device_id_label.config(text=f"Device ID: {device_id}")
                self.btn_create_device_id.pack_forget()
            except Exception:
                self.device_id_label.config(text="Device ID: (error reading file)")
                self.btn_create_device_id.pack(side=tk.LEFT, padx=10)
        else:
            self.device_id_label.config(text="Device ID: (none)")
            self.btn_create_device_id.pack(side=tk.LEFT, padx=10)

    def load_audio_files(self):
        # Clear previous
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        self.file_vars.clear()
        self.file_progress_bars.clear()

        usb_root_path = Path(self.usb_root.get())
        rec_file_folder = None
        # Find REC_FILE folder (case-insensitive) under usb_root_path
        for child in usb_root_path.iterdir():
            if child.is_dir() and child.name.lower() == "rec_file":
                rec_file_folder = child
                break
        if not rec_file_folder:
            ttk.Label(self.scrollable_frame, text="REC_FILE folder not found under USB root.", foreground="#b91c1c").pack(pady=5)
            return

        folder01 = rec_file_folder / "FOLDER01"
        if not folder01.exists() or not folder01.is_dir():
            ttk.Label(self.scrollable_frame, text="FOLDER01 folder not found under REC_FILE.", foreground="#b91c1c").pack(pady=5)
            return

        # List audio files only in FOLDER01
        audio_files = [f for f in folder01.iterdir() if f.suffix.lower() in audio_transfer.AUDIO_EXTENSIONS]

        if not audio_files:
            ttk.Label(self.scrollable_frame, text="No audio files found in FOLDER01.", foreground="#b91c1c").pack(pady=5)
            return

        for f in audio_files:
            var = tk.IntVar(value=1)
            frame = ttk.Frame(self.scrollable_frame)
            frame.pack(fill=tk.X, pady=2, padx=5)

            cb = ttk.Checkbutton(frame, text=str(f.relative_to(usb_root_path)), variable=var)
            cb.pack(side=tk.LEFT, anchor='w')

            pb = ttk.Progressbar(frame, orient='horizontal', length=180, mode='determinate')
            pb.pack(side=tk.LEFT, padx=10)

            label = ttk.Label(frame, text="0%", width=5)
            label.pack(side=tk.LEFT)

            self.file_vars.append((f, var))
            self.file_progress_bars[f] = (pb, label)

    def transfer_selected(self):
        selected_files = [f for f, var in self.file_vars if var.get() == 1]
        if not selected_files:
            messagebox.showwarning("No files selected", "Please select at least one audio file to transfer.")
            return

        self.btn_transfer.config(state='disabled')
        self.text_log.configure(state='normal')
        self.text_log.delete('1.0', tk.END)
        self.text_log.configure(state='disabled')

        self.overall_progress['value'] = 0
        self.overall_progress['maximum'] = len(selected_files)
        self.overall_label.config(text="0%")

        # Run transfer in thread to keep UI responsive
        threading.Thread(target=self.run_transfer, args=(selected_files,), daemon=True).start()

    def copy_file_with_progress(self, src: Path, dst: Path, progress_bar: ttk.Progressbar, label: ttk.Label):
        total_size = src.stat().st_size
        progress_bar['maximum'] = total_size
        chunk_size = 1024 * 1024  # 1MB
        copied = 0

        with src.open('rb') as fsrc, dst.open('wb') as fdst:
            while True:
                chunk = fsrc.read(chunk_size)
                if not chunk:
                    break
                fdst.write(chunk)
                copied += len(chunk)

                percent = math.floor(copied / total_size * 100)

                # Update progress bar and label in main thread
                self.after(0, lambda v=copied, p=percent: self.update_progress(progress_bar, label, v, p))

        # Ensure progress bar is full
        self.after(0, lambda: self.update_progress(progress_bar, label, total_size, 100))

    def update_progress(self, progress_bar, label, value, percent):
        progress_bar['value'] = value
        label.config(text=f"{percent}%")

    def run_transfer(self, files):
        # Redirect stdout to GUI
        old_stdout = sys.stdout
        sys.stdout = RedirectText(self.text_log)

        try:
            device_id_file = Path(self.usb_root.get()) / "device_id.txt"
            if not device_id_file.exists():
                print("Device ID file missing in selected USB root.")
                return
            device_id = device_id_file.read_text().strip()
            if not device_id:
                print("Device ID file is empty.")
                return

            total_files = len(files)
            completed_files = 0

            for audio_file in files:
                logical_date = audio_transfer.get_logical_date(audio_file)
                server_folder = audio_transfer.SERVER_SHARE_ROOT / logical_date / device_id
                server_folder.mkdir(parents=True, exist_ok=True)
                dst_path = server_folder / audio_file.name

                if dst_path.exists():
                    print(f"File already exists on server: {dst_path}")
                    completed_files += 1
                    self.after(0, self.overall_progress.step, 1)
                    self.after(0, self.update_overall_label, completed_files, total_files)
                    continue

                print(f"Copying {audio_file} to {dst_path}")
                pb, label = self.file_progress_bars[audio_file]
                self.copy_file_with_progress(audio_file, dst_path, pb, label)

                if audio_transfer.verify_copy(audio_file, dst_path):
                    print(f"Verified copy of {audio_file.name} successfully.")
                    audio_transfer.write_receipt(dst_path)
                    audio_transfer.move_to_trash(audio_file, logical_date, device_id)
                    print(f"Moved original file {audio_file.name} to Trash.")
                    print(f"Transferred and verified: {audio_file.name}")
                else:
                    print(f"Verification failed for {audio_file.name}")

                completed_files += 1
                self.after(0, self.overall_progress.step, 1)
                self.after(0, self.update_overall_label, completed_files, total_files)

            # Update last_run.txt
            last_run_file = Path(self.usb_root.get()) / "last_run.txt"
            last_run_file.write_text(datetime.now().isoformat())

            # Purge old trash
            audio_transfer.purge_old_trash()

            print("Transfer complete.")

        except Exception as e:
            print(f"Error during transfer: {e}")

        finally:
            sys.stdout = old_stdout
            self.btn_transfer.config(state='normal')

    def update_overall_label(self, completed, total):
        percent = math.floor(completed / total * 100)
        self.overall_label.config(text=f"{percent}%")

    def create_device_id_file(self):
        usb_root_path = Path(self.usb_root.get())
        if not usb_root_path.exists():
            messagebox.showerror("Error", f"USB root folder does not exist: {usb_root_path}")
            return
        device_id = tkinter.simpledialog.askstring("Device ID", "Enter device ID (e.g. A, B, C):")
        if not device_id:
            return
        device_id = device_id.strip().upper()
        if len(device_id) != 1 or not device_id.isalpha():
            messagebox.showerror("Invalid ID", "Device ID must be a single letter (A-Z).")
            return
        device_id_file = usb_root_path / "device_id.txt"
        try:
            device_id_file.write_text(device_id)
            messagebox.showinfo("Success", f"device_id.txt created with ID '{device_id}'")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to write device_id.txt: {e}")


if __name__ == "__main__":
    app = AudioTransferGUI()
    app.mainloop()
