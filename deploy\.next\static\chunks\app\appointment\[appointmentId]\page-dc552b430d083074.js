(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7353],{4679:(e,t,a)=>{"use strict";function r(e){if(!e||""===e.trim())return"No Patient";let t=String(e).trim();if(t.includes(",")){let e=t.split(",").map(e=>e.trim());if(e.length>=2){var a;let t=e[0].trim(),r=null==(a=e[1].split(" ")[0])?void 0:a.trim();if(r&&t){let e=r.charAt(0).toUpperCase()+r.slice(1).toLowerCase(),a=t.toUpperCase();return"".concat(e," ").concat(a)}}}let r=t.split(" ").filter(e=>e.trim());if(0===r.length)return"No Patient";if(1===r.length)return r[0].charAt(0).toUpperCase()+r[0].slice(1).toLowerCase();if(r.length>=2){let e="",t="",a=r[0],s=r[r.length-1];2===r.length||a===a.toUpperCase()&&s!==s.toUpperCase()?(e=s,t=a):(s.toUpperCase(),e=a,t=s);let n=e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),i=t.toUpperCase();return"".concat(n," ").concat(i)}return t}function s(e){var t,a;let s=null==(t=e.firstName)?void 0:t.trim(),n=null==(a=e.lastName)?void 0:a.trim();if(s&&n){let e=s.charAt(0).toUpperCase()+s.slice(1).toLowerCase(),t=n.toUpperCase();return"".concat(e," ").concat(t)}if(s)return s.charAt(0).toUpperCase()+s.slice(1).toLowerCase();if(n)return n.toUpperCase();let i=[e.firstName,e.middleName,e.lastName].filter(Boolean);return i.length>0?r(i.join(" ")):"No Patient"}a.d(t,{Qr:()=>r,hG:()=>s})},5535:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var r=a(5155),s=a(5695),n=a(2115),i=a(1362),l=a(6496),d=a(4822),c=a(9588),o=a(2178),m=a(5690),x=a(4186),g=a(9074),h=a(7434);function p(e){let{appointmentId:t,appointmentDate:a,patientName:s,isDarkMode:i}=e,[l,d]=(0,n.useState)([]),[p,u]=(0,n.useState)(!0),[b,y]=(0,n.useState)(null),[j,N]=(0,n.useState)(null),[f,k]=(0,n.useState)(!1),v=(0,n.useRef)(null);(0,n.useEffect)(()=>{w()},[t,a]);let w=async()=>{try{u(!0),y(null);let e=await fetch("/api/voice/recordings-fast");if(!e.ok)throw Error("Failed to load recordings");let r=((await e.json()).recordings||[]).filter(e=>{if(e.matchedAppointmentId===t)return!0;if(!e.matchedAppointmentId){let t=new Date(e.createdAt).toISOString().split("T")[0],r=a.split("T")[0];return t===r}return!1});d(r)}catch(e){console.error("Failed to load recordings:",e),y(e instanceof Error?e.message:"Failed to load recordings")}finally{u(!1)}},A=e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))},C=e=>"".concat((e/1048576).toFixed(1)," MB"),S=e=>new Date(e).toLocaleString(),P=e=>{var t,a;(null==j?void 0:j.id)===e.id&&(null==(t=v.current)?void 0:t.paused)===!1?(null==(a=v.current)||a.pause(),N(null),k(!1)):v.current&&(v.current.src=e.localPath||e.networkPath||"",v.current.play(),N(e),k(!0))};return p?(0,r.jsxs)("div",{className:"p-6 rounded-lg border ".concat(i?"bg-gray-800 border-gray-700":"bg-white border-gray-200"),children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 mr-2 text-orange-600"}),(0,r.jsx)("h3",{className:"text-lg font-medium ".concat(i?"text-white":"text-gray-900"),children:"Voice Recordings for this Appointment"})]}),(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"})]})]}):b?(0,r.jsxs)("div",{className:"p-6 rounded-lg border ".concat(i?"bg-gray-800 border-gray-700":"bg-white border-gray-200"),children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 mr-2 text-orange-600"}),(0,r.jsx)("h3",{className:"text-lg font-medium ".concat(i?"text-white":"text-gray-900"),children:"Voice Recordings for this Appointment"})]}),(0,r.jsxs)("div",{className:"text-red-600 dark:text-red-400 text-sm",children:["Error loading recordings: ",b]})]}):(0,r.jsxs)("div",{className:"p-6 rounded-lg border ".concat(i?"bg-gray-800 border-gray-700":"bg-white border-gray-200"),children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 mr-2 text-orange-600"}),(0,r.jsx)("h3",{className:"text-lg font-medium ".concat(i?"text-white":"text-gray-900"),children:"Voice Recordings for this Appointment"}),(0,r.jsxs)("span",{className:"ml-2 px-2 py-1 text-xs rounded-full ".concat(l.length>0?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400"),children:[l.length," recording",1!==l.length?"s":""]})]})}),0===l.length?(0,r.jsxs)("div",{className:"text-center py-8 ".concat(i?"text-gray-400":"text-gray-500"),children:[(0,r.jsx)(c.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{className:"text-sm",children:"No voice recordings found for this appointment."}),(0,r.jsx)("p",{className:"text-xs mt-2",children:"Recordings are automatically matched to appointments or can be manually linked in the Voice Recordings section."})]}):(0,r.jsx)("div",{className:"space-y-3",children:l.map(e=>(0,r.jsx)("div",{className:"p-4 border rounded-lg transition-all ".concat(e.matchedAppointmentId===t?"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800":"bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"),children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("button",{onClick:()=>P(e),className:"flex-shrink-0 p-2 rounded-full transition-colors ".concat((null==j?void 0:j.id)===e.id&&f?"bg-orange-600 text-white":"bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300 hover:bg-orange-100 dark:hover:bg-orange-900/30"),disabled:!e.localPath&&!e.networkPath,children:(null==j?void 0:j.id)===e.id&&f?(0,r.jsx)(o.A,{className:"h-4 w-4"}):(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("h4",{className:"text-sm font-medium truncate ".concat(i?"text-white":"text-gray-900"),children:e.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsx)(x.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:A(e.duration)}),(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{children:C(e.size)})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"h-3 w-3 mr-1"}),S(e.createdAt)]}),e.matchedAppointmentId===t&&(0,r.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full",children:"Matched to this appointment"})]}),e.transcription&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center mb-1",children:[(0,r.jsx)(h.A,{className:"h-3 w-3 mr-1 text-blue-600"}),(0,r.jsx)("span",{className:"text-xs font-medium text-blue-600 dark:text-blue-400",children:"Transcription"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-300 line-clamp-2",children:e.transcription.length>150?"".concat(e.transcription.substring(0,150),"..."):e.transcription})]}),e.summary&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs font-medium text-blue-800 dark:text-blue-300 mb-1",children:"Visit Summary"}),(0,r.jsx)("div",{className:"text-sm text-blue-900 dark:text-blue-100 leading-relaxed",children:e.summary})]})]})})]})]})},e.id))}),(0,r.jsx)("audio",{ref:v,onEnded:()=>{N(null),k(!1)},onError:()=>{N(null),k(!1)},style:{display:"none"}})]})}var u=a(4679);function b(){let e=(0,s.useParams)(),t=(0,s.useRouter)(),{theme:a}=(0,i.D)(),c=null==e?void 0:e.appointmentId,[o,m]=(0,n.useState)(null),[x,g]=(0,n.useState)(!0),[h,b]=(0,n.useState)(null);(0,n.useEffect)(()=>{c&&(async()=>{try{g(!0);let e=await fetch("/api/appointments/".concat(c),{method:"GET",headers:{"Content-Type":"application/json"},credentials:"same-origin"});if(!e.ok){let t=await e.text();throw console.error("❌ API Error Response: ".concat(t)),Error("Failed to fetch appointment: ".concat(e.status))}let t=await e.json();m(t)}catch(e){console.error("❌ Error fetching appointment:",e),b(e instanceof Error?e.message:"Failed to load appointment")}finally{g(!1)}})()},[c]);let y=e=>{if(!e)return"";try{let[t,a]=e.split(":"),r=parseInt(t);return"".concat(0===r?12:r>12?r-12:r,":").concat(a," ").concat(r>=12?"PM":"AM")}catch(t){return e}},j=e=>{if(!e)return"";let t=Math.floor(e/60),a=e%60;return t>0?"".concat(t,"h ").concat(a,"m"):"".concat(a,"m")},N=()=>{try{if(null==o?void 0:o.startTime){let e=new Date(o.startTime);if(!isNaN(e.getTime()))return e.toISOString().split("T")[0]}let e=new URLSearchParams(window.location.search).get("date");if(e)return e;if(document.referrer){let e=new URL(document.referrer).searchParams.get("date");if(e)return e}return console.warn("No appointment date found, using today as fallback"),new Date().toISOString().split("T")[0]}catch(e){return console.warn("Could not determine appointment date, using today:",e),new Date().toISOString().split("T")[0]}};if(x)return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)(l.z,{title:"Dentalapp",showBackButton:!0,onBackClick:()=>t.back(),backButtonLabel:"Back"}),(0,r.jsx)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"})]})})})]});if(h||!o){let e=N();return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)(l.z,{title:"Dentalapp",showBackButton:!0,onBackClick:()=>t.back(),backButtonLabel:"Back"}),(0,r.jsx)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:h?"Error Loading Appointment":"Appointment Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:h||"The requested appointment could not be found. It may have been moved, cancelled, or the ID may be invalid."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,r.jsxs)("button",{onClick:()=>t.push("/schedule?date=".concat(e)),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md",children:["View Schedule for ",new Date(e).toLocaleDateString()]}),(0,r.jsx)("button",{onClick:()=>t.back(),className:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md",children:"Go Back"})]})]})})})]})}return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)(l.z,{title:"Dentalapp",showBackButton:!0,onBackClick:()=>t.back(),backButtonLabel:"Back to Schedule"}),(0,r.jsx)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden",children:[(0,r.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:[(0,r.jsxs)("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[(0,u.Qr)(o.patient_name)," - Appointment"]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:[y(o.startTime),o.length&&" • ".concat(j(o.length)),o.operatory&&" • ".concat(o.operatory)]})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Patient Information"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Patient Name"}),(0,r.jsxs)("div",{className:"mt-1 flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-900 dark:text-white",children:(0,u.Qr)(o.patient_name)}),o.patient_id&&(0,r.jsx)("button",{onClick:()=>t.push("/patient/".concat(o.patient_id)),className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-xs underline",children:"View Patient"})]})]}),o.patient_dob&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Date of Birth"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:o.patient_dob})]}),o.patient_phone&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Phone"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:o.patient_phone})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Appointment Details"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Time"}),(0,r.jsxs)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:[y(o.startTime),o.endTime&&" - ".concat(y(o.endTime))]})]}),o.length&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Duration"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:j(o.length)})]}),o.type&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Appointment Type"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:o.type})]}),o.provider&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Provider"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:"string"==typeof o.provider?o.provider:"object"==typeof o.provider&&o.provider.href?"Provider (see API)":"Unknown Provider"})]}),o.status&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Status"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:o.status})]})]})]})]}),o.notes&&(0,r.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Appointment Notes"}),(0,r.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-md p-4",children:(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white whitespace-pre-wrap",children:o.notes})})]}),o.patient_id&&(0,r.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,r.jsx)(d.r,{patientId:o.patient_id,appointmentId:o.id,appointmentDate:N(),title:"Clinical Notes for this Appointment",showPagination:!1,showFilters:!1,isDarkMode:"dark"===a,maxHeight:"300px"})}),(0,r.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,r.jsx)(p,{appointmentId:o.id,appointmentDate:N(),patientName:(0,u.Qr)(o.patient_name),isDarkMode:"dark"===a})}),(0,r.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-3",children:[o.patient_id&&(0,r.jsx)("button",{onClick:()=>t.push("/patient/".concat(o.patient_id)),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"View Patient Details"}),o.patient_id&&(0,r.jsx)("a",{href:"https://clinic.overjet.ai/app/fmx/dailypatients/".concat(o.patient_id),target:"_blank",rel:"noopener noreferrer",className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"View in Overjet"}),(0,r.jsx)("button",{onClick:()=>t.back(),className:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Back to Schedule"})]})})]})]})})]})}},7877:(e,t,a)=>{Promise.resolve().then(a.bind(a,5535))}},e=>{var t=t=>e(e.s=t);e.O(0,[8096,6496,4822,7358],()=>t(7877)),_N_E=e.O()}]);