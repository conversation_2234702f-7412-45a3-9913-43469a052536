2025-07-14T19:03:14.1928288Z,Updating submodules.,f7cfe9d6-934a-4c44-9a0c-641c32975617,0
2025-07-14T19:03:15.7887035Z,Preparing deployment for commit id '69aac71f-1'.,26674857-dd42-4e24-b96e-e2734bd528ea,0
2025-07-14T19:03:17.8615779Z,PreDeployment: context.CleanOutputPath False,248883fd-0c04-4ed9-9cf9-68efc55500fa,0
2025-07-14T19:03:18.1761397Z,PreDeployment: context.OutputPath /home/<USER>/wwwroot,a43906cc-bed2-4ec4-974f-bca7ee38bfd3,0
2025-07-14T19:03:18.4382541Z,Repository path is /tmp/zipdeploy/extracted,390d16cf-0d43-493e-b24b-8a4e2c3c75c9,0
2025-07-14T19:03:18.6604689Z,Running oryx build...,c9f5bb5a-827a-4871-a859-3fa63ff93eee,0
	2025-07-14T19:03:18.7078409Z,Command: oryx build /tmp/zipdeploy/extracted -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log  -i /tmp/8ddc30916ce8612 -p compress_node_modules=tar-gz | tee /tmp/oryx-build.log,,0
	2025-07-14T19:03:28.4890727Z,Operation performed by Microsoft Oryx&comma; https://github.com/Microsoft/Oryx,,0
	2025-07-14T19:03:28.6913720Z,You can report issues at https://github.com/Microsoft/Oryx/issues,,0
	2025-07-14T19:03:28.8105919Z,,,0
	2025-07-14T19:03:28.9326357Z,Oryx Version: 0.2.20250611.1+0649de32f1279969c9023dd41b389cce4bb94493&comma; Commit: 0649de32f1279969c9023dd41b389cce4bb94493&comma; ReleaseTagName: 20250611.1,,0
	2025-07-14T19:03:29.0239180Z,,,0
	2025-07-14T19:03:29.0849387Z,Build Operation ID: aebe9d32fe7cfc39,,0
	2025-07-14T19:03:29.1416927Z,Repository Commit : 69aac71f-1eaf-42ed-b1fa-d221fce582c3,,0
	2025-07-14T19:03:29.2021811Z,OS Type           : bookworm,,0
	2025-07-14T19:03:29.2959487Z,Image Type        : githubactions,,0
	2025-07-14T19:03:29.3552982Z,,,0
	2025-07-14T19:03:29.4159976Z,Primary SDK Storage URL: https://oryx-cdn.microsoft.io,,0
	2025-07-14T19:03:29.6242787Z,Backup SDK Storage URL: https://oryxsdks-cdn.azureedge.net,,0
	2025-07-14T19:03:29.7448399Z,Detecting platforms...,,0
	2025-07-14T19:03:43.9319511Z,Detected following platforms:,,0
	2025-07-14T19:03:44.0212927Z,  nodejs: 20.19.3,,0
	2025-07-14T19:03:44.1637119Z,  python: 3.8.18,,0
	2025-07-14T19:03:44.2650304Z,Version '20.19.3' of platform 'nodejs' is not installed. Generating script to install it...,,0
	2025-07-14T19:03:44.3656856Z,Version '3.8.18' of platform 'python' is not installed. Generating script to install it...,,0
	2025-07-14T19:03:45.6338842Z,Detected the following frameworks: Typescript&comma;Next.js,,0
	2025-07-14T19:03:48.6421845Z,Warning: An outdated version of python was detected (3.8.18). Consider updating.\nVersions supported by Oryx: https://github.com/microsoft/Oryx,,0
	2025-07-14T19:03:48.7331903Z,,,0
	2025-07-14T19:03:48.7946040Z,,,0
	2025-07-14T19:03:48.9561672Z,Using intermediate directory '/tmp/8ddc30916ce8612'.,,0
	2025-07-14T19:03:48.9964476Z,,,0
	2025-07-14T19:03:49.0434623Z,Copying files to the intermediate directory...,,0
	2025-07-14T19:03:49.7468060Z,/tmp/BuildScriptGenerator/c2575a4d53444ceab5181dfe953876da/build.sh: line 74:   885 Killed                  rsync -rcE --delete $excludedDirectories . "$INTERMEDIATE_DIR",,1
	2025-07-14T19:03:49.9273907Z,/bin/bash: line 1:   855 Killed                  oryx build /tmp/zipdeploy/extracted -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log -i /tmp/8ddc30916ce8612 -p compress_node_modules=tar-gz,,1
	2025-07-14T19:03:50.0052580Z,       856                       | tee /tmp/oryx-build.log,,1
	2025-07-14T19:03:50.5908613Z,/tmp/BuildScriptGenerator/c2575a4d53444ceab5181dfe953876da/build.sh: line 74:   885 Killed                  rsync -rcE --delete $excludedDirectories . "$INTERMEDIATE_DIR"\n/bin/bash: line 1:   855 Killed                  oryx build /tmp/zipdeploy/extracted -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log -i /tmp/8ddc30916ce8612 -p compress_node_modules=tar-gz\n       856                       | tee /tmp/oryx-build.log\n/bin/bash -c "oryx build /tmp/zipdeploy/extracted -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log  -i /tmp/8ddc30916ce8612 -p compress_node_modules=tar-gz | tee /tmp/oryx-build.log ; exit $PIPESTATUS ",,2
