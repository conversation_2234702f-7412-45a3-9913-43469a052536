exports.id=1232,exports.ids=[1232],exports.modules={8402:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>l});var a=r(60687),s=r(10218),n=r(25995);let i={revalidateOnFocus:!1,revalidateOnReconnect:!0,dedupingInterval:3e5,refreshInterval:0,errorRetryCount:1,shouldRetryOnError:!1,revalidateIfStale:!1,revalidateOnMount:!0,fetcher:async e=>{let t=new AbortController,r=setTimeout(()=>t.abort(),1e4);try{let a=await fetch(e,{signal:t.signal,headers:{"Cache-Control":"max-age=300"}});if(clearTimeout(r),!a.ok)throw Error(`Failed to fetch: ${a.status} ${a.statusText}`);return a.json()}catch(e){if(clearTimeout(r),"AbortError"===e.name)throw Error("Request timeout");throw e}}};function l({children:e,...t}){return(0,a.jsx)(s.N,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,suppressHydrationWarning:!0,...t,children:(0,a.jsx)(n.BE,{value:i,children:e})})}},11799:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},26748:(e,t,r)=>{"use strict";r.d(t,{AuthWrapper:()=>l});var a=r(60687),s=r(43210),n=r(16189);let i=()=>!1,l=({children:e})=>{let[t,r]=(0,s.useState)(!0),[l,o]=(0,s.useState)(!1);return((0,n.useRouter)(),(0,s.useEffect)(()=>{if(i()){o(!0),r(!1);return}let e=async()=>{try{let e=await fetch("/.auth/me",{credentials:"include"});if(await e.json(),404===e.status)return o(!0),r(!1),!0}catch(e){return o(!0),r(!1),!0}return!1},t=async()=>{try{let e=await fetch("/.auth/me",{credentials:"include",headers:{"Cache-Control":"no-cache",Pragma:"no-cache"}});if(e.ok){let t=await e.json();if(t?.clientPrincipal)return o(!0),r(!1),!0}return!1}catch(e){return console.error("Auth check failed:",e),!1}};(async()=>{if((window.location.search.includes("code=")||window.location.search.includes("id_token=")||window.location.search.includes("token="))&&await t())return window.history.replaceState({},document.title,window.location.pathname);if(!await e()&&!await t()&&!window.location.pathname.startsWith("/.auth/")){let e=encodeURIComponent(window.location.href);window.location.href=`/.auth/login/aad?post_login_redirect_uri=${e}`}})()},[]),t)?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-lg text-slate-600 dark:text-slate-400",children:"Checking authentication status..."})]})}):l?(0,a.jsx)(a.Fragment,{children:e}):(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg text-slate-600 dark:text-slate-400 mb-4",children:"Redirecting to login..."}),(0,a.jsx)("button",{onClick:()=>window.location.href="/.auth/login/aad",className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"Click here if not redirected"})]})})}},27206:(e,t,r)=>{Promise.resolve().then(r.bind(r,78972)),Promise.resolve().then(r.bind(r,76034))},45358:(e,t,r)=>{Promise.resolve().then(r.bind(r,8402)),Promise.resolve().then(r.bind(r,26748))},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>i,viewport:()=>l});var a=r(37413);r(82704);var s=r(78972),n=r(76034);let i={title:"Dental Schedule App",description:"A web-based application for viewing dental appointments by operatory",manifest:"/api/manifest",icons:{icon:[{url:"/favicon.svg",type:"image/svg+xml"},{url:"/favicon.ico",sizes:"any"}],apple:[{url:"/apple-touch-icon.png",sizes:"180x180",type:"image/png"}],other:[{url:"/android-chrome-192x192.png",sizes:"192x192",type:"image/png"},{url:"/android-chrome-512x512.png",sizes:"512x512",type:"image/png"}]},appleWebApp:{capable:!0,statusBarStyle:"default",title:"Dental App"}},l={width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,themeColor:[{media:"(prefers-color-scheme: light)",color:"#ffffff"},{media:"(prefers-color-scheme: dark)",color:"#000000"}]},o=`
  (function() {
    // Check for saved theme preference or use system preference
    const theme = (() => {
      if (typeof localStorage !== 'undefined' && localStorage.theme) {
        return localStorage.theme;
      }
      if (typeof window !== 'undefined' && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark';
      }
      return 'light';
    })();

    // Apply the theme immediately
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  })();
`;function c({children:e}){return(0,a.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,className:"antialiased",children:[(0,a.jsx)("head",{children:(0,a.jsx)("script",{dangerouslySetInnerHTML:{__html:o}})}),(0,a.jsx)("body",{className:"font-sans bg-slate-50 dark:bg-slate-950 text-slate-900 dark:text-slate-100 transition-colors duration-200",children:(0,a.jsx)(s.ThemeProvider,{children:(0,a.jsx)(n.AuthWrapper,{children:e})})})]})}},75139:(e,t,r)=>{"use strict";r.d(t,{U:()=>l});var a=r(60687),s=r(43210),n=r(21134),i=r(363);function l(){let[e,t]=(0,s.useState)(!1),[r,l]=(0,s.useState)(!1),o=e=>{};return r?(0,a.jsx)("button",{onClick:()=>{let r=!e;t(r);let a=r?"dark":"light";localStorage.setItem("theme",a),o(r)},className:"p-2 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors","aria-label":`Switch to ${e?"light":"dark"} mode`,style:{backgroundColor:e?"#374151":"#e5e7eb",color:e?"#e5e7eb":"#374151"},children:e?(0,a.jsx)(n.A,{className:"h-5 w-5"}):(0,a.jsx)(i.A,{className:"h-5 w-5"})}):(0,a.jsx)("div",{className:"p-2 w-10 h-10 rounded-md bg-gray-200 dark:bg-gray-700"})}},76034:(e,t,r)=>{"use strict";r.d(t,{AuthWrapper:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AuthWrapper() from the server but AuthWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\dentalapp\\src\\components\\auth\\AuthWrapper.tsx","AuthWrapper")},78972:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\dentalapp\\app\\providers.tsx","ThemeProvider")},82704:()=>{},90220:(e,t,r)=>{"use strict";r.d(t,{z:()=>y});var a=r(60687),s=r(16189),n=r(40228),i=r(16023),l=r(73259),o=r(10022),c=r(83753),d=r(82679),h=r(28559),m=r(32192),u=r(75139),p=r(43210),x=r(99270),b=r(11860),g=r(58869);function f({isDarkMode:e}){let t=(0,s.useRouter)(),[r,n]=(0,p.useState)(!1),[i,l]=(0,p.useState)(""),[o,c]=(0,p.useState)([]),[d,h]=(0,p.useState)(!1),[m,u]=(0,p.useState)(!1),f=(0,p.useRef)(null),v=(0,p.useRef)(null),y=async e=>{if(e.trim().length<2){c([]),h(!1);return}h(!0);try{let t=await fetch(`/api/patients/search?q=${encodeURIComponent(e)}`);if(!t.ok)throw Error("Failed to search patients");let r=await t.json();c(r.patients||[])}catch(e){console.error("Search error:",e),c([])}finally{h(!1)}},w=e=>{t.push(`/patient/${e.id}`),n(!1),l(""),c([])},j=e=>{let t=e.firstName?.charAt(0).toUpperCase()+e.firstName?.slice(1).toLowerCase(),r=e.lastName?.toUpperCase();return t&&r?`${t} ${r}`:t?t:r?r:[e.firstName,e.middleName,e.lastName].filter(Boolean).join(" ")};return(0,a.jsxs)("div",{ref:f,className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>n(!r),className:`flex items-center px-2 sm:px-3 py-2 rounded-md transition-colors ${!m?"bg-gray-100 hover:bg-gray-200 text-gray-700":r?"bg-blue-600 hover:bg-blue-700 text-white":e?"bg-gray-700 hover:bg-gray-600 text-gray-200":"bg-gray-100 hover:bg-gray-200 text-gray-700"}`,title:"Search patients",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 sm:mr-2"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Patient Search"}),(0,a.jsx)("span",{className:"sm:hidden ml-1 text-xs",children:"Search"})]}),r&&m&&(0,a.jsx)("div",{className:`absolute right-0 mt-2 w-72 sm:w-80 md:w-96 rounded-md shadow-lg z-50 ${e?"bg-gray-800 border border-gray-700":"bg-white border border-gray-200"} max-w-[calc(100vw-2rem)]`,children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h3",{className:`font-medium ${e?"text-white":"text-gray-900"}`,children:"Patient Search"}),(0,a.jsx)("button",{onClick:()=>n(!1),className:"text-gray-400 hover:text-gray-500",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)("input",{type:"text",value:i,onChange:e=>{let t=e.target.value;if(l(t),v.current&&clearTimeout(v.current),t.trim().length<2){c([]),h(!1);return}h(!0),v.current=setTimeout(()=>{y(t)},300)},placeholder:"Search patients...",autoFocus:!0,className:`w-full pl-10 pr-4 py-2 border rounded-md ${e?"bg-gray-700 border-gray-600 text-white placeholder-gray-400":"bg-white border-gray-300 text-gray-900 placeholder-gray-500"}`})]}),d?(0,a.jsx)("div",{className:"py-4 text-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-400 mx-auto"})}):o.length>0?(0,a.jsxs)("div",{className:"max-h-80 overflow-y-auto",children:[o.map(t=>(0,a.jsx)("div",{onClick:()=>w(t),className:`p-3 cursor-pointer rounded-md ${e?"hover:bg-gray-700":"hover:bg-gray-50"}`,children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:`font-medium ${e?"text-white":"text-gray-900"}`,children:j(t)}),t.dateOfBirth&&(0,a.jsxs)("p",{className:`text-xs ${e?"text-gray-400":"text-gray-500"}`,children:["DOB: ",new Date(t.dateOfBirth).toLocaleDateString()]})]})]})},t.id)),o.length>5&&(0,a.jsx)("div",{className:"p-2 text-center",children:(0,a.jsx)("button",{onClick:()=>{t.push(`/patient-search?q=${encodeURIComponent(i)}`),n(!1)},className:"text-blue-500 hover:text-blue-600 text-sm",children:"View all results →"})})]}):i.length>1?(0,a.jsx)("div",{className:"py-4 text-center text-gray-500",children:"No patients found"}):null]})})]})}var v=r(10218);function y({title:e,showBackButton:t=!1,onBackClick:r,backButtonLabel:p="Back",isHomePage:x=!1,activeTab:b="schedule",onTabChange:g}){let y=(0,s.useRouter)(),w=(0,s.usePathname)(),{theme:j}=(0,v.D)(),N=[{path:"/",label:"Schedule",icon:n.A,tabId:"schedule"},{path:"/",label:"Voice Upload",icon:i.A,tabId:"voice-upload"},{path:"/",label:"Voice Recordings",icon:l.A,tabId:"voice-recordings"},{path:"/",label:"Clinical Notes",icon:o.A,tabId:"clinical-notes"},{path:"/",label:"AI Office Manager",icon:c.A,tabId:"ai-assistant"},{path:"/",label:"Tools & Settings",icon:d.A,tabId:"tools-settings"}];return(0,a.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"py-4 flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[t&&(0,a.jsx)("button",{onClick:()=>{r?r():y.back()},className:"p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors","aria-label":p,children:(0,a.jsx)(h.A,{className:"h-5 w-5"})}),(0,a.jsxs)("button",{onClick:()=>{x||y.push("/")},className:`text-xl font-semibold text-gray-900 dark:text-white transition-all duration-200 flex items-center space-x-2 ${x?"cursor-default":"hover:text-blue-600 dark:hover:text-blue-400 hover:scale-105 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded-md px-2 py-1"}`,disabled:x,title:x?e:"\uD83C\uDFE0 Click to return to home page",children:[!x&&(0,a.jsx)(m.A,{className:"h-5 w-5 opacity-60"}),(0,a.jsx)("span",{children:e})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(f,{isDarkMode:"dark"===j}),(0,a.jsx)(u.U,{})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 dark:border-gray-700",children:(0,a.jsx)("nav",{className:"flex gap-1 sm:gap-2 py-2 sm:py-3 px-2 overflow-x-auto scrollbar-hide",children:N.map(e=>{let t=e.icon,r=x?b===e.tabId:b===e.tabId||w.includes("/schedule")&&"schedule"===e.tabId||w.includes("/clinical-notes")&&"clinical-notes"===e.tabId||w.includes("/patient-search")&&"patient-search"===e.tabId;return(0,a.jsxs)("button",{onClick:()=>{x&&g?g(e.tabId):y.push(`${e.path}?tab=${e.tabId}`)},className:`flex items-center space-x-1 px-2 py-2 rounded-md text-xs font-medium transition-colors flex-shrink-0 justify-center lg:justify-start ${r?"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300":"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700"}`,children:[(0,a.jsx)(t,{className:"w-4 h-4 flex-shrink-0"}),(0,a.jsx)("span",{className:"hidden sm:inline text-sm",children:e.label}),(0,a.jsx)("span",{className:"sm:hidden sr-only",children:e.label})]},e.tabId)})})})]})})}},95299:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},95979:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))}};