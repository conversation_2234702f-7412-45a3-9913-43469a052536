import requests
import json
from datetime import datetime, timedelta
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 60

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def fetch_appointments_by_date(request_key, target_date):
    """Fetch appointments where the actual appointment date matches the target date."""
    headers = {"Request-Key": request_key}
    
    print(f"Fetching appointments for date: {target_date}")
    
    # We need to fetch all appointments and filter them ourselves
    # since the API's date parameter filters by appointment_made_date, not the actual appointment date
    
    # Try different date ranges to find appointments
    # Start with the target date itself
    date_ranges = [
        target_date,  # The target date
        (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"),  # 30 days ago
        (datetime.now() - timedelta(days=60)).strftime("%Y-%m-%d"),  # 60 days ago
        (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d"),  # 90 days ago
    ]
    
    all_appointments = []
    
    for date_range in date_ranges:
        print(f"\nTrying to fetch appointments made on: {date_range}")
        
        try:
            resp = requests.get(
                f"{API_BASE_V2}/appointments", 
                headers=headers, 
                params={"date": date_range},
                timeout=API_TIMEOUT
            )
            
            if resp.status_code == 200:
                data = resp.json()
                
                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    print(f"Found {len(items)} appointments made on {date_range}")
                    
                    # Filter for appointments on the target date
                    target_appointments = [a for a in items if a.get("date") == target_date]
                    print(f"Of these, {len(target_appointments)} are scheduled for {target_date}")
                    
                    all_appointments.extend(target_appointments)
                else:
                    print(f"No appointments found for {date_range}")
            else:
                print(f"Error: {resp.status_code}")
                print(resp.text)
        except Exception as e:
            print(f"Error: {e}")
    
    print(f"\nTotal appointments found for {target_date}: {len(all_appointments)}")
    
    # Remove duplicates
    unique_appointments = []
    seen_appointment_ids = set()
    
    for appt in all_appointments:
        appt_id = appt.get("appointment_sr_no")
        if appt_id not in seen_appointment_ids:
            seen_appointment_ids.add(appt_id)
            unique_appointments.append(appt)
    
    if len(unique_appointments) < len(all_appointments):
        print(f"Removed {len(all_appointments) - len(unique_appointments)} duplicate appointments")
    
    # Group appointments by operatory
    appts_by_operatory = {}
    for appt in unique_appointments:
        operatory = appt.get("operatory", "N/A")
        if operatory not in appts_by_operatory:
            appts_by_operatory[operatory] = []
        appts_by_operatory[operatory].append(appt)
    
    print("\nAppointments by operatory:")
    for op, appts in sorted(appts_by_operatory.items()):
        print(f"  {op}: {len(appts)} appointments")
        for appt in sorted(appts, key=lambda a: a.get("time", "")):
            time = appt.get("time", "")
            patient_name = appt.get("patient_name", "Unknown")
            provider_id = appt.get("provider_id", "N/A")
            description = appt.get("description", "")
            print(f"    {time} - {patient_name} - {provider_id} - {description}")
    
    return unique_appointments

def main():
    # Get date from command line or use default
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        date = "2025-05-16"  # Default date
    
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Fetch appointments by date
    appointments = fetch_appointments_by_date(request_key, date)
    
    print("\nFetch complete.")

if __name__ == "__main__":
    main()
