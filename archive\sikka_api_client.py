import requests
import json

API_BASE = "https://api.sikkasoft.com/v4"


def safe_json_parse(response, label):
    try:
        return response.json()
    except json.JSONDecodeError:
        print(f"Failed to parse JSON from {label}. Raw response:")
        print(response.text)
        return None


def fetch_paginated(endpoint, headers, params=None, limit=500):
    """
    Generic pagination helper. Returns list of items from endpoint.
    """
    items = []
    offset = 0
    params = params or {}
    while True:
        paged = params.copy()
        paged.update({"offset": offset, "limit": limit})
        resp = requests.get(f"{API_BASE}/{endpoint}", headers=headers, params=paged)
        if resp.status_code == 204:
            break
        if resp.status_code != 200:
            print(f"Failed {endpoint} call at offset {offset}: HTTP {resp.status_code}")
            print(resp.text)
            break
        data = safe_json_parse(resp, f"{endpoint} @ offset {offset}")
        batch = data.get("items", []) if data else []
        items.extend(batch)
        print(f"Retrieved {len(batch)} items from {endpoint} at offset {offset}")
        if len(batch) < limit:
            break
        offset += limit
    return items


def extract_practice_schedule(app_id, app_key, office_id, target_date):
    """
    Fetch practice schedule for all operatories on a specific date.
    Returns a dict mapping operatory resource_id to list of appointment_sr_no.
    Authenticates via app-id/app-key headers.
    """
    params = {"practice_id": office_id, "date": target_date, "resource_type": "operatory"}
    headers = {"app-id": app_id, "app-key": app_key}
    resp = requests.get(f"{API_BASE}/practice_schedule", headers=headers, params=params)
    if resp.status_code != 200:
        print(f"Failed practice_schedule call: HTTP {resp.status_code}")
        return {}
    data = safe_json_parse(resp, "practice_schedule API")
    items = data.get("items", []) if data else []

    oper_map = {}
    # Events may not be included here; check 'events' key if present
    for item in items:
        oper_id = item.get("resource_id")
        events = item.get("events") or []
        sr_nos = [ev.get("appointment_sr_no") for ev in events if ev.get("appointment_sr_no")]
        if oper_id and sr_nos:
            oper_map[oper_id] = sr_nos
    return oper_map


def extract_procedures(appt):
    """
    Extract all procedure codes with amounts.
    """
    procedures = []
    for key, value in appt.items():
        if key.startswith("procedure_code") and not key.endswith("_amount") and value:
            amount_key = f"{key}_amount"
            procedures.append((value, appt.get(amount_key)))
    return procedures


def main():
    # Credentials and practice
    app_id = "fdd52aaffb0c1bead647874ba551db0c"
    app_key = "88254bfa2224607ef425646aafe5f722"
    office_id = "D43989"
    secret_key = "35442814D4396E20C222"
    target_date = "2025-05-16"

    # Step 1: Obtain request_key (for appointments)
    print("== Obtaining request_key ==")
    auth_resp = requests.post(
        f"{API_BASE}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": office_id,
            "secret_key": secret_key,
            "app_id": app_id,
            "app_key": app_key
        }
    )
    auth_resp.raise_for_status()
    request_key = safe_json_parse(auth_resp, "request_key API").get("request_key")
    print("Request Key:", request_key)

    # Step 2: Fetch appointments and filter by date
    print("\n== Fetching appointments ==")
    appt_headers = {"Request-Key": request_key}
    appointments = fetch_paginated("appointments", appt_headers)
    filtered = [a for a in appointments if a.get("date", "").startswith(target_date)]
    print(f"Filtered {len(filtered)} appointments on {target_date}")

    # Step 3: Fetch practice schedule mapping (operatory info)
    print("\n== Fetching practice schedule for operatory info ==")
    oper_map = extract_practice_schedule(app_id, app_key, office_id, target_date)
    if not oper_map:
        print("No operatory schedules found or unauthorized.")

    # Step 4: Display by operatory
    print(f"\nOperatories with appointments on {target_date}: {list(oper_map.keys())}")
    print("\n== Patients by Operatory ==")
    for oper_id, sr_nos in oper_map.items():
        print(f"Operatory ID: {oper_id}")
        for appt in filtered:
            if appt.get("appointment_sr_no") in sr_nos:
                pname = appt.get("patient_name")
                time = appt.get("time")
                print(f"  - {pname} at {time} (SR: {appt.get('appointment_sr_no')})")

if __name__ == "__main__":
    main()
