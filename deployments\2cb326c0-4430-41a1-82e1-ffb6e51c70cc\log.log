2025-07-14T10:36:55.9692670Z,Updating submodules.,7043e4c7-6e93-42b3-bc40-3feff506ad1b,0
2025-07-14T10:36:57.0842924Z,Preparing deployment for commit id '2cb326c0-4'.,1aa6727a-0e3b-4058-baea-be3c9a25328a,0
2025-07-14T10:36:57.3060756Z,PreDeployment: context.CleanOutputPath False,3aa3abe5-2906-4fb5-9291-c4394a759593,0
2025-07-14T10:36:57.3959742Z,PreDeployment: context.OutputPath /home/<USER>/wwwroot,03db9dab-5e00-49fa-82ce-ea164c927d60,0
2025-07-14T10:36:57.5238413Z,Repository path is /tmp/zipdeploy/extracted,1d7c103c-251c-4455-a34a-8d857650086b,0
2025-07-14T10:36:57.6211288Z,Running oryx build...,0f2c1372-eb8d-4e97-b30f-0514f52575a7,0
	2025-07-14T10:36:57.6493488Z,Command: oryx build /tmp/zipdeploy/extracted -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log  -i /tmp/8ddc2c25b8dc163 -p compress_node_modules=tar-gz | tee /tmp/oryx-build.log,,0
	2025-07-14T10:36:58.1562317Z,Operation performed by Microsoft Oryx&comma; https://github.com/Microsoft/Oryx,,0
	2025-07-14T10:36:58.1785626Z,You can report issues at https://github.com/Microsoft/Oryx/issues,,0
	2025-07-14T10:36:58.1903434Z,,,0
	2025-07-14T10:36:58.1972872Z,Oryx Version: 0.2.20250611.1+0649de32f1279969c9023dd41b389cce4bb94493&comma; Commit: 0649de32f1279969c9023dd41b389cce4bb94493&comma; ReleaseTagName: 20250611.1,,0
	2025-07-14T10:36:58.2151235Z,,,0
	2025-07-14T10:36:58.2273417Z,Build Operation ID: 9f279459b75f715d,,0
	2025-07-14T10:36:58.2355001Z,Repository Commit : 2cb326c0-4430-41a1-82e1-ffb6e51c70cc,,0
	2025-07-14T10:36:58.2484810Z,OS Type           : bookworm,,0
	2025-07-14T10:36:58.2643657Z,Image Type        : githubactions,,0
	2025-07-14T10:36:58.2779181Z,,,0
	2025-07-14T10:36:58.2845077Z,Primary SDK Storage URL: https://oryx-cdn.microsoft.io,,0
	2025-07-14T10:36:58.2984729Z,Backup SDK Storage URL: https://oryxsdks-cdn.azureedge.net,,0
	2025-07-14T10:36:58.3061235Z,Detecting platforms...,,0
	2025-07-14T10:36:58.3122535Z,External SDK provider is enabled.,,0
	2025-07-14T10:36:58.3214569Z,Requesting metadata for platform nodejs from external SDK provider,,0
	2025-07-14T10:36:58.6045188Z,Requesting metadata for platform nodejs from external SDK provider,,0
	2025-07-14T10:36:58.7314545Z,Requesting metadata for platform python from external SDK provider,,0
	2025-07-14T10:36:58.8021808Z,Requesting metadata for platform python from external SDK provider,,0
	2025-07-14T10:36:58.8605826Z,Requesting metadata for platform python from external SDK provider,,0
	2025-07-14T10:36:58.9691409Z,Requesting metadata for platform python from external SDK provider,,0
	2025-07-14T10:36:59.1732334Z,Detected following platforms:,,0
	2025-07-14T10:36:59.1922532Z,  nodejs: 20.19.3,,0
	2025-07-14T10:36:59.2069669Z,  python: 3.8.18,,0
	2025-07-14T10:36:59.2174856Z,Requesting metadata for platform nodejs from external SDK provider,,0
	2025-07-14T10:36:59.4275724Z,Version '20.19.3' of platform 'nodejs' is not installed. Generating script to install it...,,0
	2025-07-14T10:36:59.4379632Z,Requesting metadata for platform python from external SDK provider,,0
	2025-07-14T10:36:59.7048172Z,Version '3.8.18' of platform 'python' is not installed. Generating script to install it...,,0
	2025-07-14T10:36:59.7762419Z,Detected the following frameworks: Typescript&comma;Next.js,,0
	2025-07-14T10:36:59.8669055Z,Warning: An outdated version of python was detected (3.8.18). Consider updating.\nVersions supported by Oryx: https://github.com/microsoft/Oryx,,0
	2025-07-14T10:36:59.8881567Z,,,0
	2025-07-14T10:36:59.8977391Z,,,0
	2025-07-14T10:36:59.9148933Z,Using intermediate directory '/tmp/8ddc2c25b8dc163'.,,0
	2025-07-14T10:36:59.9213157Z,,,0
	2025-07-14T10:36:59.9273646Z,Copying files to the intermediate directory...,,0
	2025-07-14T10:37:01.4414036Z,Done in 2 sec(s).,,0
	2025-07-14T10:37:01.5182489Z,,,0
	2025-07-14T10:37:01.5246135Z,Source directory     : /tmp/8ddc2c25b8dc163,,0
	2025-07-14T10:37:01.5339400Z,Destination directory: /home/<USER>/wwwroot,,0
	2025-07-14T10:37:01.5494886Z,,,0
	2025-07-14T10:37:01.5561834Z,,,0
	2025-07-14T10:37:01.5666924Z,Downloading and extracting 'nodejs' version '20.19.3' to '/tmp/oryx/platforms/nodejs/20.19.3'...,,0
	2025-07-14T10:37:01.5776480Z,Detected image debian flavor: bookworm.,,0
	2025-07-14T10:37:01.5885769Z,Skipping download of nodejs version 20.19.3 as it is available in external sdk provider cache...,,0
	2025-07-14T10:37:01.5996074Z,Extracting contents...,,0
	2025-07-14T10:37:03.0682968Z,Successfully extracted nodejs version 20.19.3 from external sdk provider cache...,,0
	2025-07-14T10:37:03.1023010Z,Done in 2 sec(s).,,0
	2025-07-14T10:37:03.1303603Z,,,0
	2025-07-14T10:37:03.1404220Z,,,0
	2025-07-14T10:37:03.1494790Z,Downloading and extracting 'python' version '3.8.18' to '/tmp/oryx/platforms/python/3.8.18'...,,0
	2025-07-14T10:37:03.1668438Z,Detected image debian flavor: bookworm.,,0
	2025-07-14T10:37:03.1826733Z,Skipping download of python version 3.8.18 as it is available in external sdk provider cache...,,0
	2025-07-14T10:37:03.2014067Z,Extracting contents...,,0
	2025-07-14T10:37:04.9549129Z,Successfully extracted python version 3.8.18 from external sdk provider cache...,,0
	2025-07-14T10:37:04.9793540Z,Done in 1 sec(s).,,0
	2025-07-14T10:37:05.0018929Z,,,0
	2025-07-14T10:37:05.0842470Z,Removing existing manifest file,,0
	2025-07-14T10:37:05.1119707Z,Creating directory for command manifest file if it does not exist,,0
	2025-07-14T10:37:05.1238149Z,Creating a manifest file...,,0
	2025-07-14T10:37:05.1443622Z,Node Build Command Manifest file created.,,0
	2025-07-14T10:37:05.1515009Z,,,0
	2025-07-14T10:37:05.1577422Z,Using Node version:,,0
	2025-07-14T10:37:05.1689480Z,v20.19.3,,0
	2025-07-14T10:37:05.1760014Z,,,0
	2025-07-14T10:37:05.1825661Z,Using Npm version:,,0
	2025-07-14T10:37:05.2448691Z,10.8.2,,0
	2025-07-14T10:37:05.2622525Z,,,0
	2025-07-14T10:37:05.2711908Z,Running 'npm install'...,,0
	2025-07-14T10:37:05.2800321Z,,,0
	2025-07-14T10:37:06.7409087Z,(node:1057) Warning: Setting the NODE_TLS_REJECT_UNAUTHORIZED environment variable to '0' makes TLS connections and HTTPS requests insecure by disabling certificate verification.,,1
	2025-07-14T10:37:06.7692286Z,(Use `node --trace-warnings ...` to show where the warning was created),,1
	2025-07-14T10:38:26.4833254Z,,,0
	2025-07-14T10:38:26.4982660Z,added 993 packages&comma; and audited 994 packages in 1m,,0
	2025-07-14T10:38:26.5142103Z,,,0
	2025-07-14T10:38:26.5213741Z,208 packages are looking for funding,,0
	2025-07-14T10:38:26.5327692Z,  run `npm fund` for details,,0
	2025-07-14T10:38:26.5489892Z,,,0
	2025-07-14T10:38:26.5570067Z,2 moderate severity vulnerabilities,,0
	2025-07-14T10:38:26.5682889Z,,,0
	2025-07-14T10:38:26.5770218Z,Some issues need review&comma; and may require choosing,,0
	2025-07-14T10:38:26.5962406Z,a different dependency.,,0
	2025-07-14T10:38:26.6163526Z,,,0
	2025-07-14T10:38:26.6442640Z,Run `npm audit` for details.,,0
	2025-07-14T10:38:26.6607622Z,,,0
	2025-07-14T10:38:26.6830321Z,Running 'npm run build'...,,0
	2025-07-14T10:38:26.6960969Z,,,0
	2025-07-14T10:38:26.9456525Z,,,0
	2025-07-14T10:38:26.9772483Z,> dental-schedule-next@2.0.1 build,,0
	2025-07-14T10:38:27.0095461Z,> next build,,0
	2025-07-14T10:38:27.0598439Z,,,0
	2025-07-14T10:38:27.7543864Z,,,0
	2025-07-14T10:38:27.7719132Z,🔧 Environment Configuration Status (Enhanced with Job Queue Service):,,0
	2025-07-14T10:38:27.7973941Z,,,0
	2025-07-14T10:38:27.8056203Z,✅ Configured variables:,,0
	2025-07-14T10:38:27.8159243Z,   OPENAI_API_KEY (voice_processing),,0
	2025-07-14T10:38:27.8419615Z,   AZURE_STORAGE_CONNECTION_STRING (storage),,0
	2025-07-14T10:38:27.8494927Z,   SIKKA_API_KEY (dental_integration),,0
	2025-07-14T10:38:27.8600406Z,   AzureWebJobsStorage (azure_functions),,0
	2025-07-14T10:38:27.8674545Z,   JOB_QUEUE_MAX_RETRIES (job_queue),,0
	2025-07-14T10:38:27.8851690Z,,,0
	2025-07-14T10:38:27.9123095Z,⚠️  Missing optional variables (reduced functionality):,,0
	2025-07-14T10:38:27.9473873Z,   SIKKA_PRACTICE_ID (dental_integration): Sikka practice ID for patient data access,,0
	2025-07-14T10:38:27.9660163Z,   APPINSIGHTS_INSTRUMENTATIONKEY (monitoring): Application Insights key for monitoring,,0
	2025-07-14T10:38:28.0143821Z,   SENTRY_DSN (monitoring): Sentry DSN for error tracking,,0
	2025-07-14T10:38:28.0310753Z,   JOB_QUEUE_REDIS_URL (job_queue): Redis URL for job queue service (production),,0
	2025-07-14T10:38:28.0467353Z,,,0
	2025-07-14T10:38:28.0544132Z,🎉 All critical environment variables are configured!,,0
	2025-07-14T10:38:28.0674229Z,✨ Job Queue Service: Ready for reliable background processing,,0
	2025-07-14T10:38:28.0855607Z,🚀 Deployment Optimizations: Enabled for faster builds,,0
	2025-07-14T10:38:28.1007849Z,Attention: Next.js now collects completely anonymous telemetry regarding usage.,,0
	2025-07-14T10:38:28.1171215Z,This information is used to shape Next.js' roadmap and prioritize features.,,0
	2025-07-14T10:38:28.1316840Z,You can learn more&comma; including how to opt-out if you'd not like to participate in this anonymous program&comma; by visiting the following URL:,,0
	2025-07-14T10:38:28.1451143Z,https://nextjs.org/telemetry,,0
	2025-07-14T10:38:28.1514562Z,,,0
	2025-07-14T10:38:28.1716308Z,   ▲ Next.js 15.3.5,,0
	2025-07-14T10:38:28.1818869Z,,,0
	2025-07-14T10:38:28.1915958Z,   Creating an optimized production build ...,,0
	2025-07-14T10:38:28.2074278Z, ⚠ Invalid next.config.js options detected: ,,1
	2025-07-14T10:38:28.2209199Z, ⚠     Expected object&comma; received boolean at "experimental.serverActions",,1
	2025-07-14T10:38:28.2352293Z, ⚠     Unrecognized key(s) in object: 'serverComponentsExternalPackages'&comma; 'missingSuspenseWithCSRBailout'&comma; 'instrumentationHook' at "experimental",,1
	2025-07-14T10:38:28.2485767Z, ⚠     Unrecognized key(s) in object: 'swcMinify'&comma; 'validateEnvironment'&comma; 'validateConnectivity',,1
	2025-07-14T10:38:28.2599984Z, ⚠ See more info here: https://nextjs.org/docs/messages/invalid-next-config,,1
	2025-07-14T10:38:28.2794291Z, ⚠ The config property `experimental.turbo` is deprecated. Move this setting to `config.turbopack` as Turbopack is now stable.,,1
	2025-07-14T10:38:28.2921591Z, ⚠ `experimental.instrumentationHook` is no longer needed&comma; because `instrumentation.js` is available by default. You can remove it from next.config.js.,,1
	2025-07-14T10:38:28.3058562Z, ⚠ `experimental.serverComponentsExternalPackages` has been moved to `serverExternalPackages`. Please update your next.config.js file accordingly.,,1
	2025-07-14T10:38:28.3137499Z,(node:1180) Warning: Setting the NODE_TLS_REJECT_UNAUTHORIZED environment variable to '0' makes TLS connections and HTTPS requests insecure by disabling certificate verification.,,1
	2025-07-14T10:38:28.3201108Z,(Use `node --trace-warnings ...` to show where the warning was created),,1
	2025-07-14T10:38:55.3038212Z, ✓ Compiled successfully in 26.0s,,0
	2025-07-14T10:38:55.3517489Z,   Skipping validation of types,,0
	2025-07-14T10:38:55.3679732Z,   Skipping linting,,0
	2025-07-14T10:38:55.6207411Z,   Collecting page data ...,,0
	2025-07-14T10:38:56.6978704Z,(node:1223) Warning: Setting the NODE_TLS_REJECT_UNAUTHORIZED environment variable to '0' makes TLS connections and HTTPS requests insecure by disabling certificate verification.,,1
	2025-07-14T10:38:56.7181046Z,(Use `node --trace-warnings ...` to show where the warning was created),,1
	2025-07-14T10:38:59.1821426Z,   Generating static pages (0/96) ...,,0
	2025-07-14T10:39:00.5896408Z,   Generating static pages (24/96) ,,0
	2025-07-14T10:39:00.7936933Z,   Generating static pages (48/96) ,,0
	2025-07-14T10:39:00.9536968Z,(node:1234) Warning: Setting the NODE_TLS_REJECT_UNAUTHORIZED environment variable to '0' makes TLS connections and HTTPS requests insecure by disabling certificate verification.,,1
	2025-07-14T10:39:00.9900611Z,(Use `node --trace-warnings ...` to show where the warning was created),,1
	2025-07-14T10:39:01.1428636Z,   Generating static pages (72/96) ,,0
	2025-07-14T10:39:01.7299002Z, ✓ Generating static pages (96/96),,0
	2025-07-14T10:39:01.7454247Z,Failed to initialize Azure Table Storage: TypeError: Invalid URL,,1
	2025-07-14T10:39:01.7617890Z,    at a.ensureInitialized (.next/server/job-queue.js:1:337),,1
	2025-07-14T10:39:01.7795763Z,    at async a.listJobs (.next/server/job-queue.js:1:1516),,1
	2025-07-14T10:39:01.7927594Z,    at async a.getNextPendingJob (.next/server/job-queue.js:1:2556),,1
	2025-07-14T10:39:01.8036982Z,    at async o.processNextJob (.next/server/job-queue.js:1:9085),,1
	2025-07-14T10:39:01.8153441Z,    at async Timeout._onTimeout (.next/server/job-queue.js:3:2363) {,,1
	2025-07-14T10:39:01.8300238Z,  code: 'ERR_INVALID_URL'&comma;,,1
	2025-07-14T10:39:01.8518037Z,  input: 'DefaultEndpointsProtocol=https;AccountName=dentalrecordings;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net',,1
	2025-07-14T10:39:01.8903238Z,},,1
	2025-07-14T10:39:01.9020635Z,{"timestamp":"2025-07-14T10:39:01.671Z"&comma;"level":"error"&comma;"message":"Error in job-processing"&comma;"context":{"jobId":"processor"&comma;"filename":"queue"&comma;"errorCode":"UNKNOWN"&comma;"category":"system_error"&comma;"retryable":false}&comma;"correlationId":"*************-mrcuhs1tx"&comma;"metadata":{"version":"2.0.1"&comma;"environment":"production"&comma;"hostname":"dentalapp-kudu-5bd0b906"&comma;"pid":1223}&comma;"error":{"name":"Error"&comma;"message":"Azure Table Storage initialization failed"&comma;"stack":"Error: Azure Table Storage initialization failed\n    at a.ensureInitialized (/tmp/8ddc2c25b8dc163/.next/server/job-queue.js:1:514)\n    at async a.listJobs (/tmp/8ddc2c25b8dc163/.next/server/job-queue.js:1:1516)\n    at async a.getNextPendingJob (/tmp/8ddc2c25b8dc163/.next/server/job-queue.js:1:2556)\n    at async o.processNextJob (/tmp/8ddc2c25b8dc163/.next/server/job-queue.js:1:9085)\n    at async Timeout._onTimeout (/tmp/8ddc2c25b8dc163/.next/server/job-queue.js:3:2363)"}},,1
	2025-07-14T10:39:03.0495619Z,   Finalizing page optimization ...,,0
	2025-07-14T10:39:03.0682398Z,   Collecting build traces ...,,0
	2025-07-14T10:39:22.0554160Z,,,0
	2025-07-14T10:39:22.0728639Z,Route (app)                                   Size  First Load JS,,0
	2025-07-14T10:39:22.0832550Z,┌ ○ /                                      4.24 kB         226 kB,,0
	2025-07-14T10:39:22.0946495Z,├ ○ /_not-found                              196 B         219 kB,,0
	2025-07-14T10:39:22.1117660Z,├ ○ /ai-assistant                          2.08 kB         224 kB,,0
	2025-07-14T10:39:22.1256357Z,├ ƒ /api/ai/chat                             350 B         219 kB,,0
	2025-07-14T10:39:22.1353595Z,├ ƒ /api/appointments                        350 B         219 kB,,0
	2025-07-14T10:39:22.1531417Z,├ ƒ /api/appointments/[id]                   350 B         219 kB,,0
	2025-07-14T10:39:22.1645459Z,├ ƒ /api/cache-bust                          350 B         219 kB,,0
	2025-07-14T10:39:22.1760819Z,├ ƒ /api/clinical-notes                      350 B         219 kB,,0
	2025-07-14T10:39:22.1879849Z,├ ƒ /api/clinical-notes/professionalize      350 B         219 kB,,0
	2025-07-14T10:39:22.1993173Z,├ ƒ /api/clinical-notes/quick-summary        350 B         219 kB,,0
	2025-07-14T10:39:22.2072062Z,├ ƒ /api/debug-clinical-search               350 B         219 kB,,0
	2025-07-14T10:39:22.2198684Z,├ ƒ /api/debug-notes                         350 B         219 kB,,0
	2025-07-14T10:39:22.2311894Z,├ ƒ /api/debug/azure-connection              350 B         219 kB,,0
	2025-07-14T10:39:22.2483573Z,├ ƒ /api/debug/env                           350 B         219 kB,,0
	2025-07-14T10:39:22.2682532Z,├ ƒ /api/debug/system-status                 350 B         219 kB,,0
	2025-07-14T10:39:22.2821418Z,├ ƒ /api/dental-notes/generate               350 B         219 kB,,0
	2025-07-14T10:39:22.2929974Z,├ ƒ /api/deployment-info                     350 B         219 kB,,0
	2025-07-14T10:39:22.3028545Z,├ ƒ /api/manifest                            350 B         219 kB,,0
	2025-07-14T10:39:22.3128523Z,├ ƒ /api/medical-notes                       350 B         219 kB,,0
	2025-07-14T10:39:22.3244729Z,├ ƒ /api/note-types                          350 B         219 kB,,0
	2025-07-14T10:39:22.3425492Z,├ ƒ /api/openai/config                       350 B         219 kB,,0
	2025-07-14T10:39:22.3543186Z,├ ƒ /api/openai/models                       350 B         219 kB,,0
	2025-07-14T10:39:22.3669892Z,├ ƒ /api/operatories                         350 B         219 kB,,0
	2025-07-14T10:39:22.3757109Z,├ ƒ /api/patients/[id]                       350 B         219 kB,,0
	2025-07-14T10:39:22.3848481Z,├ ƒ /api/patients/[id]/clinical-notes        350 B         219 kB,,0
	2025-07-14T10:39:22.3937077Z,├ ƒ /api/patients/[id]/visit-count           350 B         219 kB,,0
	2025-07-14T10:39:22.4082006Z,├ ƒ /api/patients/[id]/visits                350 B         219 kB,,0
	2025-07-14T10:39:22.4183167Z,├ ƒ /api/patients/search                     350 B         219 kB,,0
	2025-07-14T10:39:22.4330980Z,├ ƒ /api/settings                            350 B         219 kB,,0
	2025-07-14T10:39:22.4463367Z,├ ƒ /api/sikka/appointments                  350 B         219 kB,,0
	2025-07-14T10:39:22.4555481Z,├ ƒ /api/test-azure-fix                      350 B         219 kB,,0
	2025-07-14T10:39:22.4642258Z,├ ƒ /api/test-clinical-direct                350 B         219 kB,,0
	2025-07-14T10:39:22.4809338Z,├ ƒ /api/test-sikka                          350 B         219 kB,,0
	2025-07-14T10:39:22.4905077Z,├ ƒ /api/tools/usb-portable/download         350 B         219 kB,,0
	2025-07-14T10:39:22.5001936Z,├ ƒ /api/tools/usb-portable/version          350 B         219 kB,,0
	2025-07-14T10:39:22.5112247Z,├ ƒ /api/tools/usb-transfer/download         350 B         219 kB,,0
	2025-07-14T10:39:22.5213854Z,├ ƒ /api/tools/usb-transfer/version          350 B         219 kB,,0
	2025-07-14T10:39:22.5307332Z,├ ƒ /api/voice/analyze-files                 350 B         219 kB,,0
	2025-07-14T10:39:22.5449529Z,├ ƒ /api/voice/auto-process                  350 B         219 kB,,0
	2025-07-14T10:39:22.5586533Z,├ ƒ /api/voice/auto-process-azure-direct     350 B         219 kB,,0
	2025-07-14T10:39:22.5691587Z,├ ƒ /api/voice/background-status             350 B         219 kB,,0
	2025-07-14T10:39:22.5779745Z,├ ƒ /api/voice/batch-process-robust          350 B         219 kB,,0
	2025-07-14T10:39:22.6191451Z,├ ƒ /api/voice/batch-summarize               350 B         219 kB,,0
	2025-07-14T10:39:22.6276607Z,├ ƒ /api/voice/batch-transcribe              350 B         219 kB,,0
	2025-07-14T10:39:22.6358148Z,├ ƒ /api/voice/batch-transcribe-all          350 B         219 kB,,0
	2025-07-14T10:39:22.6452146Z,├ ƒ /api/voice/cache-invalidate              350 B         219 kB,,0
	2025-07-14T10:39:22.6614032Z,├ ƒ /api/voice/cache-status                  350 B         219 kB,,0
	2025-07-14T10:39:22.6703181Z,├ ƒ /api/voice/copy-paste-mode               350 B         219 kB,,0
	2025-07-14T10:39:22.6874901Z,├ ƒ /api/voice/correct-date                  350 B         219 kB,,0
	2025-07-14T10:39:22.7063114Z,├ ƒ /api/voice/debug-status                  350 B         219 kB,,0
	2025-07-14T10:39:22.7195021Z,├ ƒ /api/voice/debug-transcribe              350 B         219 kB,,0
	2025-07-14T10:39:22.7297343Z,├ ƒ /api/voice/debug-transcription           350 B         219 kB,,0
	2025-07-14T10:39:22.7444607Z,├ ƒ /api/voice/generate-professional-note    350 B         219 kB,,0
	2025-07-14T10:39:22.7547605Z,├ ƒ /api/voice/job-monitor                   350 B         219 kB,,0
	2025-07-14T10:39:22.7630575Z,├ ƒ /api/voice/mark-copied                   350 B         219 kB,,0
	2025-07-14T10:39:22.7742212Z,├ ƒ /api/voice/match-recording               350 B         219 kB,,0
	2025-07-14T10:39:22.7817310Z,├ ƒ /api/voice/network-check                 350 B         219 kB,,0
	2025-07-14T10:39:22.7965199Z,├ ƒ /api/voice/next-file                     350 B         219 kB,,0
	2025-07-14T10:39:22.8064701Z,├ ƒ /api/voice/process-all                   350 B         219 kB,,0
	2025-07-14T10:39:22.8166745Z,├ ƒ /api/voice/process-batch-simple          350 B         219 kB,,0
	2025-07-14T10:39:22.8261356Z,├ ƒ /api/voice/process-single                350 B         219 kB,,0
	2025-07-14T10:39:22.8354281Z,├ ƒ /api/voice/process-test                  350 B         219 kB,,0
	2025-07-14T10:39:22.8437159Z,├ ƒ /api/voice/process-unified               350 B         219 kB,,0
	2025-07-14T10:39:22.8576968Z,├ ƒ /api/voice/processing-status             350 B         219 kB,,0
	2025-07-14T10:39:22.8698544Z,├ ƒ /api/voice/recorder-setup                350 B         219 kB,,0
	2025-07-14T10:39:22.9061225Z,├ ƒ /api/voice/recording-dates               350 B         219 kB,,0
	2025-07-14T10:39:22.9164798Z,├ ƒ /api/voice/recordings                    350 B         219 kB,,0
	2025-07-14T10:39:22.9402316Z,├ ƒ /api/voice/recordings-cache              350 B         219 kB,,0
	2025-07-14T10:39:22.9837688Z,├ ƒ /api/voice/recordings-fast               350 B         219 kB,,0
	2025-07-14T10:39:23.0020269Z,├ ƒ /api/voice/recordings/[id]               350 B         219 kB,,0
	2025-07-14T10:39:23.0180478Z,├ ƒ /api/voice/recordings/correct-date       350 B         219 kB,,0
	2025-07-14T10:39:23.0287824Z,├ ƒ /api/voice/smart-match                   350 B         219 kB,,0
	2025-07-14T10:39:23.0359254Z,├ ƒ /api/voice/smart-sort                    350 B         219 kB,,0
	2025-07-14T10:39:23.0527112Z,├ ƒ /api/voice/status                        350 B         219 kB,,0
	2025-07-14T10:39:23.0642959Z,├ ƒ /api/voice/summarize                     350 B         219 kB,,0
	2025-07-14T10:39:23.1101137Z,├ ƒ /api/voice/sync                          350 B         219 kB,,0
	2025-07-14T10:39:23.1225208Z,├ ƒ /api/voice/test-date-parsing             350 B         219 kB,,0
	2025-07-14T10:39:23.1283942Z,├ ƒ /api/voice/test-pipeline                 350 B         219 kB,,0
	2025-07-14T10:39:23.1342679Z,├ ƒ /api/voice/transcribe                    350 B         219 kB,,0
	2025-07-14T10:39:23.1398098Z,├ ƒ /api/voice/transcribe-all                350 B         219 kB,,0
	2025-07-14T10:39:23.1463633Z,├ ƒ /api/voice/transcribe-file               350 B         219 kB,,0
	2025-07-14T10:39:23.1525445Z,├ ƒ /api/voice/transcribe-stream             350 B         219 kB,,0
	2025-07-14T10:39:23.1593770Z,├ ƒ /api/voice/transcribe/progress           350 B         219 kB,,0
	2025-07-14T10:39:23.1652552Z,├ ƒ /api/voice/transcription-metadata        350 B         219 kB,,0
	2025-07-14T10:39:23.1717690Z,├ ƒ /api/voice/transfer                      350 B         219 kB,,0
	2025-07-14T10:39:23.1794266Z,├ ƒ /api/voice/transfer-stream               350 B         219 kB,,0
	2025-07-14T10:39:23.2297637Z,├ ƒ /api/voice/unmatch-recording             350 B         219 kB,,0
	2025-07-14T10:39:23.2497231Z,├ ƒ /api/voice/usb-setup                     350 B         219 kB,,0
	2025-07-14T10:39:23.2568610Z,├ ƒ /api/voice/webusb-upload                 350 B         219 kB,,0
	2025-07-14T10:39:23.2738185Z,├ ƒ /appointment/[appointmentId]           4.21 kB         230 kB,,0
	2025-07-14T10:39:23.2816048Z,├ ○ /cache-test                              296 B         219 kB,,0
	2025-07-14T10:39:23.2897610Z,├ ○ /debug-transcription                     301 B         219 kB,,0
	2025-07-14T10:39:23.2964152Z,├ ○ /operatories                           2.85 kB         225 kB,,0
	2025-07-14T10:39:23.3031873Z,├ ○ /patient-search                        4.42 kB         230 kB,,0
	2025-07-14T10:39:23.3098949Z,├ ƒ /patient/[id]                          2.48 kB         228 kB,,0
	2025-07-14T10:39:23.3154467Z,├ ○ /schedule                              6.11 kB         228 kB,,0
	2025-07-14T10:39:23.3352650Z,├ ○ /settings                              7.05 kB         229 kB,,0
	2025-07-14T10:39:23.3414769Z,├ ○ /test-webusb                             319 B         219 kB,,0
	2025-07-14T10:39:23.3482854Z,├ ○ /transcription-control                  4.7 kB         223 kB,,0
	2025-07-14T10:39:23.3678830Z,├ ○ /voice-workflow                        3.92 kB         226 kB,,0
	2025-07-14T10:39:23.3741687Z,└ ○ /webusb-transfer                       14.6 kB         236 kB,,0
	2025-07-14T10:39:23.3803547Z,+ First Load JS shared by all               219 kB,,0
	2025-07-14T10:39:23.3861719Z,  └ chunks/vendors-b9fa356fdf2388ff.js      217 kB,,0
	2025-07-14T10:39:23.3925152Z,  └ other shared chunks (total)            2.15 kB,,0
	2025-07-14T10:39:23.3981236Z,,,0
	2025-07-14T10:39:23.4050463Z,,,0
	2025-07-14T10:39:23.4117300Z,○  (Static)   prerendered as static content,,0
	2025-07-14T10:39:23.4217635Z,ƒ  (Dynamic)  server-rendered on demand,,0
	2025-07-14T10:39:23.4337702Z,,,0
	2025-07-14T10:39:23.4427734Z,,,0
	2025-07-14T10:39:23.4489937Z,Zipping existing node_modules folder...,,0
	2025-07-14T10:40:06.0710205Z,Done in 44 sec(s).,,0
	2025-07-14T10:40:06.0899254Z,Preparing output...,,0
	2025-07-14T10:40:06.1017572Z,,,0
	2025-07-14T10:40:06.1087302Z,Copying files to destination directory '/home/<USER>/wwwroot'...,,0
	2025-07-14T10:40:51.1416456Z,Done in 45 sec(s).,,0
	2025-07-14T10:40:51.1656230Z,,,0
	2025-07-14T10:40:51.1747304Z,Removing existing manifest file,,0
	2025-07-14T10:40:51.2781060Z,Creating a manifest file...,,0
	2025-07-14T10:40:51.2866805Z,Manifest file created.,,0
	2025-07-14T10:40:51.2967542Z,Copying .ostype to manifest output directory.,,0
	2025-07-14T10:40:51.3072339Z,,,0
	2025-07-14T10:40:51.3220241Z,Done in 232 sec(s).,,0
2025-07-14T10:40:51.6611270Z,Running post deployment command(s)...,14280a77-087b-457c-8358-b1364ef16adf,0
2025-07-14T10:40:51.7612072Z,,ab834424-37b8-4e04-ba18-0edc92931b6f,0
2025-07-14T10:40:51.8398718Z,Generating summary of Oryx build,92c9a637-1b52-412c-bdb7-927730503d1a,0
2025-07-14T10:40:51.9389212Z,Parsing the build logs,64f3bd84-2819-4771-91ba-c217330f432a,0
2025-07-14T10:40:52.0590732Z,Found 0 issue(s),d0b3aabf-01c3-4bda-81ac-b202846ffccb,0
2025-07-14T10:40:52.1633535Z,,4b2fa4fd-2489-4f9a-aab5-d7b6492c46ff,0
2025-07-14T10:40:52.2530835Z,Build Summary :,a08ce26c-3d43-4235-97f8-43f3297d59b3,0
2025-07-14T10:40:52.3517514Z,===============,eab850de-a4e5-487f-94fe-9d4773612b53,0
2025-07-14T10:40:52.4318094Z,Errors (0),4a8adf00-7fd3-4fd9-a4aa-5de0368ea7f3,0
2025-07-14T10:40:52.5474110Z,Warnings (0),3b8b27c9-52c0-4bba-91ff-d0f6be9d6520,0
2025-07-14T10:40:52.6555246Z,,6cea69a5-37ed-4f3c-a565-76df05f0a880,0
2025-07-14T10:40:52.7923731Z,Triggering container recycle for OneDeploy by adding/updating restartTrigger.txt to the site root path,78456b98-b804-49f9-87b3-96221193017c,0
2025-07-14T10:40:52.9501703Z,Deployment successful. deployer = OneDeploy deploymentPath = OneDeploy,1765aaf2-3a96-4d38-8492-7d13f4cc291e,0
