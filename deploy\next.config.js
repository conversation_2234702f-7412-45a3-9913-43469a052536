/** @type {import('next').NextConfig} */

/**
 * Environment validation for critical voice processing functionality
 * Ensures required environment variables are available at build time
 */
function validateEnvironment() {
  const requiredVars = {
    // Critical for voice processing pipeline
    'OPENAI_API_KEY': {
      required: true,
      description: 'OpenAI API key for transcription and summarization',
      category: 'voice_processing'
    },
    'AZURE_STORAGE_CONNECTION_STRING': {
      required: true,
      description: 'Azure Storage connection string for voice recordings',
      category: 'storage'
    },
    
    // Important for dental practice integration
    'SIKKA_API_KEY': {
      required: false, // Optional but recommended
      description: 'Sikka API key for dental practice management integration',
      category: 'dental_integration'
    },
    'SIKKA_PRACTICE_ID': {
      required: false,
      description: 'Sikka practice ID for patient data access',
      category: 'dental_integration'
    },
    
    // Monitoring and error tracking (recommended but not critical)
    'APPINSIGHTS_INSTRUMENTATIONKEY': {
      required: false,
      description: 'Application Insights key for monitoring',
      category: 'monitoring'
    },
    'SENTRY_DSN': {
      required: false,
      description: 'Sentry DSN for error tracking',
      category: 'monitoring'
    },
    
    // Azure Functions configuration
    'AzureWebJobsStorage': {
      required: false, // Only required for Azure Functions deployment
      description: 'Azure WebJobs storage for Azure Functions',
      category: 'azure_functions'
    },
    
    // Job queue service configuration
    'JOB_QUEUE_REDIS_URL': {
      required: false, // Falls back to in-memory for development
      description: 'Redis URL for job queue service (production)',
      category: 'job_queue'
    },
    'JOB_QUEUE_MAX_RETRIES': {
      required: false,
      description: 'Maximum retry attempts for failed jobs',
      category: 'job_queue'
    }
  };
  
  const missing = [];
  const warnings = [];
  const configured = [];
  
  // Check each environment variable
  Object.entries(requiredVars).forEach(([varName, config]) => {
    const value = process.env[varName];
    const hasValue = value && value.trim() !== '' && !value.includes('your_') && !value.includes('_here');
    
    if (config.required && !hasValue) {
      missing.push({
        name: varName,
        description: config.description,
        category: config.category
      });
    } else if (!config.required && !hasValue) {
      warnings.push({
        name: varName,
        description: config.description,
        category: config.category
      });
    } else if (hasValue) {
      configured.push({
        name: varName,
        description: config.description,
        category: config.category
      });
    }
  });
  
  // Log configuration status
  console.log('\n🔧 Environment Configuration Status (Enhanced with Job Queue Service):');
  
  if (configured.length > 0) {
    console.log('\n✅ Configured variables:');
    configured.forEach(({ name, category }) => {
      console.log(`   ${name} (${category})`);
    });
  }
  
  if (warnings.length > 0) {
    console.log('\n⚠️  Missing optional variables (reduced functionality):');
    warnings.forEach(({ name, description, category }) => {
      console.log(`   ${name} (${category}): ${description}`);
    });
  }
  
  if (missing.length > 0) {
    console.log('\n❌ Missing required variables:');
    missing.forEach(({ name, description, category }) => {
      console.log(`   ${name} (${category}): ${description}`);
    });
    
    console.log('\n🚨 CRITICAL: Voice processing will not work without these variables!');
    console.log('📖 See .env.sample for configuration guidance.');
    console.log('🔗 Documentation: VOICE_PROCESSING_RECOVERY_GUIDE.md\n');
    
    // In development, continue with warnings. In production, this could be fatal.
    if (process.env.NODE_ENV === 'production') {
      console.log('🛑 Production build requires all critical environment variables.');
      console.log('   Set missing variables and rebuild.');
      // For deployment validation, exit with error code to prevent deployment
      if (process.env.DEPLOYMENT_VALIDATION === 'true') {
        console.log('🚨 Deployment validation failed due to missing environment variables.');
        process.exit(1);
      }
    }
  } else {
    console.log('\n🎉 All critical environment variables are configured!');
    console.log('✨ Job Queue Service: Ready for reliable background processing');
    console.log('🚀 Deployment Optimizations: Enabled for faster builds');
  }
  
  // Enhanced validation results with deployment metadata
  return {
    isValid: missing.length === 0,
    missing,
    warnings,
    configured,
    summary: {
      total: Object.keys(requiredVars).length,
      configured: configured.length,
      missing: missing.length,
      warnings: warnings.length
    },
    deployment: {
      ready: missing.length === 0,
      timestamp: new Date().toISOString(),
      buildId: process.env.BUILD_ID || 'dev',
      nodeEnv: process.env.NODE_ENV || 'development'
    }
  };
}

/**
 * Validate connectivity for critical services (async check)
 */
function validateConnectivity() {
  // This will be used by the validation script for runtime checks
  const services = {
    openai: {
      name: 'OpenAI API',
      check: 'https://api.openai.com/v1/models',
      envVar: 'OPENAI_API_KEY'
    },
    azure_storage: {
      name: 'Azure Storage',
      check: 'connection_string',
      envVar: 'AZURE_STORAGE_CONNECTION_STRING'
    },
    sikka: {
      name: 'Sikka API',
      check: 'https://api.sikka.com/v1/health',
      envVar: 'SIKKA_API_KEY'
    }
  };
  
  return services;
}

// Run validation during build
const validationResult = validateEnvironment();

// Make validation results available to the application with deployment info
process.env.NEXT_PUBLIC_ENV_VALIDATION = JSON.stringify({
  timestamp: new Date().toISOString(),
  ...validationResult.summary,
  deploymentReady: validationResult.deployment.ready,
  buildId: validationResult.deployment.buildId
});

// Add deployment validation check
if (process.env.DEPLOYMENT_VALIDATION === 'true' && !validationResult.isValid) {
  console.error('🚨 Deployment validation failed: Missing required environment variables');
  process.exit(1);
}

const nextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  output: 'standalone',
  serverExternalPackages: ['better-sqlite3', 'sqlite3'],
  trailingSlash: false,
  reactStrictMode: false,
  pageExtensions: ['ts', 'tsx', 'js', 'jsx'],
  distDir: '.next',
  
  // Enhanced performance optimizations
  images: {
    unoptimized: true,
    // Optimize image loading for dental practice photos
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  
  // Build performance optimizations
  swcMinify: true,
  compiler: {
    // Remove console logs in production
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
  },
  
  experimental: {
    serverComponentsExternalPackages: ['better-sqlite3', 'sqlite3'],
    missingSuspenseWithCSRBailout: false,
    // Optimize job queue service performance
    instrumentationHook: true,
    // Improve build cache performance
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
    // Optimize server actions for job processing
    serverActions: true,
    // Improve bundling for deployment
    optimizePackageImports: ['lucide-react', '@radix-ui/react-progress'],
  },
  webpack: (config, { isServer, dev, webpack }) => {
    // Force POSIX paths for cross-platform compatibility
    config.resolve = config.resolve || {};
    config.resolve.symlinks = false;
    
    // Force POSIX path separators for Linux deployment
    const path = require('path');
    const originalJoin = path.join;
    path.join = (...args) => originalJoin(...args).replace(/\\/g, '/');
    
    // Override path separator for chunk naming
    if (config.output) {
      config.output.filename = config.output.filename?.replace(/\\/g, '/');
      config.output.chunkFilename = config.output.chunkFilename?.replace(/\\/g, '/');
      // Force POSIX paths in output
      config.output.path = config.output.path?.replace(/\\/g, '/');
    }
    
    // Server-side optimizations
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push('better-sqlite3');
      
      // Optimize job queue service dependencies
      config.externals.push({
        '@azure/data-tables': 'commonjs @azure/data-tables',
        '@azure/storage-blob': 'commonjs @azure/storage-blob',
      });
    }
    
    // Production optimizations
    if (!dev) {
      // Split chunks for better caching
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all',
          },
          // Separate chunk for dental practice APIs
          dentalAPIs: {
            test: /[\\/]src[\\/]lib[\\/](sikka-api|azure-storage|ai-service)/,
            name: 'dental-apis',
            priority: 5,
            chunks: 'all',
          },
          // Separate chunk for job queue service
          jobQueue: {
            test: /[\\/]src[\\/]lib[\\/](job-queue-service|job-persistence)/,
            name: 'job-queue',
            priority: 10,
            chunks: 'all',
          },
        },
      };
      
      // Enable production optimizations
      config.plugins.push(
        new webpack.DefinePlugin({
          'process.env.NODE_ENV': JSON.stringify('production'),
          'process.env.NEXT_TELEMETRY_DISABLED': JSON.stringify('1'),
        })
      );
    }
    
    // Better handling of TypeScript and ES modules
    config.resolve = {
      ...config.resolve,
      extensionAlias: {
        '.js': ['.js', '.ts'],
        '.jsx': ['.jsx', '.tsx'],
      },
    };
    
    return config;
  },
  // Enhanced environment configuration with job queue service support
  env: {
    // Application metadata with deployment optimization markers
    NEXT_PUBLIC_APP_VERSION: process.env.npm_package_version || '2.0.1',
    NEXT_PUBLIC_BUILD_DATE: (() => {
      try {
        const now = new Date();
        return isNaN(now.getTime()) ? '2025-01-01' : now.toISOString().split('T')[0];
      } catch (error) {
        console.warn('Error generating build date:', error);
        return '2025-01-01';
      }
    })(),
    NEXT_PUBLIC_COMMIT_HASH: process.env.GITHUB_SHA || process.env.VERCEL_GIT_COMMIT_SHA || 'dev',
    
    // Enhanced system capabilities with deployment validation
    NEXT_PUBLIC_JOB_QUEUE_SERVICE_ENABLED: 'true',
    NEXT_PUBLIC_DEPLOYMENT_OPTIMIZATION_ENABLED: 'true',
    NEXT_PUBLIC_BUILD_CACHE_VERSION: 'v3',
    NEXT_PUBLIC_RELIABILITY_IMPROVEMENTS: 'enabled',
    NEXT_PUBLIC_DEPLOYMENT_VALIDATION_ENABLED: 'true',
    NEXT_PUBLIC_SOURCE_PUSH_DEPLOYMENT: 'enabled',
    
    // Feature flags based on environment availability
    NEXT_PUBLIC_VOICE_PROCESSING_ENABLED: validationResult.configured.some(c => c.name === 'OPENAI_API_KEY') ? 'true' : 'false',
    NEXT_PUBLIC_AZURE_STORAGE_ENABLED: validationResult.configured.some(c => c.name === 'AZURE_STORAGE_CONNECTION_STRING') ? 'true' : 'false',
    NEXT_PUBLIC_SIKKA_INTEGRATION_ENABLED: validationResult.configured.some(c => c.name === 'SIKKA_API_KEY') ? 'true' : 'false',
    NEXT_PUBLIC_MONITORING_ENABLED: validationResult.configured.some(c => c.category === 'monitoring') ? 'true' : 'false',
    
    // Performance and optimization flags with deployment enhancements
    NEXT_PUBLIC_PERFORMANCE_OPTIMIZATIONS: 'enabled',
    NEXT_PUBLIC_HTTP_COMPRESSION: 'enabled',
    NEXT_PUBLIC_ASSET_OPTIMIZATION: 'enabled',
    NEXT_PUBLIC_CROSS_PLATFORM_COMPATIBILITY: 'enabled',
    NEXT_PUBLIC_PATH_NORMALIZATION: 'posix',
  },
  
  // Enhanced headers for better performance and security
  async headers() {
    return [
      {
        source: '/api/voice/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload',
          },
        ],
      },
    ];
  },
};

// Export validation utilities for use by deployment scripts
nextConfig.validateEnvironment = validateEnvironment;
nextConfig.validateConnectivity = validateConnectivity;

module.exports = nextConfig;
