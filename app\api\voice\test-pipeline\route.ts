import { NextRequest, NextResponse } from 'next/server';
import { AzureStorageService } from '../../../../src/lib/azure-storage';

export async function GET(request: NextRequest) {
  const testResults = {
    azureStorage: 'checking...',
    openaiKey: 'checking...',
    backgroundProcessing: 'checking...',
    apiEndpoints: 'checking...'
  };

  try {
    // Test 1: Azure Storage Configuration
    if (AzureStorageService.isConfigured()) {
      try {
        // Try to list files to test connection
        const files = await AzureStorageService.listFiles('');
        testResults.azureStorage = `✅ Connected (${files.length} files found)`;
      } catch (error) {
        testResults.azureStorage = `❌ Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      }
    } else {
      testResults.azureStorage = `❌ Not configured - missing connection string`;
    }

    // Test 2: OpenAI API Key
    if (process.env.OPENAI_API_KEY) {
      testResults.openaiKey = `✅ OpenAI API key is configured`;
    } else {
      testResults.openaiKey = `❌ OpenAI API key missing`;
    }

    // Test 3: Background Processing Environment
    const environment = {
      nodeEnv: process.env.NODE_ENV,
      vercelUrl: process.env.VERCEL_URL,
      websiteHostname: process.env.WEBSITE_HOSTNAME,
      nextPublicApiUrl: process.env.NEXT_PUBLIC_API_URL
    };

    const baseUrl = process.env.VERCEL_URL 
      ? `https://${process.env.VERCEL_URL}`
      : process.env.WEBSITE_HOSTNAME 
      ? `https://${process.env.WEBSITE_HOSTNAME}`
      : 'http://localhost:3000';

    testResults.backgroundProcessing = `Environment: ${JSON.stringify(environment, null, 2)}\nComputed base URL: ${baseUrl}`;

    // Test 4: API Endpoint Connectivity
    try {
      const testResponse = await fetch(`${baseUrl}/api/voice/recordings`, {
        method: 'GET',
        headers: {
          'X-Internal-Call': 'true'
        }
      });

      if (testResponse.ok) {
        const data = await testResponse.json();
        testResults.apiEndpoints = `✅ API reachable (${data.recordings?.length || 0} recordings)`;
      } else {
        testResults.apiEndpoints = `❌ API call failed: ${testResponse.status} ${testResponse.statusText}`;
      }
    } catch (error) {
      testResults.apiEndpoints = `❌ API call error: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      testResults,
      recommendations: generateRecommendations(testResults)
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      testResults
    }, { status: 500 });
  }
}

function generateRecommendations(testResults: any): string[] {
  const recommendations = [];

  if (testResults.azureStorage.includes('❌')) {
    recommendations.push('Fix Azure Storage configuration by setting AZURE_STORAGE_CONNECTION_STRING environment variable');
  }

  if (testResults.openaiKey.includes('❌')) {
    recommendations.push('Set OPENAI_API_KEY environment variable in Azure App Service configuration');
  }

  if (testResults.apiEndpoints.includes('❌')) {
    recommendations.push('Background processing may fail due to API connectivity issues - check network configuration');
  }

  if (recommendations.length === 0) {
    recommendations.push('All tests passed! The processing pipeline should work correctly.');
  }

  return recommendations;
}