import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { filename } = await request.json();

    if (!filename) {
      return NextResponse.json({ error: 'Filename is required' }, { status: 400 });
    }

    console.log(`🎤 Processing single recording: ${filename}`);

    const baseUrl = process.env.VERCEL_URL 
      ? `https://${process.env.VERCEL_URL}`
      : process.env.NEXT_PUBLIC_API_URL
      ? process.env.NEXT_PUBLIC_API_URL
      : 'http://localhost:3000';

    // Step 1: Transcribe
    console.log(`📝 Transcribing: ${filename}`);
    const transcribeResponse = await fetch(`${baseUrl}/api/voice/transcribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Internal-Call': 'true'
      },
      body: JSON.stringify({
        fileName: filename,
        recordingId: filename,
        forceRetranscribe: false
      })
    });

    const transcribeResult = await transcribeResponse.json();
    
    if (!transcribeResponse.ok) {
      // Check if it's a rate limit or quota issue
      if (transcribeResult.error?.includes('rate limit') || transcribeResult.error?.includes('quota')) {
        throw new Error(`OpenAI API rate limit exceeded. Please wait a few minutes and try again.`);
      }
      throw new Error(`Transcription failed: ${transcribeResult.error}`);
    }

    console.log(`✅ Transcription completed: ${transcribeResult.transcription?.length || 0} characters`);

    // Step 2: Summarize
    console.log(`📋 Summarizing: ${filename}`);
    const summarizeResponse = await fetch(`${baseUrl}/api/voice/summarize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Internal-Call': 'true'
      },
      body: JSON.stringify({
        recordingId: filename,
        transcription: transcribeResult.transcription,
        summaryType: 'visit-summary',
        promptId: 'dental-visit-summary'
      })
    });

    const summarizeResult = await summarizeResponse.json();
    
    if (!summarizeResponse.ok) {
      throw new Error(`Summarization failed: ${summarizeResult.error}`);
    }

    console.log(`✅ Summary completed: ${summarizeResult.summary?.length || 0} characters`);

    return NextResponse.json({
      success: true,
      filename,
      transcription: transcribeResult.transcription,
      summary: summarizeResult.summary,
      transcriptionLength: transcribeResult.transcription?.length || 0,
      summaryLength: summarizeResult.summary?.length || 0
    });

  } catch (error) {
    console.error('Single processing error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      details: 'Failed to process recording'
    }, { status: 500 });
  }
}