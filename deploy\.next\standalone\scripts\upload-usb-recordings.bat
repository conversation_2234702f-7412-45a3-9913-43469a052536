@echo off
REM USB Dental Recordings Upload - Batch Wrapper
REM This batch file provides easy access to the PowerShell upload script

echo.
echo ========================================
echo   USB Dental Recordings Upload Tool
echo ========================================
echo.

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PowerShell is not available or accessible
    echo Please ensure PowerShell is installed and accessible
    pause
    exit /b 1
)

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"
set "PS_SCRIPT=%SCRIPT_DIR%Upload-USBRecordings.ps1"

REM Check if PowerShell script exists
if not exist "%PS_SCRIPT%" (
    echo ERROR: PowerShell script not found at: %PS_SCRIPT%
    echo Please ensure Upload-USBRecordings.ps1 is in the same directory
    pause
    exit /b 1
)

echo Available options:
echo.
echo 1. Upload all .mp3 files from USB drives
echo 2. Dry run (show what would be uploaded)
echo 3. Upload from specific drive letter
echo 4. Force upload (overwrite existing files)
echo 5. Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" (
    echo.
    echo Starting upload of all .mp3 files from detected USB drives...
    powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%"
) else if "%choice%"=="2" (
    echo.
    echo Starting dry run - showing what would be uploaded...
    powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -WhatIf
) else if "%choice%"=="3" (
    set /p drive="Enter drive letter (e.g., E:): "
    echo.
    echo Starting upload from drive %drive%...
    powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -USBDriveLetter "%drive%"
) else if "%choice%"=="4" (
    echo.
    echo Starting force upload (will overwrite existing files)...
    powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Force
) else if "%choice%"=="5" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice. Please run the script again.
    pause
    exit /b 1
)

echo.
echo Upload process completed.
echo Check the output above for results.
echo.
pause
