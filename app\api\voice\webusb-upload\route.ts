import { NextRequest, NextResponse } from 'next/server';
import { TranscriptionService } from '../../../../src/lib/transcription-service';
import path from 'path';

interface UploadProgress {
  fileName: string;
  progress: number;
  status: 'pending' | 'transcribing' | 'completed' | 'error' | 'skipped';
  error?: string;
  transcriptionId?: string;
  transcriptionText?: string;
  summaryText?: string;
  confidenceScore?: number;
  size?: number;
  skipReason?: string;
}

interface BulkUploadResult {
  success: boolean;
  totalFiles: number;
  successfulUploads: number;
  skippedUploads: number;
  failedUploads: number;
  results: UploadProgress[];
  transcriptionSummary: {
    totalTranscriptions: number;
    averageConfidence: number;
    processingTimeMs: number;
  };
  archiveInfo?: {
    enabled: boolean;
    message: string;
  };
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const deviceId = (formData.get('deviceId') as string) || 'webusb-upload';
    const archiveEnabled = formData.get('archiveEnabled') === 'true';
    const patientId = formData.get('patientId') as string | undefined;
    const userId = (formData.get('userId') as string) || 'webusb-user';

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No audio files provided for transcription' },
        { status: 400 }
      );
    }

    console.log(`🚀 Starting WebUSB transcription workflow: ${files.length} files`);

    // Check OpenAI configuration (replaces Azure Storage check)
    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        {
          error: 'OpenAI API key not configured',
          details: 'WebUSB transcription requires OPENAI_API_KEY environment variable.',
        },
        { status: 500 }
      );
    }

    const uploadResults: UploadProgress[] = [];
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'webusb-client';

    // Process each file with immediate transcription
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileName = file.name;

      console.log(`🎤 Processing file ${i + 1}/${files.length}: ${fileName}`);

      try {
        // Read file data
        const arrayBuffer = await file.arrayBuffer();
        const audioBuffer = Buffer.from(arrayBuffer);

        // Basic size validation (25MB limit for OpenAI Whisper)
        const maxSize = 25 * 1024 * 1024;
        if (audioBuffer.length > maxSize) {
          uploadResults.push({
            fileName,
            progress: 0,
            status: 'error',
            error: `File too large: ${Math.round(audioBuffer.length / 1024 / 1024)}MB (max 25MB)`,
            size: audioBuffer.length,
          });
          continue;
        }

        // Check for duplicate transcriptions based on filename
        // Simple duplicate detection - could be enhanced
        const existingResults = uploadResults.find(r => r.fileName === fileName);
        if (existingResults) {
          uploadResults.push({
            fileName,
            progress: 100,
            status: 'skipped',
            skipReason: 'Duplicate filename in current batch',
            size: audioBuffer.length,
          });
          console.log(`⏭️ Skipped ${fileName}: Duplicate in current batch`);
          continue;
        }

        // Update progress to transcribing
        uploadResults.push({
          fileName,
          progress: 25,
          status: 'transcribing',
          size: audioBuffer.length,
        });

        console.log(`🎤 Transcribing audio: ${fileName}`);

        // Process audio: transcribe → summarize → store (no file storage!)
        const transcriptionResult = await TranscriptionService.processAudioFile(
          audioBuffer,
          {
            filename: fileName,
            deviceId,
            patientId,
            userId,
            ipAddress: clientIP,
            retainAudio: false // WebUSB default: don't retain audio
          }
        );

        // Update result based on transcription outcome
        const resultIndex = uploadResults.findIndex(r => r.fileName === fileName && r.status === 'transcribing');
        
        if (transcriptionResult.success) {
          uploadResults[resultIndex] = {
            fileName,
            progress: 100,
            status: 'completed',
            transcriptionId: transcriptionResult.transcriptionId!,
            transcriptionText: transcriptionResult.transcriptionText!,
            summaryText: transcriptionResult.summaryText!,
            confidenceScore: transcriptionResult.confidenceScore!,
            size: audioBuffer.length,
          };
          console.log(`✅ Successfully transcribed: ${fileName} (ID: ${transcriptionResult.transcriptionId})`);
          
          if (transcriptionResult.confidenceScore && transcriptionResult.confidenceScore < 0.8) {
            console.log(`⚠️ Low confidence (${Math.round(transcriptionResult.confidenceScore * 100)}%) - may require review`);
          }
        } else {
          uploadResults[resultIndex] = {
            fileName,
            progress: 0,
            status: 'error',
            error: transcriptionResult.error || 'Transcription failed',
            size: audioBuffer.length,
          };
          console.log(`❌ Failed to transcribe: ${fileName} - ${transcriptionResult.error}`);
        }

      } catch (error) {
        console.error(`❌ Processing error for ${fileName}:`, error);
        
        const existingIndex = uploadResults.findIndex(r => r.fileName === fileName);
        const errorResult: UploadProgress = {
          fileName,
          progress: 0,
          status: 'error',
          error: error instanceof Error ? error.message : 'Processing failed',
          size: 0,
        };

        if (existingIndex >= 0) {
          uploadResults[existingIndex] = errorResult;
        } else {
          uploadResults.push(errorResult);
        }
      }
    }

    // Calculate results
    const successfulUploads = uploadResults.filter(r => r.status === 'completed');
    const skippedUploads = uploadResults.filter(r => r.status === 'skipped');
    const failedUploads = uploadResults.filter(r => r.status === 'error');

    // Calculate transcription metrics
    const processingTimeMs = Date.now() - startTime;
    const confidenceScores = successfulUploads
      .map(r => r.confidenceScore)
      .filter((score): score is number => score !== undefined);
    
    const averageConfidence = confidenceScores.length > 0 
      ? confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length
      : 0;

    console.log(
      `📊 Transcription summary: ${successfulUploads.length}/${files.length} successful, ${skippedUploads.length} skipped, ${failedUploads.length} failed (${processingTimeMs}ms)`
    );

    // Log successful transcription details
    successfulUploads.forEach(result => {
      const preview = result.transcriptionText?.substring(0, 100) || '';
      console.log(`   ✅ ${result.fileName}: "${preview}${preview.length >= 100 ? '...' : ''}"`);
      if (result.confidenceScore && result.confidenceScore < 0.8) {
        console.log(`      ⚠️ Low confidence: ${Math.round(result.confidenceScore * 100)}%`);
      }
    });

    // Log skipped file details
    skippedUploads.forEach(skip => {
      console.log(`   ⏭️ Skipped ${skip.fileName}: ${skip.skipReason}`);
    });

    // Log failed transcription details
    failedUploads.forEach(failed => {
      console.log(`   ❌ ${failed.fileName}: ${failed.error}`);
    });

    // Build response
    const response: BulkUploadResult = {
      success: successfulUploads.length > 0 || skippedUploads.length > 0,
      totalFiles: files.length,
      successfulUploads: successfulUploads.length,
      skippedUploads: skippedUploads.length,
      failedUploads: failedUploads.length,
      results: uploadResults,
      transcriptionSummary: {
        totalTranscriptions: successfulUploads.length,
        averageConfidence: Math.round(averageConfidence * 100) / 100, // Round to 2 decimal places
        processingTimeMs,
      },
      archiveInfo: {
        enabled: archiveEnabled,
        message: archiveEnabled
          ? 'Files will be archived on USB drive after transcription'
          : 'USB archiving disabled - transcriptions stored in database only',
      },
    };

    console.log('🎯 WebUSB transcription workflow complete', {
      successful: successfulUploads.length,
      skipped: skippedUploads.length,
      failed: failedUploads.length,
      averageConfidence: Math.round(averageConfidence * 100),
      processingTimeMs,
      archiveEnabled,
    });

    return NextResponse.json(response);
  } catch (error) {
    const processingTimeMs = Date.now() - startTime;
    console.error('WebUSB transcription workflow error:', error);
    return NextResponse.json(
      {
        error: 'WebUSB transcription failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        processingTimeMs,
      },
      { status: 500 }
    );
  }
}

// Streaming upload endpoint for real-time progress
export async function PUT(request: NextRequest) {
  return NextResponse.json({ message: "PUT request received" });
}