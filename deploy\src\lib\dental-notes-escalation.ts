/**
 * Dental Notes AI Escalation System
 * Implements the two-tier AI system with automatic escalation logic
 */

export interface EscalationResult {
  shouldEscalate: boolean;
  reason: string;
  confidence: number;
  triggers: string[];
}

export interface DentalNoteMetadata {
  wordCount: number;
  procedureCount: number;
  diagnosisCount: number;
  hasComplexProcedures: boolean;
  hasMedicalHistory: boolean;
  hasContraindications: boolean;
  riskFactors: string[];
}

export interface ProcessingResult {
  finalNote: string;
  status: 'OK' | 'NEEDS_HUMAN_REVIEW' | 'ERROR';
  escalated: boolean;
  processingTime: number;
  metadata: DentalNoteMetadata;
  escalationReason?: string;
  reviewComments?: string;
  riskFactors?: string[];
}

// High-risk procedure keywords that trigger automatic escalation
const HIGH_RISK_PROCEDURES = [
  'sedation', 'extraction', 'sinus', 'implant', 'surgery', 'surgical',
  'bone graft', 'membrane', 'flap', 'periodontal surgery', 'endodontic surgery',
  'apicoectomy', 'hemisection', 'root amputation', 'crown lengthening',
  'sinus lift', 'ridge augmentation', 'socket preservation'
];

// Medical complexity indicators
const MEDICAL_COMPLEXITY_INDICATORS = [
  'anticoagulant', 'warfarin', 'coumadin', 'plavix', 'eliquis',
  'diabetes', 'hypertension', 'heart condition', 'cardiac',
  'bisphosphonate', 'fosamax', 'boniva', 'prolia',
  'chemotherapy', 'radiation', 'immunocompromised',
  'pregnancy', 'pregnant', 'nursing', 'breastfeeding'
];

// Legal/documentation red flags
const LEGAL_RED_FLAGS = [
  'refused', 'declined', 'against medical advice', 'ama',
  'complication', 'adverse', 'reaction', 'emergency',
  'pain', 'severe pain', 'numbness', 'paresthesia',
  'infection', 'swelling', 'bleeding', 'dry socket'
];

/**
 * Analyzes note content and determines if escalation is needed
 */
export function analyzeForEscalation(
  draftNote: string,
  escalationFlag: 'LOW' | 'MEDIUM' | 'HIGH',
  voiceNotes?: string
): EscalationResult {
  const triggers: string[] = [];
  let shouldEscalate = false;
  let confidence = 0.7;
  let reason = '';

  const noteText = (draftNote + ' ' + (voiceNotes || '')).toLowerCase();

  // Check AI-generated escalation flag
  if (escalationFlag === 'HIGH') {
    shouldEscalate = true;
    triggers.push('AI flagged as HIGH risk');
    reason = 'Primary AI identified high-risk factors';
    confidence = 0.9;
  } else if (escalationFlag === 'MEDIUM') {
    shouldEscalate = true;
    triggers.push('AI flagged as MEDIUM risk');
    reason = 'Primary AI identified moderate complexity';
    confidence = 0.8;
  }

  // Check for high-risk procedures
  const foundProcedures = HIGH_RISK_PROCEDURES.filter(proc => 
    noteText.includes(proc.toLowerCase())
  );
  if (foundProcedures.length > 0) {
    shouldEscalate = true;
    triggers.push(`High-risk procedures: ${foundProcedures.join(', ')}`);
    reason = reason || 'High-risk procedures detected';
    confidence = Math.max(confidence, 0.85);
  }

  // Check for medical complexity
  const foundComplexity = MEDICAL_COMPLEXITY_INDICATORS.filter(indicator =>
    noteText.includes(indicator.toLowerCase())
  );
  if (foundComplexity.length > 0) {
    shouldEscalate = true;
    triggers.push(`Medical complexity: ${foundComplexity.join(', ')}`);
    reason = reason || 'Medical complexity indicators found';
    confidence = Math.max(confidence, 0.8);
  }

  // Check for legal red flags
  const foundRedFlags = LEGAL_RED_FLAGS.filter(flag =>
    noteText.includes(flag.toLowerCase())
  );
  if (foundRedFlags.length > 0) {
    shouldEscalate = true;
    triggers.push(`Legal concerns: ${foundRedFlags.join(', ')}`);
    reason = reason || 'Legal/documentation red flags detected';
    confidence = Math.max(confidence, 0.95);
  }

  // Check note length (very long notes may indicate complexity)
  if (draftNote.length > 1500) {
    shouldEscalate = true;
    triggers.push('Unusually long note (>1500 characters)');
    reason = reason || 'Note length indicates complexity';
    confidence = Math.max(confidence, 0.75);
  }

  // Check for multiple diagnoses (count "D" codes)
  const diagnosisCount = (draftNote.match(/\bD\d{4}/g) || []).length;
  if (diagnosisCount > 2) {
    shouldEscalate = true;
    triggers.push(`Multiple diagnoses (${diagnosisCount} D-codes)`);
    reason = reason || 'Multiple diagnoses require review';
    confidence = Math.max(confidence, 0.8);
  }

  return {
    shouldEscalate,
    reason: reason || 'Low complexity case',
    confidence,
    triggers
  };
}

/**
 * Extracts metadata from the dental note for analysis
 */
export function extractNoteMetadata(noteText: string): DentalNoteMetadata {
  const text = noteText.toLowerCase();
  
  return {
    wordCount: noteText.split(/\s+/).length,
    procedureCount: (noteText.match(/procedure|treatment|therapy/gi) || []).length,
    diagnosisCount: (noteText.match(/\bD\d{4}/g) || []).length,
    hasComplexProcedures: HIGH_RISK_PROCEDURES.some(proc => text.includes(proc)),
    hasMedicalHistory: MEDICAL_COMPLEXITY_INDICATORS.some(indicator => text.includes(indicator)),
    hasContraindications: LEGAL_RED_FLAGS.some(flag => text.includes(flag)),
    riskFactors: [
      ...HIGH_RISK_PROCEDURES.filter(proc => text.includes(proc)),
      ...MEDICAL_COMPLEXITY_INDICATORS.filter(indicator => text.includes(indicator)),
      ...LEGAL_RED_FLAGS.filter(flag => text.includes(flag))
    ]
  };
}

/**
 * Implements random QA escalation (1% of LOW cases)
 */
export function shouldRandomlyEscalate(): boolean {
  return Math.random() < 0.01; // 1% chance
}

/**
 * Main processing function that implements the two-tier system
 */
export async function processDentalNote(
  voiceNotes: string[],
  dentrixNotes: string[],
  patientInfo: any,
  appointmentInfo: any,
  forceEscalation: boolean = false,
  skipEscalation: boolean = false
): Promise<ProcessingResult> {
  const startTime = Date.now();
  
  try {
    // Step 1: Generate draft with primary AI (GPT-4.1)
    const draftResponse = await generatePrimaryDraft(
      voiceNotes,
      dentrixNotes,
      patientInfo,
      appointmentInfo
    );
    
    if (!draftResponse.success) {
      return {
        finalNote: '',
        status: 'ERROR',
        escalated: false,
        processingTime: Date.now() - startTime,
        metadata: extractNoteMetadata(''),
        escalationReason: 'Primary AI generation failed'
      };
    }

    const { draft, escalationFlag } = draftResponse;
    
    // Step 2: Analyze for escalation
    const escalationAnalysis = analyzeForEscalation(
      draft,
      escalationFlag,
      voiceNotes.join(' ')
    );
    
    // Step 3: Determine if escalation is needed
    const shouldEscalate = forceEscalation || 
      (!skipEscalation && (
        escalationAnalysis.shouldEscalate || 
        shouldRandomlyEscalate()
      ));

    let finalNote = draft;
    let status: 'OK' | 'NEEDS_HUMAN_REVIEW' = 'OK';
    let reviewComments: string | undefined;
    let riskFactors: string[] | undefined;

    // Step 4: Escalate if needed
    if (shouldEscalate) {
      const escalationResponse = await escalateToSeniorReviewer(
        voiceNotes.join('\n\n'),
        draft,
        escalationFlag
      );
      
      if (escalationResponse.success) {
        finalNote = escalationResponse.finalNote;
        status = escalationResponse.status;
        reviewComments = escalationResponse.reviewComments;
        riskFactors = escalationResponse.riskFactors;
      }
    }

    return {
      finalNote,
      status,
      escalated: shouldEscalate,
      processingTime: Date.now() - startTime,
      metadata: extractNoteMetadata(finalNote),
      escalationReason: escalationAnalysis.reason,
      reviewComments,
      riskFactors
    };

  } catch (error) {
    console.error('Error processing dental note:', error);
    return {
      finalNote: '',
      status: 'ERROR',
      escalated: false,
      processingTime: Date.now() - startTime,
      metadata: extractNoteMetadata(''),
      escalationReason: `Processing error: ${error}`
    };
  }
}

// Placeholder functions - will be implemented in the API layer
async function generatePrimaryDraft(
  voiceNotes: string[],
  dentrixNotes: string[],
  patientInfo: any,
  appointmentInfo: any
): Promise<{ success: boolean; draft: string; escalationFlag: 'LOW' | 'MEDIUM' | 'HIGH' }> {
  // This will be implemented in the API route
  throw new Error('Not implemented - use API endpoint');
}

async function escalateToSeniorReviewer(
  rawNotes: string,
  draftNote: string,
  escalationFlag: string
): Promise<{ 
  success: boolean; 
  finalNote: string; 
  status: 'OK' | 'NEEDS_HUMAN_REVIEW';
  reviewComments?: string;
  riskFactors?: string[];
}> {
  // This will be implemented in the API route
  throw new Error('Not implemented - use API endpoint');
}
