import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function POST(request: NextRequest) {
  try {
    const { recordingId, appointmentId, patientId } = await request.json();

    if (!recordingId || !appointmentId) {
      return NextResponse.json(
        { error: 'Recording ID and appointment ID are required' },
        { status: 400 }
      );
    }

    console.log('Combining clinical notes for:', { recordingId, appointmentId, patientId });

    // Load credentials and connect to Sikka API
    const credentials = loadCredentials();
    const sikkaClient = new SikkaApiClient(credentials);

    // Fetch the generated note from voice recording
    let generatedNote = '';
    try {
      const voiceNoteResponse = await fetch(`${request.url.split('/api')[0]}/api/voice/recordings/${recordingId}/note`, {
        method: 'GET'
      });
      
      if (voiceNoteResponse.ok) {
        const voiceNoteData = await voiceNoteResponse.json();
        generatedNote = voiceNoteData.professionalNote || voiceNoteData.note || '';
      }
    } catch (error) {
      console.error('Error fetching generated note:', error);
    }

    // Fetch existing clinical notes from Sikka API
    let existingNotes = '';
    try {
      await sikkaClient.authenticate();
      
      // Get medical notes by appointment ID
      const medicalNotes = await sikkaClient.getMedicalNotesByAppointment(appointmentId, patientId);
      
      if (medicalNotes.length > 0) {
        // Combine all existing notes for this appointment
        existingNotes = medicalNotes
          .map(note => note.text || note.notes || '')
          .filter(text => text.trim())
          .join('\n\n');
      }
    } catch (error) {
      console.error('Error fetching existing notes from Sikka:', error);
    }

    // Create combined note
    const combinedNote = createCombinedNote(existingNotes, generatedNote, appointmentId);

    return NextResponse.json({
      combinedNote,
      existingNote: existingNotes,
      generatedNote,
      appointmentId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error combining clinical notes:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to combine clinical notes' },
      { status: 500 }
    );
  }
}

function createCombinedNote(existingNote: string, generatedNote: string, appointmentId: string): string {
  const timestamp = new Date().toLocaleString();
  const separator = '=' + '='.repeat(60) + '=';
  
  let combined = '';
  
  // Add header
  combined += `${separator}\n`;
  combined += `CLINICAL NOTE - APPOINTMENT ${appointmentId}\n`;
  combined += `Updated: ${timestamp}\n`;
  combined += `${separator}\n\n`;
  
  // Add existing Dentrix note if present
  if (existingNote && existingNote.trim()) {
    combined += `EXISTING DENTRIX NOTE:\n`;
    combined += `${'-'.repeat(30)}\n`;
    combined += existingNote.trim();
    combined += `\n\n`;
  }
  
  // Add AI-generated note if present
  if (generatedNote && generatedNote.trim()) {
    combined += `AI-GENERATED NOTE FROM VOICE RECORDING:\n`;
    combined += `${'-'.repeat(30)}\n`;
    combined += generatedNote.trim();
    combined += `\n\n`;
  }
  
  // Add footer
  combined += `${separator}\n`;
  combined += `Note combined from Dentrix and voice recording transcription\n`;
  combined += `Generated by Dentalapp Voice Workflow System\n`;
  combined += `${separator}`;
  
  return combined;
}

// Handle OPTIONS request for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}