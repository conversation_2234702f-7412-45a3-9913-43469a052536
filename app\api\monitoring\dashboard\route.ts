import { NextResponse } from 'next/server';
import { healthService } from '@/lib/monitoring/health-service';
import { transcriptionQueue } from '@/lib/transcription-queue';
import { VercelDB } from '@/lib/vercel-db';

export async function GET() {
  try {
    const [healthStatus, queueStats, dbStats] = await Promise.allSettled([
      healthService.checkHealth(),
      Promise.resolve(transcriptionQueue.getStats()),
      VercelDB.getStats()
    ]);

    const dashboardData = {
      timestamp: new Date().toISOString(),
      health: healthStatus.status === 'fulfilled' ? healthStatus.value : { 
        status: 'unhealthy', 
        error: 'Health check failed' 
      },
      queue: queueStats.status === 'fulfilled' ? queueStats.value : {
        total: 0,
        queued: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        avgProcessingTimeMs: 0,
        throughputPerHour: 0
      },
      database: dbStats.status === 'fulfilled' ? dbStats.value : {
        total_transcriptions: 0,
        successful_transcriptions: 0,
        failed_transcriptions: 0,
        avg_confidence_score: 0,
        transcriptions_today: 0,
        error: 'Database stats unavailable'
      },
      system: {
        uptime: process.uptime(),
        node_version: process.version,
        platform: process.platform,
        memory_usage: process.memoryUsage(),
        environment: process.env.NODE_ENV || 'development'
      }
    };

    return NextResponse.json(dashboardData);
  } catch (error) {
    console.error('❌ Dashboard data retrieval failed:', error);
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Dashboard data unavailable',
      status: 'error'
    }, { status: 500 });
  }
}