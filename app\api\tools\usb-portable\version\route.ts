import { NextRequest, NextResponse } from 'next/server';

// USB Portable Tool Version Information
const USB_PORTABLE_VERSION = {
  version: "1.2.0",
  releaseDate: "2025-06-29",
  description: "USB Portable Dental Recordings Upload Tool",
  features: [
    "Auto-detects USB drive location",
    "Scans multiple recorder folder paths", 
    "Uploads to Azure Blob Storage",
    "Archives uploaded files locally",
    "Smart update checking",
    "Progress tracking and error handling"
  ],
  requirements: [
    "Windows PowerShell",
    "Internet connection for uploads",
    "Azure authentication configured"
  ],
  changelog: {
    "1.2.0": [
      "NEW: Single-file GUI version (dental-usb-uploader-gui.ps1)",
      "Added minimal Windows Forms interface for corporate environments",
      "Real-time progress tracking with visual progress bar",
      "Console and GUI modes in one file",
      "Enhanced user experience with status updates and error handling",
      "Corporate-friendly single file solution"
    ],
    "1.1.2": [
      "Fixed PowerShell syntax errors (missing braces, unescaped ampersands)",
      "Corrected URL parameter escaping in auto-update function",
      "Fixed string terminator issues in error messages",
      "Resolved all parser errors preventing script execution",
      "Enhanced PowerShell compatibility and error handling"
    ],
    "1.1.1": [
      "Fixed batch file immediate exit issue",
      "Added bulletproof debugging and error display",
      "Enhanced PowerShell execution with -NoExit flag",
      "Better error reporting and pause functionality",
      "Improved USB drive compatibility"
    ],
    "1.1.0": [
      "Automatic code-pulling updates (no manual file replacement)",
      "Raw content API for seamless updates",
      "Improved update process with backup creation",
      "Enhanced cache invalidation after uploads",
      "Better error handling and user feedback"
    ],
    "1.0.0": [
      "Initial release",
      "Auto USB drive detection",
      "Multi-path scanning support",
      "Azure Blob Storage integration",
      "Local file archiving",
      "Update checking system"
    ]
  }
};

export async function GET(request: NextRequest) {
  try {
    // Log the version check request
    console.log('USB Portable Tool version check requested');
    
    // Return version information
    return NextResponse.json({
      success: true,
      version: USB_PORTABLE_VERSION.version,
      releaseDate: USB_PORTABLE_VERSION.releaseDate,
      description: USB_PORTABLE_VERSION.description,
      features: USB_PORTABLE_VERSION.features,
      requirements: USB_PORTABLE_VERSION.requirements,
      changelog: USB_PORTABLE_VERSION.changelog,
      downloadUrl: `/api/tools/usb-portable/download`,
      checkTime: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error checking USB portable tool version:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to check version',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
