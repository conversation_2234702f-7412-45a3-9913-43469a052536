import { NextRequest, NextResponse } from 'next/server';
import { AzureStorageService } from '@/lib/azure-storage';
import { getCurrentSettings } from '../../settings/route';
import { OpenAI } from 'openai';
import { FileResolver } from '@/lib/file-resolver';
import { createJobLogger, logger, logError, withTiming } from '@/lib/logger';
import { createJob, getJob, getProcessingStats, getRecordingsByStatus } from '@/lib/database/index';
import { jobStore } from '../../../../archive/legacy-voice-processing/job-persistence';
import { JobStatus, JobPriority } from '@/types/recordings';

// Direct transcription function to avoid HTTP calls
async function transcribeFileDirect(
  fileName: string, 
  settings: { model: string; promptId: string; enableSpeakerSeparation: boolean; forceRetranscribe: boolean },
  customPrompt?: string
): Promise<{ success: boolean; transcription?: string; error?: string }> {
  try {
    // Use the FileResolver to find the actual file - MUCH SIMPLER!
    console.log(`🎯 TRANSCRIPTION: Starting file resolution for "${fileName}"`);
    const fileResult = await FileResolver.findAudioFile(fileName);
    
    if (!fileResult.found) {
      console.error(`❌ TRANSCRIPTION: File not found - ${fileResult.error}`);
      return { 
        success: false, 
        error: `File not found: ${fileName}. ${fileResult.error}` 
      };
    }
    
    const actualFilePath = fileResult.actualPath!;
    console.log(`✅ TRANSCRIPTION: File resolved - "${fileName}" -> "${actualFilePath}"`)

    // Create JSON file path (same path as audio file, but with .json extension)
    const baseName = actualFilePath.replace(/\.[^/.]+$/, "");
    const jsonPath = `${baseName}.json`;

    // Check if transcription already exists (unless forcing retranscription)
    if (!settings.forceRetranscribe) {
      try {
        const existingData = await AzureStorageService.downloadJson(jsonPath);
        if (existingData && existingData.transcription) {
          return { success: true, transcription: existingData.transcription };
        }
      } catch (error) {
        // No existing transcription, proceed with new one
      }
    }

    // Get audio file from Azure Blob Storage
    const audioBuffer = await AzureStorageService.downloadFile(actualFilePath);
    
    // Check for minimum file size
    if (audioBuffer.length < 1024) {
      return { success: false, error: `File too small (${audioBuffer.length} bytes)` };
    }

    // Initialize OpenAI client
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    // Create proper File object for OpenAI
    const fileObj = Object.assign(audioBuffer, {
      name: fileName.split('/').pop() || fileName,
      type: getContentTypeFromFileName(fileName),
      lastModified: Date.now(),
      webkitRelativePath: '',
      size: audioBuffer.length,
      stream: () => new ReadableStream(),
      text: () => Promise.resolve(''),
      arrayBuffer: () => Promise.resolve(audioBuffer.buffer),
      slice: () => audioBuffer
    }) as File;

    // Call OpenAI transcription
    const transcription = await openai.audio.transcriptions.create({
      file: fileObj,
      model: settings.model,
      response_format: 'json',
      temperature: 0.2
    });

    // Save transcription to Azure
    await AzureStorageService.uploadJson(jsonPath, {
      transcription: transcription.text,
      createdAt: new Date().toISOString(),
      model: settings.model,
      fileName: fileName
    });

    return { success: true, transcription: transcription.text };
  } catch (error) {
    console.error(`Direct transcription failed for ${fileName}:`, error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Helper function to get content type from filename
function getContentTypeFromFileName(fileName: string): string {
  const ext = fileName.toLowerCase().split('.').pop();
  switch (ext) {
    case 'mp3': return 'audio/mpeg';
    case 'wav': return 'audio/wav';
    case 'm4a': return 'audio/mp4';
    case 'webm': return 'audio/webm';
    case 'ogg': return 'audio/ogg';
    case 'flac': return 'audio/flac';
    default: return 'audio/mpeg';
  }
}

// Direct summary function to avoid HTTP calls
async function generateSummaryDirect(
  transcription: string,
  summaryType: string = 'visit-summary',
  summaryPromptId: string = 'dental-visit-summary'
): Promise<{ success: boolean; summary?: string; error?: string }> {
  try {
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const prompt = `Please create a professional dental visit summary from the following transcription. Focus on:
- Patient concerns and symptoms
- Clinical findings and observations
- Treatments performed
- Follow-up recommendations
- Any medications prescribed

Transcription: ${transcription}

Summary:`;

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a dental assistant creating professional clinical summaries. Keep summaries concise, professional, and focused on clinical information."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 500
    });

    const summary = completion.choices[0]?.message?.content || '';
    
    return { success: true, summary };
  } catch (error) {
    console.error('Direct summary generation failed:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// No longer needed - replaced with job queue service
// TranscribeAllJob interface and in-memory storage removed in favor of
// reliable persistent job storage via jobQueueService

// Helper function to display progress in terminal
function logProgress(job: TranscribeAllJob) {
  const completedFiles = job.processedFiles + job.failedFiles;
  const progressBar = '█'.repeat(Math.floor(job.progress / 5)) + '░'.repeat(20 - Math.floor(job.progress / 5));

  console.log(`\n📊 TRANSCRIBE ALL PROGRESS - Job ${job.id.split('-').pop()}`);
  console.log(`[${progressBar}] ${job.progress}%`);
  console.log(`📁 Files: ${completedFiles}/${job.totalFiles} | ✅ Success: ${job.processedFiles} | ❌ Failed: ${job.failedFiles}`);
  if (job.currentFile) {
    console.log(`🎵 Current: ${job.currentFile}`);
  }
  console.log(`⏱️  Started: ${new Date(job.startedAt).toLocaleTimeString()}`);
  console.log('─'.repeat(60));
}

/**
 * Enhanced transcribe-all route with reliable job queue service integration
 * 
 * This replaces the unreliable in-memory transcribeAllJobs Map with the new job queue service
 * that provides persistent storage, retry logic, and monitoring capabilities.
 * Jobs survive server restarts and can be recovered reliably.
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  const requestLogger = createJobLogger('batch-transcribe', 'transcribe-all');
  
  try {
    const { 
      forceRetranscribe = false,
      enableSpeakerSeparation = true,
      promptId = 'dental-transcription-context',
      customPrompt,
      openaiModel,
      fileFilter = 'all', // 'all', 'untranscribed', 'old-transcriptions'
      autoSummarize = false,
      summaryType = 'visit-summary',
      summaryPromptId = 'dental-visit-summary'
    } = await request.json();

    requestLogger.info('Starting transcribe-all job', {
      forceRetranscribe,
      enableSpeakerSeparation,
      fileFilter,
      model: openaiModel || 'default',
      autoSummarize,
      summaryType
    });

    if (!AzureStorageService.isConfigured()) {
      requestLogger.error('Azure Storage not configured');
      return NextResponse.json(
        { error: 'Azure Storage is not configured' },
        { status: 400 }
      );
    }

    // Load user settings
    const userSettings = await getCurrentSettings();
    const selectedModel = openaiModel || userSettings.transcriptionModel || 'whisper-1';

    // Get all audio files from Azure
    console.log('🔍 Scanning Azure Blob Storage for audio files...');
    const allFiles = await AzureStorageService.listFiles('');
    
    // Filter for actual audio files
    const audioFiles = allFiles.filter(file => {
      const ext = file.name.toLowerCase().split('.').pop();
      const isAudioExtension = ['mp3', 'wav', 'm4a', 'webm', 'ogg', 'flac', 'aac'].includes(ext || '');
      const isMetadataPath = file.path.includes('metadata/');
      const isJsonFile = ext === 'json';
      const isSystemFile = file.name.startsWith('.') || file.name.startsWith('_');

      // Only include files that are audio extensions and not system/metadata files
      return isAudioExtension &&
             !isMetadataPath &&
             !isJsonFile &&
             !isSystemFile &&
             file.size > 1024; // Exclude very small files (likely corrupted or empty)
    });

    console.log(`📁 Found ${audioFiles.length} audio files in Azure Storage`);

    if (audioFiles.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No audio files found to transcribe',
        totalFiles: 0
      });
    }

    // Load existing transcriptions to determine what needs processing
    let existingTranscriptions = {};
    try {
      existingTranscriptions = await AzureStorageService.downloadJson('metadata/transcriptions.json');
      console.log(`📚 Loaded ${Object.keys(existingTranscriptions).length} existing transcriptions`);
    } catch (error) {
      console.log('📝 No existing transcriptions found');
    }

    // Get current recordings to determine what needs processing
    const baseUrl = request.url.split('/api')[0];
    let recordingsData;
    try {
      const recordingsResponse = await fetch(`${baseUrl}/api/voice/recordings`);
      if (recordingsResponse.ok) {
        recordingsData = await recordingsResponse.json();
      }
    } catch (error) {
      console.log('⚠️ Could not fetch recordings data, falling back to file-based filtering');
    }

    return await withTiming(requestLogger, 'batch-transcribe-creation', async () => {
      // Filter files based on the selected filter
      let filesToProcess = audioFiles;
      
      if (fileFilter === 'untranscribed' && !forceRetranscribe && recordingsData?.recordings) {
        // Use actual recording data to filter
        const recordings = recordingsData.recordings;
        filesToProcess = audioFiles.filter(file => {
          const recording = recordings.find(r => r.fileName === file.name || r.name === file.name);
          if (!recording) return true; // If not found in recordings, include it
          return !(recording.transcription && recording.transcription.trim());
        });
        requestLogger.info('Filtered to untranscribed files', { 
          originalCount: audioFiles.length,
          filteredCount: filesToProcess.length 
        });
      } else if (fileFilter === 'old-transcriptions') {
        // This would require transcription metadata to determine which are "old"
        // For now, treat as all files if force retranscribe is enabled
        if (!forceRetranscribe) {
          filesToProcess = [];
          requestLogger.info('Old transcriptions filter requires force retranscribe to be enabled');
        }
      }

      if (filesToProcess.length === 0) {
        requestLogger.info('No files need transcription', {
          totalFiles: audioFiles.length,
          filteredFiles: 0,
          fileFilter
        });
        return NextResponse.json({
          success: true,
          message: 'No files need transcription based on current filter settings',
          totalFiles: audioFiles.length,
          filteredFiles: 0
        });
      }

      // Create individual jobs for each file using the reliable job queue service
      const jobIds: string[] = [];
      const errors: Array<{ filename: string; error: string }> = [];
      
      const batchId = `batch-transcribe-${Date.now()}`;
      requestLogger.info('Creating individual jobs for files', {
        totalFiles: filesToProcess.length,
        batchId,
        autoSummarize
      });

      // Create jobs for each file
      for (const file of filesToProcess) {
        try {
          const jobId = await createJob({
            filename: file.name,
            containerPath: file.path,
            retryCount: 0,
            metadata: {
              type: autoSummarize ? 'full_processing' : 'transcription',
              batchId,
              model: selectedModel,
              promptId,
              enableSpeakerSeparation,
              forceRetranscribe,
              customPrompt,
              autoSummarize,
              summaryType,
              summaryPromptId,
              source: 'transcribe-all',
              userId: request.headers.get('x-user-id') || undefined,
              sessionId: request.headers.get('x-session-id') || undefined,
              enqueuedAt: new Date().toISOString()
            }
          }, {
            priority: 2, // Higher priority for batch processing
            timeout: autoSummarize ? 600000 : 300000, // 10 minutes for full processing, 5 for transcription only
            metadata: {
              batchId,
              requestSource: 'transcribe-all-api'
            }
          });

          jobIds.push(jobId);
          
          requestLogger.info('Job enqueued for file', {
            jobId,
            filename: file.name,
            type: autoSummarize ? 'full_processing' : 'transcription'
          });

        } catch (error) {
          logError(requestLogger, error, 'file-job-creation', { filename: file.name });
          errors.push({
            filename: file.name,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      requestLogger.info('Batch transcribe job creation completed', {
        batchId,
        created: jobIds.length,
        failed: errors.length,
        totalRequested: filesToProcess.length
      });

      return NextResponse.json({
        success: jobIds.length > 0,
        batchId,
        jobIds,
        totalFiles: filesToProcess.length,
        created: jobIds.length,
        failed: errors.length,
        errors: errors.length > 0 ? errors : undefined,
        message: `Started transcription of ${jobIds.length} files${autoSummarize ? ' with summarization' : ''}${errors.length > 0 ? `, ${errors.length} failed to queue` : ''}`,
        estimatedTimeMinutes: Math.ceil(jobIds.length * (autoSummarize ? 4 : 2)),
        monitorUrl: `/api/voice/job-monitor?batchId=${batchId}`
      }, { 
        status: jobIds.length > 0 ? 202 : 400 
      });
    });

  } catch (error) {
    logError(requestLogger, error, 'transcribe-all-request');
    return NextResponse.json({
      success: false,
      error: 'Failed to start transcribe-all batch processing',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Get transcribe-all batch status using job queue service
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  const requestLogger = createJobLogger('batch-status', 'transcribe-all');
  
  try {
    const { searchParams } = new URL(request.url);
    const batchId = searchParams.get('batchId');
    const jobId = searchParams.get('jobId'); // Support legacy jobId parameter

    if (jobId) {
      // Get specific job status using job queue service
      return await withTiming(requestLogger, 'get-job-by-id', async () => {
        const job = await getJob(jobId);
        
        if (!job) {
          return NextResponse.json({
            success: false,
            error: 'Job not found'
          }, { status: 404 });
        }

        requestLogger.debug('Retrieved job status', {
          jobId: job.id,
          status: job.status,
          filename: job.filename
        });

        return NextResponse.json({
          success: true,
          job: {
            id: job.id,
            filename: job.filename,
            containerPath: job.containerPath,
            status: job.status,
            retryCount: job.retryCount,
            createdAt: job.createdAt,
            updatedAt: job.updatedAt,
            startedAt: job.startedAt,
            completedAt: job.completedAt,
            errorDetails: job.errorDetails,
            results: job.results,
            metadata: job.metadata
          }
        });
      });
    } else if (batchId) {
      // Get batch status - aggregate all jobs with this batchId
      return await withTiming(requestLogger, 'get-batch-status', async () => {
        // This would require implementing batch tracking in job-persistence
        // For now, return a simplified response
        const stats = await getProcessingStats();
        
        requestLogger.debug('Retrieved batch status', {
          batchId,
          queueStats: stats
        });
        
        return NextResponse.json({
          success: true,
          batchId,
          queueStats: stats,
          message: 'Use job-monitor API for detailed batch tracking',
          monitorUrl: `/api/voice/job-monitor?batchId=${batchId}`
        });
      });
    } else {
      // Get queue summary
      return await withTiming(requestLogger, 'get-queue-summary', async () => {
        const stats = await getProcessingStats();
        
        requestLogger.debug('Retrieved queue summary', {
          totalJobs: stats.total,
          pendingJobs: stats.pending,
          queueHealth: stats.queueHealth
        });

        return NextResponse.json({
          success: true,
          queueStats: stats,
          timestamp: new Date().toISOString(),
          message: 'Use job-monitor API for detailed job tracking'
        });
      });
    }

  } catch (error) {
    logError(requestLogger, error, 'batch-status-query');
    return NextResponse.json({
      success: false,
      error: 'Failed to get transcribe-all status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Background processing functions are now handled by the job queue service
// The individual transcription and summarization logic is executed within
// the JobQueueService.executeJob method based on job metadata.
// This eliminates the need for manual batch processing and provides
// reliable job tracking, retry logic, and error handling.
