import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const patientId = searchParams.get('patient_id');

    if (!patientId) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      );
    }

    console.log(`Debug Notes API: Fetching raw notes for patient: ${patientId}`);

    const credentials = loadCredentials();
    const sikkaClient = new SikkaApiClient(credentials);
    await sikkaClient.authenticate();

    // Get raw medical notes directly from Sikka API
    const requestKey = await sikkaClient.getRequestKey();
    const url = `https://api.sikkasoft.com/v4/medical_notes`;
    const params = new URLSearchParams({
      patient_id: patientId,
      limit: '50'
    });

    const response = await fetch(`${url}?${params}`, {
      method: 'GET',
      headers: {
        'Request-Key': requestKey,
      },
    });

    if (!response.ok) {
      throw new Error(`Sikka API error: ${response.status}`);
    }

    const rawData = await response.json();
    const allNotes = Array.isArray(rawData) ? rawData : rawData.items || [];

    console.log(`Debug Notes API: Found ${allNotes.length} raw notes for patient ${patientId}`);

    // Return raw notes with minimal processing
    const processedNotes = allNotes.map((note: any, index: number) => ({
      index: index + 1,
      id: note.id || `note-${index}`,
      date: note.date,
      type: note.type,
      description: note.description,
      text: note.text,
      textLength: note.text ? note.text.length : 0,
      provider_id: note.provider_id,
      appointment_sr_no: note.appointment_sr_no,
      rawNote: note // Include full raw note for debugging
    }));

    return NextResponse.json({
      success: true,
      patientId,
      totalNotes: allNotes.length,
      notes: processedNotes,
      summary: {
        types: [...new Set(allNotes.map(n => n.type))],
        dateRange: {
          earliest: allNotes.length > 0 ? Math.min(...allNotes.map(n => new Date(n.date).getTime())) : null,
          latest: allNotes.length > 0 ? Math.max(...allNotes.map(n => new Date(n.date).getTime())) : null
        }
      }
    });

  } catch (error) {
    console.error('Debug Notes API error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Failed to fetch debug notes',
        patientId: request.nextUrl.searchParams.get('patient_id')
      },
      { status: 500 }
    );
  }
}
