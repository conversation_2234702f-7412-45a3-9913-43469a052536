import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

export interface VoiceRecording {
  id: string;
  filename: string;
  device_id?: string;
  file_path?: string;
  file_size?: number;
  imported_at?: string;
  recording_date?: string;
  status: 'pending' | 'transcribing' | 'transcribed' | 'summarizing' | 'summarized' | 'error';
  transcription?: string;
  transcribed_at?: string;
  clinical_summary?: string;
  category?: 'clinical' | 'administrative' | 'other';
  posted_to_dentrix?: boolean;
  isUploaded?: boolean;
  created_at: string;
  updated_at: string;
}

export class VoiceRecordingsSQLiteDB {
  private db: Database.Database;
  private dbPath: string;

  constructor() {
    // Use /tmp directory for Vercel serverless compatibility
    const dataDir = process.env.VERCEL ? '/tmp' : path.join(process.cwd(), 'data');
    
    // Ensure directory exists and is writable
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    this.dbPath = path.join(dataDir, 'voice-recordings.db');
    this.db = new Database(this.dbPath);
    this.initializeDatabase();
  }

  private initializeDatabase() {
    // Create tables if they don't exist
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS voice_recordings (
        id TEXT PRIMARY KEY,
        filename TEXT NOT NULL,
        device_id TEXT,
        file_path TEXT,
        file_size INTEGER,
        imported_at TEXT,
        recording_date TEXT,
        status TEXT DEFAULT 'pending',
        transcription TEXT,
        transcribed_at TEXT,
        clinical_summary TEXT,
        category TEXT DEFAULT 'clinical',
        posted_to_dentrix BOOLEAN DEFAULT FALSE,
        isUploaded BOOLEAN DEFAULT TRUE,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );

      CREATE INDEX IF NOT EXISTS idx_filename ON voice_recordings(filename);
      CREATE INDEX IF NOT EXISTS idx_status ON voice_recordings(status);
      CREATE INDEX IF NOT EXISTS idx_transcribed_at ON voice_recordings(transcribed_at);
      CREATE INDEX IF NOT EXISTS idx_created_at ON voice_recordings(created_at);
    `);

    console.log(`📊 SQLite database initialized at: ${this.dbPath}`);
  }

  // Create a new recording
  createRecording(recording: Omit<VoiceRecording, 'created_at' | 'updated_at'>): VoiceRecording {
    const now = new Date().toISOString();
    const fullRecording: VoiceRecording = {
      ...recording,
      created_at: now,
      updated_at: now
    };

    const stmt = this.db.prepare(`
      INSERT INTO voice_recordings (
        id, filename, device_id, file_path, file_size, imported_at, recording_date,
        status, transcription, transcribed_at, clinical_summary, category,
        posted_to_dentrix, isUploaded, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      fullRecording.id,
      fullRecording.filename,
      fullRecording.device_id,
      fullRecording.file_path,
      fullRecording.file_size,
      fullRecording.imported_at,
      fullRecording.recording_date,
      fullRecording.status,
      fullRecording.transcription,
      fullRecording.transcribed_at,
      fullRecording.clinical_summary,
      fullRecording.category,
      fullRecording.posted_to_dentrix ? 1 : 0,
      fullRecording.isUploaded ? 1 : 0,
      fullRecording.created_at,
      fullRecording.updated_at
    );

    console.log(`💾 Created new recording: ${fullRecording.filename}`);
    return fullRecording;
  }

  // Get a recording by ID
  getRecording(id: string): VoiceRecording | null {
    const stmt = this.db.prepare('SELECT * FROM voice_recordings WHERE id = ?');
    const row = stmt.get(id) as any;
    
    if (!row) return null;

    return {
      ...row,
      posted_to_dentrix: Boolean(row.posted_to_dentrix),
      isUploaded: Boolean(row.isUploaded)
    };
  }

  // Get a recording by filename
  getRecordingByFilename(filename: string): VoiceRecording | null {
    const stmt = this.db.prepare('SELECT * FROM voice_recordings WHERE filename = ?');
    const row = stmt.get(filename) as any;
    
    if (!row) return null;

    return {
      ...row,
      posted_to_dentrix: Boolean(row.posted_to_dentrix),
      isUploaded: Boolean(row.isUploaded)
    };
  }

  // Update a recording
  updateRecording(id: string, updates: Partial<VoiceRecording>): boolean {
    const now = new Date().toISOString();
    const updateFields: string[] = [];
    const values: any[] = [];

    // Build dynamic update query
    Object.entries(updates).forEach(([key, value]) => {
      if (key !== 'id' && key !== 'created_at') {
        updateFields.push(`${key} = ?`);
        values.push(value);
      }
    });

    if (updateFields.length === 0) return false;

    updateFields.push('updated_at = ?');
    values.push(now);
    values.push(id);

    const stmt = this.db.prepare(`
      UPDATE voice_recordings 
      SET ${updateFields.join(', ')} 
      WHERE id = ?
    `);

    const result = stmt.run(...values);
    const success = result.changes > 0;

    if (success) {
      console.log(`💾 Updated recording: ${id}`);
    } else {
      console.warn(`⚠️ No recording found to update: ${id}`);
    }

    return success;
  }

  // Get all recordings
  getAllRecordings(): VoiceRecording[] {
    const stmt = this.db.prepare('SELECT * FROM voice_recordings ORDER BY created_at DESC');
    const rows = stmt.all() as any[];
    
    return rows.map(row => ({
      ...row,
      posted_to_dentrix: Boolean(row.posted_to_dentrix),
      isUploaded: Boolean(row.isUploaded)
    }));
  }

  // Get recordings by status
  getRecordingsByStatus(status: VoiceRecording['status']): VoiceRecording[] {
    const stmt = this.db.prepare('SELECT * FROM voice_recordings WHERE status = ? ORDER BY created_at DESC');
    const rows = stmt.all(status) as any[];
    
    return rows.map(row => ({
      ...row,
      posted_to_dentrix: Boolean(row.posted_to_dentrix),
      isUploaded: Boolean(row.isUploaded)
    }));
  }

  // Get recordings that need transcription
  getRecordingsNeedingTranscription(): VoiceRecording[] {
    const stmt = this.db.prepare(`
      SELECT * FROM voice_recordings 
      WHERE status IN ('pending', 'error') 
      OR (status = 'transcribed' AND transcription IS NULL)
      ORDER BY created_at ASC
    `);
    const rows = stmt.all() as any[];
    
    return rows.map(row => ({
      ...row,
      posted_to_dentrix: Boolean(row.posted_to_dentrix),
      isUploaded: Boolean(row.isUploaded)
    }));
  }

  // Get recordings that need summarization
  getRecordingsNeedingSummarization(): VoiceRecording[] {
    const stmt = this.db.prepare(`
      SELECT * FROM voice_recordings 
      WHERE status = 'transcribed' 
      AND transcription IS NOT NULL 
      AND (clinical_summary IS NULL OR clinical_summary = '')
      ORDER BY transcribed_at ASC
    `);
    const rows = stmt.all() as any[];
    
    return rows.map(row => ({
      ...row,
      posted_to_dentrix: Boolean(row.posted_to_dentrix),
      isUploaded: Boolean(row.isUploaded)
    }));
  }

  // Delete a recording
  deleteRecording(id: string): boolean {
    const stmt = this.db.prepare('DELETE FROM voice_recordings WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // Get database statistics
  getStats() {
    try {
      // Ensure database is initialized first
      this.initializeDatabase();
      
      const stats = {
        total: this.db.prepare('SELECT COUNT(*) as count FROM voice_recordings').get() as { count: number },
        pending: this.db.prepare('SELECT COUNT(*) as count FROM voice_recordings WHERE status = "pending"').get() as { count: number },
        transcribed: this.db.prepare('SELECT COUNT(*) as count FROM voice_recordings WHERE status = "transcribed"').get() as { count: number },
        summarized: this.db.prepare('SELECT COUNT(*) as count FROM voice_recordings WHERE status = "summarized"').get() as { count: number },
        error: this.db.prepare('SELECT COUNT(*) as count FROM voice_recordings WHERE status = "error"').get() as { count: number }
      };

      return {
        total: stats.total.count,
        pending: stats.pending.count,
        transcribed: stats.transcribed.count,
        summarized: stats.summarized.count,
        error: stats.error.count
      };
    } catch (error) {
      console.error('Error getting database stats:', error);
      // Return empty stats on error
      return {
        total: 0,
        pending: 0,
        transcribed: 0,
        summarized: 0,
        error: 0
      };
    }
  }

  // Close database connection
  close() {
    this.db.close();
  }

  // Backup database
  backup(backupPath: string) {
    const backup = new Database(backupPath);
    this.db.backup(backup);
    backup.close();
    console.log(`💾 Database backed up to: ${backupPath}`);
  }
}

// Export singleton instance
export const VoiceRecordingsSQLiteDBInstance = new VoiceRecordingsSQLiteDB(); 