name: Simple Package and Release (DISABLED - Using Azure)

on:
  # Disabled - using Azure App Service deployment instead
  # push:
  #   branches: [ main ]
  workflow_dispatch:

jobs:
  package:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Create release package (source code only)
      run: |
        # Create release directory structure
        mkdir -p release/app
        mkdir -p release/config
        mkdir -p release/scripts
        
        # Copy ALL source files (no building required)
        cp -r src release/app/ || echo "No src directory"
        cp -r app release/app/ || echo "No app directory" 
        cp -r public release/app/ || echo "No public directory"
        cp -r components release/app/ || echo "No components directory"
        cp -r lib release/app/ || echo "No lib directory"
        cp -r utils release/app/ || echo "No utils directory"
        cp -r styles release/app/ || echo "No styles directory"
        cp package.json release/app/ || echo "No package.json"
        cp package-lock.json release/app/ || echo "No package-lock.json"
        cp next.config.js release/app/ || echo "No next.config.js"
        cp tailwind.config.ts release/app/ || echo "No tailwind.config.ts"
        cp tsconfig.json release/app/ || echo "No tsconfig.json"
        cp -r .env* release/app/ 2>/dev/null || echo "No .env files"
        
        # Copy configuration files
        cp USB-Deploy/config/ecosystem.config.js release/config/ || echo "No ecosystem.config.js"
        cp USB-Deploy/config/.env.production release/config/ || echo "No .env.production"
        
        # Copy management scripts
        cp USB-Deploy/scripts/*.bat release/scripts/ || echo "No .bat scripts"
        
        # Create version file
        echo "${GITHUB_SHA:0:7}" > release/version.txt
        if [ "${{ github.ref_type }}" = "tag" ]; then
          echo "${{ github.ref_name }}" > release/version.txt
        fi
        
        # Create installation instructions
        cat > release/README.txt << 'EOF'
        Dental App Release Package (Source Code)
        ========================================
        
        INSTALLATION:
        1. Extract this package to C:\dentalappserver\
        2. Run scripts\install-from-github.bat as Administrator
        3. The installer will build the app on the target machine
        4. Access app at http://localhost:3000
        
        UPDATE:
        1. Run scripts\update-from-github.bat as Administrator
        
        MANAGEMENT:
        - Start: scripts\start-service.bat
        - Stop: scripts\stop-service.bat
        - Status: scripts\status.bat
        - Logs: scripts\logs.bat
        - Backup: scripts\backup.bat
        
        This package contains source code that will be built during installation.
        EOF
        
    - name: Create ZIP package
      run: |
        cd release
        zip -r ../dental-app-release.zip .
        cd ..
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: dental-app-release
        path: dental-app-release.zip
        retention-days: 30

    - name: Package Complete
      run: |
        echo "✅ Dental App package created successfully!"
        echo "📦 Package size: $(ls -lh dental-app-release.zip | awk '{print $5}')"
        echo "📁 Package contents:"
        unzip -l dental-app-release.zip | head -20
        echo ""
        echo "🚀 Package is ready for deployment!"
        echo "📥 Download from GitHub Actions artifacts"
        echo "💻 Extract to C:\dentalappserver\ and run install script"
