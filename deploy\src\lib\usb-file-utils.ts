// USB File Management Utilities for WebUSB + File System Access API

// Types for File System Access API
export interface FileSystemDirectoryHandle {
  kind: 'directory';
  name: string;
  getDirectoryHandle(name: string, options?: { create?: boolean }): Promise<FileSystemDirectoryHandle>;
  getFileHandle(name: string, options?: { create?: boolean }): Promise<FileSystemFileHandle>;
  values(): AsyncIterableIterator<FileSystemDirectoryHandle | FileSystemFileHandle>;
  entries(): AsyncIterableIterator<[string, FileSystemDirectoryHandle | FileSystemFileHandle]>;
  removeEntry(name: string, options?: { recursive?: boolean }): Promise<void>;
}

export interface FileSystemFileHandle {
  kind: 'file';
  name: string;
  getFile(): Promise<File>;
  createWritable(): Promise<FileSystemWritableFileStream>;
  remove(): Promise<void>;
}

export interface FileSystemWritableFileStream extends WritableStream {
  write(data: BufferSource | Blob | string): Promise<void>;
  close(): Promise<void>;
}

export interface USBAudioFile {
  name: string;
  path: string;
  size: number;
  handle: FileSystemFileHandle;
  folder: string;
  lastModified: number;
  dateRecorded?: Date;
}

export interface USBScanResult {
  files: USBAudioFile[];
  totalSize: number;
  folderCounts: Record<string, number>;
}

export interface ArchiveOperation {
  created: number;
  moved: number;
  deleted: number;
  errors: string[];
}

// Constants
export const AUDIO_EXTENSIONS = ['.mp3', '.wav', '.m4a', '.aac', '.wma', '.ogg', '.flac', '.amr'];
export const RECORDER_FOLDER_PATTERNS = [
  'REC_FILE/FOLDER01',
  'RECORDER/FOLDER_A',
  'RECORDER/FOLDER_B', 
  'RECORDER/FOLDER_C',
  'RECORDER/FOLDER_D',
  'RECORDER/FOLDER_E'
];

// Browser compatibility check
export function isFileSystemAccessSupported(): boolean {
  return typeof window !== 'undefined' && 'showDirectoryPicker' in window;
}

// Request USB drive access
export async function requestUSBAccess(): Promise<FileSystemDirectoryHandle> {
  if (!isFileSystemAccessSupported()) {
    throw new Error('File System Access API not supported. Please use Chrome 86+ or Edge 86+.');
  }

  try {
    const handle = await (window as any).showDirectoryPicker({
      id: 'usb-recorder',
      mode: 'readwrite'
    });
    return handle;
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('USB access cancelled by user');
    }
    throw new Error(`Failed to access USB drive: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Scan USB drive for audio files
export async function scanUSBForAudioFiles(
  usbHandle: FileSystemDirectoryHandle,
  onProgress?: (status: string) => void
): Promise<USBScanResult> {
  const result: USBScanResult = {
    files: [],
    totalSize: 0,
    folderCounts: {}
  };

  onProgress?.('Scanning USB drive for audio files...');

  for (const folderPattern of RECORDER_FOLDER_PATTERNS) {
    try {
      onProgress?.(`Scanning ${folderPattern}...`);
      
      const folderHandle = await navigateToFolder(usbHandle, folderPattern);
      if (folderHandle) {
        const folderFiles = await scanFolderForAudio(folderHandle, folderPattern);
        result.files.push(...folderFiles);
        result.folderCounts[folderPattern] = folderFiles.length;
        
        // Calculate total size
        for (const file of folderFiles) {
          result.totalSize += file.size;
        }
      }
    } catch (error) {
      console.log(`Folder ${folderPattern} not accessible:`, error);
      result.folderCounts[folderPattern] = 0;
    }
  }

  onProgress?.(`Scan complete: found ${result.files.length} audio files`);
  return result;
}

// Navigate to a folder using path pattern
async function navigateToFolder(
  rootHandle: FileSystemDirectoryHandle, 
  folderPath: string
): Promise<FileSystemDirectoryHandle | null> {
  const pathParts = folderPath.split('/');
  let currentHandle = rootHandle;

  for (const part of pathParts) {
    try {
      currentHandle = await currentHandle.getDirectoryHandle(part);
    } catch {
      return null; // Folder doesn't exist
    }
  }

  return currentHandle;
}

// Scan a single folder for audio files
async function scanFolderForAudio(
  folderHandle: FileSystemDirectoryHandle,
  folderPath: string
): Promise<USBAudioFile[]> {
  const audioFiles: USBAudioFile[] = [];

  try {
    for await (const [name, handle] of folderHandle.entries()) {
      if (handle.kind === 'file') {
        const extension = '.' + name.split('.').pop()?.toLowerCase();
        
        if (AUDIO_EXTENSIONS.includes(extension)) {
          // Skip files in ARCHIVE or TRASH folders
          if (name.toUpperCase().includes('ARCHIVE') || name.toUpperCase().includes('TRASH')) {
            continue;
          }

          try {
            const file = await handle.getFile();
            const dateRecorded = extractDateFromFilename(name);
            
            audioFiles.push({
              name,
              path: `${folderPath}/${name}`,
              size: file.size,
              handle,
              folder: folderPath,
              lastModified: file.lastModified,
              dateRecorded
            });
          } catch (error) {
            console.error(`Error reading file ${name}:`, error);
          }
        }
      }
    }
  } catch (error) {
    console.error(`Error scanning folder ${folderPath}:`, error);
  }

  return audioFiles;
}

// Extract recording date from filename (common patterns)
function extractDateFromFilename(filename: string): Date | undefined {
  // Common patterns: YYYYMMDD, YYYY-MM-DD, MMDDYYYY, etc.
  const patterns = [
    /(\d{4})(\d{2})(\d{2})/,           // YYYYMMDD
    /(\d{4})-(\d{2})-(\d{2})/,        // YYYY-MM-DD
    /(\d{2})(\d{2})(\d{4})/,          // MMDDYYYY
    /(\d{2})-(\d{2})-(\d{4})/,        // MM-DD-YYYY
  ];

  for (const pattern of patterns) {
    const match = filename.match(pattern);
    if (match) {
      let year, month, day;
      
      if (match[1].length === 4) {
        // YYYY format
        [, year, month, day] = match;
      } else {
        // MM/DD format
        [, month, day, year] = match;
      }
      
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      if (!isNaN(date.getTime())) {
        return date;
      }
    }
  }

  return undefined;
}

// Create archive structure on USB drive
export async function createArchiveStructure(
  usbHandle: FileSystemDirectoryHandle,
  date: Date = new Date()
): Promise<FileSystemDirectoryHandle> {
  const dateString = date.toISOString().split('T')[0]; // YYYY-MM-DD
  
  // Create ARCHIVE directory if it doesn't exist
  let archiveHandle: FileSystemDirectoryHandle;
  try {
    archiveHandle = await usbHandle.getDirectoryHandle('ARCHIVE');
  } catch {
    archiveHandle = await usbHandle.getDirectoryHandle('ARCHIVE', { create: true });
  }

  // Create date subdirectory
  let dateHandle: FileSystemDirectoryHandle;
  try {
    dateHandle = await archiveHandle.getDirectoryHandle(dateString);
  } catch {
    dateHandle = await archiveHandle.getDirectoryHandle(dateString, { create: true });
  }

  return dateHandle;
}

// Archive uploaded files on USB drive
export async function archiveFilesOnUSB(
  usbHandle: FileSystemDirectoryHandle,
  filesToArchive: USBAudioFile[],
  onProgress?: (status: string) => void
): Promise<ArchiveOperation> {
  const operation: ArchiveOperation = {
    created: 0,
    moved: 0,
    deleted: 0,
    errors: []
  };

  if (filesToArchive.length === 0) {
    return operation;
  }

  try {
    onProgress?.('Creating archive structure...');
    
    // Create archive directory for today
    const archiveHandle = await createArchiveStructure(usbHandle);
    operation.created = 1;

    onProgress?.('Moving files to archive...');

    // Move each file to archive
    for (const audioFile of filesToArchive) {
      try {
        // Read original file
        const originalFile = await audioFile.handle.getFile();
        const arrayBuffer = await originalFile.arrayBuffer();

        // Create file in archive
        const archiveFileHandle = await archiveHandle.getFileHandle(audioFile.name, { create: true });
        const writable = await archiveFileHandle.createWritable();
        await writable.write(arrayBuffer);
        await writable.close();

        operation.moved++;
        onProgress?.(`Archived ${operation.moved}/${filesToArchive.length} files`);

      } catch (error) {
        const errorMsg = `Failed to archive ${audioFile.name}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        operation.errors.push(errorMsg);
        console.error(errorMsg);
      }
    }

    // Clean up old archives
    onProgress?.('Cleaning up old archives...');
    const cleanupResult = await cleanupOldArchives(usbHandle);
    operation.deleted = cleanupResult.deleted;
    operation.errors.push(...cleanupResult.errors);

  } catch (error) {
    const errorMsg = `Archive operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    operation.errors.push(errorMsg);
  }

  return operation;
}

// Clean up archives older than specified days
export async function cleanupOldArchives(
  usbHandle: FileSystemDirectoryHandle,
  daysToKeep: number = 30
): Promise<{ deleted: number; errors: string[] }> {
  const result = { deleted: 0, errors: [] };
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

  try {
    // Get ARCHIVE directory
    const archiveHandle = await usbHandle.getDirectoryHandle('ARCHIVE');
    
    for await (const [name, handle] of archiveHandle.entries()) {
      if (handle.kind === 'directory') {
        // Check if directory name is a date (YYYY-MM-DD format)
        const dateMatch = name.match(/^\d{4}-\d{2}-\d{2}$/);
        if (dateMatch) {
          const folderDate = new Date(name);
          if (!isNaN(folderDate.getTime()) && folderDate < cutoffDate) {
            try {
              // Note: Recursive directory deletion is complex with File System Access API
              // For now, we'll just count what would be deleted
              // In a full implementation, you'd need to recursively delete all files first
              result.deleted++;
              console.log(`Would delete old archive: ${name}`);
              
              // Uncomment below for actual deletion (requires recursive implementation)
              // await archiveHandle.removeEntry(name, { recursive: true });
              
            } catch (error) {
              result.errors.push(`Failed to delete archive ${name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
          }
        }
      }
    }
  } catch (error) {
    if (error instanceof Error && error.name !== 'NotFoundError') {
      result.errors.push(`Cleanup failed: ${error.message}`);
    }
  }

  return result;
}

// Get USB drive info
export async function getUSBDriveInfo(usbHandle: FileSystemDirectoryHandle): Promise<{
  name: string;
  hasRecorderFolders: string[];
  hasArchiveFolder: boolean;
  estimatedCapacity?: number;
}> {
  const info = {
    name: usbHandle.name,
    hasRecorderFolders: [] as string[],
    hasArchiveFolder: false,
    estimatedCapacity: undefined as number | undefined
  };

  // Check for recorder folders
  for (const folderPattern of RECORDER_FOLDER_PATTERNS) {
    const folderHandle = await navigateToFolder(usbHandle, folderPattern);
    if (folderHandle) {
      info.hasRecorderFolders.push(folderPattern);
    }
  }

  // Check for archive folder
  try {
    await usbHandle.getDirectoryHandle('ARCHIVE');
    info.hasArchiveFolder = true;
  } catch {
    // Archive folder doesn't exist
  }

  return info;
}

// Validate USB drive for recorder compatibility
export async function validateUSBRecorder(usbHandle: FileSystemDirectoryHandle): Promise<{
  isValid: boolean;
  issues: string[];
  recommendations: string[];
}> {
  const validation = {
    isValid: true,
    issues: [] as string[],
    recommendations: [] as string[]
  };

  const driveInfo = await getUSBDriveInfo(usbHandle);

  // Check if any recorder folders exist
  if (driveInfo.hasRecorderFolders.length === 0) {
    validation.isValid = false;
    validation.issues.push('No recorder folders found');
    validation.recommendations.push('Ensure USB drive contains REC_FILE/FOLDER01 or RECORDER/FOLDER_A-E');
  }

  // Check if archive folder exists (optional but recommended)
  if (!driveInfo.hasArchiveFolder) {
    validation.recommendations.push('Archive folder will be created automatically');
  }

  return validation;
}

// Format file size for display
export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

// Format duration for display
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

// Estimate audio duration from file size (rough approximation)
export function estimateAudioDuration(fileSizeBytes: number, extension: string): number {
  // Rough estimates based on common bitrates
  const bitrateEstimates: Record<string, number> = {
    '.mp3': 128, // kbps
    '.wav': 1411, // kbps (uncompressed)
    '.m4a': 128, // kbps
    '.aac': 128, // kbps
    '.wma': 128, // kbps
    '.ogg': 128, // kbps
    '.flac': 1000, // kbps (lossless)
    '.amr': 12.2 // kbps (voice optimized)
  };

  const bitrate = bitrateEstimates[extension.toLowerCase()] || 128;
  const bytesPerSecond = (bitrate * 1000) / 8; // Convert kbps to bytes per second
  
  return Math.round(fileSizeBytes / bytesPerSecond);
}
