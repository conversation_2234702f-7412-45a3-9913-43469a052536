import requests
import json
from datetime import datetime, timedelta
from collections import defaultdict
import sys
import argparse

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 30

def get_request_key(office_id, secret_key, app_id, app_key):
    """Obtain a request key for API authentication."""
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": office_id,
            "secret_key": secret_key,
            "app_id": app_id,
            "app_key": app_key
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None

    data = auth_resp.json()
    return data.get("request_key")

def fetch_transactions(request_key, date):
    """Fetch all transactions for a specific date."""
    headers = {"Request-Key": request_key}
    params = {"date": date}

    # Fetch all pages to make sure we get all transactions
    all_items = []
    offset = 0
    limit = 100  # Increased limit to reduce number of API calls
    max_pages = 5  # Limit the number of pages to fetch

    print("Fetching transactions (this may take a moment)...")

    for page in range(max_pages):
        paged_params = params.copy()
        paged_params.update({"offset": offset, "limit": limit})

        try:
            resp = requests.get(
                f"{API_BASE_V4}/transactions",
                headers=headers,
                params=paged_params,
                timeout=API_TIMEOUT
            )

            if resp.status_code != 200:
                print(f"Error fetching transactions: {resp.status_code}")
                print(resp.text)
                break

            data = resp.json()
            items = data.get("items", [])
            all_items.extend(items)

            print(f"  Fetched {len(items)} transactions (page {page+1})")

            if len(items) < limit:
                break

            offset += limit
        except requests.exceptions.Timeout:
            print(f"Timeout while fetching transactions. Proceeding with {len(all_items)} transactions.")
            break
        except Exception as e:
            print(f"Error: {e}")
            break

    # Filter for procedure transactions only
    procedure_items = [t for t in all_items
                      if t.get("transaction_type") == "Procedure"
                      and float(t.get("amount", "0")) > 0]

    # Create a dictionary of patient names by patient ID
    patient_names = {}
    for item in all_items:
        patient_id = item.get("patient_id")
        patient_name = item.get("patient_name")
        if patient_id and patient_name and patient_name != "Unknown Patient":
            patient_names[patient_id] = patient_name

    # Update any transactions with missing patient names
    for item in procedure_items:
        patient_id = item.get("patient_id")
        if patient_id and (not item.get("patient_name") or item.get("patient_name") == "Unknown Patient"):
            if patient_id in patient_names:
                item["patient_name"] = patient_names[patient_id]

    print(f"Found {len(procedure_items)} procedure transactions")
    return procedure_items

def fetch_operatories(request_key, date_range=30):
    """Fetch a list of all operatories from appointment data."""
    print("Fetching list of operatories...")
    headers = {"Request-Key": request_key}

    # Try to get appointments for the last X days to find operatories
    operatories = set()

    # Try different date ranges
    end_date = datetime.now()
    start_date = end_date - timedelta(days=date_range)

    # Format dates as YYYY-MM-DD
    date_format = "%Y-%m-%d"
    current_date = start_date

    # Check every 7 days to avoid too many API calls
    while current_date <= end_date:
        date_str = current_date.strftime(date_format)

        try:
            resp = requests.get(
                f"{API_BASE_V2}/appointments",
                headers=headers,
                params={"date": date_str},
                timeout=API_TIMEOUT
            )

            if resp.status_code == 200:
                data = resp.json()

                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])

                    # Extract operatories
                    for item in items:
                        operatory = item.get("operatory")
                        if operatory and operatory not in ["", "N/A"]:
                            operatories.add(operatory)
        except Exception as e:
            print(f"Error fetching operatories: {e}")

        # Move to next date
        current_date += timedelta(days=7)

    # Print the list of operatories
    if operatories:
        print(f"Found {len(operatories)} operatories")
    else:
        print("No operatories found in appointment data")

    return sorted(operatories)

def fetch_appointments(request_key, date):
    """Fetch all appointments for a specific date."""
    headers = {"Request-Key": request_key}

    # Get all appointments for the day
    print(f"Fetching all appointments for {date}...")

    # Try different date formats
    date_formats = [
        date,  # Try the original format (YYYY-MM-DD)
        date.replace("-", "/"),  # Try YYYY/MM/DD
        f"{date.split('-')[1]}/{date.split('-')[2]}/{date.split('-')[0]}"  # Try MM/DD/YYYY
    ]

    all_items = []
    best_format = None

    # First, find which date format works
    for date_format in date_formats:
        try:
            params = {"date": date_format}
            resp = requests.get(
                f"{API_BASE_V2}/appointments",
                headers=headers,
                params=params,
                timeout=API_TIMEOUT
            )

            if resp.status_code == 200:
                data = resp.json()

                # Extract items from v2 response (which has a different structure)
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    if items:
                        print(f"Found appointments using date format: {date_format}")
                        best_format = date_format
                        break  # Stop trying different formats if we found appointments
            elif resp.status_code != 204:
                print(f"Error with date format {date_format}: {resp.status_code}")
        except Exception as e:
            print(f"Error with date format {date_format}: {e}")

    if not best_format:
        print("No appointments found with any date format.")
        return []

    # Now fetch all pages of appointments using the best format
    print(f"Fetching all pages of appointments using format: {best_format}")

    # The v2 API doesn't support pagination directly, so we'll try to get all appointments
    # by using different time ranges throughout the day
    time_ranges = [
        "00:00-08:00",  # Early morning
        "08:00-12:00",  # Morning
        "12:00-17:00",  # Afternoon
        "17:00-23:59"   # Evening
    ]

    for time_range in time_ranges:
        try:
            params = {
                "date": best_format,
                "time_range": time_range
            }

            resp = requests.get(
                f"{API_BASE_V2}/appointments",
                headers=headers,
                params=params,
                timeout=API_TIMEOUT
            )

            if resp.status_code == 200:
                data = resp.json()

                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    if items:
                        all_items.extend(items)
                        print(f"  Found {len(items)} appointments in time range {time_range}")
        except Exception as e:
            print(f"Error fetching time range {time_range}: {e}")

    # Also try the v4 endpoint to get any additional appointments
    try:
        params = {"date": date}
        offset = 0
        limit = 100

        while True:
            paged_params = params.copy()
            paged_params.update({"offset": offset, "limit": limit})

            resp = requests.get(
                f"{API_BASE_V4}/appointments",
                headers=headers,
                params=paged_params,
                timeout=API_TIMEOUT
            )

            if resp.status_code == 200:
                data = resp.json()
                items = data.get("items", [])

                # Check if these are new appointments (not duplicates)
                new_items = []
                existing_ids = set(a.get("appointment_sr_no") for a in all_items if a.get("appointment_sr_no"))

                for item in items:
                    if item.get("appointment_sr_no") not in existing_ids:
                        new_items.append(item)

                if new_items:
                    print(f"  Found {len(new_items)} additional appointments from v4 endpoint")
                    all_items.extend(new_items)

                if len(items) < limit:
                    break

                offset += limit
            else:
                break
    except Exception as e:
        print(f"Error fetching from v4 endpoint: {e}")

    if not all_items:
        print("No appointments found.")
        return []

    print(f"Total appointments found: {len(all_items)}")

    # Print all operatories found in appointments
    operatories_found = set(a.get("operatory") for a in all_items if a.get("operatory"))
    if operatories_found:
        print(f"Operatories found in appointments: {', '.join(sorted(operatories_found))}")

    # Filter to only include appointments for the specified date
    # (in case we got appointments for other dates)
    date_items = []
    for a in all_items:
        appt_date = a.get("date", "")
        if appt_date:
            # Try to normalize the date format
            try:
                if "/" in appt_date:
                    parts = appt_date.split("/")
                    if len(parts[2]) == 4:  # MM/DD/YYYY
                        normalized_date = f"{parts[2]}-{parts[0]}-{parts[1]}"
                    else:  # YYYY/MM/DD
                        normalized_date = appt_date.replace("/", "-")
                else:
                    normalized_date = appt_date

                if normalized_date.startswith(date):
                    date_items.append(a)
            except:
                # If we can't parse the date, include it anyway
                date_items.append(a)

    # Remove duplicate appointments (the API returns each appointment 3 times)
    unique_items = []
    seen_appointments = set()

    for appt in date_items:
        # Create a unique key for each appointment
        key = (
            appt.get("patient_id", ""),
            appt.get("time", ""),
            appt.get("operatory", ""),
            appt.get("description", "")
        )

        if key not in seen_appointments:
            seen_appointments.add(key)
            unique_items.append(appt)

    print(f"Removed {len(date_items) - len(unique_items)} duplicate appointments")

    return unique_items

def display_operatory_schedule(appointments, transactions, operatories, date_str, include_modno=True):
    """Display a side-by-side schedule for multiple operatories."""
    # Remove duplicate appointments (the API returns each appointment 3 times)
    unique_appointments = []
    seen_appointments = set()

    for appt in appointments:
        # Create a unique key for each appointment
        key = (
            appt.get("patient_id", ""),
            appt.get("time", ""),
            appt.get("operatory", ""),
            appt.get("description", "")
        )

        if key not in seen_appointments:
            seen_appointments.add(key)
            unique_appointments.append(appt)

    print(f"Removed {len(appointments) - len(unique_appointments)} duplicate appointments")
    appointments = unique_appointments

    # Identify no-show and cancelled appointments
    modno_appointments = []
    regular_appointments = []

    for appt in appointments:
        description = appt.get("description", "").upper()
        if "MODNO" in description:
            modno_appointments.append(appt)
        else:
            regular_appointments.append(appt)

    # If include_modno is True, add MODNO appointments to regular_appointments
    if include_modno:
        regular_appointments.extend(modno_appointments)

    # Find no-show transactions (including those with $0 fee)
    no_show_transactions = [
        t for t in transactions
        if t.get("transaction_type") == "Procedure" and
        ("NO SHOW" in t.get("procedure_description", "").upper() or
         "CANCEL" in t.get("procedure_description", "").upper() or
         "MISSED" in t.get("procedure_description", "").upper() or
         "LATE" in t.get("procedure_description", "").upper())
    ]

    # Also check for specific no-show procedure codes (D9986, D9987)
    no_show_codes = ["D9986", "D9987"]  # D9986 = Missed Appointment, D9987 = Cancelled Appointment
    code_no_show_transactions = [
        t for t in transactions
        if t.get("transaction_type") == "Procedure" and
        t.get("procedure_code") in no_show_codes
    ]

    # Combine both lists
    no_show_transactions.extend(code_no_show_transactions)

    # Create a set of patient IDs with no-show transactions
    no_show_patient_ids = set(t.get("patient_id") for t in no_show_transactions)

    # Identify MODNO appointments without a corresponding no-show fee
    modno_without_fee = []
    for appt in modno_appointments:
        patient_id = appt.get("patient_id")
        if patient_id not in no_show_patient_ids:
            modno_without_fee.append(appt)

    # Group appointments by operatory
    appts_by_operatory = {}
    for op in operatories:
        appts_by_operatory[op] = []

    # Print all appointments for debugging
    print("\nAll appointments found:")
    print(f"{'TIME':<8}{'PATIENT':<25}{'OPERATORY':<10}{'PROVIDER':<10}{'DESCRIPTION':<30}")
    print(f"{'-' * 8}{'-' * 25}{'-' * 10}{'-' * 10}{'-' * 30}")

    for appt in sorted(appointments, key=lambda a: a.get("time", "")):
        time = appt.get("time", "")
        patient_name = appt.get("patient_name", "Unknown Patient")
        operatory = appt.get("operatory", "N/A")
        provider_id = appt.get("provider_id", "N/A")
        description = appt.get("description", "")

        # Truncate long names/descriptions
        if len(patient_name) > 22:
            patient_name = patient_name[:19] + "..."
        if len(description) > 27:
            description = description[:24] + "..."

        print(f"{time:<8}{patient_name:<25}{operatory:<10}{provider_id:<10}{description:<30}")

    # Print regular appointments (excluding MODNO)
    print("\nRegular appointments (excluding MODNO):")
    print(f"{'TIME':<8}{'PATIENT':<25}{'OPERATORY':<10}{'PROVIDER':<10}{'DESCRIPTION':<30}")
    print(f"{'-' * 8}{'-' * 25}{'-' * 10}{'-' * 10}{'-' * 30}")

    for appt in sorted(regular_appointments, key=lambda a: a.get("time", "")):
        time = appt.get("time", "")
        patient_name = appt.get("patient_name", "Unknown Patient")
        operatory = appt.get("operatory", "N/A")
        provider_id = appt.get("provider_id", "N/A")
        description = appt.get("description", "")

        # Truncate long names/descriptions
        if len(patient_name) > 22:
            patient_name = patient_name[:19] + "..."
        if len(description) > 27:
            description = description[:24] + "..."

        print(f"{time:<8}{patient_name:<25}{operatory:<10}{provider_id:<10}{description:<30}")

    # Group appointments by operatory
    for appt in regular_appointments:
        operatory = appt.get("operatory")
        if operatory in operatories:
            appts_by_operatory[operatory].append(appt)

    # Print appointments for selected operatories
    print(f"\nAppointments for selected operatories ({', '.join(operatories)}):")
    for op in operatories:
        print(f"\nOperatory: {op}")
        if appts_by_operatory[op]:
            for appt in sorted(appts_by_operatory[op], key=lambda a: a.get("time", "")):
                time = appt.get("time", "")
                patient_name = appt.get("patient_name", "Unknown Patient")
                provider_id = appt.get("provider_id", "N/A")
                description = appt.get("description", "")
                print(f"  {time} - {patient_name} - {provider_id} - {description}")
        else:
            print("  No appointments found")

    # Format date for display
    date_obj = datetime.strptime(date_str, "%Y-%m-%d")
    formatted_date = date_obj.strftime("%A, %B %d, %Y")

    # Display the schedule header
    print(f"\n{'=' * 100}")
    print(f"{'DAILY SCHEDULE BY OPERATORY':^100}")
    print(f"{formatted_date:^100}")
    print(f"{'=' * 100}")

    # Create time slots from 8:00 to 17:00 in 10-minute increments
    start_time = datetime.strptime("08:00", "%H:%M")
    end_time = datetime.strptime("17:00", "%H:%M")

    # Create a dictionary of appointments by time for each operatory
    appts_by_time = {}
    for op in operatories:
        appts_by_time[op] = {}
        for appt in appts_by_operatory[op]:
            time_str = appt.get("time", "")
            if time_str:
                try:
                    time_obj = datetime.strptime(time_str, "%H:%M")
                    appts_by_time[op][time_str] = appt
                except ValueError:
                    # Skip appointments with invalid time format
                    continue

    # Calculate column width for each operatory
    col_width = (100 - 10) // len(operatories)

    # Print header with operatory names
    print(f"{'TIME':<10}", end="")
    for op in operatories:
        print(f"{op:^{col_width}}", end="")
    print()
    print("-" * 10 + "-" * (col_width * len(operatories)))

    # Track active appointments for each operatory
    active_appts = {}
    for op in operatories:
        active_appts[op] = None

    # Display the schedule
    current_time = start_time
    while current_time <= end_time:
        time_str = current_time.strftime("%H:%M")

        # Check if any appointment starts at this time in any operatory
        new_appt_started = False
        for op in operatories:
            if time_str in appts_by_time[op]:
                new_appt_started = True
                appt = appts_by_time[op][time_str]
                length_min = float(appt.get("length", "0"))
                end_time_obj = current_time + timedelta(minutes=length_min)

                active_appts[op] = {
                    "appt": appt,
                    "end_time": end_time_obj,
                    "start_time": current_time
                }

        # Only show time slots at hour and half-hour marks, or when an appointment starts
        if time_str.endswith(":00") or time_str.endswith(":30") or new_appt_started:
            print(f"{time_str:<10}", end="")

            # Display appointment info for each operatory
            for op in operatories:
                if active_appts[op] and active_appts[op]["end_time"] > current_time:
                    appt = active_appts[op]["appt"]
                    patient_name = appt.get("patient_name", "Unknown")
                    description = appt.get("description", "")
                    provider_id = appt.get("provider_id", "")

                    # Truncate long names/descriptions
                    display_text = f"{patient_name} - {provider_id}"
                    if len(display_text) > col_width - 4:
                        display_text = display_text[:col_width-7] + "..."

                    print(f"{display_text:<{col_width}}", end="")
                else:
                    print(f"{'[OPEN]':<{col_width}}", end="")
            print()

        # Update active appointments
        for op in operatories:
            if active_appts[op] and active_appts[op]["end_time"] <= current_time:
                active_appts[op] = None

        # Move to next time slot (10 minutes)
        current_time += timedelta(minutes=10)

    # Display MODNO appointments without a no-show fee
    if modno_without_fee:
        print(f"\n{'=' * 100}")
        print(f"{'WARNING: MODNO APPOINTMENTS WITHOUT NO-SHOW CODES':^100}")
        print(f"{'=' * 100}")
        print(f"The following patients were marked as MODNO but don't have a no-show transaction posted:")
        print(f"(Even if you waive the fee, please post a $0 no-show code for tracking purposes)")
        print(f"\n{'TIME':<8}{'PATIENT':<25}{'OPERATORY':<10}{'PROVIDER':<10}")
        print(f"{'-' * 8}{'-' * 25}{'-' * 10}{'-' * 10}")

        for appt in sorted(modno_without_fee, key=lambda a: a.get("time", "")):
            time = appt.get("time", "")
            patient_name = appt.get("patient_name", "Unknown Patient")
            operatory = appt.get("operatory", "N/A")
            provider_id = appt.get("provider_id", "N/A")

            # Truncate long patient names
            if len(patient_name) > 22:
                patient_name = patient_name[:19] + "..."

            print(f"{time:<8}{patient_name:<25}{operatory:<10}{provider_id:<10}")

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Generate a side-by-side operatory schedule")
    parser.add_argument("date", help="Date in YYYY-MM-DD format")
    parser.add_argument("--operatories", help="Comma-separated list of operatories to display (e.g., DL01,DL02)")
    parser.add_argument("--office-id", default="D43989", help="Office ID")
    parser.add_argument("--secret-key", default="35442814D4396E20C222", help="Secret key")
    parser.add_argument("--app-id", default="fdd52aaffb0c1bead647874ba551db0c", help="App ID")
    parser.add_argument("--app-key", default="88254bfa2224607ef425646aafe5f722", help="App key")
    parser.add_argument("--list-operatories", action="store_true", help="List all available operatories and exit")

    args = parser.parse_args()

    # Validate date format
    try:
        datetime.strptime(args.date, "%Y-%m-%d")
    except ValueError:
        print("Error: Date must be in YYYY-MM-DD format")
        return

    # Get request key
    print("Getting request key...")
    request_key = get_request_key(args.office_id, args.secret_key, args.app_id, args.app_key)
    if not request_key:
        print("Failed to get request key. Exiting.")
        return

    print("Request key obtained successfully")

    # If --list-operatories flag is set, just list operatories and exit
    if args.list_operatories:
        operatories = fetch_operatories(request_key)
        print("\nAvailable operatories:")
        for op in operatories:
            print(f"  - {op}")
        return

    # Parse operatories if provided, otherwise use default
    if args.operatories:
        selected_operatories = [op.strip() for op in args.operatories.split(",")]
    else:
        selected_operatories = ["DL01", "DL02"]  # Default operatories

    print(f"Displaying schedule for operatories: {', '.join(selected_operatories)}")

    # Fetch all transactions for the office on the selected date
    print(f"Fetching all transactions for {args.date}...")
    transactions = fetch_transactions(request_key, args.date)

    # Fetch all appointments for the selected date
    print(f"Fetching all appointments for {args.date}...")
    appointments = fetch_appointments(request_key, args.date)

    # Display the operatory schedule
    display_operatory_schedule(appointments, transactions, selected_operatories, args.date)

if __name__ == "__main__":
    main()
