#!/usr/bin/env python3
"""
Dental Appointment Viewer
A simple GUI for viewing dental appointments by operatory
"""

import json
import sys
import os
import requests
from datetime import datetime
from gooey import Gooey, GooeyParser

# API configuration
API_BASE_V2 = "https://api.sikkasoft.com/v2"
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_TIMEOUT = 30  # seconds

# Define operatory names - these are the actual operatories from your system
OPERATORIES = [
    'DAZ1', 'DAZ2', 'DL01', 'DL02', 'NS02', 'X-PS', 'Y-10', 'ZZ01', 'ZZ02'
]

def load_credentials():
    """Load API credentials from credentials.json file."""
    try:
        with open("credentials.json", "r") as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: credentials.json file not found.")
        print("Please create a credentials.json file with your Sikka API credentials.")
        sys.exit(1)
    except json.JSONDecodeError:
        print("Error: Invalid JSO<PERSON> in credentials.json file.")
        sys.exit(1)

def authenticate(app_id, app_key, office_id, secret_key):
    """Authenticate with Sikka API and get request key."""
    try:
        auth_resp = requests.post(
            f"{API_BASE_V4}/request_key",
            headers={"Content-Type": "application/json"},
            json={
                "grant_type": "request_key",
                "office_id": office_id,
                "secret_key": secret_key,
                "app_id": app_id,
                "app_key": app_key
            },
            timeout=API_TIMEOUT
        )

        if auth_resp.status_code == 200:
            data = auth_resp.json()
            request_key = data.get("request_key")
            if request_key:
                return request_key
            else:
                print("Error: No request key in response.")
        else:
            print(f"Authentication failed with status code: {auth_resp.status_code}")
    except Exception as e:
        print(f"Error during authentication: {e}")

    sys.exit(1)

def fetch_appointments(request_key, target_date, selected_operatories):
    """Fetch appointments for a specific date and filter by operatories."""
    headers = {"Request-Key": request_key}

    # Set up parameters with the correct date filtering parameters
    params = {
        "startdate": target_date,
        "enddate": target_date,
        "date_filter_on": "appointment_date"
    }

    try:
        resp = requests.get(
            f"{API_BASE_V2}/appointments",
            headers=headers,
            params=params,
            timeout=API_TIMEOUT
        )

        if resp.status_code == 200:
            data = resp.json()

            # Extract items from v2 response
            if isinstance(data, list) and len(data) > 0:
                all_items = data[0].get("items", [])

                # Filter by selected operatories
                if selected_operatories:
                    filtered_items = [a for a in all_items if a.get("operatory") in selected_operatories]
                    return filtered_items
                else:
                    return all_items
            else:
                return []
        else:
            print(f"Error fetching appointments: {resp.status_code}")
            return []
    except Exception as e:
        print(f"Error: {e}")
        return []

def format_appointments(appointments):
    """Format appointments into a readable text format."""
    if not appointments:
        return "No appointments found for the selected date and operatories."

    # Sort appointments by operatory and time
    sorted_appointments = sorted(
        appointments,
        key=lambda a: (a.get("operatory", ""), a.get("time", ""))
    )

    # Format the output
    output = []
    current_operatory = None

    for appt in sorted_appointments:
        operatory = appt.get("operatory", "N/A")

        # Print operatory header when it changes
        if operatory != current_operatory:
            output.append("\n" + "=" * 50)
            output.append(f"OPERATORY: {operatory}")
            output.append("=" * 50)
            current_operatory = operatory

        # Get appointment details
        time = appt.get("time", "")
        patient_name = appt.get("patient_name", "Unknown Patient")
        appt_sr_no = appt.get("appointment_sr_no", "")
        length = appt.get("length", "")
        provider_id = appt.get("provider_id", "")
        description = appt.get("description", "")

        # Check if this is a real patient appointment or just blocked time
        is_blocked = "Blocked" in str(appt_sr_no) or not patient_name or patient_name.strip() == ""

        # Format appointment details
        output.append(f"\nTime: {time} | Length: {length} min | Provider: {provider_id}")
        output.append(f"Patient: {patient_name}")
        output.append(f"Appt Serial #: {appt_sr_no}")
        output.append(f"Description: {description}")
        output.append("-" * 50)

    return "\n".join(output)

@Gooey(
    program_name="Dental Appointment Viewer",
    program_description="View dental appointments by date and operatory",
    default_size=(800, 600),
    navigation='TABBED',
    tabbed_groups=True,
    show_success_modal=False,
    show_failure_modal=False,
    clear_before_run=True,
    timing_options={
        'show_time_remaining': True,
        'hide_time_remaining_on_complete': True
    },
    use_legacy_titles=False,
    required_cols=1,
    optional_cols=1,
    column_span=1
)
def main():
    # Get today's date in YYYY-MM-DD format
    today = datetime.now().strftime("%Y-%m-%d")

    # Create the parser
    parser = GooeyParser(description="View dental appointments by date and operatory")

    # Create the main group
    main_group = parser.add_argument_group("Appointment Options")

    # Add date field with calendar widget
    main_group.add_argument(
        "--date",
        metavar="Select Date",
        help="Choose appointment date",
        default=today,
        widget="DateChooser"
    )

    # Add operatories field with horizontal checkboxes
    for operatory in OPERATORIES:
        main_group.add_argument(
            f"--op_{operatory}",
            metavar=operatory,
            action="store_true",
            help=f"Include {operatory}",
            default=False,
            widget="CheckBox"
        )

    # Parse the arguments
    args = parser.parse_args()

    # Format date if needed (DateChooser might return a different format)
    try:
        date_obj = datetime.strptime(args.date, "%Y-%m-%d")
    except ValueError:
        try:
            # Try alternative format
            date_obj = datetime.strptime(args.date, "%m/%d/%Y")
        except ValueError:
            print("Error: Invalid date format. Please use YYYY-MM-DD format.")
            sys.exit(1)

    # Format date as YYYY-MM-DD
    date_str = date_obj.strftime("%Y-%m-%d")

    # Get selected operatories from checkboxes
    selected_operatories = []
    for operatory in OPERATORIES:
        if getattr(args, f"op_{operatory}"):
            selected_operatories.append(operatory)

    # Check if at least one operatory is selected
    if not selected_operatories:
        print("Error: Please select at least one operatory.")
        sys.exit(1)

    # Load credentials
    credentials = load_credentials()
    app_id = credentials.get("app_id")
    app_key = credentials.get("app_key")
    office_id = credentials.get("office_id")
    secret_key = credentials.get("secret_key")

    # Authenticate and get request key
    print("Authenticating with Sikka API...")
    request_key = authenticate(app_id, app_key, office_id, secret_key)

    # Fetch appointments
    print(f"Fetching appointments for {date_str}...")
    appointments = fetch_appointments(request_key, date_str, selected_operatories)

    # Format and display appointments
    print(f"Found {len(appointments)} appointments for {date_str} in operatories: {', '.join(selected_operatories)}")
    result = format_appointments(appointments)

    # Write results to output file
    output_file = f"appointments_{date_str}.txt"
    with open(output_file, "w") as f:
        f.write(result)

    print(f"Results saved to {output_file}")
    print("\n" + result)

    return 0

if __name__ == "__main__":
    main()
