"use strict";exports.id=8689,exports.ids=[8689],exports.modules={48689:(e,r,t)=>{t.d(r,{TranscriptionErrorHandler:()=>i});class i{static analyzeError(e){let r=e?.message||e?.toString()||"Unknown error";return r.includes("ECONNRESET")||r.includes("ETIMEDOUT")||r.includes("ENOTFOUND")||r.includes("ECONNREFUSED")?{type:"network",message:"Network connectivity issue. Please check your internet connection and try again.",retryable:!0,retryDelay:1e4,originalError:e}:r.includes("rate limit")||r.includes("quota")||r.includes("429")?{type:"quota",message:"OpenAI API rate limit exceeded. Please wait a few minutes and try again.",retryable:!0,retryDelay:6e4,originalError:e}:r.includes("timeout")||r.includes("TIMEDOUT")?{type:"timeout",message:"Request timed out. The audio file may be too large or the service is slow.",retryable:!0,retryDelay:15e3,originalError:e}:r.includes("OpenAI")||r.includes("invalid_request_error")||r.includes("authentication_error")?{type:"api",message:"OpenAI API error. Please check your API key and try again.",retryable:!1,originalError:e}:{type:"unknown",message:`Transcription failed: ${r}`,retryable:!1,originalError:e}}static async withRetry(e,r=3,t=1e3){let i;for(let a=1;a<=r;a++)try{return await e()}catch(n){i=n;let e=this.analyzeError(n);if(console.warn(`⚠️ Transcription attempt ${a}/${r} failed:`,e.message),!e.retryable||a>=r)throw n;let s=e.retryDelay||t*Math.pow(2,a-1);await new Promise(e=>setTimeout(e,s))}throw i}static{this.circuitBreaker={failures:0,lastFailureTime:0,state:"closed",threshold:5,timeout:6e4}}static async withCircuitBreaker(e){let r=Date.now();if("open"===this.circuitBreaker.state)if(r-this.circuitBreaker.lastFailureTime>this.circuitBreaker.timeout)this.circuitBreaker.state="half-open";else throw Error("Circuit breaker is open - transcription service temporarily unavailable");try{let r=await e();return"half-open"===this.circuitBreaker.state&&(this.circuitBreaker.state="closed",this.circuitBreaker.failures=0),r}catch(e){throw this.circuitBreaker.failures++,this.circuitBreaker.lastFailureTime=r,this.circuitBreaker.failures>=this.circuitBreaker.threshold&&(this.circuitBreaker.state="open"),e}}static getUserFriendlyMessage(e){switch(this.analyzeError(e).type){case"network":return"Network connection issue. Please check your internet connection and try again.";case"quota":return"Service temporarily unavailable due to high demand. Please try again in a few minutes.";case"timeout":return"The request took too long to complete. Please try again with a smaller audio file.";case"api":return"Service configuration issue. Please contact support.";default:return"An unexpected error occurred. Please try again or contact support."}}}}};