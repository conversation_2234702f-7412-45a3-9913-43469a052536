"use strict";exports.id=7149,exports.ids=[7149],exports.modules={47149:(e,t,s)=>{s.d(t,{r:()=>p});var r=s(60687),a=s(43210),l=s(78122),n=s(93613),i=s(10022),d=s(80462),c=s(99270),o=s(40228),x=s(58869),g=s(56085),m=s(41862),h=s(3589),u=s(78272),b=s(47033),y=s(14952);function p({patientId:e,appointmentId:t,appointmentDate:s,dateRange:p=7,title:j="Clinical Notes",showPagination:N=!0,showFilters:v=!1,maxHeight:w="400px",isDarkMode:f=!1}){let[S,$]=(0,a.useState)(new Set),[k,C]=(0,a.useState)(""),[A,L]=(0,a.useState)(!1),[P,D]=(0,a.useState)({}),[T,F]=(0,a.useState)(new Set),{notes:U,loading:E,error:R,pagination:_,summary:I,debug:B,refetch:G,nextPage:H,prevPage:M,hasNotes:O,isEmpty:V}=function(e={}){let{patientId:t,appointmentId:s,appointmentDate:r,dateRange:l=7,limit:n=20,page:i=1,autoFetch:d=!0}=e,[c,o]=(0,a.useState)([]),[x,g]=(0,a.useState)(!1),[m,h]=(0,a.useState)(null),[u,b]=(0,a.useState)(null),[y,p]=(0,a.useState)(null),[j,N]=(0,a.useState)(null),[v,w]=(0,a.useState)(i),f=(0,a.useCallback)(async()=>{if(!t){o([]),b(null),p(null),N(null);return}g(!0),h(null);try{let e=new URLSearchParams({limit:n.toString(),page:v.toString()});s&&e.append("appointment_id",s),r&&(e.append("appointment_date",r),e.append("date_range",l.toString()));let a=await fetch(`/api/patients/${t}/clinical-notes?${e}`);if(!a.ok){let e=await a.json();throw Error(e.error||`HTTP ${a.status}: ${a.statusText}`)}let i=await a.json();o(i.notes||[]),b(i.pagination),p(i.summary),N(i.debug)}catch(e){console.error("Error fetching clinical notes:",e),h(e.message||"Failed to fetch clinical notes"),o([]),b(null),p(null)}finally{g(!1)}},[t,s,r,l,n,v]),S=(0,a.useCallback)(async()=>{w(1),await f()},[f]),$=(0,a.useCallback)(async()=>{u?.hasNext&&w(e=>e+1)},[u?.hasNext]),k=(0,a.useCallback)(async()=>{u?.hasPrev&&w(e=>e-1)},[u?.hasPrev]),C=c.length>0;return{notes:c,loading:x,error:m,pagination:u,summary:y,debug:j,fetchNotes:f,refetch:S,nextPage:$,prevPage:k,hasNotes:C,isEmpty:!x&&!C&&!m}}({patientId:e,appointmentId:t,appointmentDate:s,dateRange:p,limit:10,autoFetch:!0}),q=e=>e.replace(/\{LF\}/g,"\n").replace(/\{cm\}/g,", ").replace(/\{CR\}/g,"\n").replace(/\{TAB\}/g,"  ").replace(/\{[^}]+\}/g,"").replace(/\s+/g," ").trim(),z=e=>{let t=new Set(S);t.has(e)?t.delete(e):t.add(e),$(t)},J=U.filter(e=>{if(!k)return!0;let t=k.toLowerCase();return(e.text||e.notes||"").toLowerCase().includes(t)||e.provider.toLowerCase().includes(t)||(e.appointmentType||"").toLowerCase().includes(t)||e.procedures.some(e=>e.toLowerCase().includes(t))||(e.tooth_number||"").toLowerCase().includes(t)||(e.surface||"").toLowerCase().includes(t)}).reduce((e,t)=>{let s=t.date;return e[s]||(e[s]=[]),e[s].push(t),e},{});return E?(0,r.jsx)("div",{className:`p-4 rounded-lg border ${f?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)(l.A,{className:"w-4 h-4 animate-spin text-blue-600"}),(0,r.jsx)("span",{className:`text-sm ${f?"text-gray-300":"text-gray-600"}`,children:"Loading clinical notes..."})]})}):R?(0,r.jsx)("div",{className:`p-4 rounded-lg border ${f?"bg-red-900/20 border-red-800":"bg-red-50 border-red-200"}`,children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 text-red-600"}),(0,r.jsxs)("span",{className:`text-sm ${f?"text-red-300":"text-red-800"}`,children:["Error loading clinical notes: ",R]}),(0,r.jsx)("button",{onClick:G,className:`ml-2 px-2 py-1 text-xs rounded ${f?"bg-red-800 hover:bg-red-700 text-red-200":"bg-red-100 hover:bg-red-200 text-red-800"}`,children:"Retry"})]})}):V?(0,r.jsx)("div",{className:`p-4 rounded-lg border ${f?"bg-gray-800 border-gray-700":"bg-gray-50 border-gray-200"}`,children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.A,{className:`w-8 h-8 mx-auto mb-2 ${f?"text-gray-500":"text-gray-400"}`}),(0,r.jsxs)("p",{className:`text-sm ${f?"text-gray-400":"text-gray-600"}`,children:["No clinical notes found",s&&` within ${p} days of ${s}`]})]})}):(0,r.jsxs)("div",{className:`rounded-lg border ${f?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[(0,r.jsxs)("div",{className:`p-4 border-b ${f?"border-gray-700":"border-gray-200"}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.A,{className:"w-5 h-5 text-blue-600"}),(0,r.jsx)("h3",{className:`font-semibold ${f?"text-white":"text-gray-900"}`,children:j}),I&&(0,r.jsxs)("span",{className:`text-xs px-2 py-1 rounded-full ${f?"bg-blue-900/30 text-blue-300":"bg-blue-100 text-blue-800"}`,children:[I.totalNotes," notes"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[v&&(0,r.jsx)("button",{onClick:()=>L(!A),className:`p-1 rounded ${f?"hover:bg-gray-700 text-gray-400":"hover:bg-gray-100 text-gray-600"}`,title:"Debug info",children:(0,r.jsx)(d.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:G,className:`p-1 rounded ${f?"hover:bg-gray-700 text-gray-400":"hover:bg-gray-100 text-gray-600"}`,title:"Refresh",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})]}),v&&(0,r.jsx)("div",{className:"mt-3",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.A,{className:`w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 ${f?"text-gray-500":"text-gray-400"}`}),(0,r.jsx)("input",{type:"text",placeholder:"Search notes...",value:k,onChange:e=>C(e.target.value),className:`w-full pl-9 pr-4 py-2 text-sm rounded border ${f?"bg-gray-700 border-gray-600 text-white placeholder-gray-400":"bg-white border-gray-300 text-gray-900 placeholder-gray-500"} focus:ring-2 focus:ring-blue-500 focus:border-transparent`})]})}),A&&B&&(0,r.jsxs)("div",{className:`mt-3 p-2 rounded text-xs ${f?"bg-gray-900/50 text-gray-400":"bg-gray-50 text-gray-600"}`,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Strategy:"})," ",B.searchStrategy]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"API Calls:"})," ",B.apiCalls.join(", ")]}),B.errors.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Errors:"})," ",B.errors.join(", ")]})]})]}),(0,r.jsx)("div",{className:"max-h-96 overflow-y-auto",children:Object.entries(J).sort(([e],[t])=>t.localeCompare(e)).map(([e,t])=>(0,r.jsxs)("div",{className:`border-b last:border-b-0 ${f?"border-gray-700":"border-gray-100"}`,children:[(0,r.jsx)("div",{className:`px-4 py-2 ${f?"bg-gray-900/50":"bg-gray-50"}`,children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 text-blue-600"}),(0,r.jsx)("span",{className:`text-sm font-medium ${f?"text-gray-300":"text-gray-700"}`,children:new Date(e).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}),(0,r.jsxs)("span",{className:`text-xs px-2 py-1 rounded-full ${f?"bg-gray-700 text-gray-400":"bg-gray-200 text-gray-600"}`,children:[t.length," note",t.length>1?"s":""]})]})}),t.map(e=>{let t=function(e){let t=new Date(e.date),s=Math.floor((new Date().getTime()-t.getTime())/864e5);return{formattedDate:t.toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric"}),shortDate:t.toLocaleDateString("en-US",{month:"short",day:"numeric"}),providerInitials:e.provider.split(" ").map(e=>e.charAt(0)).join("").toUpperCase(),procedureList:e.procedures.length>0?e.procedures.join(", "):"No procedures",notePreview:e.preview||(e.text||e.notes||"").substring(0,200)+((e.text||e.notes||"").length>200?"...":""),isRecent:s<=30}}(e),s=S.has(e.id);return(0,r.jsx)("div",{className:`p-3 sm:p-4 ${f?"hover:bg-gray-700/50":"hover:bg-gray-50"}`,children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 text-blue-600 flex-shrink-0"}),(0,r.jsx)("span",{className:`text-sm font-medium ${f?"text-gray-300":"text-gray-700"}`,children:new Date(e.date).toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric",year:"numeric"})})]}),t.provider&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(x.A,{className:"w-4 h-4 text-green-600 flex-shrink-0"}),(0,r.jsx)("span",{className:`text-sm ${f?"text-green-400":"text-green-700"}`,children:t.provider})]})]}),(0,r.jsxs)("div",{className:`text-sm ${f?"text-gray-300":"text-gray-800"} bg-gray-50 dark:bg-gray-900/50 p-3 rounded border-l-4 border-blue-500`,children:[P[e.id]&&!s&&(0,r.jsx)("div",{className:"mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded border-l-2 border-blue-400",children:(0,r.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,r.jsx)(g.A,{className:"w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs text-blue-600 dark:text-blue-400 font-medium mb-1",children:"Visit Summary"}),(0,r.jsx)("div",{className:"text-sm text-blue-800 dark:text-blue-300",children:P[e.id]})]})]})}),T.has(e.id)&&!P[e.id]&&!s&&(0,r.jsx)("div",{className:"mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded border-l-2 border-gray-300",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 text-gray-500 animate-spin"}),(0,r.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Generating summary..."})]})}),(0,r.jsx)("div",{className:"whitespace-pre-wrap break-words leading-relaxed",children:s?q(e.text||e.notes||""):P[e.id]?`${q(e.text||e.notes||"").substring(0,100)}...`:q(t.notePreview)})]}),(e.text||e.notes||"").length>100&&(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsx)("button",{onClick:()=>z(e.id),className:`flex items-center space-x-1 px-2 py-1 text-xs rounded ${f?"hover:bg-gray-600 text-gray-400":"hover:bg-gray-200 text-gray-600"}`,title:s?"Show summary":"Show full note",children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:"Show Summary"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:"Show Full Note"})]})}),P[e.id]&&!s&&(0,r.jsxs)("div",{className:"text-xs text-blue-600 dark:text-blue-400 flex items-center space-x-1",children:[(0,r.jsx)(g.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:"AI Summary"})]})]})]})},e.id)})]},e))}),N&&_&&_.totalPages>1&&(0,r.jsx)("div",{className:`p-4 border-t ${f?"border-gray-700":"border-gray-200"}`,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:`text-xs ${f?"text-gray-400":"text-gray-600"}`,children:["Page ",_.page," of ",_.totalPages," (",_.total," total notes)"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:M,disabled:!_.hasPrev,className:`p-1 rounded ${_.hasPrev?f?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-700":"text-gray-400 cursor-not-allowed"}`,children:(0,r.jsx)(b.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:H,disabled:!_.hasNext,className:`p-1 rounded ${_.hasNext?f?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-700":"text-gray-400 cursor-not-allowed"}`,children:(0,r.jsx)(y.A,{className:"w-4 h-4"})})]})]})})]})}}};