const SIKKA_API_BASE_URL = 'https://api.sikka.ai/v2';

export class SikkaApiService {
  private apiKey: string;
  private practiceId: string;

  constructor() {
    this.apiKey = process.env.SIKKA_API_KEY || '';
    this.practiceId = process.env.SIKKA_PRACTICE_ID || '';

    if (!this.apiKey) {
      console.warn('SIKKA_API_KEY is not set. Sikka API calls will fail.');
    }
    if (!this.practiceId) {
      console.warn('SIKKA_PRACTICE_ID is not set. Sikka API calls will fail.');
    }
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${SIKKA_API_BASE_URL}/${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`,
      ...options.headers,
    };

    const response = await fetch(url, { ...options, headers });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Sikka API Error: ${errorData.message || response.statusText}`);
    }

    return response.json();
  }

  async getClinicalNotes(patientId: string): Promise<any[]> {
    if (!this.apiKey || !this.practiceId) {
      console.log(`Sikka API key or Practice ID not set. Returning empty array for patient ${patientId}`);
      return [];
    }
    
    // This is a placeholder for the actual Sikka API endpoint for clinical notes.
    // The real endpoint might be different.
    // Example: `Practices/${this.practiceId}/Patients/${patientId}/ClinicalNotes`
    try {
      const data = await this.request(`Patients/${patientId}/ClinicalNotes`);
      return data.items || [];
    } catch (error) {
      console.error(`Failed to fetch clinical notes for patient ${patientId} from Sikka API:`, error);
      // In case of error, you might want to return an empty array or re-throw
      return [];
    }
  }

  async getPatientById(patientId: string): Promise<any> {
    if (!this.apiKey || !this.practiceId) {
        return null;
    }
    try {
        const data = await this.request(`Practices/${this.practiceId}/Patients/${patientId}`);
        return data;
    } catch (error) {
        console.error(`Failed to fetch patient ${patientId} from Sikka API:`, error);
        return null;
    }
  }
}
