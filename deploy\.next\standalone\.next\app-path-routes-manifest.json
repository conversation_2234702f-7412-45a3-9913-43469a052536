{"/api/ai/chat/route": "/api/ai/chat", "/api/appointments/route": "/api/appointments", "/api/cache-bust/route": "/api/cache-bust", "/api/appointments/[id]/route": "/api/appointments/[id]", "/api/clinical-notes/quick-summary/route": "/api/clinical-notes/quick-summary", "/api/clinical-notes/route": "/api/clinical-notes", "/api/debug-clinical-search/route": "/api/debug-clinical-search", "/api/debug-notes/route": "/api/debug-notes", "/api/debug/azure-connection/route": "/api/debug/azure-connection", "/api/debug/env/route": "/api/debug/env", "/api/debug/system-status/route": "/api/debug/system-status", "/api/deployment-info/route": "/api/deployment-info", "/api/manifest/route": "/api/manifest", "/api/medical-notes/route": "/api/medical-notes", "/api/note-types/route": "/api/note-types", "/api/openai/config/route": "/api/openai/config", "/api/operatories/route": "/api/operatories", "/api/patients/[id]/clinical-notes/route": "/api/patients/[id]/clinical-notes", "/api/patients/[id]/route": "/api/patients/[id]", "/api/patients/[id]/visit-count/route": "/api/patients/[id]/visit-count", "/api/patients/[id]/visits/route": "/api/patients/[id]/visits", "/api/patients/search/route": "/api/patients/search", "/api/settings/route": "/api/settings", "/api/sikka/appointments/route": "/api/sikka/appointments", "/api/test-azure-fix/route": "/api/test-azure-fix", "/api/test-sikka/route": "/api/test-sikka", "/api/test-clinical-direct/route": "/api/test-clinical-direct", "/api/tools/usb-portable/download/route": "/api/tools/usb-portable/download", "/api/tools/usb-portable/version/route": "/api/tools/usb-portable/version", "/api/tools/usb-transfer/version/route": "/api/tools/usb-transfer/version", "/api/tools/usb-transfer/download/route": "/api/tools/usb-transfer/download", "/api/voice/analyze-files/route": "/api/voice/analyze-files", "/api/voice/auto-process-azure-direct/route": "/api/voice/auto-process-azure-direct", "/api/voice/auto-process/route": "/api/voice/auto-process", "/api/voice/background-status/route": "/api/voice/background-status", "/api/voice/batch-process-robust/route": "/api/voice/batch-process-robust", "/api/voice/batch-summarize/route": "/api/voice/batch-summarize", "/api/voice/batch-transcribe-all/route": "/api/voice/batch-transcribe-all", "/api/voice/batch-transcribe/route": "/api/voice/batch-transcribe", "/api/voice/cache-invalidate/route": "/api/voice/cache-invalidate", "/api/voice/cache-status/route": "/api/voice/cache-status", "/api/voice/copy-paste-mode/route": "/api/voice/copy-paste-mode", "/api/voice/correct-date/route": "/api/voice/correct-date", "/api/voice/debug-status/route": "/api/voice/debug-status", "/api/voice/debug-transcribe/route": "/api/voice/debug-transcribe", "/api/voice/debug-transcription/route": "/api/voice/debug-transcription", "/api/voice/generate-professional-note/route": "/api/voice/generate-professional-note", "/api/voice/job-monitor/route": "/api/voice/job-monitor", "/api/voice/mark-copied/route": "/api/voice/mark-copied", "/api/voice/match-recording/route": "/api/voice/match-recording", "/api/voice/network-check/route": "/api/voice/network-check", "/api/voice/process-all/route": "/api/voice/process-all", "/api/voice/next-file/route": "/api/voice/next-file", "/api/voice/process-batch-simple/route": "/api/voice/process-batch-simple", "/api/voice/process-single/route": "/api/voice/process-single", "/api/voice/process-test/route": "/api/voice/process-test", "/api/voice/process-unified/route": "/api/voice/process-unified", "/api/voice/processing-status/route": "/api/voice/processing-status", "/api/voice/recorder-setup/route": "/api/voice/recorder-setup", "/api/voice/recording-dates/route": "/api/voice/recording-dates", "/api/voice/recordings-cache/route": "/api/voice/recordings-cache", "/api/voice/recordings-fast/route": "/api/voice/recordings-fast", "/api/voice/recordings/[id]/route": "/api/voice/recordings/[id]", "/api/voice/recordings/correct-date/route": "/api/voice/recordings/correct-date", "/api/voice/recordings/route": "/api/voice/recordings", "/api/voice/smart-sort/route": "/api/voice/smart-sort", "/api/voice/status/route": "/api/voice/status", "/api/voice/summarize/route": "/api/voice/summarize", "/api/voice/sync/route": "/api/voice/sync", "/api/voice/test-date-parsing/route": "/api/voice/test-date-parsing", "/api/voice/test-pipeline/route": "/api/voice/test-pipeline", "/api/voice/transcribe-all/route": "/api/voice/transcribe-all", "/api/voice/transcribe-stream/route": "/api/voice/transcribe-stream", "/api/voice/transcribe/progress/route": "/api/voice/transcribe/progress", "/api/voice/transcription-metadata/route": "/api/voice/transcription-metadata", "/api/voice/transfer-stream/route": "/api/voice/transfer-stream", "/api/voice/transfer/route": "/api/voice/transfer", "/api/voice/unmatch-recording/route": "/api/voice/unmatch-recording", "/api/voice/usb-setup/route": "/api/voice/usb-setup", "/api/voice/webusb-upload/route": "/api/voice/webusb-upload", "/api/voice/smart-match/route": "/api/voice/smart-match", "/favicon.ico/route": "/favicon.ico", "/_not-found/page": "/_not-found", "/api/openai/models/route": "/api/openai/models", "/api/dental-notes/generate/route": "/api/dental-notes/generate", "/api/voice/transcribe-file/route": "/api/voice/transcribe-file", "/api/clinical-notes/professionalize/route": "/api/clinical-notes/professionalize", "/api/voice/transcribe/route": "/api/voice/transcribe", "/ai-assistant/page": "/ai-assistant", "/operatories/page": "/operatories", "/appointment/[appointmentId]/page": "/appointment/[appointmentId]", "/debug-transcription/page": "/debug-transcription", "/cache-test/page": "/cache-test", "/patient-search/page": "/patient-search", "/page": "/", "/patient/[id]/page": "/patient/[id]", "/schedule/page": "/schedule", "/settings/page": "/settings", "/transcription-control/page": "/transcription-control", "/test-webusb/page": "/test-webusb", "/voice-workflow/page": "/voice-workflow", "/webusb-transfer/page": "/webusb-transfer"}