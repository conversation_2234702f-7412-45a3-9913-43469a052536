(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{3574:(e,t,a)=>{"use strict";a.d(t,{ThemeProvider:()=>s});var r=a(5155),n=a(1362),i=a(838);let o={revalidateOnFocus:!1,revalidateOnReconnect:!0,dedupingInterval:3e5,refreshInterval:0,errorRetryCount:1,shouldRetryOnError:!1,revalidateIfStale:!1,revalidateOnMount:!0,fetcher:async e=>{let t=new AbortController,a=setTimeout(()=>t.abort(),1e4);try{let r=await fetch(e,{signal:t.signal,headers:{"Cache-Control":"max-age=300"}});if(clearTimeout(a),!r.ok)throw Error("Failed to fetch: ".concat(r.status," ").concat(r.statusText));return r.json()}catch(e){if(clearTimeout(a),"AbortError"===e.name)throw Error("Request timeout");throw e}}};function s(e){let{children:t,...a}=e;return(0,r.jsx)(n.N,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,suppressHydrationWarning:!0,...a,children:(0,r.jsx)(i.BE,{value:o,children:t})})}},7242:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,9324,23)),Promise.resolve().then(a.bind(a,3574)),Promise.resolve().then(a.bind(a,9452))},9324:()=>{},9452:(e,t,a)=>{"use strict";a.d(t,{AuthWrapper:()=>s});var r=a(5155),n=a(2115),i=a(5695);let o=()=>"localhost"===window.location.hostname||window.location.hostname.includes("azurewebsites.net"),s=e=>{let{children:t}=e,[a,s]=(0,n.useState)(!0),[c,l]=(0,n.useState)(!1);return((0,i.useRouter)(),(0,n.useEffect)(()=>{if(o()){l(!0),s(!1);return}let e=async()=>{try{let e=await fetch("/.auth/me",{credentials:"include"});if(await e.json(),404===e.status)return l(!0),s(!1),!0}catch(e){return l(!0),s(!1),!0}return!1},t=async()=>{try{let e=await fetch("/.auth/me",{credentials:"include",headers:{"Cache-Control":"no-cache",Pragma:"no-cache"}});if(e.ok){let t=await e.json();if(null==t?void 0:t.clientPrincipal)return l(!0),s(!1),!0}return!1}catch(e){return console.error("Auth check failed:",e),!1}};(async()=>{if((window.location.search.includes("code=")||window.location.search.includes("id_token=")||window.location.search.includes("token="))&&await t())return window.history.replaceState({},document.title,window.location.pathname);if(!await e()&&!await t()&&!window.location.pathname.startsWith("/.auth/")){let e=encodeURIComponent(window.location.href);window.location.href="/.auth/login/aad?post_login_redirect_uri=".concat(e)}})()},[]),a)?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-lg text-slate-600 dark:text-slate-400",children:"Checking authentication status..."})]})}):c?(0,r.jsx)(r.Fragment,{children:t}):(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-lg text-slate-600 dark:text-slate-400 mb-4",children:"Redirecting to login..."}),(0,r.jsx)("button",{onClick:()=>window.location.href="/.auth/login/aad",className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"Click here if not redirected"})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2533,8096,7358],()=>t(7242)),_N_E=e.O()}]);