@echo off
echo ========================================
echo    Restarting Dental App Service
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

cd /d "C:\DentalApp"

echo Restarting Dental App service...
call pm2 restart dental-app

:: Wait for service to restart
echo Waiting for service to restart...
timeout /t 5 /nobreak >nul

:: Check service status
call pm2 list | findstr "dental-app" | findstr "online" >nul
if %errorLevel%==0 (
    echo.
    echo ========================================
    echo    Service Restarted Successfully!
    echo ========================================
    echo.
    echo Dental App is now running.
    echo Access the app at: http://localhost:3000
    echo.
    echo Restart completed at: %date% %time%
    echo.
) else (
    echo.
    echo ========================================
    echo    Service Restart Failed
    echo ========================================
    echo.
    echo The service could not be restarted.
    echo Please check the logs for more information.
    echo.
    echo Run: C:\DentalApp\scripts\logs.bat
    echo Or try: C:\DentalApp\scripts\stop-service.bat
    echo Then:   C:\DentalApp\scripts\start-service.bat
    echo.
)

echo Press any key to continue...
pause >nul
