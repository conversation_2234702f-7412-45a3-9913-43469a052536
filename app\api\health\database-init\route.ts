import { NextRequest, NextResponse } from 'next/server';
import { VercelDB } from '@/lib/vercel-db';
import { featureFlags } from '@/lib/feature-flags';

/**
 * Database Initialization Endpoint
 * Initializes Vercel Postgres tables for transcriptions, appointments, and audit logs
 */
export async function POST(request: NextRequest) {
  try {
    // Check if Vercel Postgres is enabled
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        message: 'Enable FEATURE_FLAG_VERCEL_POSTGRES=true to initialize database',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    console.log('🚀 Initializing Vercel Postgres database tables...');

    // Initialize all database tables
    const result = await VercelDB.initializeTables();

    return NextResponse.json({
      success: true,
      message: 'Database tables initialized successfully',
      tables_created: [
        'transcriptions',
        'audit_log', 
        'appointments'
      ],
      indexes_created: [
        'idx_transcriptions_patient_id',
        'idx_transcriptions_recording_date',
        'idx_transcriptions_status',
        'idx_audit_log_timestamp',
        'idx_audit_log_resource_id',
        'idx_appointments_date',
        'idx_appointments_operatory',
        'idx_appointments_provider',
        'idx_appointments_sikka_id'
      ],
      result
    });

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    
    return NextResponse.json({
      error: 'Database initialization failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      solution: 'Check VERCEL_POSTGRES_URL environment variable and database connectivity'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    endpoint: '/api/health/database-init',
    method: 'POST',
    description: 'Initialize Vercel Postgres database tables',
    tables: ['transcriptions', 'audit_log', 'appointments'],
    usage: 'Send POST request to initialize database schema'
  });
}