{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "excludedTypes": "Request"}}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[3.*, 4.0.0)"}, "functionTimeout": "00:10:00", "retry": {"strategy": "exponentialBackoff", "maxRetryCount": 3, "minimumInterval": "00:00:02", "maximumInterval": "00:00:30"}, "extensions": {"blobs": {"maxDegreeOfParallelism": 2}}, "queues": {"maxPollingInterval": "00:00:02", "visibilityTimeout": "00:00:30", "batchSize": 1, "maxDequeueCount": 3, "newBatchThreshold": 1}}