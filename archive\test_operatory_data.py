import requests
import json
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Target date and provider
TARGET_DATE = "2023-05-16"  # Try an earlier date
TARGET_PROVIDER = "LL01"

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        }
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None

    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def fetch_practice_schedule(request_key):
    """Fetch practice schedule and analyze the response structure."""
    print(f"\nFetching practice schedule for {TARGET_DATE}...")
    headers = {"Request-Key": request_key}
    params = {"date": TARGET_DATE}

    resp = requests.get(f"{API_BASE_V2}/practice_schedule", headers=headers, params=params)

    print(f"Response status: {resp.status_code}")
    if resp.status_code == 200:
        try:
            data = resp.json()
            print(f"Success! Got data")

            # Analyze the structure of the response
            print("\nAnalyzing response structure:")
            if isinstance(data, list):
                print(f"Response is a list with {len(data)} items")

                for i, item in enumerate(data):
                    print(f"\nItem {i+1}:")
                    print(f"Keys: {list(item.keys())}")

                    if "items" in item:
                        sub_items = item.get("items", [])
                        print(f"Contains {len(sub_items)} sub-items")

                        # Group sub-items by resource_type
                        resource_types = {}
                        for sub_item in sub_items:
                            resource_type = sub_item.get("resource_type")
                            if resource_type not in resource_types:
                                resource_types[resource_type] = []
                            resource_types[resource_type].append(sub_item)

                        print(f"Resource types found: {list(resource_types.keys())}")

                        # Look for operatories (resource_type 2)
                        operatories = resource_types.get("2", [])
                        print(f"Found {len(operatories)} operatories")

                        if operatories:
                            print("\nOperatory details:")
                            for j, op in enumerate(operatories[:3]):  # Show first 3 operatories
                                print(f"  Operatory {j+1}:")
                                print(f"  ID: {op.get('resource_id')}")
                                print(f"  Name: {op.get('resource_name')}")

                                # Check for events/appointments
                                events = op.get("events", [])
                                print(f"  Events: {len(events)}")

                                if events:
                                    print("  First few events:")
                                    for k, event in enumerate(events[:3]):  # Show first 3 events
                                        print(f"    Event {k+1}:")
                                        print(f"    Keys: {list(event.keys())}")
                                        print(f"    Appointment SR#: {event.get('appointment_sr_no')}")
                                        print(f"    Start time: {event.get('start_time')}")
                                        print(f"    End time: {event.get('end_time')}")
                                        print(f"    Patient: {event.get('patient_name')}")
            else:
                print(f"Response is not a list. Keys: {list(data.keys())}")

            return data
        except Exception as e:
            print(f"Error parsing response: {e}")
            print(f"Response text: {resp.text[:200]}...")
            return {}
    else:
        print("Error response:")
        print(resp.text[:500])
        return {}

def fetch_appointments(request_key):
    """Fetch appointments and check for operatory information."""
    print(f"\nFetching appointments for {TARGET_DATE}...")
    headers = {"Request-Key": request_key}
    params = {"date": TARGET_DATE}

    resp = requests.get(f"{API_BASE_V4}/appointments", headers=headers, params=params)

    if resp.status_code == 200:
        data = resp.json()
        items = data.get("items", [])

        print(f"Found {len(items)} appointments")

        if items:
            print("\nChecking appointment data for operatory information:")
            print(f"First appointment keys: {list(items[0].keys())}")

            # Check if any appointments have operatory information
            operatory_fields = [key for key in items[0].keys() if "operatory" in key.lower()]
            print(f"Operatory-related fields: {operatory_fields}")

            if operatory_fields:
                print("\nSample operatory values:")
                for i, appt in enumerate(items[:5]):  # Check first 5 appointments
                    print(f"Appointment {i+1}:")
                    for field in operatory_fields:
                        print(f"  {field}: {appt.get(field)}")
            else:
                print("No operatory fields found in appointment data")

        return items
    else:
        print(f"Error: {resp.status_code}")
        print(resp.text)
        return []

def main():
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return

    # Fetch and analyze practice schedule
    schedule_data = fetch_practice_schedule(request_key)

    # Fetch and check appointments for operatory information
    appointments = fetch_appointments(request_key)

    print("\nAnalysis complete.")

if __name__ == "__main__":
    main()
