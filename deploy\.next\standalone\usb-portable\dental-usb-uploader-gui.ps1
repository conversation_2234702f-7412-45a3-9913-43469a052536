# Dental USB Uploader GUI - Standalone Single File
# Version 1.0.0 - Corporate-Friendly Single File Solution

[CmdletBinding()]
param(
    [Parameter(Mandatory=$false)]
    [switch]$Console = $false
)

# Configuration - Enhanced for Corporate Environments
$CURRENT_VERSION = "1.1.0"
$AZURE_BASE_URL = "https://dentalapp-cpcwgucdawhmb4hu.centralus-01.azurewebsites.net"
$UPDATE_CHECK_URL = "$AZURE_BASE_URL/api/tools/usb-transfer/version"
$JOB_MONITOR_URL = "$AZURE_BASE_URL/api/voice/job-monitor"
$CACHE_INVALIDATE_URL = "$AZURE_BASE_URL/api/voice/cache-invalidate"

# Azure Storage Configuration
$STORAGE_ACCOUNT = "dentalrecordings"
$CONTAINER_NAME = "recordings"
$CLIENT_ID = "f830136a-8621-438b-a3cb-a260f8f3a089"
$CLIENT_SECRET = "****************************************"
$TENANT_ID = "********-eb4a-4bac-ac7c-21c8ce299844"

# Global variables - Enhanced with job tracking
$script:TotalFiles = 0
$script:SuccessfulFiles = 0
$script:FailedFiles = 0
$script:AccessToken = $null
$script:JobIds = @()
$script:BatchId = $null
$script:UploadStartTime = $null

# Load Windows Forms if GUI mode
if (-not $Console) {
    Add-Type -AssemblyName System.Windows.Forms
    Add-Type -AssemblyName System.Drawing
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    if ($Console) {
        Write-Host $Message -ForegroundColor $Color
    } else {
        # For GUI mode, we'll update the form instead
        if ($global:StatusTextBox) {
            $global:StatusTextBox.AppendText("$Message`r`n")
            $global:StatusTextBox.ScrollToCaret()
            [System.Windows.Forms.Application]::DoEvents()
        }
    }
}

function Get-USBDrives {
    Write-ColorOutput "🔍 Detecting USB drives..." "Cyan"
    
    $usbDrives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {
        $_.DriveType -eq 2 -and $_.Size -gt 0
    }
    
    if ($usbDrives) {
        foreach ($drive in $usbDrives) {
            $sizeGB = [math]::Round($drive.Size / 1GB, 2)
            $freeGB = [math]::Round($drive.FreeSpace / 1GB, 2)
            Write-ColorOutput "  📱 Found USB: $($drive.DeviceID) ($sizeGB GB, $freeGB GB free)" "Green"
        }
        return $usbDrives
    } else {
        Write-ColorOutput "❌ No USB drives detected" "Red"
        return $null
    }
}

function Get-AccessToken {
    Write-ColorOutput "🔐 Authenticating with Azure (Enhanced for Corporate Networks)..." "Cyan"
    
    $body = @{
        grant_type    = "client_credentials"
        client_id     = $CLIENT_ID
        client_secret = $CLIENT_SECRET
        scope         = "https://storage.azure.com/.default"
    }
    
    try {
        # Enhanced for corporate firewalls with better timeout and retry
        $authUri = "https://login.microsoftonline.com/$TENANT_ID/oauth2/v2.0/token"
        $response = Invoke-RestMethod -Uri $authUri -Method Post -Body $body -TimeoutSec 60
        Write-ColorOutput "✅ Azure authentication successful (Corporate-Compatible)" "Green"
        return $response.access_token
    }
    catch {
        Write-ColorOutput "❌ Azure authentication failed: $($_.Exception.Message)" "Red"
        Write-ColorOutput "💡 If you're in a corporate environment, check proxy settings" "Yellow"
        throw
    }
}

function Get-AudioFiles {
    param([string]$DrivePath)
    
    Write-ColorOutput "🎵 Scanning for audio files in $DrivePath..." "Cyan"
    
    # Common audio file extensions
    $audioExtensions = @("*.mp3", "*.wav", "*.m4a", "*.wma", "*.aac")
    $audioFiles = @()
    
    foreach ($ext in $audioExtensions) {
        $files = Get-ChildItem -Path $DrivePath -Filter $ext -Recurse -ErrorAction SilentlyContinue | Where-Object {
            $_.FullName -notlike "*\archive\*" -and $_.FullName -notlike "*\Trash\*"
        }
        $audioFiles += $files
    }
    
    if ($audioFiles) {
        Write-ColorOutput "  📁 Found $($audioFiles.Count) audio files" "Green"
        foreach ($file in $audioFiles) {
            $sizeMB = [math]::Round($file.Length / 1MB, 2)
            Write-ColorOutput "    🎵 $($file.Name) ($sizeMB MB)" "Gray"
        }
    } else {
        Write-ColorOutput "  📁 No audio files found" "Yellow"
    }
    
    return $audioFiles
}

function Get-AzureBlobPath {
    param([System.IO.FileInfo]$File)
    
    # Extract date from filename (format: YYMMDD_HHMM.mp3)
    if ($File.BaseName -match '^(\d{6})_\d{4}') {
        $dateStr = $Matches[1]
        $year = "20" + $dateStr.Substring(0, 2)
        $month = $dateStr.Substring(2, 2)
        $day = $dateStr.Substring(4, 2)
        $dateFolder = "$year-$month-$day"
    } else {
        # Fallback to current date
        $dateFolder = Get-Date -Format "yyyy-MM-dd"
    }
    
    return "$dateFolder/usb-gui/$($File.Name)"
}

function Upload-FileToAzure {
    param(
        [System.IO.FileInfo]$File,
        [string]$BlobPath,
        [string]$AccessToken
    )
    
    $uri = "https://$STORAGE_ACCOUNT.blob.core.windows.net/$CONTAINER_NAME/$BlobPath"
    $headers = @{
        "Authorization" = "Bearer $AccessToken"
        "x-ms-blob-type" = "BlockBlob"
        "x-ms-version" = "2020-04-08"
        "Content-Type" = "audio/mpeg"
        "x-ms-meta-source" = "dental-usb-uploader-gui"
        "x-ms-meta-version" = $CURRENT_VERSION
        "x-ms-meta-batch-id" = $script:BatchId
        "x-ms-meta-upload-time" = (Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ")
    }
    
    try {
        $fileBytes = [System.IO.File]::ReadAllBytes($File.FullName)
        
        # Enhanced upload with better timeout and retry for corporate networks
        $response = Invoke-RestMethod -Uri $uri -Method Put -Headers $headers -Body $fileBytes -TimeoutSec 600
        
        # Trigger job creation for enhanced processing
        try {
            $jobResponse = Invoke-RestMethod -Uri "$AZURE_BASE_URL/api/voice/auto-process" -Method Post -Body (@{
                recordingIds = @($BlobPath)
                source = "usb-uploader-gui-v$CURRENT_VERSION"
                batchId = $script:BatchId
                settings = @{
                    userId = "usb-uploader"
                    enableSpeakerSeparation = $true
                    model = "whisper-1"
                    promptId = "dental-transcription-context"
                }
            } | ConvertTo-Json -Depth 3) -ContentType "application/json" -TimeoutSec 30
            
            if ($jobResponse.success -and $jobResponse.data.jobIds) {
                $script:JobIds += $jobResponse.data.jobIds
                Write-ColorOutput "    🎯 Processing job created: $($jobResponse.data.jobIds[0])" "Cyan"
            }
        } catch {
            Write-ColorOutput "    ⚠️  Upload successful but processing job creation failed" "Yellow"
        }
        
        return $true
    }
    catch {
        Write-ColorOutput "    ❌ Upload failed: $($_.Exception.Message)" "Red"
        if ($_.Exception.Message -like "*timeout*") {
            Write-ColorOutput "    💡 Network timeout - this may be due to corporate firewall restrictions" "Yellow"
        }
        return $false
    }
}

function Move-ToArchive {
    param([System.IO.FileInfo]$File)
    
    $archivePath = Join-Path $File.Directory.Root "archive"
    
    if (-not (Test-Path $archivePath)) {
        New-Item -Path $archivePath -ItemType Directory -Force | Out-Null
        Write-ColorOutput "    📁 Created archive directory: $archivePath" "Cyan"
    }
    
    $destinationPath = Join-Path $archivePath $File.Name
    
    try {
        Move-Item -Path $File.FullName -Destination $destinationPath -Force
        Write-ColorOutput "    📦 Moved to archive: $($File.Name)" "Green"
        return $true
    }
    catch {
        Write-ColorOutput "    ❌ Failed to move to archive: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Process-USBDrive {
    param([string]$DrivePath)
    
    Write-ColorOutput "`n🚀 Processing USB drive: $DrivePath (Enhanced Processing)" "Magenta"
    
    $audioFiles = Get-AudioFiles -DrivePath $DrivePath
    
    if (-not $audioFiles) {
        Write-ColorOutput "⏭️  No audio files to process" "Yellow"
        return
    }
    
    $script:TotalFiles += $audioFiles.Count
    
    # Initialize batch tracking for enhanced job monitoring
    if (-not $script:BatchId) {
        $script:BatchId = "usb-batch-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        $script:UploadStartTime = Get-Date
        Write-ColorOutput "🏷️  Batch ID: $script:BatchId" "Cyan"
    }
    
    foreach ($file in $audioFiles) {
        $blobPath = Get-AzureBlobPath -File $file
        $sizeMB = [math]::Round($file.Length / 1MB, 2)
        
        Write-ColorOutput "`n  🎵 Processing: $($file.Name) ($sizeMB MB)" "White"
        Write-ColorOutput "    📍 Target: $blobPath" "Gray"
        
        # Upload to Azure
        $uploadSuccess = Upload-FileToAzure -File $file -BlobPath $blobPath -AccessToken $script:AccessToken
        
        if ($uploadSuccess) {
            Write-ColorOutput "    ✅ Upload successful" "Green"
            $script:SuccessfulFiles++
            
            # Move to archive
            $archiveSuccess = Move-ToArchive -File $file
            if (-not $archiveSuccess) {
                Write-ColorOutput "    ⚠️  Upload succeeded but archiving failed" "Yellow"
            }
        } else {
            $script:FailedFiles++
        }
        
        # Update progress if in GUI mode
        if ($global:ProgressBar) {
            $progress = [math]::Round((($script:SuccessfulFiles + $script:FailedFiles) / $script:TotalFiles) * 100)
            $global:ProgressBar.Value = $progress
            $global:ProgressLabel.Text = "Progress: $progress% ($($script:SuccessfulFiles + $script:FailedFiles)/$script:TotalFiles files)"
            [System.Windows.Forms.Application]::DoEvents()
        }
    }
}

function Show-Summary {
    Write-ColorOutput "`n" "White"
    Write-ColorOutput "📊 ENHANCED UPLOAD SUMMARY" "Magenta"
    Write-ColorOutput "===========================" "Magenta"
    Write-ColorOutput "📁 Total files found: $script:TotalFiles" "White"
    Write-ColorOutput "✅ Successful uploads: $script:SuccessfulFiles" "Green"
    Write-ColorOutput "❌ Failed uploads: $script:FailedFiles" "Red"
    Write-ColorOutput "🏷️  Batch ID: $script:BatchId" "Cyan"
    
    if ($script:UploadStartTime) {
        $uploadDuration = (Get-Date) - $script:UploadStartTime
        Write-ColorOutput "⏱️  Upload duration: $([math]::Round($uploadDuration.TotalMinutes, 1)) minutes" "Gray"
    }
    
    if ($script:JobIds.Count -gt 0) {
        Write-ColorOutput "🎯 Processing jobs created: $($script:JobIds.Count)" "Cyan"
    }
    
    if ($script:FailedFiles -eq 0) {
        Write-ColorOutput "`n🎉 All files uploaded successfully!" "Green"
        Write-ColorOutput "🤖 Enhanced job queue service will process your recordings automatically" "Cyan"
        Write-ColorOutput "📈 Expect faster, more reliable processing with automatic retry" "Cyan"
    } else {
        Write-ColorOutput "`n⚠️  Some uploads failed. Check the output above for details." "Yellow"
        Write-ColorOutput "💡 Successful uploads will still be processed automatically" "Cyan"
    }
}

function Start-Upload {
    try {
        # Reset counters
        $script:TotalFiles = 0
        $script:SuccessfulFiles = 0
        $script:FailedFiles = 0
        
        Write-ColorOutput "🎵 Dental USB Uploader v$CURRENT_VERSION" "Magenta"
        Write-ColorOutput "=======================================" "Magenta"
        
        # Get USB drives
        $usbDrives = Get-USBDrives
        
        if (-not $usbDrives) {
            Write-ColorOutput "❌ No USB drives to process" "Red"
            return $false
        }
        
        # Get Azure access token
        $script:AccessToken = Get-AccessToken
        
        # Process each USB drive
        foreach ($drive in $usbDrives) {
            Process-USBDrive -DrivePath $drive.DeviceID
        }
        
        # Show summary
        Show-Summary
        
        # Enhanced notification and job monitoring
        try {
            # Invalidate cache
            $cacheResponse = Invoke-RestMethod -Uri $CACHE_INVALIDATE_URL -Method Post -Body (@{
                source = "dental-usb-uploader-gui-v$CURRENT_VERSION"
                rebuild = $true
                batchId = $script:BatchId
                fileCount = $script:SuccessfulFiles
            } | ConvertTo-Json) -ContentType "application/json" -TimeoutSec 30
            
            Write-ColorOutput "`n🔄 Notified dental app of new files" "Cyan"
            
            # Show job monitoring information
            if ($script:JobIds.Count -gt 0) {
                Write-ColorOutput "🎯 Processing Jobs Started:" "Cyan"
                foreach ($jobId in $script:JobIds) {
                    Write-ColorOutput "  📋 Job ID: $jobId" "Gray"
                }
                Write-ColorOutput "📊 Monitor progress at: $AZURE_BASE_URL/voice-workflow" "Cyan"
                
                # Provide job monitoring instructions
                Write-ColorOutput "`n💡 Job Monitoring:" "Yellow"
                Write-ColorOutput "  • Your files are being processed in the background" "White"
                Write-ColorOutput "  • Transcription and summarization will happen automatically" "White"
                Write-ColorOutput "  • Check the Voice Workflow tab in the dental app for progress" "White"
                Write-ColorOutput "  • Batch ID: $script:BatchId" "White"
            }
        } catch {
            Write-ColorOutput "`n⚠️  Could not notify dental app (files still uploaded successfully)" "Yellow"
            Write-ColorOutput "💡 Files will be processed when the app next scans for new recordings" "Cyan"
        }
        
        return ($script:FailedFiles -eq 0)

    } catch {
        Write-ColorOutput "`n❌ Upload failed: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Show-GUI {
    # Create main form
    $form = New-Object System.Windows.Forms.Form
    $form.Text = "Dental USB Uploader v$CURRENT_VERSION"
    $form.Size = New-Object System.Drawing.Size(600, 500)
    $form.StartPosition = "CenterScreen"
    $form.FormBorderStyle = "FixedDialog"
    $form.MaximizeBox = $false
    $form.BackColor = [System.Drawing.Color]::White

    # Title label
    $titleLabel = New-Object System.Windows.Forms.Label
    $titleLabel.Text = "🦷 Dental USB Uploader"
    $titleLabel.Font = New-Object System.Drawing.Font("Segoe UI", 16, [System.Drawing.FontStyle]::Bold)
    $titleLabel.ForeColor = [System.Drawing.Color]::DarkBlue
    $titleLabel.Location = New-Object System.Drawing.Point(20, 20)
    $titleLabel.Size = New-Object System.Drawing.Size(400, 35)
    $form.Controls.Add($titleLabel)

    # Version label
    $versionLabel = New-Object System.Windows.Forms.Label
    $versionLabel.Text = "Version $CURRENT_VERSION - Enhanced Corporate Solution with Job Queue"
    $versionLabel.Font = New-Object System.Drawing.Font("Segoe UI", 9)
    $versionLabel.ForeColor = [System.Drawing.Color]::Gray
    $versionLabel.Location = New-Object System.Drawing.Point(20, 55)
    $versionLabel.Size = New-Object System.Drawing.Size(500, 20)
    $form.Controls.Add($versionLabel)

    # Instructions label
    $instructionsLabel = New-Object System.Windows.Forms.Label
    $instructionsLabel.Text = "Insert your USB drive with audio recordings and click 'Start Upload'. Enhanced with reliable job queue processing."
    $instructionsLabel.Font = New-Object System.Drawing.Font("Segoe UI", 10)
    $instructionsLabel.Location = New-Object System.Drawing.Point(20, 85)
    $instructionsLabel.Size = New-Object System.Drawing.Size(550, 25)
    $form.Controls.Add($instructionsLabel)

    # Status text box
    $global:StatusTextBox = New-Object System.Windows.Forms.TextBox
    $global:StatusTextBox.Multiline = $true
    $global:StatusTextBox.ScrollBars = "Vertical"
    $global:StatusTextBox.ReadOnly = $true
    $global:StatusTextBox.Font = New-Object System.Drawing.Font("Consolas", 9)
    $global:StatusTextBox.Location = New-Object System.Drawing.Point(20, 120)
    $global:StatusTextBox.Size = New-Object System.Drawing.Size(550, 250)
    $global:StatusTextBox.BackColor = [System.Drawing.Color]::Black
    $global:StatusTextBox.ForeColor = [System.Drawing.Color]::LightGreen
    $form.Controls.Add($global:StatusTextBox)

    # Progress bar
    $global:ProgressBar = New-Object System.Windows.Forms.ProgressBar
    $global:ProgressBar.Location = New-Object System.Drawing.Point(20, 380)
    $global:ProgressBar.Size = New-Object System.Drawing.Size(400, 25)
    $global:ProgressBar.Style = "Continuous"
    $form.Controls.Add($global:ProgressBar)

    # Progress label
    $global:ProgressLabel = New-Object System.Windows.Forms.Label
    $global:ProgressLabel.Text = "Ready to start..."
    $global:ProgressLabel.Font = New-Object System.Drawing.Font("Segoe UI", 9)
    $global:ProgressLabel.Location = New-Object System.Drawing.Point(430, 385)
    $global:ProgressLabel.Size = New-Object System.Drawing.Size(140, 20)
    $form.Controls.Add($global:ProgressLabel)

    # Start Upload button
    $uploadButton = New-Object System.Windows.Forms.Button
    $uploadButton.Text = "🚀 Start Upload"
    $uploadButton.Font = New-Object System.Drawing.Font("Segoe UI", 11, [System.Drawing.FontStyle]::Bold)
    $uploadButton.Location = New-Object System.Drawing.Point(20, 420)
    $uploadButton.Size = New-Object System.Drawing.Size(150, 35)
    $uploadButton.BackColor = [System.Drawing.Color]::DodgerBlue
    $uploadButton.ForeColor = [System.Drawing.Color]::White
    $uploadButton.FlatStyle = "Flat"

    $uploadButton.Add_Click({
        $uploadButton.Enabled = $false
        $uploadButton.Text = "⏳ Uploading..."
        $global:StatusTextBox.Clear()
        $global:ProgressBar.Value = 0
        $global:ProgressLabel.Text = "Starting upload..."

        $success = Start-Upload

        if ($success) {
            $uploadButton.Text = "✅ Upload Complete"
            $uploadButton.BackColor = [System.Drawing.Color]::Green
        } else {
            $uploadButton.Text = "❌ Upload Failed"
            $uploadButton.BackColor = [System.Drawing.Color]::Red
        }

        # Re-enable button after 3 seconds
        $timer = New-Object System.Windows.Forms.Timer
        $timer.Interval = 3000
        $timer.Add_Tick({
            $uploadButton.Enabled = $true
            $uploadButton.Text = "🚀 Start Upload"
            $uploadButton.BackColor = [System.Drawing.Color]::DodgerBlue
            $timer.Stop()
        })
        $timer.Start()
    })

    $form.Controls.Add($uploadButton)

    # Check Updates button
    $updateButton = New-Object System.Windows.Forms.Button
    $updateButton.Text = "🔄 Check Updates"
    $updateButton.Font = New-Object System.Drawing.Font("Segoe UI", 10)
    $updateButton.Location = New-Object System.Drawing.Point(180, 420)
    $updateButton.Size = New-Object System.Drawing.Size(130, 35)
    $updateButton.BackColor = [System.Drawing.Color]::Orange
    $updateButton.ForeColor = [System.Drawing.Color]::White
    $updateButton.FlatStyle = "Flat"

    $updateButton.Add_Click({
        try {
            $response = Invoke-RestMethod -Uri $UPDATE_CHECK_URL -TimeoutSec 10
            if ($response.version -ne $CURRENT_VERSION) {
                [System.Windows.Forms.MessageBox]::Show("New version $($response.version) is available!`n`nPlease download the latest version from the dental app.", "Update Available", "OK", "Information")
            } else {
                [System.Windows.Forms.MessageBox]::Show("You have the latest version ($CURRENT_VERSION)", "Up to Date", "OK", "Information")
            }
        } catch {
            [System.Windows.Forms.MessageBox]::Show("Could not check for updates. Please check your internet connection.", "Update Check Failed", "OK", "Warning")
        }
    })

    $form.Controls.Add($updateButton)

    # Close button
    $closeButton = New-Object System.Windows.Forms.Button
    $closeButton.Text = "❌ Close"
    $closeButton.Font = New-Object System.Drawing.Font("Segoe UI", 10)
    $closeButton.Location = New-Object System.Drawing.Point(320, 420)
    $closeButton.Size = New-Object System.Drawing.Size(100, 35)
    $closeButton.BackColor = [System.Drawing.Color]::Gray
    $closeButton.ForeColor = [System.Drawing.Color]::White
    $closeButton.FlatStyle = "Flat"
    $closeButton.Add_Click({ $form.Close() })
    $form.Controls.Add($closeButton)

    # Show initial status
    Write-ColorOutput "🦷 Dental USB Uploader v$CURRENT_VERSION (Enhanced)" "Cyan"
    Write-ColorOutput "Ready to upload audio files with reliable job queue processing." "White"
    Write-ColorOutput "✨ New features: Enhanced corporate compatibility, job monitoring, automatic retry" "Green"
    Write-ColorOutput "Click 'Start Upload' when your USB drive is connected." "Yellow"
    Write-ColorOutput ""

    # Show the form
    [System.Windows.Forms.Application]::Run($form)
}

function Show-ConsoleMenu {
    while ($true) {
        Clear-Host
        Write-ColorOutput "🦷 Dental USB Uploader v$CURRENT_VERSION" "Cyan"
        Write-ColorOutput "=======================================" "Cyan"
        Write-ColorOutput ""
        Write-ColorOutput "1. 🚀 Start Upload Process" "White"
        Write-ColorOutput "2. 🔄 Check for Updates" "White"
        Write-ColorOutput "3. ❌ Exit" "White"
        Write-ColorOutput ""

        $choice = Read-Host "Select an option (1-3)"

        switch ($choice) {
            "1" {
                Clear-Host
                $success = Start-Upload
                Write-ColorOutput ""
                if ($success) {
                    Write-ColorOutput "Press any key to return to menu..." "Green"
                } else {
                    Write-ColorOutput "Press any key to return to menu..." "Red"
                }
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            "2" {
                try {
                    Write-ColorOutput "🔍 Checking for updates..." "Cyan"
                    $response = Invoke-RestMethod -Uri $UPDATE_CHECK_URL -TimeoutSec 10
                    if ($response.version -ne $CURRENT_VERSION) {
                        Write-ColorOutput "🆕 New version $($response.version) is available!" "Green"
                        Write-ColorOutput "Please download the latest version from the dental app." "Yellow"
                    } else {
                        Write-ColorOutput "✅ You have the latest version ($CURRENT_VERSION)" "Green"
                    }
                } catch {
                    Write-ColorOutput "⚠️  Could not check for updates. Please check your internet connection." "Yellow"
                }
                Write-ColorOutput ""
                Write-ColorOutput "Press any key to continue..." "White"
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            "3" {
                Write-ColorOutput "👋 Goodbye!" "Cyan"
                exit 0
            }
            default {
                Write-ColorOutput "❌ Invalid option. Please select 1, 2, or 3." "Red"
                Start-Sleep -Seconds 2
            }
        }
    }
}

# Main execution
if ($Console) {
    Show-ConsoleMenu
} else {
    Show-GUI
}
