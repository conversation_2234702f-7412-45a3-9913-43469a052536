'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useTheme } from 'next-themes';

interface TranscriptionUpdate {
  type: 'connected' | 'status' | 'recordings_loaded' | 'progress' | 'file_completed' | 'file_failed' | 'completed' | 'error' | 'file_chunking' | 'file_chunked' | 'file_summarizing' | 'analysis_complete';
  message: string;
  count?: number;
  current?: number;
  total?: number;
  fileName?: string;
  completed?: number;
  failed?: number;
  transcription?: string;
  summary?: string;
  chunksProcessed?: number;
  error?: string;
  totalFiles?: number;
  needsTranscription?: number;
  needsSummary?: number;
  skippedInvalid?: number;
  alreadyCompleted?: number;
  currentlyProcessing?: number;
  remainingNeedsTranscription?: number;
  remainingNeedsSummary?: number;
  totalUnprocessed?: number;
}

export function RealTimeTranscription() {
  const { theme, resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(true);
  const [logs, setLogs] = useState<string[]>([]);
  const [progress, setProgress] = useState(0);
  const [stats, setStats] = useState({
    total: 0,
    completed: 0,
    failed: 0,
    current: 0,
    needsTranscription: 0,
    needsSummary: 0,
    alreadyCompleted: 0,
    skippedInvalid: 0,
    currentlyProcessing: 0
  });
  const [currentFile, setCurrentFile] = useState('');
  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [hasWorkToDo, setHasWorkToDo] = useState(false);
  
  const eventSourceRef = useRef<EventSource | null>(null);
  const logsRef = useRef<HTMLDivElement>(null);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-50), `[${timestamp}] ${message}`]); // Keep last 50 logs
  };

  const scrollToBottom = () => {
    if (logsRef.current) {
      logsRef.current.scrollTop = logsRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [logs]);

  // Run initial analysis when component mounts
  useEffect(() => {
    analyzeFiles();
  }, []);

  const analyzeFiles = async () => {
    setIsAnalyzing(true);
    addLog('🔍 Analyzing current file status...');
    
    try {
      const response = await fetch('/api/voice/analyze-files');
      const data = await response.json();
      
      if (data.success) {
        setStats(prev => ({
          ...prev,
          total: data.analysis.totalFiles,
          needsTranscription: data.analysis.needsTranscription,
          needsSummary: data.analysis.needsSummary,
          alreadyCompleted: data.analysis.alreadyCompleted,
          skippedInvalid: data.analysis.skippedInvalid
        }));
        
        setHasWorkToDo(data.analysis.hasWorkToDo);
        setAnalysisComplete(true);
        
        addLog(`📊 Analysis complete: ${data.analysis.needsTranscription} need transcription, ${data.analysis.needsSummary} need summary, ${data.analysis.alreadyCompleted} complete, ${data.analysis.skippedInvalid} invalid files skipped`);
        
        if (!data.analysis.hasWorkToDo) {
          addLog('✨ All files are already processed! No work needed.');
        }
      } else {
        addLog(`❌ Analysis failed: ${data.error}`);
      }
    } catch (error) {
      addLog(`❌ Analysis error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const startTranscription = () => {
    if (isProcessing) return;
    
    setIsProcessing(true);
    setLogs([]);
    setProgress(0);
    setStats({ total: 0, completed: 0, failed: 0, current: 0, needsTranscription: 0, needsSummary: 0, alreadyCompleted: 0, skippedInvalid: 0, currentlyProcessing: 0 });
    setCurrentFile('');
    
    addLog('🚀 Starting real-time transcription...');
    
    // Create EventSource for Server-Sent Events
    eventSourceRef.current = new EventSource('/api/voice/transcribe-stream');
    
    eventSourceRef.current.onmessage = (event) => {
      try {
        const data: TranscriptionUpdate = JSON.parse(event.data);
        
        addLog(data.message);
        
        switch (data.type) {
          case 'connected':
            addLog('🔗 Connected to transcription stream');
            break;
            
          case 'recordings_loaded':
            setStats(prev => ({ ...prev, total: data.count || 0 }));
            break;
            
          case 'analysis_complete':
            setStats(prev => ({
              ...prev,
              total: data.totalFiles || 0,
              needsTranscription: data.needsTranscription || 0,
              needsSummary: data.needsSummary || 0,
              alreadyCompleted: data.alreadyCompleted || 0,
              skippedInvalid: data.skippedInvalid || 0
            }));
            break;
            
          case 'progress':
            setCurrentFile(data.fileName || '');
            setStats(prev => ({
              ...prev,
              current: data.current || 0,
              total: data.total || prev.total,
              completed: data.completed || prev.completed,
              failed: data.failed || prev.failed,
              currentlyProcessing: data.currentlyProcessing || prev.currentlyProcessing
            }));
            
            if (data.total && data.current) {
              setProgress((data.current / data.total) * 100);
            }
            break;
            
          case 'file_chunking':
          case 'file_chunked':
          case 'file_summarizing':
            // These are intermediate status updates - just log them
            break;
            
          case 'file_completed':
            setStats(prev => ({
              ...prev,
              completed: data.completed || prev.completed + 1,
              currentlyProcessing: data.currentlyProcessing || prev.currentlyProcessing,
              needsTranscription: data.remainingNeedsTranscription ?? prev.needsTranscription,
              needsSummary: data.remainingNeedsSummary ?? prev.needsSummary
            }));
            break;
            
          case 'file_failed':
            setStats(prev => ({
              ...prev,
              failed: data.failed || prev.failed + 1,
              currentlyProcessing: data.currentlyProcessing || prev.currentlyProcessing
            }));
            break;
            
          case 'completed':
            setIsProcessing(false);
            setCurrentFile('');
            setProgress(100);
            addLog('🎉 All processing complete!');
            break;
            
          case 'error':
            setIsProcessing(false);
            setCurrentFile('');
            addLog(`💥 Error: ${data.message}`);
            break;
        }
      } catch (error) {
        addLog(`⚠️ Failed to parse update: ${error}`);
      }
    };
    
    eventSourceRef.current.onerror = (error) => {
      addLog('❌ Connection error occurred');
      setIsProcessing(false);
      eventSourceRef.current?.close();
    };
  };

  const stopTranscription = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setIsProcessing(false);
    setCurrentFile('');
    addLog('⏹️ Transcription stopped by user');
  };

  return (
    <Card className={`w-full ${isDarkMode ? 'bg-gray-800 border-gray-700' : ''}`}>
      <CardHeader>
        <CardTitle className={`flex items-center gap-2 ${isDarkMode ? 'text-white' : ''}`}>
          🎯 Real-Time Transcription Control
          {isProcessing && (
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Pre-Analysis Status */}
        {analysisComplete && !isProcessing && (
          <div className={`p-4 rounded-lg border ${hasWorkToDo 
            ? isDarkMode ? 'bg-blue-900/20 border-blue-700' : 'bg-blue-50 border-blue-200'
            : isDarkMode ? 'bg-green-900/20 border-green-700' : 'bg-green-50 border-green-200'
          }`}>
            <h4 className={`font-semibold mb-2 ${hasWorkToDo 
              ? isDarkMode ? 'text-blue-300' : 'text-blue-800'
              : isDarkMode ? 'text-green-300' : 'text-green-800'
            }`}>
              {hasWorkToDo ? '📋 Files Ready for Processing' : '✨ All Files Complete'}
            </h4>
            <p className={`text-sm ${hasWorkToDo 
              ? isDarkMode ? 'text-blue-200' : 'text-blue-700'
              : isDarkMode ? 'text-green-200' : 'text-green-700'
            }`}>
              {hasWorkToDo 
                ? `Found ${stats.needsTranscription + stats.needsSummary} files that need processing. Click the button below to start.`
                : 'All your files already have transcriptions and summaries. No processing needed!'
              }
            </p>
          </div>
        )}

        {/* Controls */}
        <div className="flex gap-4 items-center">
          <Button 
            onClick={startTranscription} 
            disabled={isProcessing || isAnalyzing || !hasWorkToDo}
            className={hasWorkToDo ? "bg-green-600 hover:bg-green-700" : "bg-gray-400"}
          >
            {isProcessing ? '⚡ Processing...' : 
             isAnalyzing ? '🔍 Analyzing...' :
             !hasWorkToDo ? '✅ All Files Complete' :
             '🚀 Start Real-Time Processing'}
          </Button>
          {isProcessing && (
            <Button 
              onClick={stopTranscription} 
              variant="destructive"
            >
              ⏹️ Stop
            </Button>
          )}
          {analysisComplete && !isProcessing && (
            <Button 
              onClick={analyzeFiles} 
              variant="outline"
              disabled={isAnalyzing}
            >
              🔄 Refresh Analysis
            </Button>
          )}
        </div>

        {/* Progress */}
        {isProcessing && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>Progress: {stats.current}/{stats.total}</span>
              <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-3" />
            {currentFile && (
              <p className={`text-sm font-mono ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                🎵 Currently processing: {currentFile}
              </p>
            )}
            {stats.currentlyProcessing > 0 && (
              <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                ⚡ Parallel processing: {stats.currentlyProcessing} files running simultaneously
              </p>
            )}
          </div>
        )}

        {/* Primary Stats - Emphasize Unprocessed */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className={`text-center p-3 rounded border-2 ${isDarkMode ? 'bg-orange-900/20 border-orange-700' : 'bg-orange-50 border-orange-200'}`}>
            <div className={`text-3xl font-bold ${isDarkMode ? 'text-orange-400' : 'text-orange-600'}`}>{stats.needsTranscription + stats.needsSummary}</div>
            <div className={`text-sm font-medium ${isDarkMode ? 'text-orange-300' : 'text-orange-700'}`}>Unprocessed Files</div>
          </div>
          <div className={`text-center p-3 rounded ${isDarkMode ? 'bg-green-900/20' : 'bg-green-50'}`}>
            <div className={`text-2xl font-bold ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>{stats.completed}</div>
            <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Processed</div>
          </div>
          <div className={`text-center p-3 rounded ${isDarkMode ? 'bg-yellow-900/20' : 'bg-yellow-50'}`}>
            <div className={`text-2xl font-bold ${isDarkMode ? 'text-yellow-400' : 'text-yellow-600'}`}>{stats.current}</div>
            <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Current</div>
          </div>
          <div className={`text-center p-3 rounded ${isDarkMode ? 'bg-red-900/20' : 'bg-red-50'}`}>
            <div className={`text-2xl font-bold ${isDarkMode ? 'text-red-400' : 'text-red-600'}`}>{stats.failed}</div>
            <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Failed</div>
          </div>
        </div>

        {/* Detailed Breakdown */}
        {analysisComplete && (stats.needsTranscription > 0 || stats.needsSummary > 0 || stats.alreadyCompleted > 0) && (
          <div className="space-y-3">
            <h4 className={`text-sm font-medium border-b pb-1 ${isDarkMode ? 'text-gray-300 border-gray-600' : 'text-gray-700 border-gray-300'}`}>Detailed Breakdown</h4>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
              <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-orange-900/20' : 'bg-orange-50'}`}>
                <div className={`text-lg font-bold ${isDarkMode ? 'text-orange-400' : 'text-orange-600'}`}>{stats.needsTranscription}</div>
                <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Need Transcription</div>
              </div>
              <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-purple-900/20' : 'bg-purple-50'}`}>
                <div className={`text-lg font-bold ${isDarkMode ? 'text-purple-400' : 'text-purple-600'}`}>{stats.needsSummary}</div>
                <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Need Summary</div>
              </div>
              <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <div className={`text-lg font-bold ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>{stats.alreadyCompleted}</div>
                <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Already Complete</div>
              </div>
              <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-slate-700' : 'bg-slate-50'}`}>
                <div className={`text-lg font-bold ${isDarkMode ? 'text-slate-400' : 'text-slate-600'}`}>{stats.skippedInvalid}</div>
                <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Invalid Files</div>
              </div>
              <div className={`text-center p-2 rounded ${isDarkMode ? 'bg-blue-900/20' : 'bg-blue-50'}`}>
                <div className={`text-lg font-bold ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>{stats.total}</div>
                <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Files</div>
              </div>
            </div>
          </div>
        )}

        {/* Live Logs */}
        <div>
          <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>🔴 Live Activity Stream</h3>
          <div 
            ref={logsRef}
            className="bg-black text-green-400 p-4 rounded h-64 overflow-y-auto font-mono text-sm"
          >
            {logs.length === 0 ? (
              <div className="text-gray-500">Ready to start processing. Click the button above to begin.</div>
            ) : (
              logs.map((log, i) => (
                <div key={i} className="mb-1">{log}</div>
              ))
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className={`border rounded p-4 ${isDarkMode ? 'bg-blue-900/20 border-blue-700' : 'bg-blue-50 border-blue-200'}`}>
          <h4 className={`font-semibold mb-2 ${isDarkMode ? 'text-blue-300' : 'text-blue-800'}`}>How This Works:</h4>
          <ul className={`text-sm space-y-1 ${isDarkMode ? 'text-blue-200' : 'text-blue-700'}`}>
            <li>• Real-time streaming updates using Server-Sent Events</li>
            <li>• Processes files directly from Azure Storage</li>
            <li>• ⚡ High-speed parallel processing - up to 5 transcriptions + 8 summaries simultaneously</li>
            <li>• 🔄 Two-phase workflow: transcriptions first, then summaries</li>
            <li>• Intelligent analysis - only processes files that need work</li>
            <li>• Shows live progress for each file being processed</li>
            <li>• No more "nothing happens" - you see everything in real-time!</li>
          </ul>
        </div>

      </CardContent>
    </Card>
  );
}