import { NextRequest, NextResponse } from 'next/server';
import { sql } from '@vercel/postgres';
import { VercelDB } from '@/lib/vercel-db';
import { featureFlags } from '@/lib/feature-flags';

export interface TranscriptionMetadata {
  transcriptionId: string;
  filename: string;
  deviceId: string;
  
  // Transcription details
  transcribedAt: string;
  modelUsed: string;
  serviceUsed: string;
  confidenceScore: number;
  
  // Processing details
  processingTimeMs?: number;
  fileSizeMB?: number;
  audioDurationSeconds?: number;
  
  // Quality metrics
  transcriptionLength: number;
  summaryLength: number;
  hasPatientId: boolean;
  
  // Content analysis
  extractedKeywords?: string[];
  detectedLanguage?: string;
  contentType?: string;
  
  // Status and timestamps
  status: string;
  createdAt: string;
  updatedAt: string;
  
  // Additional metadata
  metadata: any;
}

/**
 * GET endpoint to retrieve transcription metadata
 */
export async function GET(request: NextRequest) {
  try {
    // Check if new architecture is enabled
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const transcriptionId = searchParams.get('id');
    const filename = searchParams.get('filename');
    const includeContent = searchParams.get('includeContent') === 'true';

    if (!transcriptionId && !filename) {
      return NextResponse.json({
        error: 'Transcription ID or filename is required',
        code: 'MISSING_IDENTIFIER'
      }, { status: 400 });
    }

    // Query transcription data
    let query;
    let params: any[];

    if (transcriptionId) {
      query = `
        SELECT 
          id,
          filename,
          device_id,
          transcription_text,
          summary_text,
          confidence_score,
          patient_id,
          status,
          created_at,
          updated_at,
          metadata
        FROM transcriptions 
        WHERE id = $1
      `;
      params = [transcriptionId];
    } else {
      query = `
        SELECT 
          id,
          filename,
          device_id,
          transcription_text,
          summary_text,
          confidence_score,
          patient_id,
          status,
          created_at,
          updated_at,
          metadata
        FROM transcriptions 
        WHERE filename = $1
        ORDER BY created_at DESC
        LIMIT 1
      `;
      params = [filename];
    }

    const result = await sql.query(query, params);

    if (result.rows.length === 0) {
      return NextResponse.json({
        error: 'Transcription not found',
        code: 'NOT_FOUND'
      }, { status: 404 });
    }

    const transcription = result.rows[0];
    
    // Build metadata response
    const metadata: TranscriptionMetadata = {
      transcriptionId: transcription.id,
      filename: transcription.filename,
      deviceId: transcription.device_id,
      
      // Transcription details
      transcribedAt: transcription.created_at,
      modelUsed: transcription.metadata?.whisper_model || 'whisper-1',
      serviceUsed: 'openai-whisper',
      confidenceScore: transcription.confidence_score || 0,
      
      // Processing details
      processingTimeMs: transcription.metadata?.processing_time_ms,
      fileSizeMB: transcription.metadata?.file_size ? 
        Math.round((transcription.metadata.file_size / 1024 / 1024) * 100) / 100 : undefined,
      audioDurationSeconds: transcription.metadata?.duration,
      
      // Quality metrics
      transcriptionLength: transcription.transcription_text?.length || 0,
      summaryLength: transcription.summary_text?.length || 0,
      hasPatientId: !!transcription.patient_id,
      
      // Content analysis
      extractedKeywords: extractKeywords(transcription.transcription_text, transcription.summary_text),
      detectedLanguage: transcription.metadata?.language || 'en',
      contentType: determineContentType(transcription.transcription_text),
      
      // Status and timestamps
      status: transcription.status,
      createdAt: transcription.created_at,
      updatedAt: transcription.updated_at,
      
      // Additional metadata
      metadata: transcription.metadata || {}
    };

    // Add content if requested
    const response: any = { 
      success: true,
      metadata 
    };

    if (includeContent) {
      response.content = {
        transcriptionText: transcription.transcription_text,
        summaryText: transcription.summary_text
      };
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Error retrieving transcription metadata:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to retrieve metadata',
      code: 'METADATA_ERROR'
    }, { status: 500 });
  }
}

/**
 * POST endpoint to update transcription metadata
 */
export async function POST(request: NextRequest) {
  try {
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { transcriptionId, metadata: newMetadata } = await request.json();

    if (!transcriptionId) {
      return NextResponse.json({
        error: 'Transcription ID is required',
        code: 'MISSING_ID'
      }, { status: 400 });
    }

    // Get current metadata
    const currentResult = await sql`
      SELECT metadata FROM transcriptions WHERE id = ${transcriptionId}
    `;

    if (currentResult.rows.length === 0) {
      return NextResponse.json({
        error: 'Transcription not found',
        code: 'NOT_FOUND'
      }, { status: 404 });
    }

    // Merge metadata
    const currentMetadata = currentResult.rows[0].metadata || {};
    const updatedMetadata = {
      ...currentMetadata,
      ...newMetadata,
      lastUpdated: new Date().toISOString()
    };

    // Update transcription
    await sql`
      UPDATE transcriptions 
      SET 
        metadata = ${JSON.stringify(updatedMetadata)},
        updated_at = NOW()
      WHERE id = ${transcriptionId}
    `;

    console.log(`✅ Updated metadata for transcription: ${transcriptionId}`);

    return NextResponse.json({
      success: true,
      transcriptionId,
      metadata: updatedMetadata,
      message: 'Metadata updated successfully'
    });

  } catch (error) {
    console.error('❌ Error updating transcription metadata:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to update metadata',
      code: 'UPDATE_ERROR'
    }, { status: 500 });
  }
}

/**
 * GET endpoint for bulk metadata retrieval
 */
export async function PUT(request: NextRequest) {
  try {
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled', 
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { transcriptionIds } = await request.json();

    if (!transcriptionIds || !Array.isArray(transcriptionIds)) {
      return NextResponse.json({
        error: 'Transcription IDs array is required',
        code: 'MISSING_IDS'
      }, { status: 400 });
    }

    const placeholders = transcriptionIds.map((_, index) => `$${index + 1}`).join(',');
    const query = `
      SELECT 
        id,
        filename,
        device_id,
        confidence_score,
        patient_id,
        status,
        created_at,
        updated_at,
        CHAR_LENGTH(transcription_text) as transcription_length,
        CHAR_LENGTH(summary_text) as summary_length,
        metadata
      FROM transcriptions 
      WHERE id IN (${placeholders})
      ORDER BY created_at DESC
    `;

    const result = await sql.query(query, transcriptionIds);

    const metadataList = result.rows.map(row => ({
      transcriptionId: row.id,
      filename: row.filename,
      deviceId: row.device_id,
      confidenceScore: row.confidence_score || 0,
      hasPatientId: !!row.patient_id,
      status: row.status,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      transcriptionLength: row.transcription_length || 0,
      summaryLength: row.summary_length || 0,
      metadata: row.metadata || {}
    }));

    return NextResponse.json({
      success: true,
      count: metadataList.length,
      metadata: metadataList
    });

  } catch (error) {
    console.error('❌ Error retrieving bulk metadata:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to retrieve bulk metadata',
      code: 'BULK_ERROR'
    }, { status: 500 });
  }
}

// Helper functions
function extractKeywords(transcriptionText?: string, summaryText?: string): string[] {
  const content = `${transcriptionText || ''} ${summaryText || ''}`.toLowerCase();
  
  // Define dental/medical keywords to look for
  const dentalKeywords = [
    'cleaning', 'examination', 'cavity', 'filling', 'crown', 'root canal',
    'extraction', 'x-ray', 'pain', 'sensitivity', 'checkup', 'hygiene',
    'plaque', 'tartar', 'gingivitis', 'periodontal', 'molar', 'incisor',
    'restoration', 'anesthesia', 'procedure', 'treatment', 'diagnosis'
  ];

  const foundKeywords = dentalKeywords.filter(keyword => 
    content.includes(keyword)
  );

  return foundKeywords.slice(0, 10); // Return max 10 keywords
}

function determineContentType(transcriptionText?: string): string {
  if (!transcriptionText) return 'unknown';
  
  const content = transcriptionText.toLowerCase();
  
  if (content.includes('examination') || content.includes('checkup')) {
    return 'examination';
  } else if (content.includes('cleaning') || content.includes('hygiene')) {
    return 'cleaning';
  } else if (content.includes('procedure') || content.includes('treatment')) {
    return 'procedure';
  } else if (content.includes('consultation') || content.includes('discuss')) {
    return 'consultation';
  } else {
    return 'general';
  }
}