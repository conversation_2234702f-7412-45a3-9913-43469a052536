"use client";

import React, { useState, useEffect } from 'react';
import { 
  RefreshCw, 
  Download, 
  Upload, 
  CheckCircle, 
  AlertCircle, 
  XCircle,
  Wifi,
  WifiOff,
  HardDrive,
  Cloud,
  ArrowUpDown,
  ArrowDown,
  ArrowUp
} from 'lucide-react';

interface SyncStatus {
  status: 'success' | 'partial' | 'failed';
  networkToLocal: {
    transferred: number;
    skipped: number;
    errors: string[];
  };
  localToNetwork: {
    transferred: number;
    skipped: number;
    errors: string[];
  };
  summary: {
    totalFiles: number;
    networkFiles: number;
    localFiles: number;
    duplicates: number;
    syncedFiles: number;
  };
  debug: {
    networkAvailable: boolean;
    localAvailable: boolean;
    syncDirection: 'bidirectional' | 'network-to-local' | 'local-to-network' | 'none';
    lastSyncTime: string;
  };
}

interface SyncManagerProps {
  isDarkMode?: boolean;
  onSyncComplete?: (result: SyncStatus) => void;
}

export function SyncManager({ isDarkMode = false, onSyncComplete }: SyncManagerProps) {
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);
  const [isAzureMode, setIsAzureMode] = useState(false);

  // Load initial sync status
  useEffect(() => {
    checkSyncStatus();
    // Check if we're in Azure mode by looking at recordings API
    checkAzureMode();
  }, []);

  const checkAzureMode = async () => {
    try {
      const response = await fetch('/api/voice/recordings');
      if (response.ok) {
        const data = await response.json();
        // If debug info shows Azure storage type, we're in Azure mode
        if (data.debug?.storageType === 'Azure Blob Storage' || data.debug?.azureConfigured) {
          setIsAzureMode(true);
        }
      }
    } catch (error) {
      // Ignore errors, default to network sync mode
    }
  };

  const checkSyncStatus = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/voice/sync');
      if (response.ok) {
        const data = await response.json();
        setSyncStatus(data);
        setLastCheck(new Date());
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to check sync status');
      }
    } catch (err: any) {
      setError(`Network error: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const performSync = async (force: boolean = false) => {
    setIsSyncing(true);
    setError(null);

    try {
      const response = await fetch('/api/voice/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ force }),
      });

      if (response.ok) {
        const result = await response.json();
        setSyncStatus(result);
        setLastCheck(new Date());
        onSyncComplete?.(result);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Sync failed');
      }
    } catch (err: any) {
      setError(`Sync error: ${err.message}`);
    } finally {
      setIsSyncing(false);
    }
  };

  const getSyncDirectionIcon = () => {
    if (!syncStatus?.debug) return <ArrowUpDown className="w-4 h-4" />;
    
    switch (syncStatus.debug.syncDirection) {
      case 'bidirectional':
        return <ArrowUpDown className="w-4 h-4 text-blue-600" />;
      case 'network-to-local':
        return <ArrowDown className="w-4 h-4 text-green-600" />;
      case 'local-to-network':
        return <ArrowUp className="w-4 h-4 text-orange-600" />;
      default:
        return <XCircle className="w-4 h-4 text-red-600" />;
    }
  };

  const getSyncStatusIcon = () => {
    if (!syncStatus) return <RefreshCw className="w-4 h-4" />;
    
    switch (syncStatus.status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'partial':
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <RefreshCw className="w-4 h-4" />;
    }
  };

  const getSyncStatusText = () => {
    if (!syncStatus) return 'Unknown';
    
    switch (syncStatus.status) {
      case 'success':
        return 'Synced';
      case 'partial':
        return 'Partial Sync';
      case 'failed':
        return 'Sync Failed';
      default:
        return 'Unknown';
    }
  };

  // Show Azure mode interface if detected
  if (isAzureMode) {
    return (
      <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <CheckCircle className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Azure Blob Storage Active
            </h3>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Recordings are now stored in Azure Blob Storage. No manual sync required.
            </p>
          </div>
        </div>
        <div className={`mt-4 p-3 rounded-lg ${isDarkMode ? 'bg-green-900/20 border border-green-800' : 'bg-green-50 border border-green-200'}`}>
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-4 h-4 text-green-600" />
            <span className={`text-sm font-medium ${isDarkMode ? 'text-green-300' : 'text-green-800'}`}>
              ✅ Automatic cloud storage and backup
            </span>
          </div>
          <div className="flex items-center space-x-2 mt-1">
            <CheckCircle className="w-4 h-4 text-green-600" />
            <span className={`text-sm font-medium ${isDarkMode ? 'text-green-300' : 'text-green-800'}`}>
              ✅ Accessible from anywhere with authentication
            </span>
          </div>
          <div className="flex items-center space-x-2 mt-1">
            <CheckCircle className="w-4 h-4 text-green-600" />
            <span className={`text-sm font-medium ${isDarkMode ? 'text-green-300' : 'text-green-800'}`}>
              ✅ HIPAA compliant and secure
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <RefreshCw className="w-5 h-5 text-blue-600" />
          <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            Recordings Sync
          </h3>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={checkSyncStatus}
            disabled={isLoading}
            className={`p-1 rounded ${isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'}`}
            title="Check sync status"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
          <button
            onClick={() => performSync(false)}
            disabled={isSyncing || !(syncStatus?.debug?.networkAvailable ?? false)}
            className={`px-3 py-1 text-sm rounded ${
              isDarkMode 
                ? 'bg-blue-900/30 hover:bg-blue-900/50 text-blue-300 disabled:opacity-50' 
                : 'bg-blue-100 hover:bg-blue-200 text-blue-800 disabled:opacity-50'
            }`}
          >
            {isSyncing ? 'Syncing...' : 'Sync Now'}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className={`mb-4 p-3 rounded ${isDarkMode ? 'bg-red-900/20 border border-red-800' : 'bg-red-50 border border-red-200'}`}>
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-4 h-4 text-red-600" />
            <span className={`text-sm ${isDarkMode ? 'text-red-300' : 'text-red-800'}`}>
              {error}
            </span>
          </div>
        </div>
      )}

      {/* Sync Status */}
      {syncStatus && (
        <div className="space-y-4">
          {/* Overall Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getSyncStatusIcon()}
              <span className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {getSyncStatusText()}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              {getSyncDirectionIcon()}
              <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {syncStatus?.debug?.syncDirection?.replace('-', ' → ') || 'Unknown'}
              </span>
            </div>
          </div>

          {/* Storage Status */}
          <div className="grid grid-cols-2 gap-4">
            {/* Network Storage */}
            <div className={`p-3 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className="flex items-center space-x-2 mb-2">
                {syncStatus?.debug?.networkAvailable ? (
                  <Wifi className="w-4 h-4 text-green-600" />
                ) : (
                  <WifiOff className="w-4 h-4 text-red-600" />
                )}
                <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Network Share
                </span>
              </div>
              <div className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {syncStatus?.summary?.networkFiles ?? 0}
              </div>
              <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                files
              </div>
            </div>

            {/* Local Storage */}
            <div className={`p-3 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className="flex items-center space-x-2 mb-2">
                <HardDrive className="w-4 h-4 text-blue-600" />
                <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Local Backup
                </span>
              </div>
              <div className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {syncStatus?.summary?.localFiles ?? 0}
              </div>
              <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                files
              </div>
            </div>
          </div>

          {/* Sync Statistics */}
          {(syncStatus?.networkToLocal?.transferred > 0 || syncStatus?.localToNetwork?.transferred > 0) && (
            <div className="space-y-2">
              <h4 className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Last Sync Results
              </h4>
              
              {syncStatus?.networkToLocal?.transferred > 0 && (
                <div className="flex items-center space-x-2">
                  <Download className="w-4 h-4 text-green-600" />
                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Downloaded {syncStatus?.networkToLocal?.transferred || 0} files
                  </span>
                </div>
              )}
              
              {syncStatus?.localToNetwork?.transferred > 0 && (
                <div className="flex items-center space-x-2">
                  <Upload className="w-4 h-4 text-blue-600" />
                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Uploaded {syncStatus?.localToNetwork?.transferred || 0} files
                  </span>
                </div>
              )}
              
              {(syncStatus?.summary?.duplicates ?? 0) > 0 && (
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-gray-500" />
                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {syncStatus?.summary?.duplicates ?? 0} files already synced
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Errors */}
          {(syncStatus?.networkToLocal?.errors?.length > 0 || syncStatus?.localToNetwork?.errors?.length > 0) && (
            <div className="space-y-2">
              <h4 className={`text-sm font-medium text-red-600`}>
                Sync Errors
              </h4>
              <div className="space-y-1">
                {[...(syncStatus?.networkToLocal?.errors || []), ...(syncStatus?.localToNetwork?.errors || [])]
                  .slice(0, 3)
                  .map((error, index) => (
                    <div key={index} className={`text-xs ${isDarkMode ? 'text-red-300' : 'text-red-700'}`}>
                      • {error}
                    </div>
                  ))}
                {((syncStatus?.networkToLocal?.errors?.length || 0) + (syncStatus?.localToNetwork?.errors?.length || 0)) > 3 && (
                  <div className={`text-xs ${isDarkMode ? 'text-red-300' : 'text-red-700'}`}>
                    ... and {((syncStatus?.networkToLocal?.errors?.length || 0) + (syncStatus?.localToNetwork?.errors?.length || 0)) - 3} more
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Last Check Time */}
          {lastCheck && (
            <div className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
              Last checked: {lastCheck.toLocaleTimeString()}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
