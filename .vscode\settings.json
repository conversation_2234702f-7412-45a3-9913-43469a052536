{
    "azureFunctions.deploySubpath": "azure-functions",
    "azureFunctions.postDeployTask": "npm install (functions)",
    "azureFunctions.projectLanguage": "TypeScript",
    "azureFunctions.projectRuntime": "~4",
    "debug.internalConsoleOptions": "neverOpen",
    "azureFunctions.projectLanguageModel": 4,
    "azureFunctions.preDeployTask": "npm prune (functions)",
    "appService.zipIgnorePattern": [
        "node_modules{,/**}",
        ".vscode{,/**}"
    ],
    "appService.defaultWebAppToDeploy": "/subscriptions/eb6394eb-6952-435e-8f81-b1cb5f82d3a5/resourceGroups/dentalapp/providers/Microsoft.Web/sites/dentalapp",
    "appService.deploySubpath": ".",
    "traycer.enableAutoAnalysis": true,
}