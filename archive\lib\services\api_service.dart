import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/appointment.dart';
import '../utils/credentials.dart';

class ApiService extends ChangeNotifier {
  bool _isAuthenticated = false;
  String? _token;
  String? _errorMessage;
  bool _isLoading = false;

  bool get isAuthenticated => _isAuthenticated;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;

  // Base URL for Sikka API
  final String _baseUrl = 'https://api.sikkasoft.com/v2';

  // Authenticate with Sikka API
  Future<bool> authenticate(String appId, String appKey, String officeId, String secretKey) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/login'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'app_id': appId,
          'app_key': appKey,
          'office_id': officeId,
          'secret_key': secret<PERSON>ey,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        _token = data['token'];
        _isAuthenticated = true;
        _errorMessage = null;
      } else {
        _errorMessage = data['message'] ?? 'Authentication failed';
        _isAuthenticated = false;
      }
    } catch (e) {
      _errorMessage = 'Error connecting to Sikka API: $e';
      _isAuthenticated = false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }

    return _isAuthenticated;
  }

  // Fetch appointments for a specific date
  Future<List<Appointment>> fetchAppointments(String date) async {
    if (!_isAuthenticated || _token == null) {
      throw Exception('Not authenticated');
    }

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/appointments?startdate=$date&enddate=$date&date_filter_on=appointment_date'),
        headers: {
          'Authorization': 'Bearer $_token',
          'Content-Type': 'application/json',
        },
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        final List<dynamic> appointmentsJson = data['appointments'] ?? [];
        
        // Handle pagination if needed
        int totalPages = data['total_pages'] ?? 1;
        int currentPage = data['current_page'] ?? 1;
        
        List<Appointment> allAppointments = appointmentsJson
            .map((json) => Appointment.fromJson(json))
            .toList();
        
        // If there are more pages, fetch them
        if (totalPages > 1 && currentPage < totalPages) {
          for (int page = currentPage + 1; page <= totalPages; page++) {
            final pageResponse = await http.get(
              Uri.parse('$_baseUrl/appointments?startdate=$date&enddate=$date&date_filter_on=appointment_date&page=$page'),
              headers: {
                'Authorization': 'Bearer $_token',
                'Content-Type': 'application/json',
              },
            );
            
            final pageData = jsonDecode(pageResponse.body);
            
            if (pageResponse.statusCode == 200 && pageData['success'] == true) {
              final List<dynamic> pageAppointmentsJson = pageData['appointments'] ?? [];
              allAppointments.addAll(
                pageAppointmentsJson
                    .map((json) => Appointment.fromJson(json))
                    .toList()
              );
            }
          }
        }
        
        return allAppointments;
      } else {
        _errorMessage = data['message'] ?? 'Failed to fetch appointments';
        return [];
      }
    } catch (e) {
      _errorMessage = 'Error fetching appointments: $e';
      return [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
