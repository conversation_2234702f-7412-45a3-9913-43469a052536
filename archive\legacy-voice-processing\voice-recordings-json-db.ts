/**
 * Unified Voice Recordings Database using JSON receipts and file storage
 * Works offline, no network dependencies, uses existing JSON receipt system
 */

import fs from 'fs/promises';
import path from 'path';
import { existsSync } from 'fs';

export interface VoiceRecording {
  id: string;
  filename: string;
  device_id: string;
  file_path: string;
  file_size?: number;
  duration_seconds?: number;
  imported_at: string;
  recording_date: string;
  
  // Workflow status
  status: 'unassigned' | 'assigned' | 'transcribing' | 'transcribed' | 'reviewing' | 'completed';
  
  // Assignment info
  assigned_patient_id?: string;
  assigned_appointment_id?: string;
  assigned_appointment_date?: string;
  assigned_provider?: string;
  assigned_at?: string;
  assigned_by?: string;
  
  // Transcription
  transcription?: string;
  transcription_confidence?: number;
  transcribed_at?: string;
  
  // Clinical notes
  clinical_summary?: string;
  clinical_notes?: string;
  notes_posted_at?: string;
  posted_to_dentrix: boolean;
  
  // Metadata
  category: 'clinical' | 'administrative' | 'consultation' | 'other';
  tags?: string[];
  notes?: string;
  
  // UI state
  isUploaded: boolean;
  localPath?: string;
  networkPath?: string;
  
  // Matching info
  matchedAppointmentId?: string;
  patientName?: string;
  appointmentDate?: string;
  provider?: string;
  confidence?: number;
  
  // Timestamps
  created_at: string;
  updated_at: string;
}

export interface VoiceRecordingMatch {
  recordingId: string;
  appointmentId: string;
  patientName: string;
  appointmentDate: string;
  provider: string;
  matchedAt: string;
}

export class VoiceRecordingsDB {
  private static readonly DATA_DIR = path.join(process.cwd(), 'data');
  private static readonly RECORDINGS_FILE = path.join(this.DATA_DIR, 'voice-recordings.json');
  private static readonly MATCHES_FILE = path.join(this.DATA_DIR, 'recording-matches.json');
  private static readonly TRANSCRIPTIONS_FILE = path.join(this.DATA_DIR, 'transcriptions.json');
  
  // Ensure data directory exists
  private static async ensureDataDir(): Promise<void> {
    if (!existsSync(this.DATA_DIR)) {
      await fs.mkdir(this.DATA_DIR, { recursive: true });
    }
  }
  
  // Load recordings from JSON file
  private static async loadRecordings(): Promise<VoiceRecording[]> {
    await this.ensureDataDir();
    
    try {
      if (existsSync(this.RECORDINGS_FILE)) {
        const data = await fs.readFile(this.RECORDINGS_FILE, 'utf-8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.error('Error loading recordings:', error);
    }
    
    return [];
  }
  
  // Save recordings to JSON file
  private static async saveRecordings(recordings: VoiceRecording[]): Promise<void> {
    await this.ensureDataDir();
    
    try {
      // Atomic write: write to temp file, then rename
      const tempFile = this.RECORDINGS_FILE + '.tmp';
      await fs.writeFile(tempFile, JSON.stringify(recordings, null, 2), 'utf-8');
      await fs.rename(tempFile, this.RECORDINGS_FILE);
    } catch (error) {
      console.error('Error saving recordings:', error);
      throw error;
    }
  }
  
  // Load matches from JSON file
  private static async loadMatches(): Promise<Record<string, VoiceRecordingMatch>> {
    await this.ensureDataDir();
    
    try {
      if (existsSync(this.MATCHES_FILE)) {
        const data = await fs.readFile(this.MATCHES_FILE, 'utf-8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.error('Error loading matches:', error);
    }
    
    return {};
  }
  
  // Save matches to JSON file
  private static async saveMatches(matches: Record<string, VoiceRecordingMatch>): Promise<void> {
    await this.ensureDataDir();
    
    try {
      const tempFile = this.MATCHES_FILE + '.tmp';
      await fs.writeFile(tempFile, JSON.stringify(matches, null, 2), 'utf-8');
      await fs.rename(tempFile, this.MATCHES_FILE);
    } catch (error) {
      console.error('Error saving matches:', error);
      throw error;
    }
  }
  
  // Load transcriptions from JSON file
  private static async loadTranscriptions(): Promise<Record<string, string>> {
    await this.ensureDataDir();
    
    try {
      if (existsSync(this.TRANSCRIPTIONS_FILE)) {
        const data = await fs.readFile(this.TRANSCRIPTIONS_FILE, 'utf-8');
        return JSON.parse(data);
      }
    } catch (error) {
      console.error('Error loading transcriptions:', error);
    }
    
    return {};
  }
  
  // Save transcriptions to JSON file
  private static async saveTranscriptions(transcriptions: Record<string, string>): Promise<void> {
    await this.ensureDataDir();
    
    try {
      const tempFile = this.TRANSCRIPTIONS_FILE + '.tmp';
      await fs.writeFile(tempFile, JSON.stringify(transcriptions, null, 2), 'utf-8');
      await fs.rename(tempFile, this.TRANSCRIPTIONS_FILE);
    } catch (error) {
      console.error('Error saving transcriptions:', error);
      throw error;
    }
  }
  
  // Scan for recordings from file system and JSON receipts
  // Get all recordings (alias for scanAndSyncRecordings for compatibility)
  static async getAllRecordings(): Promise<VoiceRecording[]> {
    return this.scanAndSyncRecordings();
  }

  // Update a specific recording
  static async updateRecording(id: string, updates: Partial<VoiceRecording>): Promise<void> {
    const recordings = await this.loadRecordings();
    const index = recordings.findIndex(r => r.id === id);
    
    if (index >= 0) {
      recordings[index] = { 
        ...recordings[index], 
        ...updates, 
        updated_at: new Date().toISOString() 
      };
      await this.saveRecordings(recordings);
    }
  }

  // Create a new recording
  static async createRecording(recording: VoiceRecording): Promise<void> {
    const recordings = await this.loadRecordings();
    recordings.push(recording);
    await this.saveRecordings(recordings);
  }

  static async scanAndSyncRecordings(): Promise<VoiceRecording[]> {
    const recordings: VoiceRecording[] = [];
    const existingRecordings = await this.loadRecordings();
    const existingMap = new Map(existingRecordings.map(r => [r.id, r]));
    
    // Scan network share (if available)
    const networkPath = '\\\\192.168.0.2\\share\\RECORDINGS';
    try {
      await this.scanRecordingsFromPath(networkPath, true, recordings, existingMap);
    } catch (error) {
      console.log('Network share not available, using local only');
    }
    
    // Scan local backup
    const localPath = path.join(process.cwd(), 'voice-recordings');
    try {
      await this.scanRecordingsFromPath(localPath, false, recordings, existingMap);
    } catch (error) {
      console.log('Local recordings not available');
    }
    
    // Add existing recordings that weren't found in file system
    for (const existing of existingRecordings) {
      if (!recordings.find(r => r.id === existing.id)) {
        recordings.push(existing);
      }
    }
    
    // Load and merge additional data
    const matches = await this.loadMatches();
    const transcriptions = await this.loadTranscriptions();
    
    // Enrich recordings with matches and transcriptions
    for (const recording of recordings) {
      // Add transcription
      if (transcriptions[recording.id]) {
        recording.transcription = transcriptions[recording.id];
        recording.status = 'transcribed';
      }
      
      // Add match info
      const match = matches[recording.id];
      if (match) {
        recording.matchedAppointmentId = match.appointmentId;
        recording.patientName = match.patientName;
        recording.appointmentDate = match.appointmentDate;
        recording.provider = match.provider;
        recording.confidence = 1.0;
        recording.assigned_appointment_id = match.appointmentId;
        recording.assigned_patient_id = match.patientName;
        recording.assigned_appointment_date = match.appointmentDate;
        recording.assigned_provider = match.provider;
        recording.assigned_at = match.matchedAt;
        recording.status = recording.transcription ? 'transcribed' : 'assigned';
      }
    }
    
    // Save updated recordings
    await this.saveRecordings(recordings);
    
    return recordings;
  }
  
  // Scan recordings from a specific path
  private static async scanRecordingsFromPath(
    dirPath: string, 
    isNetwork: boolean, 
    recordings: VoiceRecording[], 
    existingMap: Map<string, VoiceRecording>
  ): Promise<void> {
    // Implementation continues in next part...
  }
}
