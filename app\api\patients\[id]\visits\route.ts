import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');

    if (!id) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      );
    }

    console.log(`API: Fetching visits for patient: ${id}`, {
      date,
      startDate,
      endDate
    });

    const credentials = loadCredentials();
    const sikkaClient = new SikkaApiClient(credentials);
    await sikkaClient.authenticate();
    let visits = await sikkaClient.getPatientVisits(id);

    // Filter by date if specified
    if (date) {
      visits = visits.filter(visit => visit.date === date);
    } else if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      visits = visits.filter(visit => {
        const visitDate = new Date(visit.date);
        return visitDate >= start && visitDate <= end;
      });
    }

    // Extract procedure codes from visits
    const enhancedVisits = visits.map(visit => {
      const procedures = [];

      // Extract procedure codes from visit data (procedure_code1 through procedure_code7)
      for (let i = 1; i <= 7; i++) {
        const procCode = visit[`procedure_code${i}`];
        const procAmount = visit[`procedure_code${i}_amount`];
        const procDescription = visit[`procedure_code${i}_description`];

        if (procCode && procCode !== '' && parseFloat(procAmount || '0') > 0) {
          procedures.push({
            code: procCode,
            description: procDescription || `Procedure ${procCode}`,
            amount: parseFloat(procAmount || '0'),
            sequence: i
          });
        }
      }

      return {
        ...visit,
        procedures,
        procedureCount: procedures.length,
        totalAmount: procedures.reduce((sum, proc) => sum + proc.amount, 0)
      };
    });

    console.log(`API: Found ${enhancedVisits.length} visits for patient ${id}`);

    return NextResponse.json({
      patientId: id,
      visits: enhancedVisits,
      total: enhancedVisits.length,
      summary: {
        totalVisits: enhancedVisits.length,
        visitsWithProcedures: enhancedVisits.filter(v => v.procedureCount > 0).length,
        totalProcedures: enhancedVisits.reduce((sum, v) => sum + v.procedureCount, 0),
        dateRange: date ? `Single date: ${date}` : startDate && endDate ? `${startDate} to ${endDate}` : 'All visits'
      }
    });

  } catch (error) {
    console.error('Patient visits API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch patient visits' },
      { status: 500 }
    );
  }
}
