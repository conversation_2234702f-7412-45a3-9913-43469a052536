/**
 * TRANSCRIPTION DEBUG API - WITH FILE RESOLVER
 * 
 * This endpoint provides real-time debugging information for transcription issues
 * directly in the web app - no more console hunting!
 * Version: 2025-07-11 v2.0 - NOW WITH ADVANCED FILE RESOLUTION
 * 
 * NEW FEATURES:
 * - FileResolver system handles naming mismatches (e.g., file.mp3-12345-67890)
 * - Web-based debugging interface at /debug-transcription
 * - Direct OpenAI calls (no more HTTP timeouts)
 * - Comprehensive file search strategies
 * 
 * Usage:
 * GET /api/voice/debug-transcription - Shows overall system status
 * GET /api/voice/debug-transcription?file=filename.mp3 - Debug specific file
 * GET /api/voice/debug-transcription?listFiles=true - List all available files
 */

import { NextRequest, NextResponse } from 'next/server';
import { AzureStorageService } from '@/lib/azure-storage';
import { FileResolver } from '@/lib/file-resolver';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const specificFile = searchParams.get('file');
  const listFiles = searchParams.get('listFiles') === 'true';

  const debugInfo = {
    timestamp: new Date().toISOString(),
    request: {
      specificFile,
      listFiles
    },
    system: {
      azureConfigured: AzureStorageService.isConfigured(),
      openaiConfigured: !!process.env.OPENAI_API_KEY
    },
    results: {} as any
  };

  try {
    // CHECK 1: Azure Storage Configuration
    debugInfo.results.azureStorage = {
      configured: AzureStorageService.isConfigured(),
      connectionString: process.env.AZURE_STORAGE_CONNECTION_STRING ? 
        `${process.env.AZURE_STORAGE_CONNECTION_STRING.substring(0, 50)}...` : 'MISSING'
    };

    // CHECK 2: OpenAI Configuration
    debugInfo.results.openai = {
      configured: !!process.env.OPENAI_API_KEY,
      keyPrefix: process.env.OPENAI_API_KEY ? 
        `${process.env.OPENAI_API_KEY.substring(0, 10)}...` : 'MISSING'
    };

    // CHECK 3: List Files if requested
    if (listFiles) {
      const allFiles = await FileResolver.listAllFiles();
      debugInfo.results.allFiles = {
        count: allFiles.length,
        files: allFiles.slice(0, 20).map(f => ({
          name: f.name,
          path: f.path,
          size: `${Math.round(f.size / 1024)}KB`
        })),
        truncated: allFiles.length > 20
      };
    }

    // CHECK 4: Debug specific file
    if (specificFile) {
      console.log(`🔍 DEBUG: Testing file resolution for "${specificFile}"`);
      const fileResult = await FileResolver.findAudioFile(specificFile);
      
      debugInfo.results.specificFile = {
        requested: specificFile,
        found: fileResult.found,
        actualPath: fileResult.actualPath,
        originalName: fileResult.originalName,
        searchStrategies: fileResult.searchStrategies,
        error: fileResult.error
      };

      // If found, try to get file info
      if (fileResult.found && fileResult.actualPath) {
        try {
          const metadata = await AzureStorageService.getFileMetadata(fileResult.actualPath);
          debugInfo.results.specificFile.metadata = metadata;
        } catch (error) {
          debugInfo.results.specificFile.metadataError = error instanceof Error ? error.message : 'Unknown error';
        }
      }
    }

    // CHECK 5: Recent job status (if any)
    // This would check in-memory job stores for recent activity
    debugInfo.results.recentActivity = {
      message: 'Job status tracking would go here',
      note: 'This can be expanded to show recent transcription jobs'
    };

    return NextResponse.json(debugInfo, { status: 200 });

  } catch (error) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json({
      ...debugInfo,
      error: {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      }
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, fileName } = await request.json();
    
    if (action === 'testFileResolution' && fileName) {
      console.log(`🧪 DEBUG: Testing file resolution for "${fileName}"`);
      const result = await FileResolver.findAudioFile(fileName);
      
      return NextResponse.json({
        action: 'testFileResolution',
        fileName,
        result,
        timestamp: new Date().toISOString()
      });
    }

    return NextResponse.json({
      error: 'Invalid action or missing fileName'
    }, { status: 400 });

  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}