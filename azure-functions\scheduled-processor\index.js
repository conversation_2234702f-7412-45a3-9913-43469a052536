const { BlobServiceClient } = require("@azure/storage-blob");

module.exports = async function (context, myTimer) {
    const timestamp = new Date().toISOString();
    context.log(`🔄 Scheduled processor started at: ${timestamp}`);

    try {
        const blobServiceClient = BlobServiceClient.fromConnectionString(process.env.AZURE_STORAGE_CONNECTION_STRING);
        const containerClient = blobServiceClient.getContainerClient('recordings');

        // Scan for unprocessed files
        await scanForUnprocessedFiles(context, containerClient);

        // Update processing statistics
        await updateProcessingStatistics(context, containerClient);

        context.log(`✅ Scheduled processor completed successfully`);

    } catch (error) {
        context.log(`❌ Scheduled processor failed:`, error.message);
    }
};

async function scanForUnprocessedFiles(context, containerClient) {
    context.log(`🔍 Scanning for unprocessed files...`);
    
    const audioFiles = [];
    const processedFiles = new Set();
    const failedFiles = new Set();

    // Get all blobs
    for await (const blob of containerClient.listBlobsFlat()) {
        const fileName = blob.name;
        
        if (isAudioFile(fileName)) {
            audioFiles.push(fileName);
        } else if (fileName.endsWith('.json') && !fileName.endsWith('.failure.json')) {
            const baseName = fileName.replace(/\.json$/, '');
            processedFiles.add(baseName);
        } else if (fileName.endsWith('.failure.json')) {
            const baseName = fileName.replace(/\.failure\.json$/, '');
            failedFiles.add(baseName);
        }
    }

    // Find unprocessed files
    const unprocessedFiles = audioFiles.filter(fileName => {
        const baseName = fileName.replace(/\.[^/.]+$/, '');
        return !processedFiles.has(baseName) && !failedFiles.has(baseName);
    });

    context.log(`📊 Processing status:`);
    context.log(`  - Total audio files: ${audioFiles.length}`);
    context.log(`  - Processed: ${processedFiles.size}`);
    context.log(`  - Failed (permanent): ${failedFiles.size}`);
    context.log(`  - Unprocessed: ${unprocessedFiles.length}`);

    // Trigger processing for up to 5 unprocessed files
    const filesToProcess = unprocessedFiles.slice(0, 5);
    
    for (const fileName of filesToProcess) {
        context.log(`🎵 Triggering processing for missed file: ${fileName}`);
        await triggerFileProcessing(context, fileName);
    }
}

async function updateProcessingStatistics(context, containerClient) {
    const stats = {
        lastUpdated: new Date().toISOString(),
        totalFiles: 0,
        processed: 0,
        permanentlyFailed: 0,
        unprocessed: 0,
        processingQueue: 0,
        systemStatus: 'active',
        breakdown: {
            transcribed: 0,
            summarized: 0,
            complete: 0,
            validationFailed: 0,
            processingFailed: 0
        }
    };

    let audioFiles = 0;
    const processedFiles = new Set();
    const failedFiles = new Set();

    // Analyze all files
    for await (const blob of containerClient.listBlobsFlat()) {
        const fileName = blob.name;
        
        if (isAudioFile(fileName)) {
            audioFiles++;
            const baseName = fileName.replace(/\.[^/.]+$/, '');
            
            try {
                const transcriptionFile = `${baseName}.json`;
                const failureFile = `${baseName}.failure.json`;
                
                const transcriptionExists = await checkFileExists(containerClient, transcriptionFile);
                const failureExists = await checkFileExists(containerClient, failureFile);
                
                if (transcriptionExists) {
                    processedFiles.add(baseName);
                    stats.breakdown.complete++;
                } else if (failureExists) {
                    failedFiles.add(baseName);
                    stats.breakdown.validationFailed++;
                }
            } catch (error) {
                // File might be currently being processed
            }
        }
    }

    stats.totalFiles = audioFiles;
    stats.processed = processedFiles.size;
    stats.permanentlyFailed = failedFiles.size;
    stats.unprocessed = audioFiles - processedFiles.size - failedFiles.size;

    // Save statistics
    const statsFileName = '_processing_stats.json';
    await uploadJsonToBlob(containerClient, statsFileName, stats);
    
    context.log(`📊 Updated processing statistics: ${stats.processed}/${stats.totalFiles} processed, ${stats.unprocessed} pending`);
}

async function triggerFileProcessing(context, fileName) {
    try {
        const transcriptionUrl = process.env.TRANSCRIPTION_API_URL;
        
        const response = await fetch(transcriptionUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Internal-Call': 'true'
            },
            body: JSON.stringify({
                fileName: fileName,
                forceRetranscribe: false
            })
        });

        if (response.ok) {
            context.log(`✅ Successfully triggered processing for: ${fileName}`);
        } else {
            context.log(`⚠️ Failed to trigger processing for: ${fileName} - ${response.status}`);
        }
    } catch (error) {
        context.log(`❌ Error triggering processing for ${fileName}:`, error.message);
    }
}

function isAudioFile(fileName) {
    const audioExtensions = ['.mp3', '.wav', '.m4a', '.mp4', '.webm', '.ogg', '.aac', '.flac'];
    const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return audioExtensions.includes(ext);
}

async function checkFileExists(containerClient, fileName) {
    try {
        const blobClient = containerClient.getBlobClient(fileName);
        return await blobClient.exists();
    } catch {
        return false;
    }
}

async function uploadJsonToBlob(containerClient, fileName, data) {
    const blobClient = containerClient.getBlockBlobClient(fileName);
    const jsonContent = JSON.stringify(data, null, 2);
    await blobClient.upload(jsonContent, jsonContent.length, {
        blobHTTPHeaders: { blobContentType: 'application/json' }
    });
}