// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:dental_schedule/main.dart';

void main() {
  testWidgets('Dental app loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const DentalViewerApp());

    // Verify that the app title is displayed
    expect(find.text('Dental Appointment Viewer'), findsOneWidget);

    // Verify that the date selector is displayed
    expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    expect(find.byIcon(Icons.arrow_forward), findsOneWidget);

    // Verify that the refresh button is displayed
    expect(find.byIcon(Icons.refresh), findsOneWidget);
  });
}
