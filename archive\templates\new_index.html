<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dental Office Schedule Viewer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f9ff;
        }
        .time-slot {
            height: 36px; /* Reduced height */
            border-bottom: 1px solid #e5e7eb;
        }
        .appointment {
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            overflow: hidden;
            position: absolute;
            width: calc(100% - 6px);
            left: 3px;
        }
        .operatory-column {
            min-width: 180px; /* Reduced width */
        }
        .time-label {
            width: 70px; /* Reduced width */
        }
        .loading {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .toggle-button {
            transition: all 0.2s ease;
        }
        .toggle-button.active {
            background-color: #3b82f6;
            color: white;
            box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
        }
        .toggle-button.inactive {
            background-color: #f3f4f6;
            color: #6b7280;
        }
        .toggle-button.inactive:hover {
            background-color: #e5e7eb;
        }
        /* Compact design */
        .compact-padding {
            padding: 0.75rem;
        }
        .compact-gap {
            gap: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="min-h-screen">
        <header class="bg-gradient-to-r from-blue-600 to-cyan-500 text-white p-3 shadow-md">
            <div class="container mx-auto">
                <h1 class="text-2xl font-bold">Dental Schedule Viewer</h1>
                <p class="text-blue-100 text-sm">View and manage patient appointments by operatory</p>
            </div>
        </header>

        <main class="container mx-auto p-3">
            <div class="bg-white rounded-lg shadow-md p-3 mb-4">
                <div class="flex flex-wrap gap-3">
                    <!-- Date Selector -->
                    <div class="w-full sm:w-auto">
                        <label for="date" class="block text-sm font-medium text-gray-700 mb-1">
                            <i class="fas fa-calendar-alt mr-1 text-blue-500"></i>Date
                        </label>
                        <input type="date" id="date" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            value="">
                    </div>

                    <!-- Modern Operatory Selector - Horizontal Layout -->
                    <div class="flex-grow">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            <i class="fas fa-tooth mr-1 text-blue-500"></i>Operatories
                        </label>
                        <div class="flex flex-wrap gap-1" id="operatory-selector">
                            <!-- Modern toggle buttons will be inserted here -->
                        </div>
                    </div>

                    <!-- Fetch Button -->
                    <div class="flex items-end">
                        <button id="fetch-button" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors flex items-center justify-center shadow-sm">
                            <i class="fas fa-sync-alt mr-1"></i>
                            Fetch Schedule
                        </button>
                    </div>
                </div>
            </div>

            <!-- Schedule Display -->
            <div id="schedule-container" class="bg-white rounded-lg shadow-md p-2 overflow-x-auto hidden">
                <div class="flex min-w-full">
                    <div class="time-label pr-1 border-r border-gray-200">
                        <div class="h-10 flex items-center justify-center font-semibold text-gray-500 text-sm">
                            Time
                        </div>
                        <div id="time-labels"></div>
                    </div>
                    <div id="operatory-columns" class="flex flex-1"></div>
                </div>
            </div>

            <!-- Empty State -->
            <div id="empty-state" class="bg-white rounded-lg shadow-md p-6 text-center">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 24 24' fill='none' stroke='%23a0aec0' stroke-width='1' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E" 
                    class="mx-auto mb-3" alt="Calendar">
                <h3 class="text-lg font-semibold text-gray-700 mb-1">No Schedule Data</h3>
                <p class="text-gray-500 text-sm">Select a date and operatories, then click "Fetch Schedule"</p>
            </div>

            <!-- Loading State -->
            <div id="loading-state" class="bg-white rounded-lg shadow-md p-6 text-center hidden">
                <div class="loading inline-block p-3 rounded-full bg-blue-100 mb-3">
                    <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-700 mb-1">Loading Schedule Data</h3>
                <p class="text-gray-500 text-sm">Please wait while we fetch the appointment information...</p>
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date as default
            const today = new Date();
            const dateInput = document.getElementById('date');
            dateInput.value = today.toISOString().split('T')[0];
            
            // Add event listener to date input to fetch operatories when date changes
            dateInput.addEventListener('change', function() {
                fetchOperatoriesForDate(dateInput.value);
            });
            
            // Create modern operatory toggles for today's date
            fetchOperatoriesForDate(dateInput.value);
            
            // Fetch button event listener
            document.getElementById('fetch-button').addEventListener('click', fetchSchedule);
            
            // Generate time labels (8:00 AM to 5:00 PM in 10-minute increments)
            generateTimeLabels();
        });
        
        function fetchOperatoriesForDate(date) {
            // Show loading indicator for operatories
            const container = document.getElementById('operatory-selector');
            container.innerHTML = '<div class="text-sm text-gray-500">Loading operatories...</div>';
            
            // Fetch operatories for the selected date
            fetch(`/api/operatories?date=${date}`)
                .then(response => response.json())
                .then(operatories => {
                    // Clear loading indicator
                    container.innerHTML = '';
                    
                    // Create toggles for each operatory
                    operatories.forEach(op => {
                        const button = document.createElement('button');
                        button.id = op.id;
                        button.className = `toggle-button ${op.active ? 'active' : 'inactive'} px-3 py-2 rounded-md text-sm font-medium transition-all`;
                        button.dataset.active = op.active;
                        button.dataset.value = op.name;
                        button.innerHTML = op.name;
                        
                        button.addEventListener('click', function() {
                            const isActive = button.dataset.active === 'true';
                            button.dataset.active = !isActive;
                            
                            if (!isActive) {
                                button.classList.remove('inactive');
                                button.classList.add('active');
                            } else {
                                button.classList.remove('active');
                                button.classList.add('inactive');
                            }
                        });
                        
                        container.appendChild(button);
                    });
                })
                .catch(error => {
                    console.error('Error fetching operatories:', error);
                    container.innerHTML = '<div class="text-sm text-red-500">Error loading operatories</div>';
                });
        }

        function generateTimeLabels() {
            const timeLabelsContainer = document.getElementById('time-labels');
            timeLabelsContainer.innerHTML = '';
            
            // Start at 8:00 AM
            let hour = 8;
            let minute = 0;
            let period = 'AM';
            
            // Create time slots until 5:00 PM
            while (hour < 17 || (hour === 17 && minute === 0)) {
                const formattedHour = hour > 12 ? hour - 12 : hour;
                const formattedMinute = minute.toString().padStart(2, '0');
                const timeLabel = `${formattedHour}:${formattedMinute}`;
                
                const timeSlot = document.createElement('div');
                timeSlot.className = 'time-slot flex items-center justify-end pr-1 text-xs text-gray-500';
                timeSlot.textContent = timeLabel;
                timeLabelsContainer.appendChild(timeSlot);
                
                // Increment by 10 minutes
                minute += 10;
                if (minute >= 60) {
                    minute = 0;
                    hour++;
                    if (hour === 12) {
                        period = 'PM';
                    }
                }
            }
        }

        function fetchSchedule() {
            const date = document.getElementById('date').value;
            const selectedOperatories = Array.from(document.querySelectorAll('.toggle-button[data-active="true"]'))
                .map(button => button.dataset.value);
            
            if (!date || selectedOperatories.length === 0) {
                alert('Please select a date and at least one operatory.');
                return;
            }
            
            // Show loading state
            document.getElementById('empty-state').classList.add('hidden');
            document.getElementById('schedule-container').classList.add('hidden');
            document.getElementById('loading-state').classList.remove('hidden');
            
            // Fetch appointments from the API
            const queryParams = new URLSearchParams();
            queryParams.append('date', date);
            selectedOperatories.forEach(op => queryParams.append('operatories[]', op));
            
            fetch(`/api/appointments?${queryParams.toString()}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(appointments => {
                    // Hide loading state
                    document.getElementById('loading-state').classList.add('hidden');
                    document.getElementById('schedule-container').classList.remove('hidden');
                    
                    // Generate schedule display
                    generateScheduleDisplay(date, selectedOperatories, appointments);
                })
                .catch(error => {
                    console.error('Error fetching appointments:', error);
                    document.getElementById('loading-state').classList.add('hidden');
                    document.getElementById('empty-state').classList.remove('hidden');
                    alert(`Error fetching appointments: ${error.message}`);
                });
        }

        function generateScheduleDisplay(date, operatories, appointments) {
            const operatoryColumns = document.getElementById('operatory-columns');
            operatoryColumns.innerHTML = '';
            
            // Create column for each selected operatory
            operatories.forEach(operatory => {
                const column = document.createElement('div');
                column.className = 'operatory-column flex-1 min-w-[180px] border-r border-gray-200 last:border-r-0';
                
                // Column header
                const header = document.createElement('div');
                header.className = 'h-10 flex items-center justify-center font-semibold bg-blue-50 border-b border-gray-200 text-sm';
                header.textContent = operatory;
                column.appendChild(header);
                
                // Time slots container
                const slotsContainer = document.createElement('div');
                slotsContainer.className = 'relative';
                
                // Create time slots (8:00 AM to 5:00 PM in 10-minute increments)
                let hour = 8;
                let minute = 0;
                let slotCount = 0;
                
                while (hour < 17 || (hour === 17 && minute === 0)) {
                    const timeSlot = document.createElement('div');
                    timeSlot.className = 'time-slot';
                    slotsContainer.appendChild(timeSlot);
                    
                    // Increment by 10 minutes
                    minute += 10;
                    slotCount++;
                    if (minute >= 60) {
                        minute = 0;
                        hour++;
                    }
                }
                
                // Add real appointments
                addAppointments(slotsContainer, operatory, appointments);
                
                column.appendChild(slotsContainer);
                operatoryColumns.appendChild(column);
            });
        }

        function addAppointments(container, operatory, allAppointments) {
            // Filter appointments for this operatory
            const appointments = allAppointments.filter(appt => appt.operatory === operatory);
            
            appointments.forEach(appt => {
                const startTime = parseTime(appt.startTime);
                const endTime = parseTime(appt.endTime);
                
                // Calculate position and height
                const startMinutes = (startTime.hour - 8) * 60 + startTime.minute;
                const endMinutes = (endTime.hour - 8) * 60 + endTime.minute;
                const durationMinutes = endMinutes - startMinutes;
                
                const topPosition = (startMinutes / 10) * 36; // 36px per 10-minute slot
                const height = (durationMinutes / 10) * 36;
                
                // Create appointment element
                const appointmentEl = document.createElement('div');
                
                // Determine appointment color based on type
                let bgColor, borderColor, textColor;
                switch(appt.type) {
                    case 'New Patient':
                        bgColor = 'bg-blue-100';
                        borderColor = 'border-blue-600';
                        textColor = 'text-blue-800';
                        break;
                    case 'Cleaning':
                        bgColor = 'bg-green-100';
                        borderColor = 'border-green-600';
                        textColor = 'text-green-800';
                        break;
                    case 'Check-up':
                        bgColor = 'bg-purple-100';
                        borderColor = 'border-purple-600';
                        textColor = 'text-purple-800';
                        break;
                    case 'Filling':
                        bgColor = 'bg-amber-100';
                        borderColor = 'border-amber-600';
                        textColor = 'text-amber-800';
                        break;
                    case 'Crown':
                        bgColor = 'bg-rose-100';
                        borderColor = 'border-rose-600';
                        textColor = 'text-rose-800';
                        break;
                    case 'Blocked':
                        bgColor = 'bg-gray-100';
                        borderColor = 'border-gray-400';
                        textColor = 'text-gray-600';
                        break;
                    default:
                        bgColor = 'bg-gray-100';
                        borderColor = 'border-gray-600';
                        textColor = 'text-gray-800';
                }
                
                appointmentEl.className = `appointment ${bgColor} border-l-2 ${borderColor}`;
                appointmentEl.style.top = `${topPosition}px`;
                appointmentEl.style.height = `${height}px`;
                
                // Appointment content - more compact
                appointmentEl.innerHTML = `
                    <div class="p-1 h-full flex flex-col">
                        <div class="font-semibold text-xs ${textColor}">${appt.patientName}</div>
                        <div class="text-xs ${textColor.replace('800', '600')}">${appt.type}</div>
                        <div class="text-xs mt-auto text-gray-500">${appt.startTime.replace(' AM', '').replace(' PM', '')}</div>
                    </div>
                `;
                
                // Add tooltip with more details
                appointmentEl.title = `${appt.patientName}\n${appt.type}\n${appt.startTime} - ${appt.endTime}\nProvider: ${appt.provider}\n${appt.description}`;
                
                container.appendChild(appointmentEl);
            });
        }

        function parseTime(timeString) {
            const [time, period] = timeString.split(' ');
            let [hour, minute] = time.split(':').map(Number);
            
            if (period === 'PM' && hour < 12) {
                hour += 12;
            } else if (period === 'AM' && hour === 12) {
                hour = 0;
            }
            
            return { hour, minute };
        }
    </script>
</body>
</html>
