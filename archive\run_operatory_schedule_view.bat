@echo off
REM Operatory Schedule View
REM This batch file runs the operatory_schedule_view.py script with the specified parameters

echo Operatory Schedule View
echo --------------------
echo.
echo NOTE: This script is currently using TEST DATA from the Sikka API.
echo To access your real practice data, please contact Sikka support
echo to get production API credentials.
echo.

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Python is not installed or not in your PATH.
    echo Please install Python from https://www.python.org/downloads/
    pause
    exit /b
)

REM Check if required packages are installed
python -c "import requests" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Installing required packages...
    pip install requests
)

REM Get date (default to today)
set /p date="Enter date (YYYY-MM-DD) [today]: "
if "%date%"=="" (
    for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (
        set mm=%%a
        set dd=%%b
        set yy=%%c
    )
    set date=%yy%-%mm%-%dd%
)

REM Ask if user wants to list available operatories
set /p list_ops="Do you want to see a list of available operatories? (y/n) [n]: "
if /i "%list_ops%"=="y" (
    echo Listing available operatories...
    python operatory_schedule_view.py %date% --list-operatories
    pause

    REM Ask if user wants to continue after seeing the list
    set /p continue="Continue with schedule generation? (y/n) [y]: "
    if /i not "%continue%"=="y" (
        if not "%continue%"=="" (
            exit /b
        )
    )
)

REM Ask if user wants to specify operatories
set /p specify_ops="Do you want to specify which operatories to display? (y/n) [n]: "
if /i "%specify_ops%"=="y" (
    set /p operatories="Enter comma-separated list of operatories (e.g., DL01,DL02): "
    if not "%operatories%"=="" (
        set operatory_flag=--operatories=%operatories%
    ) else (
        set operatory_flag=
    )
) else (
    set operatory_flag=
    echo Using all available operatories from the API...
)

REM Ask if user wants to show MODNO appointments
set /p show_modno="Do you want to show MODNO appointments in the schedule? (y/n) [n]: "
if /i "%show_modno%"=="y" (
    set modno_flag=--show-modno
) else (
    set modno_flag=
)

REM Run the script
echo Running operatory schedule view for %date%...
python operatory_schedule_view.py %date% %operatory_flag% %modno_flag%

pause
