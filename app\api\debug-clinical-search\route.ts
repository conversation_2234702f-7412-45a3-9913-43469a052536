import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const patientId = searchParams.get('patient_id') || '41848';
    
    console.log(`Debug Clinical Search: Testing all methods for patient ${patientId}`);

    const credentials = loadCredentials();
    const sikkaClient = new SikkaApiClient(credentials);
    await sikkaClient.authenticate();

    const results: any = {
      patientId,
      methods: {},
      summary: {}
    };

    // Method 1: Try specific Clinical Note endpoint
    try {
      const requestKey = await sikkaClient.getRequestKey();
      const clinicalUrl = `https://api.sikkasoft.com/v4/medical_notes/Clinical%20Note?patient_id=${patientId}&limit=50`;
      
      const clinicalResponse = await fetch(clinicalUrl, {
        method: 'GET',
        headers: { 'Request-Key': requestKey },
      });

      if (clinicalResponse.ok) {
        const clinicalData = await clinicalResponse.json();
        results.methods.clinicalNoteEndpoint = {
          status: clinicalResponse.status,
          count: Array.isArray(clinicalData) ? clinicalData.length : (clinicalData.items?.length || 0),
          data: clinicalData
        };
      } else {
        results.methods.clinicalNoteEndpoint = {
          status: clinicalResponse.status,
          error: 'Failed to fetch'
        };
      }
    } catch (error) {
      results.methods.clinicalNoteEndpoint = { error: error.message };
    }

    // Method 2: Try PatNote endpoint
    try {
      const requestKey = await sikkaClient.getRequestKey();
      const patNoteUrl = `https://api.sikkasoft.com/v4/medical_notes/PatNote?patient_id=${patientId}&limit=50`;
      
      const patNoteResponse = await fetch(patNoteUrl, {
        method: 'GET',
        headers: { 'Request-Key': requestKey },
      });

      if (patNoteResponse.ok) {
        const patNoteData = await patNoteResponse.json();
        results.methods.patNoteEndpoint = {
          status: patNoteResponse.status,
          count: Array.isArray(patNoteData) ? patNoteData.length : (patNoteData.items?.length || 0),
          data: patNoteData
        };
      } else {
        results.methods.patNoteEndpoint = {
          status: patNoteResponse.status,
          error: 'Failed to fetch'
        };
      }
    } catch (error) {
      results.methods.patNoteEndpoint = { error: error.message };
    }

    // Method 3: Try ProcNote endpoint
    try {
      const requestKey = await sikkaClient.getRequestKey();
      const procNoteUrl = `https://api.sikkasoft.com/v4/medical_notes/ProcNote?patient_id=${patientId}&limit=50`;
      
      const procNoteResponse = await fetch(procNoteUrl, {
        method: 'GET',
        headers: { 'Request-Key': requestKey },
      });

      if (procNoteResponse.ok) {
        const procNoteData = await procNoteResponse.json();
        results.methods.procNoteEndpoint = {
          status: procNoteResponse.status,
          count: Array.isArray(procNoteData) ? procNoteData.length : (procNoteData.items?.length || 0),
          data: procNoteData
        };
      } else {
        results.methods.procNoteEndpoint = {
          status: procNoteResponse.status,
          error: 'Failed to fetch'
        };
      }
    } catch (error) {
      results.methods.procNoteEndpoint = { error: error.message };
    }

    // Method 4: Try general medical_notes with no type filter
    try {
      const requestKey = await sikkaClient.getRequestKey();
      const generalUrl = `https://api.sikkasoft.com/v4/medical_notes?patient_id=${patientId}&limit=100`;
      
      const generalResponse = await fetch(generalUrl, {
        method: 'GET',
        headers: { 'Request-Key': requestKey },
      });

      if (generalResponse.ok) {
        const generalData = await generalResponse.json();
        const notes = Array.isArray(generalData) ? generalData : (generalData.items || []);
        
        // Analyze note types
        const typeAnalysis = {};
        notes.forEach(note => {
          const type = note.type || 'Unknown';
          if (!typeAnalysis[type]) {
            typeAnalysis[type] = { count: 0, samples: [] };
          }
          typeAnalysis[type].count++;
          if (typeAnalysis[type].samples.length < 2) {
            typeAnalysis[type].samples.push({
              date: note.date,
              text: note.text?.substring(0, 200) + '...',
              textLength: note.text?.length || 0
            });
          }
        });

        results.methods.generalMedicalNotes = {
          status: generalResponse.status,
          totalCount: notes.length,
          typeAnalysis,
          clinicalLookingNotes: notes.filter(note => 
            note.text && (
              note.text.includes('{LF}') || 
              note.text.includes('Hygienist:') ||
              note.text.includes('Doctor:') ||
              note.text.includes('Reason for visit:') ||
              note.text.includes('COMPREHENSIVE EXAM') ||
              note.text.includes('PROPHYLAXIS') ||
              note.text.length > 200
            )
          ).map(note => ({
            type: note.type,
            date: note.date,
            textLength: note.text?.length,
            preview: note.text?.substring(0, 300) + '...'
          }))
        };
      } else {
        results.methods.generalMedicalNotes = {
          status: generalResponse.status,
          error: 'Failed to fetch'
        };
      }
    } catch (error) {
      results.methods.generalMedicalNotes = { error: error.message };
    }

    // Method 5: Try chart_notes endpoints
    const chartEndpoints = [
      `https://api.sikkasoft.com/v4/chart_notes?patient_id=${patientId}`,
      `https://api.sikkasoft.com/v4/patients/${patientId}/chart_notes`,
      `https://api.sikkasoft.com/v2/chart_notes?patient_id=${patientId}`,
      `https://api.sikkasoft.com/v2/patients/${patientId}/chart_notes`
    ];

    results.methods.chartNotesEndpoints = {};

    for (const url of chartEndpoints) {
      try {
        const requestKey = await sikkaClient.getRequestKey();
        const response = await fetch(url, {
          method: 'GET',
          headers: { 'Request-Key': requestKey },
        });

        const key = url.split('/').slice(-2).join('/');
        if (response.ok) {
          const data = await response.json();
          results.methods.chartNotesEndpoints[key] = {
            status: response.status,
            count: Array.isArray(data) ? data.length : (data.items?.length || data.data?.length || 0),
            hasData: !!(Array.isArray(data) ? data.length : (data.items?.length || data.data?.length))
          };
        } else {
          results.methods.chartNotesEndpoints[key] = {
            status: response.status,
            error: 'Failed to fetch'
          };
        }
      } catch (error) {
        const key = url.split('/').slice(-2).join('/');
        results.methods.chartNotesEndpoints[key] = { error: error.message };
      }
    }

    return NextResponse.json(results);

  } catch (error) {
    console.error('Debug Clinical Search error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to search clinical notes' },
      { status: 500 }
    );
  }
}
