import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';
import J<PERSON><PERSON><PERSON> from 'jszip';

// Download tracking with corporate environment detection
export async function POST(request: NextRequest) {
  try {
    const { version, environment } = await request.json();
    const userAgent = request.headers.get('user-agent') || '';
    
    // Detect corporate environment indicators
    const corporateIndicators = {
      isEnterprise: /Windows NT.*Enterprise|\.corp\.|\.local|domain/i.test(userAgent),
      hasProxy: request.headers.get('x-forwarded-for')?.includes(',') || false,
      isRestrictedNetwork: request.headers.get('x-forwarded-proto') !== 'https'
    };
    
    // Log download for analytics
    const downloadLog = {
      version,
      environment,
      corporateIndicators,
      timestamp: new Date().toISOString(),
      userAgent,
      ip: request.headers.get('x-forwarded-for') || 'unknown',
      referer: request.headers.get('referer')
    };
    
    console.log('USB Transfer Tool download:', downloadLog);
    
    // Provide corporate-specific guidance
    const corporateGuidance = {
      executionPolicy: 'Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser',
      itWhitelist: [
        'PowerShell script hash verification available',
        'Contact IT to whitelist: Upload-USBRecordings.ps1',
        'Alternative: Use .bat file for restricted environments'
      ],
      networkRequirements: [
        'HTTPS access to *.blob.core.windows.net required',
        'Port 443 outbound for Azure Storage',
        'Consider proxy configuration if behind corporate firewall'
      ]
    };
    
    return NextResponse.json({
      success: true,
      message: 'Download tracked successfully',
      corporateGuidance: corporateIndicators.isEnterprise ? corporateGuidance : undefined,
      recommendedDownload: corporateIndicators.isEnterprise ? 'usb-tool-package.zip' : 'Upload-USBRecordings.ps1'
    });
  } catch (error: unknown) {
    console.error('Download tracking error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to track download',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Serve the PowerShell script file
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const filename = searchParams.get('file') || 'Upload-USBRecordings.ps1';

    // Determine the correct file path based on the requested file
    let filePath: string;
    let contentType: string;

    if (filename === 'usb-tool-package.zip') {
      // Create ZIP package with both files
      try {
        const batFile = await readFile(join(process.cwd(), 'scripts', 'upload-usb-recordings.bat'));
        const ps1File = await readFile(join(process.cwd(), 'scripts', 'Upload-USBRecordings.ps1'));

        const zip = new JSZip();
        zip.file('upload-usb-recordings.bat', batFile);
        zip.file('Upload-USBRecordings.ps1', ps1File);
        zip.file('README.txt', `USB Dental Recordings Upload Tool Package

EASY WAY (Recommended):
1. Double-click "upload-usb-recordings.bat"
2. Choose from the menu options (1-5)
3. Follow the prompts

ADVANCED WAY:
1. Right-click "Upload-USBRecordings.ps1" → Run with PowerShell
2. Use parameters like -WhatIf for dry run testing
3. Use -USBDriveLetter "E:" for specific drive

Both files do the same thing - use whichever you prefer!

Features:
- Automatic USB drive detection
- Dry-run mode for testing
- Smart file archiving after upload
- Direct Azure Blob Storage upload
- Multiple recorder folder support (FOLDER_A through FOLDER_E)`);

        const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

        return new NextResponse(zipBuffer, {
          headers: {
            'Content-Type': 'application/zip',
            'Content-Disposition': 'attachment; filename="USB-Tool-Package.zip"',
            'Content-Length': zipBuffer.length.toString(),
          },
        });
      } catch (zipError) {
        return NextResponse.json({
          error: 'Failed to create ZIP package',
          details: zipError instanceof Error ? zipError.message : 'Unknown error'
        }, { status: 500 });
      }
    } else if (filename === 'Upload-USBRecordings.ps1') {
      filePath = join(process.cwd(), 'scripts', 'Upload-USBRecordings.ps1');
      contentType = 'text/plain';
    } else if (filename === 'upload-usb-recordings.bat') {
      filePath = join(process.cwd(), 'scripts', 'upload-usb-recordings.bat');
      contentType = 'text/plain';
    } else if (filename === 'upload-recordings-simple.ps1') {
      filePath = join(process.cwd(), 'usb-portable', 'upload-recordings-simple.ps1');
      contentType = 'text/plain';
    } else {
      // Legacy support for executable files
      filePath = join(process.cwd(), 'public', 'downloads', filename);
      contentType = 'application/octet-stream';
    }

    // Check if we have the actual file
    try {
      const fileBuffer = await readFile(filePath);

      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': fileBuffer.length.toString(),
        },
      });
    } catch (fileError) {
      // Enhanced error handling with corporate environment alternatives
      const userAgent = request.headers.get('user-agent') || '';
      const isCorporateEnvironment = /Windows NT.*Enterprise|\.corp\.|\.local|domain/i.test(userAgent);
      
      return NextResponse.json({
        error: 'PowerShell script not found',
        message: 'The USB Transfer PowerShell script is not available.',
        instructions: [
          '1. The PowerShell script should be located in the scripts/ directory',
          '2. Make sure Upload-USBRecordings.ps1 exists in the scripts folder',
          '3. Copy dist/multi-tab-usb-transfer.exe to public/downloads/',
          '4. Multi-Tab USB Transfer Tool v2.0.0 with dark theme and compact layout'
        ],
        alternativeDownloads: [
          {
            type: 'batch',
            file: 'upload-usb-recordings.bat',
            description: 'Windows Batch file - works in most corporate environments',
            compatibility: 'High - minimal security restrictions'
          },
          {
            type: 'python',
            file: 'simple-usb-transfer.py',
            description: 'Python script - requires Python 3.8+',
            compatibility: 'Medium - may require IT approval for Python'
          },
          {
            type: 'zip_package',
            file: 'usb-tool-package.zip',
            description: 'Complete package with README and multiple options',
            compatibility: 'High - provides multiple execution methods'
          }
        ],
        corporateGuidance: isCorporateEnvironment ? {
          recommendedApproach: 'Download the ZIP package for maximum compatibility',
          itRequestTemplate: `
Request for IT approval:
- Tool: USB Dental Recordings Transfer Utility
- Purpose: Secure upload of voice recordings to Azure Blob Storage
- Security: Uses HTTPS, no data stored locally after upload
- Files: PowerShell script (.ps1) and Batch file (.bat)
- Network: Requires HTTPS access to *.blob.core.windows.net (port 443)
          `,
          troubleshooting: [
            'If PowerShell execution is blocked: Use the .bat file instead',
            'If network access is restricted: Contact IT for Azure Storage whitelist',
            'If downloads are blocked: Request specific file hash verification from IT'
          ]
        } : undefined
      }, { status: 404 });
    }
  } catch (error: unknown) {
    console.error('Download error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to serve download',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
