import { NextRequest, NextResponse } from 'next/server';
import { sql } from '@vercel/postgres';
import { VercelDB } from '@/lib/vercel-db';
import { featureFlags } from '@/lib/feature-flags';

interface TranscriptionFile {
  transcriptionId: string;
  filename: string;
  transcriptionText: string;
  createdAt: string;
  confidence: number;
  deviceId: string;
  patientId?: string;
}

interface AppointmentMatch {
  appointmentId: string;
  patientName: string;
  provider: string;
  operatory: string;
  startTime: string;
  endTime: string;
  appointmentType: string;
  description: string;
  confidence: number;
  matchReason: string;
}

interface SmartSortSuggestion {
  transcription: TranscriptionFile;
  suggestedMatches: AppointmentMatch[];
  autoMatchConfidence: number;
  recommendedMatch?: AppointmentMatch;
}

interface SmartSortResult {
  date: string;
  totalTranscriptions: number;
  sortableTranscriptions: number;
  suggestions: SmartSortSuggestion[];
  autoMatches: number;
  manualReviewNeeded: number;
  availableAppointments: AppointmentMatch[];
}

/**
 * SMART SORT ENDPOINT - Transcription-Only Architecture
 * Intelligently matches transcriptions to appointments using AI analysis
 * Uses Vercel Postgres for transcription data and content-based matching
 */
export async function POST(request: NextRequest) {
  try {
    // Check if new architecture is enabled
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { transcriptionIds, date, matchingOptions = {} } = await request.json();

    if (!transcriptionIds && !date) {
      return NextResponse.json({
        error: 'Either transcription IDs or date is required',
        code: 'MISSING_PARAMETERS'
      }, { status: 400 });
    }

    console.log(`🧠 Smart sorting transcriptions for date: ${date || 'specific IDs'}`);

    let transcriptions: TranscriptionFile[] = [];

    if (transcriptionIds && Array.isArray(transcriptionIds)) {
      // Get specific transcriptions
      const placeholders = transcriptionIds.map((_, index) => `$${index + 1}`).join(',');
      const result = await sql.query(`
        SELECT 
          id as "transcriptionId",
          filename,
          transcription_text as "transcriptionText",
          created_at as "createdAt",
          confidence_score as "confidence",
          device_id as "deviceId",
          patient_id as "patientId"
        FROM transcriptions 
        WHERE id IN (${placeholders})
        AND transcription_text IS NOT NULL 
        AND transcription_text != ''
        ORDER BY created_at DESC
      `, transcriptionIds);

      transcriptions = result.rows;
    } else if (date) {
      // Get transcriptions for specific date
      const result = await sql`
        SELECT 
          id as "transcriptionId",
          filename,
          transcription_text as "transcriptionText", 
          created_at as "createdAt",
          confidence_score as "confidence",
          device_id as "deviceId",
          patient_id as "patientId"
        FROM transcriptions 
        WHERE DATE(created_at) = ${date}
        AND transcription_text IS NOT NULL 
        AND transcription_text != ''
        ORDER BY created_at DESC
        LIMIT 50
      `;

      transcriptions = result.rows;
    }

    if (transcriptions.length === 0) {
      return NextResponse.json({
        date: date || 'specific',
        totalTranscriptions: 0,
        sortableTranscriptions: 0,
        suggestions: [],
        autoMatches: 0,
        manualReviewNeeded: 0,
        availableAppointments: [],
        message: 'No transcriptions found for sorting'
      });
    }

    // Get appointments for the date (if provided) or extract dates from transcriptions
    let availableAppointments: AppointmentMatch[] = [];
    
    if (date) {
      availableAppointments = await getAppointmentsForDate(date);
    } else {
      // Extract unique dates from transcriptions and get appointments
      const uniqueDates = [...new Set(transcriptions.map(t => 
        t.createdAt.split('T')[0]
      ))];
      
      for (const dateStr of uniqueDates) {
        const dayAppointments = await getAppointmentsForDate(dateStr);
        availableAppointments.push(...dayAppointments);
      }
    }

    console.log(`📅 Found ${availableAppointments.length} appointments to match against`);

    const suggestions: SmartSortSuggestion[] = [];
    let autoMatches = 0;
    let manualReviewNeeded = 0;

    // Process each transcription for smart matching
    for (const transcription of transcriptions) {
      const suggestion = await generateSmartSortSuggestion(
        transcription, 
        availableAppointments,
        matchingOptions
      );
      
      suggestions.push(suggestion);

      // Determine if auto-match is recommended
      const autoMatchThreshold = matchingOptions.autoMatchThreshold || 0.8;
      if (suggestion.autoMatchConfidence >= autoMatchThreshold && suggestion.recommendedMatch) {
        autoMatches++;
      } else {
        manualReviewNeeded++;
      }
    }

    const result: SmartSortResult = {
      date: date || 'multiple',
      totalTranscriptions: transcriptions.length,
      sortableTranscriptions: suggestions.length,
      suggestions,
      autoMatches,
      manualReviewNeeded,
      availableAppointments
    };

    console.log(`✅ Smart sort complete: ${autoMatches} auto-matches, ${manualReviewNeeded} need review`);

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Smart sort error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Smart sort failed',
      code: 'SMART_SORT_ERROR'
    }, { status: 500 });
  }
}

/**
 * GET endpoint to retrieve appointments for a specific date
 */
export async function GET(request: NextRequest) {
  try {
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!date) {
      return NextResponse.json({
        error: 'Date parameter is required',
        code: 'MISSING_DATE'
      }, { status: 400 });
    }

    const appointments = await getAppointmentsForDate(date);

    return NextResponse.json({
      success: true,
      date,
      appointments,
      count: appointments.length
    });

  } catch (error) {
    console.error('❌ Error getting appointments for smart sort:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to get appointments',
      code: 'APPOINTMENTS_ERROR'
    }, { status: 500 });
  }
}

async function getAppointmentsForDate(date: string): Promise<AppointmentMatch[]> {
  try {
    // Check if appointments table exists and has data
    const result = await sql`
      SELECT 
        id as "appointmentId",
        patient_name as "patientName",
        provider,
        operatory,
        start_time as "startTime", 
        end_time as "endTime",
        appointment_type as "appointmentType",
        description,
        0 as confidence,
        '' as "matchReason"
      FROM appointments 
      WHERE DATE(start_time) = ${date}
      ORDER BY start_time ASC
    `;

    return result.rows;
  } catch (error) {
    console.warn(`No appointments found for ${date} - appointments table may not exist:`, error);
    return [];
  }
}

async function generateSmartSortSuggestion(
  transcription: TranscriptionFile,
  appointments: AppointmentMatch[],
  options: any = {}
): Promise<SmartSortSuggestion> {
  
  const suggestedMatches: AppointmentMatch[] = [];
  
  console.log(`🔍 Analyzing transcription ${transcription.filename} (${transcription.transcriptionText.length} chars)`);

  if (appointments.length === 0) {
    return {
      transcription,
      suggestedMatches: [],
      autoMatchConfidence: 0,
      recommendedMatch: undefined
    };
  }

  // Extract time from filename if possible
  const timeFromFilename = extractTimeFromFilename(transcription.filename);
  const transcriptionDate = transcription.createdAt.split('T')[0];

  // Score each appointment based on various factors
  for (const appointment of appointments) {
    let confidence = 0;
    const matchReasons: string[] = [];

    // 1. Content-based matching (primary signal)
    const contentScore = calculateContentMatchScore(
      transcription.transcriptionText, 
      appointment
    );
    confidence += contentScore * 0.5; // 50% weight
    
    if (contentScore > 0.7) {
      matchReasons.push('Strong content match with appointment details');
    } else if (contentScore > 0.4) {
      matchReasons.push('Moderate content correlation');
    }

    // 2. Time-based matching
    if (timeFromFilename) {
      const timeScore = calculateTimeMatchScore(
        timeFromFilename, 
        appointment.startTime, 
        appointment.endTime
      );
      confidence += timeScore * 0.3; // 30% weight
      
      if (timeScore > 0.8) {
        matchReasons.push(`Recording time (${timeFromFilename}) matches appointment time`);
      } else if (timeScore > 0.5) {
        matchReasons.push(`Recording time near appointment time`);
      }
    }

    // 3. Patient name matching (if available)
    if (transcription.patientId) {
      // If transcription already has patient ID, perfect match
      if (transcription.patientId === appointment.appointmentId) {
        confidence += 0.15; // 15% weight
        matchReasons.push('Patient ID matches appointment');
      }
    } else {
      // Content-based patient name extraction
      const nameScore = calculatePatientNameMatch(
        transcription.transcriptionText,
        appointment.patientName
      );
      confidence += nameScore * 0.15; // 15% weight
      
      if (nameScore > 0.7) {
        matchReasons.push(`Patient name "${appointment.patientName}" mentioned in transcription`);
      }
    }

    // 4. Provider/operatory matching
    const locationScore = calculateLocationMatch(
      transcription.transcriptionText,
      appointment.provider,
      appointment.operatory
    );
    confidence += locationScore * 0.05; // 5% weight
    
    if (locationScore > 0.5) {
      matchReasons.push('Provider or operatory mentioned in transcription');
    }

    // Only include matches with reasonable confidence
    if (confidence > 0.2) {
      suggestedMatches.push({
        ...appointment,
        confidence: Math.min(confidence, 1.0),
        matchReason: matchReasons.join('; ')
      });
    }
  }

  // Sort by confidence (highest first)
  suggestedMatches.sort((a, b) => b.confidence - a.confidence);

  // Determine auto-match confidence and recommended match
  let autoMatchConfidence = 0;
  let recommendedMatch: AppointmentMatch | undefined;

  if (suggestedMatches.length > 0) {
    const topMatch = suggestedMatches[0];
    autoMatchConfidence = topMatch.confidence;
    
    console.log(`🎯 Match analysis for ${transcription.filename}:`);
    console.log(`   Top match confidence: ${autoMatchConfidence.toFixed(3)} - ${topMatch.matchReason}`);
    console.log(`   Appointment: ${topMatch.patientName} at ${topMatch.startTime} with ${topMatch.provider}`);
    
    // Recommend auto-match if confidence is high and significantly better than second option
    const confidenceThreshold = options.autoMatchThreshold || 0.8;
    if (autoMatchConfidence >= confidenceThreshold) {
      const secondBestConfidence = suggestedMatches.length > 1 ? suggestedMatches[1].confidence : 0;
      const confidenceGap = options.confidenceGap || 0.15;
      
      if (autoMatchConfidence - secondBestConfidence >= confidenceGap) {
        recommendedMatch = topMatch;
        console.log(`✅ Auto-match recommended: confidence ${autoMatchConfidence.toFixed(3)}`);
      } else {
        console.log(`⚠️ Manual review needed: confidence gap too small`);
      }
    } else {
      console.log(`❌ Manual review needed: confidence ${autoMatchConfidence.toFixed(3)} below threshold`);
    }
  }

  return {
    transcription,
    suggestedMatches: suggestedMatches.slice(0, 5), // Top 5 suggestions
    autoMatchConfidence,
    recommendedMatch
  };
}

function extractTimeFromFilename(filename: string): string | null {
  // Common voice recorder filename patterns
  const patterns = [
    /(\d{2})(\d{2})(\d{2})_(\d{2})(\d{2})/,  // YYMMDD_HHMM
    /(\d{6})_(\d{4})/,                        // YYMMDD_HHMM
    /_(\d{2})(\d{2})\.mp3$/,                  // _HHMM.mp3
    /_(\d{4})\.mp3$/                          // _HHMM.mp3
  ];

  for (const pattern of patterns) {
    const match = filename.match(pattern);
    if (match) {
      // Extract time components based on pattern
      if (pattern.source.includes('_')) {
        const timeStr = match[match.length - 1] || match[match.length - 2];
        if (timeStr && timeStr.length >= 4) {
          const hours = timeStr.substring(0, 2);
          const minutes = timeStr.substring(2, 4);
          return `${hours}:${minutes}`;
        }
      }
    }
  }

  return null;
}

function calculateContentMatchScore(transcriptionText: string, appointment: AppointmentMatch): number {
  const content = transcriptionText.toLowerCase();
  const patientName = appointment.patientName.toLowerCase();
  const provider = appointment.provider.toLowerCase();
  const type = appointment.appointmentType.toLowerCase();
  const description = appointment.description.toLowerCase();
  
  let score = 0;
  let matches = 0;
  
  // Patient name in content
  if (patientName && content.includes(patientName)) {
    score += 0.4;
    matches++;
  }
  
  // Provider name in content
  if (provider && content.includes(provider)) {
    score += 0.2;
    matches++;
  }
  
  // Appointment type keywords
  const typeKeywords = type.split(' ');
  for (const keyword of typeKeywords) {
    if (keyword.length > 3 && content.includes(keyword)) {
      score += 0.1;
      matches++;
    }
  }
  
  // Description keywords
  const descKeywords = description.split(' ');
  for (const keyword of descKeywords) {
    if (keyword.length > 4 && content.includes(keyword)) {
      score += 0.05;
      matches++;
    }
  }
  
  // Dental procedure keywords matching
  const dentalKeywords = [
    'cleaning', 'examination', 'exam', 'checkup', 'cavity', 'filling',
    'crown', 'root canal', 'extraction', 'x-ray', 'pain', 'tooth'
  ];
  
  for (const keyword of dentalKeywords) {
    if (content.includes(keyword) && (type.includes(keyword) || description.includes(keyword))) {
      score += 0.1;
      matches++;
    }
  }
  
  return Math.min(score, 1.0);
}

function calculateTimeMatchScore(recordingTime: string, appointmentStart: string, appointmentEnd: string): number {
  try {
    const recordingMinutes = timeToMinutes(recordingTime);
    const startMinutes = timeToMinutes(appointmentStart);
    const endMinutes = timeToMinutes(appointmentEnd);

    // Perfect match if recording time is within appointment window
    if (recordingMinutes >= startMinutes && recordingMinutes <= endMinutes) {
      return 1.0;
    }

    // Calculate distance from appointment window
    const distanceFromStart = Math.abs(recordingMinutes - startMinutes);
    const distanceFromEnd = Math.abs(recordingMinutes - endMinutes);
    const minDistance = Math.min(distanceFromStart, distanceFromEnd);

    // Score decreases with distance (30 minutes = 0 score)
    return Math.max(0, 1 - (minDistance / 30));

  } catch (error) {
    return 0;
  }
}

function calculatePatientNameMatch(transcriptionText: string, patientName: string): number {
  const content = transcriptionText.toLowerCase();
  const name = patientName.toLowerCase();
  
  // Split name into parts
  const nameParts = name.split(' ').filter(part => part.length > 1);
  let matchCount = 0;
  
  for (const part of nameParts) {
    if (content.includes(part)) {
      matchCount++;
    }
  }
  
  return nameParts.length > 0 ? matchCount / nameParts.length : 0;
}

function calculateLocationMatch(transcriptionText: string, provider: string, operatory: string): number {
  const content = transcriptionText.toLowerCase();
  let score = 0;
  
  if (provider && content.includes(provider.toLowerCase())) {
    score += 0.6;
  }
  
  if (operatory && content.includes(operatory.toLowerCase())) {
    score += 0.4;
  }
  
  return Math.min(score, 1.0);
}

function timeToMinutes(timeStr: string): number {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}