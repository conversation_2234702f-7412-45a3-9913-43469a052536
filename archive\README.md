# Dental Schedule Viewer

A web-based application for viewing dental appointments by operatory. This application connects to the Sikka API to fetch real appointment data and displays it in a clean, modern interface.

## Core Files (Most Important)

### Main Application Files
- **dental_schedule_app.py** - The main Flask application that handles API requests and serves the web interface
- **sikka_api_client.py** - Client for connecting to the Sikka API with authentication and request handling
- **templates/index.html** - The HTML template with the schedule display and JavaScript for rendering appointments

### Configuration
- **credentials.json** - Contains API credentials for connecting to Sikka API (not included in repository)

## Other Useful Files

- **check_credentials.py** - Utility to verify Sikka API credentials
- **check_operatory_api.py** - Tests the operatory API endpoint
- **test_appointment_fields.py** - Tests appointment data fields
- **test_date_filters.py** - Tests date filtering functionality
- **sikka_api_tester.py** - Interactive command-line tool for testing Sikka API endpoints
- **test_endpoint.py** - Simple script for testing a specific API endpoint

## Unnecessary Files

The following files are not needed for the main application and can be ignored:

- All Flutter-related files (dental_app/, dental_viewer/, new_dental_app/, simple_dental_app/)
- All .bat files (run_*.bat)
- Older versions of the application (simple_dental_app.py, dental_viewer.py, etc.)
- Test data files (appointments.json, appointments_*.txt)
- Postman collection files (*.postman_collection.json)

## Features

- Date selection with a calendar
- Dynamic operatory selection based on the selected date
- Visual schedule display with color-coded appointments
- Detailed appointment information on hover
- Real-time API integration with Sikka API
- Compact view with all content on a single page

## Requirements

- Python 3.6+
- Flask
- Requests

Install requirements:

```
pip install flask requests
```

## Installation

1. Clone this repository
2. Create a `credentials.json` file with your Sikka API credentials:
   ```json
   {
       "app_id": "your_app_id",
       "app_key": "your_app_key",
       "office_id": "your_office_id",
       "secret_key": "your_secret_key"
   }
   ```

## Usage

1. Run the application:
   ```
   python dental_schedule_app.py
   ```
2. Open a web browser and navigate to http://127.0.0.1:8080
3. Select a date and operatories to view appointments

## Application Workflow

1. The application starts a Flask web server
2. When you select a date, it fetches available operatories for that date
3. When you select operatories, it fetches appointments for those operatories
4. The schedule is displayed with appointments color-coded by type
5. Hover over an appointment to see detailed information

## API Integration

This application integrates with the Sikka API to fetch real appointment data. It uses the following endpoints:

- `/v4/request_key` - For authentication
- `/v2/appointments` - For fetching appointments
- `/v2/operatories` - For fetching available operatories

## Testing Tools

For testing the API directly, you can use the included testing tools:

```
python sikka_api_tester.py
```

Or test specific endpoints:

```
python test_endpoint.py practice_schedule --header "app-id:YOUR_APP_ID" --header "app-key:YOUR_APP_KEY" --param "practice_id:YOUR_OFFICE_ID" --param "date:2023-05-16" --param "resource_type:operatory"
```

## Troubleshooting

- If you get authentication errors, verify your credentials in credentials.json
- Make sure your Sikka API account has access to the required endpoints
- Check the Flask server logs for detailed error messages
- For appointment display issues, check the browser console for JavaScript errors

## Development

The main components of the application are:

1. **Backend (dental_schedule_app.py)**
   - Flask web server
   - API client for Sikka integration
   - Endpoint handlers for the web interface

2. **Frontend (templates/index.html)**
   - HTML structure for the web interface
   - JavaScript for dynamic content loading
   - CSS for styling the schedule display

3. **API Client (sikka_api_client.py)**
   - Authentication with Sikka API
   - Request handling and error management
   - Data formatting and processing
