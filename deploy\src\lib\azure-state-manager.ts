import { AzureStorageService } from './azure-storage';

export type ProcessingStatus = 'pending' | 'transcribing' | 'summarizing' | 'complete' | 'failed';

export interface ProcessingState {
  audioFile: string;
  status: ProcessingStatus;
  transcription: {
    status: 'pending' | 'processing' | 'complete' | 'failed';
    startedAt?: string;
    completedAt?: string;
    service: 'azure-speech-to-text' | 'openai-whisper';
    result?: string;
    error?: string;
  };
  summarization: {
    status: 'pending' | 'processing' | 'complete' | 'failed';
    startedAt?: string;
    completedAt?: string;
    service: 'azure-openai' | 'openai-gpt';
    result?: string;
    error?: string;
  };
  metadata: {
    fileSize: number;
    duration?: number;
    format: string;
    processingAttempts: number;
    lastAttempt: string;
    createdAt: string;
    filePath: string;
  };
}

export class AzureStateManager {
  /**
   * Creates a state file path for an audio file
   */
  private static getStateFilePath(audioFilePath: string): string {
    const baseName = audioFilePath.replace(/\.[^/.]+$/, '');
    return `${baseName}.state.json`;
  }

  /**
   * Initialize state for a new audio file
   */
  static async initializeState(audioFilePath: string, fileSize: number, format: string): Promise<ProcessingState> {
    const initialState: ProcessingState = {
      audioFile: audioFilePath.split('/').pop() || audioFilePath,
      status: 'pending',
      transcription: {
        status: 'pending',
        service: 'azure-speech-to-text'
      },
      summarization: {
        status: 'pending',
        service: 'azure-openai'
      },
      metadata: {
        fileSize,
        format,
        processingAttempts: 0,
        lastAttempt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        filePath: audioFilePath
      }
    };

    await this.saveState(audioFilePath, initialState);
    return initialState;
  }

  /**
   * Load processing state from blob storage
   */
  static async loadState(audioFilePath: string): Promise<ProcessingState | null> {
    try {
      const stateFilePath = this.getStateFilePath(audioFilePath);
      const state = await AzureStorageService.downloadJson(stateFilePath);
      
      if (!state || !state.audioFile) {
        return null;
      }

      return state as ProcessingState;
    } catch (error) {
      console.log(`No state file found for ${audioFilePath}`);
      return null;
    }
  }

  /**
   * Save processing state to blob storage
   */
  static async saveState(audioFilePath: string, state: ProcessingState): Promise<void> {
    const stateFilePath = this.getStateFilePath(audioFilePath);
    await AzureStorageService.uploadJson(stateFilePath, state);
  }

  /**
   * Update transcription status
   */
  static async updateTranscriptionStatus(
    audioFilePath: string,
    status: ProcessingState['transcription']['status'],
    result?: string,
    error?: string
  ): Promise<void> {
    const state = await this.loadState(audioFilePath);
    if (!state) {
      throw new Error(`No state found for ${audioFilePath}`);
    }

    state.transcription.status = status;
    
    if (status === 'processing') {
      state.transcription.startedAt = new Date().toISOString();
      state.status = 'transcribing';
    } else if (status === 'complete') {
      state.transcription.completedAt = new Date().toISOString();
      state.transcription.result = result;
      state.status = 'summarizing'; // Move to next stage
    } else if (status === 'failed') {
      state.transcription.error = error;
      state.status = 'failed';
    }

    if (result) {
      state.transcription.result = result;
    }

    if (error) {
      state.transcription.error = error;
    }

    await this.saveState(audioFilePath, state);
  }

  /**
   * Update summarization status
   */
  static async updateSummarizationStatus(
    audioFilePath: string,
    status: ProcessingState['summarization']['status'],
    result?: string,
    error?: string
  ): Promise<void> {
    const state = await this.loadState(audioFilePath);
    if (!state) {
      throw new Error(`No state found for ${audioFilePath}`);
    }

    state.summarization.status = status;
    
    if (status === 'processing') {
      state.summarization.startedAt = new Date().toISOString();
    } else if (status === 'complete') {
      state.summarization.completedAt = new Date().toISOString();
      state.summarization.result = result;
      state.status = 'complete'; // Final completion
    } else if (status === 'failed') {
      state.summarization.error = error;
      state.status = 'failed';
    }

    if (result) {
      state.summarization.result = result;
    }

    if (error) {
      state.summarization.error = error;
    }

    await this.saveState(audioFilePath, state);
  }

  /**
   * Mark processing attempt
   */
  static async markProcessingAttempt(audioFilePath: string): Promise<void> {
    const state = await this.loadState(audioFilePath);
    if (!state) {
      throw new Error(`No state found for ${audioFilePath}`);
    }

    state.metadata.processingAttempts++;
    state.metadata.lastAttempt = new Date().toISOString();

    await this.saveState(audioFilePath, state);
  }

  /**
   * Get processing statistics for all files
   */
  static async getProcessingStats(): Promise<{
    total: number;
    pending: number;
    transcribing: number;
    summarizing: number;
    complete: number;
    failed: number;
  }> {
    const stats = {
      total: 0,
      pending: 0,
      transcribing: 0,
      summarizing: 0,
      complete: 0,
      failed: 0
    };

    try {
      // Get all audio files
      const audioFiles = await AzureStorageService.listFiles('');
      
      for (const file of audioFiles) {
        // Skip non-audio files
        if (!file.name.match(/\.(mp3|wav|m4a|webm|ogg|flac|aac)$/i)) {
          continue;
        }

        stats.total++;
        
        const state = await this.loadState(file.path);
        if (!state) {
          stats.pending++;
        } else {
          stats[state.status]++;
        }
      }
    } catch (error) {
      console.error('Error getting processing stats:', error);
    }

    return stats;
  }

  /**
   * Get files by status
   */
  static async getFilesByStatus(status: ProcessingStatus): Promise<string[]> {
    const files: string[] = [];

    try {
      const audioFiles = await AzureStorageService.listFiles('');
      
      for (const file of audioFiles) {
        // Skip non-audio files
        if (!file.name.match(/\.(mp3|wav|m4a|webm|ogg|flac|aac)$/i)) {
          continue;
        }

        const state = await this.loadState(file.path);
        if (!state && status === 'pending') {
          files.push(file.path);
        } else if (state && state.status === status) {
          files.push(file.path);
        }
      }
    } catch (error) {
      console.error(`Error getting files by status ${status}:`, error);
    }

    return files;
  }

  /**
   * Reset failed processing attempts
   */
  static async resetFailedFiles(): Promise<number> {
    let resetCount = 0;

    try {
      const failedFiles = await this.getFilesByStatus('failed');
      
      for (const filePath of failedFiles) {
        const state = await this.loadState(filePath);
        if (state) {
          state.status = 'pending';
          state.transcription.status = 'pending';
          state.summarization.status = 'pending';
          state.transcription.error = undefined;
          state.summarization.error = undefined;
          
          await this.saveState(filePath, state);
          resetCount++;
        }
      }
    } catch (error) {
      console.error('Error resetting failed files:', error);
    }

    return resetCount;
  }

  /**
   * Clean up old state files for non-existent audio files
   */
  static async cleanupOrphanedStates(): Promise<number> {
    let cleanedCount = 0;

    try {
      const allFiles = await AzureStorageService.listFiles('');
      const stateFiles = allFiles.filter(f => f.name.endsWith('.state.json'));
      
      for (const stateFile of stateFiles) {
        const audioFilePath = stateFile.path.replace('.state.json', '.mp3'); // Assume mp3 for now
        const audioExists = await AzureStorageService.fileExists(audioFilePath);
        
        if (!audioExists) {
          await AzureStorageService.deleteFile(stateFile.path);
          cleanedCount++;
        }
      }
    } catch (error) {
      console.error('Error cleaning up orphaned states:', error);
    }

    return cleanedCount;
  }
}