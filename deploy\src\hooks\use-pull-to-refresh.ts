'use client';

import { useEffect, useRef, useState } from 'react';

interface UsePullToRefreshOptions {
  onRefresh: () => Promise<void> | void;
  threshold?: number;
  resistance?: number;
  enabled?: boolean;
}

export function usePullToRefresh({
  onRefresh,
  threshold = 80,
  resistance = 2.5,
  enabled = true
}: UsePullToRefreshOptions) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isPulling, setIsPulling] = useState(false);
  
  const startY = useRef(0);
  const currentY = useRef(0);
  const isDragging = useRef(false);
  const containerRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (!enabled) return;

    const container = containerRef.current || document.body;
    let rafId: number;

    const handleTouchStart = (e: TouchEvent) => {
      // Only trigger if we're at the top of the page and not already refreshing
      if (window.scrollY > 0 || isRefreshing) return;
      
      startY.current = e.touches[0].clientY;
      isDragging.current = true;
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isDragging.current || isRefreshing) return;

      currentY.current = e.touches[0].clientY;
      const deltaY = currentY.current - startY.current;

      // Only allow pulling down
      if (deltaY > 0 && window.scrollY === 0) {
        // Prevent default scrolling when pulling down
        e.preventDefault();
        
        // Apply resistance to make it feel natural
        const distance = Math.min(deltaY / resistance, threshold * 1.5);
        setPullDistance(distance);
        setIsPulling(distance > 10);
      }
    };

    const handleTouchEnd = async () => {
      if (!isDragging.current) return;

      isDragging.current = false;
      
      if (pullDistance >= threshold && !isRefreshing) {
        setIsRefreshing(true);
        try {
          await onRefresh();
        } catch (error) {
          console.error('Refresh failed:', error);
        } finally {
          setIsRefreshing(false);
        }
      }
      
      // Reset pull state
      setPullDistance(0);
      setIsPulling(false);
    };

    // Add event listeners
    container.addEventListener('touchstart', handleTouchStart, { passive: false });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd);
    container.addEventListener('touchcancel', handleTouchEnd);

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
      container.removeEventListener('touchcancel', handleTouchEnd);
      
      if (rafId) {
        cancelAnimationFrame(rafId);
      }
    };
  }, [enabled, threshold, resistance, onRefresh, isRefreshing, pullDistance]);

  return {
    isRefreshing,
    pullDistance,
    isPulling,
    containerRef,
    // Helper to determine if we should show the refresh indicator
    shouldShowIndicator: isPulling || isRefreshing,
    // Progress as a percentage (0-100)
    progress: Math.min((pullDistance / threshold) * 100, 100)
  };
}
