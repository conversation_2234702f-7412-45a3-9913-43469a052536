/**
 * Transcription Error Handler
 * Handles specific errors related to OpenAI transcription API calls
 */

export interface TranscriptionError {
  type: 'network' | 'api' | 'quota' | 'timeout' | 'unknown';
  message: string;
  retryable: boolean;
  retryDelay?: number;
  originalError?: any;
}

export class TranscriptionErrorHandler {
  /**
   * Analyze and classify transcription errors
   */
  static analyzeError(error: any): TranscriptionError {
    const errorMessage = error?.message || error?.toString() || 'Unknown error';
    
    // Network connectivity issues
    if (errorMessage.includes('ECONNRESET') || 
        errorMessage.includes('ETIMEDOUT') || 
        errorMessage.includes('ENOTFOUND') ||
        errorMessage.includes('ECONNREFUSED')) {
      return {
        type: 'network',
        message: 'Network connectivity issue. Please check your internet connection and try again.',
        retryable: true,
        retryDelay: 10000, // 10 seconds
        originalError: error
      };
    }
    
    // OpenAI API rate limiting
    if (errorMessage.includes('rate limit') || 
        errorMessage.includes('quota') ||
        errorMessage.includes('429')) {
      return {
        type: 'quota',
        message: 'OpenAI API rate limit exceeded. Please wait a few minutes and try again.',
        retryable: true,
        retryDelay: 60000, // 1 minute
        originalError: error
      };
    }
    
    // Timeout errors
    if (errorMessage.includes('timeout') || 
        errorMessage.includes('TIMEDOUT')) {
      return {
        type: 'timeout',
        message: 'Request timed out. The audio file may be too large or the service is slow.',
        retryable: true,
        retryDelay: 15000, // 15 seconds
        originalError: error
      };
    }
    
    // OpenAI API errors
    if (errorMessage.includes('OpenAI') || 
        errorMessage.includes('invalid_request_error') ||
        errorMessage.includes('authentication_error')) {
      return {
        type: 'api',
        message: 'OpenAI API error. Please check your API key and try again.',
        retryable: false,
        originalError: error
      };
    }
    
    // Default unknown error
    return {
      type: 'unknown',
      message: `Transcription failed: ${errorMessage}`,
      retryable: false,
      originalError: error
    };
  }
  
  /**
   * Retry function with exponential backoff
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        const errorInfo = this.analyzeError(error);
        
        console.warn(`⚠️ Transcription attempt ${attempt}/${maxRetries} failed:`, errorInfo.message);
        
        if (!errorInfo.retryable || attempt >= maxRetries) {
          throw error;
        }
        
        const delay = errorInfo.retryDelay || (baseDelay * Math.pow(2, attempt - 1));
        console.log(`🔄 Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }
  
  /**
   * Circuit breaker pattern for transcription API
   */
  private static circuitBreaker = {
    failures: 0,
    lastFailureTime: 0,
    state: 'closed' as 'closed' | 'open' | 'half-open',
    threshold: 5,
    timeout: 60000 // 1 minute
  };
  
  static async withCircuitBreaker<T>(
    operation: () => Promise<T>
  ): Promise<T> {
    const now = Date.now();
    
    // Check if circuit breaker is open
    if (this.circuitBreaker.state === 'open') {
      if (now - this.circuitBreaker.lastFailureTime > this.circuitBreaker.timeout) {
        this.circuitBreaker.state = 'half-open';
        console.log('🔄 Circuit breaker transitioning to half-open');
      } else {
        throw new Error('Circuit breaker is open - transcription service temporarily unavailable');
      }
    }
    
    try {
      const result = await operation();
      
      // Success - reset circuit breaker
      if (this.circuitBreaker.state === 'half-open') {
        this.circuitBreaker.state = 'closed';
        this.circuitBreaker.failures = 0;
        console.log('✅ Circuit breaker reset to closed');
      }
      
      return result;
    } catch (error) {
      this.circuitBreaker.failures++;
      this.circuitBreaker.lastFailureTime = now;
      
      if (this.circuitBreaker.failures >= this.circuitBreaker.threshold) {
        this.circuitBreaker.state = 'open';
        console.log('🚨 Circuit breaker opened after', this.circuitBreaker.failures, 'failures');
      }
      
      throw error;
    }
  }
  
  /**
   * Get user-friendly error message
   */
  static getUserFriendlyMessage(error: any): string {
    const errorInfo = this.analyzeError(error);
    
    switch (errorInfo.type) {
      case 'network':
        return 'Network connection issue. Please check your internet connection and try again.';
      case 'quota':
        return 'Service temporarily unavailable due to high demand. Please try again in a few minutes.';
      case 'timeout':
        return 'The request took too long to complete. Please try again with a smaller audio file.';
      case 'api':
        return 'Service configuration issue. Please contact support.';
      default:
        return 'An unexpected error occurred. Please try again or contact support.';
    }
  }
} 