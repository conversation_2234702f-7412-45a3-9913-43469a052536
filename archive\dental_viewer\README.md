# Dental Appointment Viewer

A cross-platform dental appointment viewer application built with Flutter.

## Features

- View dental appointments by date
- Display appointments in a grid view organized by operatory
- Show appointments in 10-minute time slots from 8am to 5pm
- Distinguish between patient appointments and blocked time
- Simple and intuitive user interface

## Getting Started

### Prerequisites

1. Install Flutter: https://flutter.dev/docs/get-started/install
2. Set up your IDE (VS Code or Android Studio)
3. Your Sikka API credentials are already included in the app

### Running the Application

1. Navigate to the project directory
2. Run `flutter pub get` to install dependencies
3. Run the app:
   - For Windows: `flutter run -d windows`
   - For Web: `flutter run -d chrome`
   - For iOS: `flutter run -d ios` (requires macOS)
   - For Android: `flutter run -d android`

## Project Structure

- `lib/main.dart` - Entry point of the application
- `lib/models/` - Data models
- `lib/services/` - API services
- `lib/screens/` - App screens
- `lib/widgets/` - Reusable UI components
- `lib/utils/` - Utility functions and constants

## New Schedule Grid View

The app now includes a grid-based schedule view that:
- Shows operatories side-by-side
- Displays appointments in 10-minute time slots
- Shows the full day from 8am to 5pm
- Color-codes appointments vs. blocked time
- Allows easy navigation between dates

## Future Enhancements

- Add procedure codes display
- Add clinical notes display
- Implement a more detailed appointment view
- Add filtering options
- Support for offline mode
- Implement authentication persistence

## Platforms Supported

- iOS
- Android
- Windows
- Web
- macOS (with additional configuration)

## Troubleshooting

If you encounter any issues:

1. Make sure Flutter is installed correctly
2. Verify that your Sikka API credentials are correct
3. Check that you have an internet connection
4. Run `flutter doctor` to diagnose any Flutter installation issues

## License

This project is proprietary and confidential.
