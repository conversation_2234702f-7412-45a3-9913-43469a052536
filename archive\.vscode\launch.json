{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        
        {
            "name": "sikka api testing",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "sikka api testing (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "sikka api testing (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "dental_viewer",
            "cwd": "dental_viewer",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "dental_viewer (profile mode)",
            "cwd": "dental_viewer",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "dental_viewer (release mode)",
            "cwd": "dental_viewer",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}