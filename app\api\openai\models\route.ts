import { NextRequest, NextResponse } from 'next/server';
import { OpenAI } from 'openai';

// Pricing information (per minute) - Update these based on OpenAI's current pricing
const MODEL_PRICING = {
  'whisper-1': 0.006, // $0.006 per minute
  'gpt-4o-audio-preview': 0.100, // $0.10 per minute (estimated)
  'gpt-4o-audio-preview-2024-10-01': 0.100,
  'gpt-4o-audio-preview-2024-12-17': 0.100,
  'gpt-4o-audio-preview-2025-06-03': 0.100,
  'gpt-4o-mini-audio-preview': 0.010, // $0.01 per minute (estimated)
  'gpt-4o-mini-audio-preview-2024-12-17': 0.010,
};

export async function GET(request: NextRequest) {
  try {
    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json({ error: 'OpenAI API key not configured' }, { status: 500 });
    }

    console.log('🤖 Fetching OpenAI models...');

    // Initialize OpenAI client at runtime, not build time
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    // Get all available models
    const models = await openai.models.list();
    
    // Filter for audio/transcription models
    // Note: Only Whisper models are currently supported for transcriptions API
    const audioModels = models.data.filter(model => 
      model.id.includes('whisper')
      // GPT-4o audio models are for real-time API, not transcriptions
    );

    // Map models to our format with pricing
    const formattedModels = audioModels.map(model => {
      const pricing = MODEL_PRICING[model.id as keyof typeof MODEL_PRICING] || null;
      
      let description = 'Audio transcription model';
      let estimatedCost = null;
      
      // Add specific descriptions and pricing
      if (model.id === 'whisper-1') {
        description = 'Standard Whisper model - High accuracy, supports 50+ languages';
        estimatedCost = pricing;
      } else if (model.id.includes('gpt-4o-audio-preview')) {
        description = 'GPT-4o Audio Preview - Advanced model with enhanced accuracy';
        estimatedCost = pricing;
      } else if (model.id.includes('gpt-4o-mini-audio-preview')) {
        description = 'GPT-4o Mini Audio Preview - Fast and cost-effective';
        estimatedCost = pricing;
      }
      
      return {
        id: model.id,
        name: model.id.charAt(0).toUpperCase() + model.id.slice(1).replace('-', ' '),
        description,
        created: model.created,
        owned_by: model.owned_by,
        pricing: estimatedCost,
        available: true
      };
    });

    // If no audio models found, include default Whisper
    if (formattedModels.length === 0) {
      formattedModels.push({
        id: 'whisper-1',
        name: 'Whisper v1',
        description: 'Standard Whisper model - High accuracy, supports 50+ languages',
        created: 0,
        owned_by: 'openai',
        pricing: MODEL_PRICING['whisper-1'],
        available: true
      });
    }

    // Sort by creation date (newest first) and then by name
    formattedModels.sort((a, b) => {
      if (a.created !== b.created) {
        return b.created - a.created;
      }
      return a.name.localeCompare(b.name);
    });

    console.log(`✅ Found ${formattedModels.length} audio models`);

    return NextResponse.json({
      models: formattedModels,
      lastUpdated: new Date().toISOString(),
      notice: 'Pricing estimates based on current OpenAI rates. Actual costs may vary.'
    });

  } catch (error: any) {
    console.error('❌ Error fetching OpenAI models:', error);
    
    // Return fallback models if API fails
    const fallbackModels = [
      {
        id: 'whisper-1',
        name: 'Whisper v1',
        description: 'Standard Whisper model - High accuracy, supports 50+ languages',
        created: 0,
        owned_by: 'openai',
        pricing: MODEL_PRICING['whisper-1'],
        available: true
      }
    ];

    return NextResponse.json({
      models: fallbackModels,
      lastUpdated: new Date().toISOString(),
      notice: 'Using fallback models due to API error. Pricing estimates may not be current.',
      error: 'Could not fetch latest models from OpenAI'
    });
  }
}