import { NextRequest, NextResponse } from 'next/server';
import { sql } from '@vercel/postgres';
import { VercelDB } from '@/lib/vercel-db';
import { featureFlags } from '@/lib/feature-flags';

interface TranscriptionAnalysis {
  transcriptionId: string;
  filename: string;
  analysisDate: string;
  
  // Basic metrics
  wordCount: number;
  characterCount: number;
  duration?: number;
  confidence: number;
  
  // Content analysis
  contentType: string;
  extractedKeywords: string[];
  dentalProcedures: string[];
  sentimentScore: number;
  readabilityScore: number;
  
  // Quality assessment
  completeness: number;
  clarity: number;
  medicalAccuracy: number;
  
  // Processing status
  hasTranscription: boolean;
  hasSummary: boolean;
  needsAttention: boolean;
  
  // Additional metadata
  patientId?: string;
  deviceId: string;
  processingTimeMs?: number;
}

interface FileAnalysisResult {
  success: boolean;
  totalTranscriptions: number;
  analyzedCount: number;
  analysis: TranscriptionAnalysis[];
  summary: {
    averageWordCount: number;
    averageConfidence: number;
    contentTypeDistribution: Record<string, number>;
    needsAttentionCount: number;
    completenessDistribution: Record<string, number>;
  };
  processingTimeMs: number;
}

/**
 * ANALYZE FILES ENDPOINT - Transcription-Only Architecture
 * Comprehensive analysis of transcription files and content quality
 * Uses Vercel Postgres for data and advanced content analysis
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Check if new architecture is enabled
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { searchParams } = new URL(request.url);
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 200);
    const offset = Math.max(parseInt(searchParams.get('offset') || '0'), 0);
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const deviceId = searchParams.get('deviceId');
    const analysisType = searchParams.get('type') || 'comprehensive';

    console.log(`📊 Starting file analysis: ${analysisType} (limit: ${limit}, offset: ${offset})`);

    // Build query with filters
    let whereConditions = ['transcription_text IS NOT NULL', 'transcription_text != \'\''];
    let queryParams: any[] = [];
    let paramIndex = 1;

    if (dateFrom) {
      whereConditions.push(`created_at >= $${paramIndex}`);
      queryParams.push(dateFrom);
      paramIndex++;
    }

    if (dateTo) {
      whereConditions.push(`created_at <= $${paramIndex}`);
      queryParams.push(dateTo);
      paramIndex++;
    }

    if (deviceId) {
      whereConditions.push(`device_id = $${paramIndex}`);
      queryParams.push(deviceId);
      paramIndex++;
    }

    // Add limit and offset to params
    queryParams.push(limit, offset);

    const query = `
      SELECT 
        id,
        filename,
        transcription_text,
        summary_text,
        confidence_score,
        device_id,
        patient_id,
        created_at,
        metadata
      FROM transcriptions 
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    const result = await sql.query(query, queryParams);
    const transcriptions = result.rows;

    if (transcriptions.length === 0) {
      return NextResponse.json({
        success: true,
        totalTranscriptions: 0,
        analyzedCount: 0,
        analysis: [],
        summary: {
          averageWordCount: 0,
          averageConfidence: 0,
          contentTypeDistribution: {},
          needsAttentionCount: 0,
          completenessDistribution: {}
        },
        processingTimeMs: Date.now() - startTime,
        message: 'No transcriptions found for analysis'
      });
    }

    console.log(`🔍 Analyzing ${transcriptions.length} transcriptions...`);

    // Analyze each transcription
    const analysis: TranscriptionAnalysis[] = [];
    let totalWordCount = 0;
    let totalConfidence = 0;
    const contentTypeDistribution: Record<string, number> = {};
    const completenessDistribution: Record<string, number> = {};
    let needsAttentionCount = 0;

    for (const transcription of transcriptions) {
      const analysisResult = await analyzeTranscription(transcription, analysisType);
      analysis.push(analysisResult);

      // Update summary statistics
      totalWordCount += analysisResult.wordCount;
      totalConfidence += analysisResult.confidence;
      
      contentTypeDistribution[analysisResult.contentType] = 
        (contentTypeDistribution[analysisResult.contentType] || 0) + 1;
      
      const completenessCategory = getCompletenessCategory(analysisResult.completeness);
      completenessDistribution[completenessCategory] = 
        (completenessDistribution[completenessCategory] || 0) + 1;
      
      if (analysisResult.needsAttention) {
        needsAttentionCount++;
      }
    }

    const analysisResult: FileAnalysisResult = {
      success: true,
      totalTranscriptions: transcriptions.length,
      analyzedCount: analysis.length,
      analysis,
      summary: {
        averageWordCount: Math.round(totalWordCount / analysis.length),
        averageConfidence: Math.round((totalConfidence / analysis.length) * 100) / 100,
        contentTypeDistribution,
        needsAttentionCount,
        completenessDistribution
      },
      processingTimeMs: Date.now() - startTime
    };

    console.log(`✅ Analysis complete: ${analysis.length} files analyzed in ${analysisResult.processingTimeMs}ms`);

    return NextResponse.json(analysisResult);

  } catch (error) {
    console.error('❌ File analysis error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'File analysis failed',
      code: 'ANALYSIS_ERROR',
      processingTimeMs: Date.now() - startTime
    }, { status: 500 });
  }
}

/**
 * POST endpoint for detailed analysis of specific transcriptions
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    if (!featureFlags.enableVercelPostgres) {
      return NextResponse.json({
        error: 'Vercel Postgres not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const { transcriptionIds, analysisOptions = {} } = await request.json();

    if (!transcriptionIds || !Array.isArray(transcriptionIds)) {
      return NextResponse.json({
        error: 'Transcription IDs array is required',
        code: 'MISSING_IDS'
      }, { status: 400 });
    }

    console.log(`📊 Detailed analysis requested for ${transcriptionIds.length} transcriptions`);

    // Get transcriptions by ID
    const placeholders = transcriptionIds.map((_, index) => `$${index + 1}`).join(',');
    const query = `
      SELECT 
        id,
        filename,
        transcription_text,
        summary_text,
        confidence_score,
        device_id,
        patient_id,
        created_at,
        metadata
      FROM transcriptions 
      WHERE id IN (${placeholders})
      AND transcription_text IS NOT NULL 
      AND transcription_text != ''
    `;

    const result = await sql.query(query, transcriptionIds);
    const transcriptions = result.rows;

    if (transcriptions.length === 0) {
      return NextResponse.json({
        error: 'No valid transcriptions found',
        code: 'NO_TRANSCRIPTIONS'
      }, { status: 404 });
    }

    // Perform detailed analysis
    const analysis: TranscriptionAnalysis[] = [];
    
    for (const transcription of transcriptions) {
      const analysisResult = await analyzeTranscription(
        transcription, 
        analysisOptions.type || 'detailed'
      );
      analysis.push(analysisResult);
    }

    return NextResponse.json({
      success: true,
      totalRequested: transcriptionIds.length,
      analyzedCount: analysis.length,
      analysis,
      processingTimeMs: Date.now() - startTime
    });

  } catch (error) {
    console.error('❌ Detailed analysis error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Detailed analysis failed',
      code: 'DETAILED_ANALYSIS_ERROR',
      processingTimeMs: Date.now() - startTime
    }, { status: 500 });
  }
}

async function analyzeTranscription(transcription: any, analysisType: string): Promise<TranscriptionAnalysis> {
  const transcriptionText = transcription.transcription_text || '';
  const summaryText = transcription.summary_text || '';
  
  // Basic metrics
  const wordCount = countWords(transcriptionText);
  const characterCount = transcriptionText.length;
  const confidence = transcription.confidence_score || 0;
  
  // Content analysis
  const contentType = determineContentType(transcriptionText);
  const extractedKeywords = extractKeywords(transcriptionText);
  const dentalProcedures = extractDentalProcedures(transcriptionText);
  const sentimentScore = calculateSentimentScore(transcriptionText);
  const readabilityScore = calculateReadabilityScore(transcriptionText);
  
  // Quality assessment
  const completeness = assessCompleteness(transcriptionText, summaryText);
  const clarity = assessClarity(transcriptionText);
  const medicalAccuracy = assessMedicalAccuracy(transcriptionText);
  
  // Processing status
  const hasTranscription = transcriptionText.length > 0;
  const hasSummary = summaryText.length > 0;
  const needsAttention = determineNeedsAttention(
    confidence, 
    completeness, 
    clarity, 
    wordCount
  );
  
  return {
    transcriptionId: transcription.id,
    filename: transcription.filename,
    analysisDate: new Date().toISOString(),
    
    // Basic metrics
    wordCount,
    characterCount,
    duration: transcription.metadata?.duration,
    confidence,
    
    // Content analysis
    contentType,
    extractedKeywords,
    dentalProcedures,
    sentimentScore,
    readabilityScore,
    
    // Quality assessment
    completeness,
    clarity,
    medicalAccuracy,
    
    // Processing status
    hasTranscription,
    hasSummary,
    needsAttention,
    
    // Additional metadata
    patientId: transcription.patient_id,
    deviceId: transcription.device_id,
    processingTimeMs: transcription.metadata?.processing_time_ms
  };
}

function countWords(text: string): number {
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

function determineContentType(text: string): string {
  const content = text.toLowerCase();
  
  // Dental procedure keywords
  const procedureKeywords = [
    'cleaning', 'examination', 'x-ray', 'filling', 'crown', 'extraction',
    'root canal', 'surgery', 'anesthesia', 'procedure'
  ];
  
  const consultationKeywords = [
    'consultation', 'discuss', 'plan', 'recommend', 'options', 'treatment plan'
  ];
  
  const emergencyKeywords = [
    'pain', 'emergency', 'urgent', 'swelling', 'infection', 'broken'
  ];
  
  let procedureCount = 0;
  let consultationCount = 0;
  let emergencyCount = 0;
  
  for (const keyword of procedureKeywords) {
    if (content.includes(keyword)) procedureCount++;
  }
  
  for (const keyword of consultationKeywords) {
    if (content.includes(keyword)) consultationCount++;
  }
  
  for (const keyword of emergencyKeywords) {
    if (content.includes(keyword)) emergencyCount++;
  }
  
  if (emergencyCount >= 2) return 'emergency';
  if (procedureCount >= 3) return 'procedure';
  if (consultationCount >= 2) return 'consultation';
  if (procedureCount >= 1) return 'examination';
  
  return 'general';
}

function extractKeywords(text: string): string[] {
  const content = text.toLowerCase();
  
  // Common dental/medical keywords
  const keywords = [
    'tooth', 'teeth', 'cavity', 'filling', 'crown', 'root canal',
    'cleaning', 'examination', 'x-ray', 'pain', 'sensitivity',
    'gums', 'plaque', 'tartar', 'hygiene', 'molar', 'incisor',
    'anesthesia', 'extraction', 'surgery', 'procedure', 'treatment'
  ];
  
  const found = keywords.filter(keyword => content.includes(keyword));
  return found.slice(0, 10); // Return top 10 keywords
}

function extractDentalProcedures(text: string): string[] {
  const content = text.toLowerCase();
  
  const procedures = [
    'cleaning', 'scaling', 'polishing', 'examination', 'x-ray',
    'filling', 'restoration', 'crown', 'bridge', 'root canal',
    'extraction', 'surgery', 'implant', 'whitening', 'sealant'
  ];
  
  return procedures.filter(procedure => content.includes(procedure));
}

function calculateSentimentScore(text: string): number {
  const content = text.toLowerCase();
  
  const positiveWords = [
    'good', 'excellent', 'healthy', 'clean', 'comfortable', 'satisfied',
    'pleased', 'happy', 'great', 'fine', 'well', 'better'
  ];
  
  const negativeWords = [
    'pain', 'hurt', 'bad', 'terrible', 'uncomfortable', 'worried',
    'anxious', 'problem', 'issue', 'difficult', 'worse', 'bleeding'
  ];
  
  let positiveCount = 0;
  let negativeCount = 0;
  
  for (const word of positiveWords) {
    if (content.includes(word)) positiveCount++;
  }
  
  for (const word of negativeWords) {
    if (content.includes(word)) negativeCount++;
  }
  
  const totalSentimentWords = positiveCount + negativeCount;
  if (totalSentimentWords === 0) return 0.5; // Neutral
  
  return positiveCount / totalSentimentWords;
}

function calculateReadabilityScore(text: string): number {
  const words = countWords(text);
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
  const syllables = estimateSyllables(text);
  
  if (words === 0 || sentences === 0) return 0;
  
  // Simplified Flesch Reading Ease Score
  const score = 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words));
  return Math.max(0, Math.min(100, score)) / 100; // Normalize to 0-1
}

function estimateSyllables(text: string): number {
  const words = text.toLowerCase().split(/\s+/);
  let syllableCount = 0;
  
  for (const word of words) {
    // Simple syllable estimation
    const vowels = (word.match(/[aeiouy]+/g) || []).length;
    syllableCount += Math.max(1, vowels);
  }
  
  return syllableCount;
}

function assessCompleteness(transcriptionText: string, summaryText: string): number {
  let score = 0;
  
  // Base score for having transcription
  if (transcriptionText.length > 0) score += 0.4;
  
  // Word count adequacy
  const wordCount = countWords(transcriptionText);
  if (wordCount >= 50) score += 0.2;
  if (wordCount >= 100) score += 0.1;
  
  // Summary presence
  if (summaryText.length > 0) score += 0.2;
  
  // Content structure indicators
  const hasPatientInfo = /patient|name/i.test(transcriptionText);
  const hasProcedureInfo = /procedure|treatment|exam/i.test(transcriptionText);
  const hasConclusion = /recommend|plan|follow/i.test(transcriptionText);
  
  if (hasPatientInfo) score += 0.033;
  if (hasProcedureInfo) score += 0.033;
  if (hasConclusion) score += 0.033;
  
  return Math.min(score, 1.0);
}

function assessClarity(text: string): number {
  let score = 1.0;
  
  // Penalty for too many filler words
  const fillerWords = (text.match(/\b(um|uh|er|ah|like|you know)\b/gi) || []).length;
  const wordCount = countWords(text);
  const fillerRatio = wordCount > 0 ? fillerWords / wordCount : 0;
  
  if (fillerRatio > 0.1) score -= 0.3; // High filler word ratio
  else if (fillerRatio > 0.05) score -= 0.1; // Moderate filler word ratio
  
  // Penalty for repetitive content
  const words = text.toLowerCase().split(/\s+/);
  const uniqueWords = new Set(words);
  const repetitionRatio = words.length > 0 ? uniqueWords.size / words.length : 1;
  
  if (repetitionRatio < 0.5) score -= 0.2; // Very repetitive
  else if (repetitionRatio < 0.7) score -= 0.1; // Somewhat repetitive
  
  // Penalty for incomplete sentences
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const incompleteRatio = sentences.filter(s => s.length < 10).length / Math.max(sentences.length, 1);
  
  if (incompleteRatio > 0.3) score -= 0.2;
  
  return Math.max(score, 0);
}

function assessMedicalAccuracy(text: string): number {
  // This is a simplified assessment - in production, this could use medical NLP
  let score = 0.8; // Base score assuming reasonable accuracy
  
  const content = text.toLowerCase();
  
  // Check for medical terminology usage
  const medicalTerms = [
    'diagnosis', 'symptom', 'treatment', 'procedure', 'examination',
    'patient', 'clinical', 'medical', 'health', 'condition'
  ];
  
  const medicalTermCount = medicalTerms.filter(term => content.includes(term)).length;
  
  if (medicalTermCount >= 3) score += 0.1;
  else if (medicalTermCount === 0) score -= 0.2;
  
  // Check for anatomical terms
  const anatomicalTerms = [
    'tooth', 'teeth', 'gum', 'jaw', 'mouth', 'tongue', 'molar', 'incisor'
  ];
  
  const anatomicalTermCount = anatomicalTerms.filter(term => content.includes(term)).length;
  
  if (anatomicalTermCount >= 2) score += 0.05;
  
  return Math.min(Math.max(score, 0), 1.0);
}

function determineNeedsAttention(
  confidence: number, 
  completeness: number, 
  clarity: number, 
  wordCount: number
): boolean {
  // Needs attention if any critical metric is below threshold
  if (confidence < 0.7) return true;
  if (completeness < 0.6) return true;
  if (clarity < 0.5) return true;
  if (wordCount < 20) return true;
  
  return false;
}

function getCompletenessCategory(completeness: number): string {
  if (completeness >= 0.8) return 'excellent';
  if (completeness >= 0.6) return 'good';
  if (completeness >= 0.4) return 'fair';
  return 'poor';
}