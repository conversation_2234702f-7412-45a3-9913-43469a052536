import requests
import json
from datetime import datetime, timedelta
import sys

# API base URLs
API_BASE_V4 = "https://api.sikkasoft.com/v4"
API_BASE_V2 = "https://api.sikkasoft.com/v2"

# API credentials
OFFICE_ID = "D43989"
SECRET_KEY = "35442814D4396E20C222"
APP_ID = "fdd52aaffb0c1bead647874ba551db0c"
APP_KEY = "88254bfa2224607ef425646aafe5f722"

# Set timeout for API requests (in seconds)
API_TIMEOUT = 30

def get_request_key():
    """Obtain a request key for API authentication."""
    print("Getting request key...")
    auth_resp = requests.post(
        f"{API_BASE_V4}/request_key",
        headers={"Content-Type": "application/json"},
        json={
            "grant_type": "request_key",
            "office_id": OFFICE_ID,
            "secret_key": SECRET_KEY,
            "app_id": APP_ID,
            "app_key": APP_KEY
        },
        timeout=API_TIMEOUT
    )
    if auth_resp.status_code != 200:
        print(f"Error: {auth_resp.status_code}")
        print(auth_resp.text)
        return None
    
    data = auth_resp.json()
    request_key = data.get("request_key")
    print(f"Request key obtained: {request_key}")
    return request_key

def fetch_appointments(request_key, date, operatories=None):
    """Fetch appointments for a specific date and optionally filter by operatories."""
    headers = {"Request-Key": request_key}
    params = {"date": date}
    
    # Use v2 appointments endpoint to get operatory information
    print(f"Fetching appointments for {date}...")
    try:
        resp = requests.get(
            f"{API_BASE_V2}/appointments", 
            headers=headers, 
            params=params,
            timeout=API_TIMEOUT
        )
        
        if resp.status_code != 200:
            print(f"Error fetching appointments: {resp.status_code}")
            print(resp.text)
            return []
        
        data = resp.json()
        
        # Extract items from v2 response (which has a different structure)
        if isinstance(data, list) and len(data) > 0:
            all_items = data[0].get("items", [])
        else:
            all_items = []
        
        # Filter to only include appointments for the specified date
        all_items = [a for a in all_items if a.get("date", "").startswith(date)]
        
        # Print all appointments with operatory information
        print(f"\nFound {len(all_items)} appointments for {date}")
        print("\nAll appointments with operatory information:")
        print(f"{'TIME':<8}{'PATIENT':<25}{'OPERATORY':<10}{'PROVIDER':<10}{'DESCRIPTION'}")
        print(f"{'-' * 8}{'-' * 25}{'-' * 10}{'-' * 10}{'-' * 30}")
        
        for appt in sorted(all_items, key=lambda a: a.get("time", "")):
            time = appt.get("time", "")
            patient_name = appt.get("patient_name", "Unknown")
            operatory = appt.get("operatory", "N/A")
            provider_id = appt.get("provider_id", "N/A")
            description = appt.get("description", "")
            
            # Truncate long names/descriptions
            if len(patient_name) > 22:
                patient_name = patient_name[:19] + "..."
            if len(description) > 30:
                description = description[:27] + "..."
            
            print(f"{time:<8}{patient_name:<25}{operatory:<10}{provider_id:<10}{description}")
        
        # Filter by operatories if specified
        if operatories:
            filtered_items = [a for a in all_items if a.get("operatory") in operatories]
            print(f"\nFiltered to {len(filtered_items)} appointments in operatories: {', '.join(operatories)}")
            
            # Print filtered appointments
            if filtered_items:
                print("\nFiltered appointments:")
                print(f"{'TIME':<8}{'PATIENT':<25}{'OPERATORY':<10}{'PROVIDER':<10}{'DESCRIPTION'}")
                print(f"{'-' * 8}{'-' * 25}{'-' * 10}{'-' * 10}{'-' * 30}")
                
                for appt in sorted(filtered_items, key=lambda a: a.get("time", "")):
                    time = appt.get("time", "")
                    patient_name = appt.get("patient_name", "Unknown")
                    operatory = appt.get("operatory", "N/A")
                    provider_id = appt.get("provider_id", "N/A")
                    description = appt.get("description", "")
                    
                    # Truncate long names/descriptions
                    if len(patient_name) > 22:
                        patient_name = patient_name[:19] + "..."
                    if len(description) > 30:
                        description = description[:27] + "..."
                    
                    print(f"{time:<8}{patient_name:<25}{operatory:<10}{provider_id:<10}{description}")
            else:
                print("No appointments found in the specified operatories.")
            
            return filtered_items
        
        return all_items
    except Exception as e:
        print(f"Error: {e}")
        return []

def fetch_operatories(request_key, date_range=30):
    """Fetch a list of all operatories from appointment data."""
    print("Fetching list of operatories...")
    headers = {"Request-Key": request_key}
    
    # Try to get appointments for the last X days to find operatories
    operatories = set()
    
    # Try different date ranges
    end_date = datetime.now()
    start_date = end_date - timedelta(days=date_range)
    
    # Format dates as YYYY-MM-DD
    date_format = "%Y-%m-%d"
    current_date = start_date
    
    # Check every 7 days to avoid too many API calls
    while current_date <= end_date:
        date_str = current_date.strftime(date_format)
        
        try:
            resp = requests.get(
                f"{API_BASE_V2}/appointments", 
                headers=headers, 
                params={"date": date_str},
                timeout=API_TIMEOUT
            )
            
            if resp.status_code == 200:
                data = resp.json()
                
                # Extract items from v2 response
                if isinstance(data, list) and len(data) > 0:
                    items = data[0].get("items", [])
                    
                    # Extract operatories
                    for item in items:
                        operatory = item.get("operatory")
                        if operatory and operatory not in ["", "N/A"]:
                            operatories.add(operatory)
        except Exception as e:
            print(f"Error fetching operatories: {e}")
        
        # Move to next date
        current_date += timedelta(days=7)
    
    # Print the list of operatories
    if operatories:
        print(f"Found {len(operatories)} operatories:")
        for op in sorted(operatories):
            print(f"  - {op}")
    else:
        print("No operatories found in appointment data")
    
    return sorted(operatories)

def main():
    # Get request key
    request_key = get_request_key()
    if not request_key:
        print("Failed to get request key. Exiting.")
        return
    
    # Get date from command line or use default
    if len(sys.argv) > 1:
        date = sys.argv[1]
    else:
        date = "2025-05-16"  # Default date
    
    # Get operatories from command line or use default
    if len(sys.argv) > 2:
        operatories = sys.argv[2].split(",")
    else:
        operatories = ["DL01", "DL02"]  # Default operatories
    
    # Fetch all operatories
    all_operatories = fetch_operatories(request_key)
    
    # Fetch appointments filtered by operatories
    appointments = fetch_appointments(request_key, date, operatories)
    
    print("\nDebug complete.")

if __name__ == "__main__":
    main()
