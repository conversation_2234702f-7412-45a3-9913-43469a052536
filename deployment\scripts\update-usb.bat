@echo off
echo ========================================
echo    Dental App USB Update
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

:: Set working directory
cd /d "C:\DentalApp"

echo [1/6] Looking for USB update package...

:: Check common USB drive letters
set USB_FOUND=0
set USB_PATH=

for %%d in (D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
    if exist "%%d:\DentalApp-Update\app" (
        set USB_PATH=%%d:\DentalApp-Update
        set USB_FOUND=1
        echo Found update package on drive %%d:
        goto :found_usb
    )
)

:found_usb
if %USB_FOUND%==0 (
    echo ERROR: USB update package not found
    echo.
    echo Please ensure USB drive contains:
    echo   DentalApp-Update\
    echo     └── app\
    echo.
    echo Or specify the path manually:
    set /p USB_PATH="Enter USB path (e.g., E:\DentalApp-Update): "
    if not exist "%USB_PATH%\app" (
        echo ERROR: Invalid path or app folder not found
        pause
        exit /b 1
    )
)

echo Using update package: %USB_PATH%

echo [2/6] Stopping Dental App service...
call pm2 stop dental-app
if %errorLevel% neq 0 (
    echo Warning: Could not stop service (may not be running)
)

echo [3/6] Creating backup of current version...
set BACKUP_DIR=backups\backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_DIR=%BACKUP_DIR: =0%
mkdir "%BACKUP_DIR%" 2>nul
xcopy /E /I /Y "app" "%BACKUP_DIR%\app\" >nul
echo Backup created: %BACKUP_DIR%

echo [4/6] Installing update from USB...
echo Removing old app files...
rmdir /s /q "app" 2>nul

echo Copying new app files...
xcopy /E /I /Y "%USB_PATH%\app" "app\"
if %errorLevel% neq 0 (
    echo ERROR: Failed to copy files from USB
    echo Restoring from backup...
    xcopy /E /I /Y "%BACKUP_DIR%\app" "app\"
    call pm2 start dental-app
    echo Restore complete. App is running previous version.
    pause
    exit /b 1
)

echo [5/6] Installing dependencies...
cd app
call npm install --production
if %errorLevel% neq 0 (
    echo ERROR: Failed to install dependencies
    echo Restoring from backup...
    cd /d "C:\DentalApp"
    rmdir /s /q "app"
    xcopy /E /I /Y "%BACKUP_DIR%\app" "app\"
    call pm2 start dental-app
    echo Restore complete. App is running previous version.
    pause
    exit /b 1
)

echo [6/6] Restarting Dental App service...
cd /d "C:\DentalApp"
call pm2 restart dental-app
if %errorLevel% neq 0 (
    echo ERROR: Failed to restart service
    echo Please check logs and try manual restart
    pause
    exit /b 1
)

:: Wait for service to start
echo Waiting for service to start...
timeout /t 5 /nobreak >nul

:: Check if service is running
call pm2 list | findstr "dental-app" | findstr "online" >nul
if %errorLevel%==0 (
    echo.
    echo ========================================
    echo    USB Update Successful!
    echo ========================================
    echo.
    echo Dental App has been updated and restarted.
    echo Access the app at: http://localhost:3000
    echo.
    echo Update source: %USB_PATH%
    echo Backup location: %BACKUP_DIR%
    echo.
    echo Update completed at: %date% %time%
    echo.
    echo You can safely remove the USB drive now.
) else (
    echo.
    echo ========================================
    echo    Update Warning
    echo ========================================
    echo.
    echo Update completed but service may not be running properly.
    echo Please check the service status and logs.
    echo.
    echo Run: C:\DentalApp\scripts\status.bat
    echo Or:  C:\DentalApp\scripts\logs.bat
)

echo.
echo Press any key to continue...
pause >nul
