#!/usr/bin/env bash
#
# augment-setup.sh
#
# This script automates importing your two Postman collections
# into a single OpenAPI spec and configures VS Code's Augment plugin
# to use it. Steps:
#   1. Initialize npm if needed
#   2. Install Postman→OpenAPI converter and YAML merger
#   3. Convert each .postman_collection.json to YAML OpenAPI
#   4. Merge both YAML specs into sikka-full.yaml
#   5. Write VS Code settings so Augment picks up sikka-full.yaml
#
# Usage:
#   chmod +x augment-setup.sh
#   ./augment-setup.sh

set -e

# 1. Initialize a minimal package.json if one doesn't exist
if [ ! -f package.json ]; then
  echo "🔧 Initializing npm project..."
  npm init -y
fi

# 2. Install dev-dependencies for conversion & YAML handling
echo "📦 Installing postman-to-openapi and js-yaml..."
npm install --save-dev postman-to-openapi js-yaml

# 3. Convert each Postman collection into an OpenAPI YAML file
echo "🔄 Converting Sikka API Developer Policy and Guidelines collection..."
npx postman-to-openapi \
  "Sikka API Developer Policy and Guidelines.postman_collection.json" \
  -o sikka-dev.yaml --pretty

echo "🔄 Converting API documentation collection..."
npx postman-to-openapi \
  "API documentation.postman_collection.json" \
  -o sikka-docs.yaml --pretty

# 4. Create merge.js to combine the two YAML specs into one
cat > merge.js << 'EOF'
/**
 * merge.js
 *
 * Loads sikka-dev.yaml and sikka-docs.yaml,
 * merges their "paths" and "components" sections,
 * and writes the result to sikka-full.yaml.
 */
const fs   = require('fs');
const yaml = require('js-yaml');

// Load each spec
const dev  = yaml.load(fs.readFileSync('sikka-dev.yaml', 'utf8'));
const docs = yaml.load(fs.readFileSync('sikka-docs.yaml', 'utf8'));

// Merge shared sections
const merged = {
  openapi:    dev.openapi || docs.openapi,
  info:       dev.info    || docs.info,
  servers:    dev.servers || docs.servers,
  paths:      { ...(dev.paths||{}),    ...(docs.paths||{}) },
  components: { ...(dev.components||{}), ...(docs.components||{}) }
};

// Write the combined spec
fs.writeFileSync('sikka-full.yaml', yaml.dump(merged), 'utf8');
console.log('✔️  Created sikka-full.yaml');
EOF

# 5. Run the merge script
echo "🔗 Merging YAML specs into sikka-full.yaml..."
node merge.js

# 6. Write VS Code settings to point Augment at the merged spec
echo "⚙️  Configuring .vscode/settings.json for Augment..."
mkdir -p .vscode
cat > .vscode/settings.json << 'EOF'
{
  // Tells Augment which OpenAPI spec to use for code generation
  "augment.openApiSpec": "\${workspaceFolder}/sikka-full.yaml"
}
EOF

echo "🎉 Done! Reload VS Code and your Augment agent will have the full Sikka v2 API spec ready."
