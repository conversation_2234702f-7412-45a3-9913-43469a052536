"use client";

import React, { useState, useEffect } from "react";
import { FileText, User, Calendar, Clock, ArrowRight, CheckCircle, AlertCircle, Search, Filter, RefreshCw } from "lucide-react";
import { useRouter } from "next/navigation";

interface VoiceRecording {
  id: string;
  name: string;
  duration: number;
  size: number;
  createdAt: string;
  transcription?: string;
  summary?: string;
  matchedAppointmentId?: string;
  patientName?: string;
  appointmentDate?: string;
  provider?: string;
  clinicalNoteCreated?: boolean;
  clinicalNoteId?: string;
}

interface MatchedRecordingsManagerProps {
  isDarkMode: boolean;
}

export function MatchedRecordingsManager({ isDarkMode }: MatchedRecordingsManagerProps) {
  const router = useRouter();
  const [recordings, setRecordings] = useState<VoiceRecording[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isLoading, setIsLoading] = useState(true);

  // Filter matched recordings only
  const matchedRecordings = recordings.filter(r => r.matchedAppointmentId);

  // Apply filters
  const filteredRecordings = matchedRecordings.filter(recording => {
    const matchesSearch = searchQuery === "" || 
      recording.patientName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      recording.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      recording.provider?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === "all" || (() => {
      switch (statusFilter) {
        case "needs-notes": return !recording.clinicalNoteCreated;
        case "notes-created": return recording.clinicalNoteCreated;
        case "no-summary": return !recording.summary;
        case "has-summary": return !!recording.summary;
        default: return true;
      }
    })();

    return matchesSearch && matchesStatus;
  });

  // Group by status for better organization
  const recordingsNeedingNotes = filteredRecordings.filter(r => !r.clinicalNoteCreated);
  const recordingsWithNotes = filteredRecordings.filter(r => r.clinicalNoteCreated);

  useEffect(() => {
    loadRecordings();
  }, []);

  const loadRecordings = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/voice/recordings-fast');
      if (response.ok) {
        const data = await response.json();
        setRecordings(data.recordings || []);
      }
    } catch (error) {
      console.error('Failed to load recordings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const goToClinicalNotes = (recording: VoiceRecording) => {
    // Navigate to clinical notes tab with the specific appointment
    const params = new URLSearchParams();
    params.set('tab', 'clinical-notes');
    if (recording.matchedAppointmentId) {
      params.set('appointmentId', recording.matchedAppointmentId);
    }
    router.push(`/?${params.toString()}`);
  };

  const formatFileSize = (bytes: number): string => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getStatusBadge = (recording: VoiceRecording) => {
    if (recording.clinicalNoteCreated) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
          <CheckCircle className="h-3 w-3 mr-1" />
          Notes Created
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300">
          <AlertCircle className="h-3 w-3 mr-1" />
          Needs Notes
        </span>
      );
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Loading matched recordings...</span>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Matched Recordings
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Manage recordings that have been matched to appointments and create clinical notes.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Matched</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{matchedRecordings.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
              <AlertCircle className="h-6 w-6 text-orange-600 dark:text-orange-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Need Notes</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{recordingsNeedingNotes.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{recordingsWithNotes.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search patients, recordings, providers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="all">All recordings</option>
            <option value="needs-notes">Needs clinical notes</option>
            <option value="notes-created">Notes created</option>
            <option value="no-summary">No summary</option>
            <option value="has-summary">Has summary</option>
          </select>
        </div>
      </div>

      {/* Recordings List */}
      <div className="space-y-4">
        {filteredRecordings.length === 0 ? (
          <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow">
            <FileText className="h-16 w-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No matched recordings found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {matchedRecordings.length === 0 
                ? "Start by matching recordings to appointments in the Voice Match tab."
                : "Try adjusting your search or filter criteria."
              }
            </p>
          </div>
        ) : (
          filteredRecordings.map((recording) => (
            <div
              key={recording.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {recording.patientName}
                    </h3>
                    {getStatusBadge(recording)}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Recording Details</p>
                      <p className="font-medium text-gray-900 dark:text-white">{recording.name}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
                        <span>{formatDuration(recording.duration)}</span>
                        <span>•</span>
                        <span>{formatFileSize(recording.size)}</span>
                        <span>•</span>
                        <span>{new Date(recording.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Appointment Details</p>
                      <div className="flex items-center space-x-2 text-sm">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-900 dark:text-white">
                          {recording.appointmentDate ? new Date(recording.appointmentDate).toLocaleDateString() : 'N/A'}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm mt-1">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-900 dark:text-white">{recording.provider || 'N/A'}</span>
                      </div>
                    </div>
                  </div>

                  {recording.summary && (
                    <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                      <div className="flex items-start space-x-2">
                        <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                        <div>
                          <div className="text-xs font-medium text-blue-800 dark:text-blue-300 mb-1">
                            Visit Summary
                          </div>
                          <div className="text-sm text-blue-900 dark:text-blue-100 leading-relaxed">
                            {recording.summary}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="ml-4">
                  <button
                    onClick={() => goToClinicalNotes(recording)}
                    className={`px-4 py-2 rounded-lg font-medium text-sm transition-colors flex items-center space-x-2 ${
                      recording.clinicalNoteCreated
                        ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/50'
                        : 'bg-orange-600 hover:bg-orange-700 text-white'
                    }`}
                  >
                    <span>{recording.clinicalNoteCreated ? 'View Notes' : 'Create Notes'}</span>
                    <ArrowRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
