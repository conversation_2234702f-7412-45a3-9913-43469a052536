import { NextRequest, NextResponse } from 'next/server';
import { TranscriptionService } from '@/lib/transcription-service';
import { transcriptionQueue } from '@/lib/transcription-queue';
import { featureFlags } from '@/lib/feature-flags';

export async function POST(request: NextRequest) {
  try {
    // Check if we should use the new transcription-only workflow
    if (!featureFlags.enableTranscriptionOnlyWorkflow) {
      return NextResponse.json({
        error: 'Transcription service not enabled',
        code: 'SERVICE_DISABLED'
      }, { status: 503 });
    }

    const formData = await request.formData();
    const audioFile = formData.get('audio') as File;
    const filename = formData.get('filename') as string || audioFile?.name || 'unknown.wav';
    const deviceId = formData.get('deviceId') as string || 'unknown-device';
    const patientId = formData.get('patientId') as string;
    const userId = formData.get('userId') as string || 'system';

    if (!audioFile) {
      return NextResponse.json({
        error: 'No audio file provided',
        code: 'MISSING_FILE'
      }, { status: 400 });
    }

    // Convert File to Buffer
    const audioBuffer = Buffer.from(await audioFile.arrayBuffer());

    // Validate audio format using TranscriptionService
    const isValidFormat = await TranscriptionService.validateAudioFormat(audioBuffer, filename);
    if (!isValidFormat) {
      return NextResponse.json({
        error: `Invalid audio format for file: ${filename}. Supported formats: ${TranscriptionService.getSupportedFormats().join(', ')}`,
        code: 'INVALID_FORMAT'
      }, { status: 400 });
    }

    // Get client IP for audit logging
    const clientIp = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';

    console.log(`🎤 Processing transcription request: ${filename} (${audioBuffer.length} bytes)`);

    // Use the queue for processing to handle concurrency
    const jobId = await transcriptionQueue.enqueueJob(audioBuffer, {
      filename,
      deviceId,
      patientId,
      userId,
      ipAddress: clientIp
    }, 'normal');

    // Return job ID for tracking
    return NextResponse.json({
      success: true,
      jobId,
      message: 'Transcription job queued successfully',
      status: 'queued'
    });

  } catch (error) {
    console.error('❌ Transcription API error:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Internal server error',
      code: 'TRANSCRIPTION_ERROR'
    }, { status: 500 });
  }
}

// Get transcription job status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json({
        error: 'Job ID is required',
        code: 'MISSING_JOB_ID'
      }, { status: 400 });
    }

    const jobStatus = transcriptionQueue.getJobStatus(jobId);

    if (!jobStatus) {
      return NextResponse.json({
        error: 'Job not found',
        code: 'JOB_NOT_FOUND'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      job: {
        id: jobStatus.id,
        status: jobStatus.status,
        priority: jobStatus.priority,
        createdAt: jobStatus.createdAt,
        startedAt: jobStatus.startedAt,
        completedAt: jobStatus.completedAt,
        result: jobStatus.result,
        error: jobStatus.error,
        retryCount: jobStatus.retryCount
      }
    });

  } catch (error) {
    console.error('❌ Job status API error:', error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Internal server error',
      code: 'STATUS_ERROR'
    }, { status: 500 });
  }
}