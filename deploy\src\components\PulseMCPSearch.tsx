import React, { useState, useEffect } from 'react';
import { Search, Loader2, BookOpen, ExternalLink, X } from 'lucide-react';
import { usePulseMCP } from '@/hooks/usePulseMCP';

interface PulseMCPSearchProps {
  className?: string;
  placeholder?: string;
  onSelectResult?: (result: any) => void;
}

export function PulseMCPSearch({
  className = '',
  placeholder = 'Search documentation...',
  onSelectResult,
}: PulseMCPSearchProps) {
  const [query, setQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const { search, results, isLoading, error, sources } = usePulseMCP();

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 500);

    return () => clearTimeout(timer);
  }, [query]);

  // Trigger search when debounced query changes
  useEffect(() => {
    if (debouncedQuery) {
      search(debouncedQuery);
    } else {
      search('');
    }
  }, [debouncedQuery, search]);

  const handleResultClick = (result: any) => {
    if (onSelectResult) {
      onSelectResult(result);
    }
  };

  const showResults = isFocused && (query.length > 0 || results.length > 0);

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-gray-400" />
        </div>
        <input
          type="text"
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setTimeout(() => setIsFocused(false), 200)}
        />
        {query && (
          <button
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setQuery('')}
          >
            <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>

      {showResults && (
        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm max-h-96">
          {isLoading ? (
            <div className="px-4 py-2 text-gray-500 flex items-center">
              <Loader2 className="animate-spin h-4 w-4 mr-2" />
              Searching...
            </div>
          ) : error ? (
            <div className="px-4 py-2 text-red-500">
              Error: {error.message}
            </div>
          ) : results.length > 0 ? (
            <ul>
              {results.map((result) => (
                <li key={result.id} className="hover:bg-gray-50">
                  <button
                    className="w-full text-left px-4 py-2 flex items-start"
                    onClick={() => handleResultClick(result)}
                  >
                    <div className="flex-shrink-0 pt-0.5">
                      <BookOpen className="h-4 w-4 text-gray-400" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">
                        {result.title}
                      </p>
                      <p className="text-xs text-gray-500 line-clamp-2">
                        {result.content || result.url}
                      </p>
                      <div className="mt-1">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {result.source}
                        </span>
                      </div>
                    </div>
                  </button>
                </li>
              ))}
            </ul>
          ) : (
            <div className="px-4 py-2 text-gray-500">
              No results found
            </div>
          )}
        </div>
      )}
    </div>
  );
}
