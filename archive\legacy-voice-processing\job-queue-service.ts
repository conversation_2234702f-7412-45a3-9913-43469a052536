/**
 * Job Queue Service
 * 
 * Provides a unified interface for background job processing with reliable queue-based processing.
 * This service integrates with the existing job-persistence.ts infrastructure and provides 
 * reliable queue-based processing to replace unreliable in-memory job tracking.
 */

import { jobStore } from './job-persistence';
import { ProcessingJob, JobStatus, JobErrorDetails, QueueMessage } from '@/types/queue';
import { logger, createJobLogger, logError } from './logger';
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from './error-handler';

export interface JobQueueStats {
  total: number;
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  retrying: number;
  averageProcessingTime: number;
  queueHealth: 'healthy' | 'degraded' | 'unhealthy';
  lastProcessedAt?: string;
  stuckJobs: number;
}

export interface JobEnqueueOptions {
  priority?: number;
  retryCount?: number;
  timeout?: number;
  metadata?: Record<string, any>;
}

export interface JobRetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

export class JobQueueService {
  private static instance: JobQueueService;
  private isProcessing = false;
  private processingInterval?: NodeJS.Timeout;
  private cleanupInterval?: NodeJS.Timeout;
  private readonly defaultRetryConfig: JobRetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 60000,
    backoffMultiplier: 2
  };

  private constructor() {
    this.startProcessing();
    this.startCleanupScheduler();
  }

  public static getInstance(): JobQueueService {
    if (!JobQueueService.instance) {
      JobQueueService.instance = new JobQueueService();
    }
    return JobQueueService.instance;
  }

  /**
   * Add jobs to the processing queue
   */
  async enqueueJob(
    jobData: Omit<ProcessingJob, 'id' | 'createdAt' | 'updatedAt' | 'status'>,
    options: JobEnqueueOptions = {}
  ): Promise<string> {
    const jobLogger = createJobLogger('enqueue', jobData.filename);
    
    try {
      jobLogger.info('Enqueueing new job', {
        filename: jobData.filename,
        containerPath: jobData.containerPath,
        priority: options.priority || 1
      });

      // Create job with initial status
      const jobId = await jobStore.createJob({
        ...jobData,
        status: 'pending',
        retryCount: options.retryCount || 0,
        metadata: {
          ...jobData.metadata,
          ...options.metadata,
          priority: options.priority || 1,
          timeout: options.timeout || 300000, // 5 minutes default
          enqueuedAt: new Date().toISOString()
        }
      });

      jobLogger.info('Job enqueued successfully', { jobId });

      // Trigger immediate processing check if not already processing
      if (!this.isProcessing) {
        setImmediate(() => this.processNextJob());
      }

      return jobId;
    } catch (error) {
      logError(jobLogger, error, 'job-enqueue', { filename: jobData.filename });
      throw error;
    }
  }

  /**
   * Process the next available job
   */
  async processNextJob(): Promise<boolean> {
    if (this.isProcessing) {
      return false;
    }

    this.isProcessing = true;
    const processingLogger = createJobLogger('processor', 'queue');

    try {
      // Get next pending job
      const job = await jobStore.getNextPendingJob();
      
      if (!job) {
        // No jobs to process
        return false;
      }

      const jobLogger = createJobLogger(job.id, job.filename);
      jobLogger.info('Starting job processing', {
        jobId: job.id,
        retryCount: job.retryCount,
        enqueuedAt: job.createdAt
      });

      // Mark job as processing
      await jobStore.markJobAsProcessing(job.id);

      try {
        // Process the job based on its type
        await this.executeJob(job);
        
        // Mark as completed
        await jobStore.updateJob(job.id, {
          status: 'completed',
          completedAt: new Date().toISOString()
        });

        jobLogger.info('Job completed successfully', { jobId: job.id });
        return true;

      } catch (error) {
        await this.handleJobError(job, error as Error);
        return false;
      }

    } catch (error) {
      logError(processingLogger, error, 'job-processing');
      return false;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Retrieve current job status
   */
  async getJobStatus(jobId: string): Promise<ProcessingJob | null> {
    try {
      return await jobStore.getJob(jobId);
    } catch (error) {
      logger.error('Failed to get job status', { jobId }, error as Error);
      return null;
    }
  }

  /**
   * Retry failed job with exponential backoff
   */
  async retryFailedJob(jobId: string, config?: Partial<JobRetryConfig>): Promise<boolean> {
    const retryConfig = { ...this.defaultRetryConfig, ...config };
    const jobLogger = createJobLogger(jobId, 'retry');

    try {
      const job = await jobStore.getJob(jobId);
      if (!job) {
        jobLogger.warn('Job not found for retry', { jobId });
        return false;
      }

      if (job.status !== 'failed') {
        jobLogger.warn('Job is not in failed state for retry', { 
          jobId, 
          currentStatus: job.status 
        });
        return false;
      }

      if (job.retryCount >= retryConfig.maxRetries) {
        jobLogger.warn('Job has exceeded maximum retry attempts', {
          jobId,
          retryCount: job.retryCount,
          maxRetries: retryConfig.maxRetries
        });
        return false;
      }

      // Calculate retry delay with exponential backoff
      const delay = Math.min(
        retryConfig.baseDelay * Math.pow(retryConfig.backoffMultiplier, job.retryCount),
        retryConfig.maxDelay
      );

      jobLogger.info('Scheduling job retry', {
        jobId,
        retryCount: job.retryCount + 1,
        delayMs: delay
      });

      // Schedule retry
      setTimeout(async () => {
        try {
          await jobStore.updateJob(jobId, {
            status: 'pending',
            retryCount: job.retryCount + 1,
            updatedAt: new Date().toISOString()
          });

          // Trigger processing
          setImmediate(() => this.processNextJob());
        } catch (error) {
          logError(jobLogger, error, 'job-retry-schedule');
        }
      }, delay);

      return true;
    } catch (error) {
      logError(jobLogger, error, 'job-retry');
      return false;
    }
  }

  /**
   * Remove completed jobs older than retention period
   */
  async cleanupOldJobs(retentionDays: number = 30): Promise<number> {
    const cleanupLogger = createJobLogger('cleanup', 'queue');
    
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      cleanupLogger.info('Starting job cleanup', {
        retentionDays,
        cutoffDate: cutoffDate.toISOString()
      });

      // Get old completed jobs
      const oldJobs = await jobStore.listJobs({
        status: ['completed'],
        completedBefore: cutoffDate.toISOString(),
        limit: 1000
      });

      let cleanedCount = 0;
      for (const job of oldJobs) {
        try {
          await jobStore.deleteJob(job.id);
          cleanedCount++;
        } catch (error) {
          cleanupLogger.warn('Failed to delete job', { jobId: job.id }, error as Error);
        }
      }

      cleanupLogger.info('Job cleanup completed', {
        cleanedCount,
        retentionDays
      });

      return cleanedCount;
    } catch (error) {
      logError(cleanupLogger, error, 'job-cleanup');
      return 0;
    }
  }

  /**
   * Return queue health and statistics
   */
  async getQueueStats(): Promise<JobQueueStats> {
    try {
      const stats = await jobStore.getStats();
      const stuckJobs = await this.getStuckJobsCount();
      
      // Calculate average processing time from recent completed jobs
      const recentCompleted = await jobStore.listJobs({
        status: ['completed'],
        limit: 100
      });

      const averageProcessingTime = this.calculateAverageProcessingTime(recentCompleted);
      
      // Determine queue health
      const queueHealth = this.determineQueueHealth(stats, stuckJobs);

      // Get last processed job time
      const lastProcessedJob = recentCompleted[0];
      const lastProcessedAt = lastProcessedJob?.completedAt;

      return {
        total: stats.total,
        pending: stats.pending,
        processing: stats.processing,
        completed: stats.completed,
        failed: stats.failed,
        retrying: stats.retrying,
        averageProcessingTime,
        queueHealth,
        lastProcessedAt,
        stuckJobs
      };
    } catch (error) {
      logger.error('Failed to get queue stats', {}, error as Error);
      return {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        retrying: 0,
        averageProcessingTime: 0,
        queueHealth: 'unhealthy',
        stuckJobs: 0
      };
    }
  }

  /**
   * Get list of stuck jobs that may need intervention
   */
  async getStuckJobs(): Promise<ProcessingJob[]> {
    try {
      return await jobStore.getStuckJobs();
    } catch (error) {
      logger.error('Failed to get stuck jobs', {}, error as Error);
      return [];
    }
  }

  /**
   * Stop the queue processing and cleanup
   */
  public stop(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
    }
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = undefined;
    }

    this.isProcessing = false;
    logger.info('Job queue service stopped');
  }

  /**
   * Execute a specific job based on its type and data
   */
  private async executeJob(job: ProcessingJob): Promise<void> {
    const jobLogger = createJobLogger(job.id, job.filename);
    
    // This is where actual job processing would be integrated
    // For now, we'll simulate the processing based on job metadata
    const jobType = job.metadata?.type || 'transcription';
    
    jobLogger.info('Executing job', { jobType, filename: job.filename });

    switch (jobType) {
      case 'transcription':
        await this.executeTranscriptionJob(job);
        break;
      case 'summarization':
        await this.executeSummarizationJob(job);
        break;
      case 'full_processing':
        await this.executeFullProcessingJob(job);
        break;
      default:
        throw new Error(`Unknown job type: ${jobType}`);
    }
  }

  /**
   * Execute transcription job
   */
  private async executeTranscriptionJob(job: ProcessingJob): Promise<void> {
    const jobLogger = createJobLogger(job.id, job.filename);
    
    try {
      const { OpenAI } = await import('openai');
      const { AzureStorageService } = await import('./azure-storage');
      const { transcribeWithChunking, needsChunking } = await import('./audio-chunking');
      const { TranscriptionErrorHandler } = await import('./transcription-error-handler');
      
      jobLogger.info('Starting transcription job');
      
      // Download audio file from Azure
      const audioBuffer = await AzureStorageService.downloadFile(job.filename);
      if (!audioBuffer) {
        throw new Error(`Failed to download audio file: ${job.filename}`);
      }
      
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
      
      let transcriptionText: string;
      let transcriptionMethod: string;
      
      // Check if file needs chunking for large files
      if (needsChunking(audioBuffer)) {
        jobLogger.info('Using chunked transcription for large file');
        const chunkingResult = await transcribeWithChunking(audioBuffer, job.filename, openai);
        
        if (!chunkingResult.success || !chunkingResult.fullTranscription) {
          throw new Error(`Chunked transcription failed: ${chunkingResult.error}`);
        }
        
        transcriptionText = chunkingResult.fullTranscription;
        transcriptionMethod = 'chunked';
      } else {
        // Use direct transcription for smaller files
        jobLogger.info('Using direct transcription for regular file');
        
        const fileName = job.filename.split('/').pop() || job.filename;
        const fileObj = Object.assign(audioBuffer, {
          name: fileName,
          type: 'audio/mpeg'
        }) as any;
        
        const transcription = await openai.audio.transcriptions.create({
          file: fileObj,
          model: 'whisper-1',
          response_format: 'json',
          temperature: 0.2
        });
        
        transcriptionText = transcription.text;
        transcriptionMethod = 'direct';
      }
      
      // Save transcription to Azure storage
      const jsonPath = job.filename.replace(/\.[^/.]+$/, '.json');
      const transcriptionData = {
        transcription: transcriptionText,
        transcribedAt: new Date().toISOString(),
        model: 'whisper-1',
        fileName: job.filename,
        audioFileSize: audioBuffer.length,
        transcriptionLength: transcriptionText.length,
        transcriptionMethod
      };
      
      await AzureStorageService.uploadJson(jsonPath, transcriptionData);
      
      // Update job with transcription results
      await jobStore.updateJob(job.id, {
        results: {
          transcription: {
            text: transcriptionText,
            confidence: 0.95,
            processingTimeMs: Date.now() - new Date(job.startedAt || job.createdAt).getTime(),
            method: transcriptionMethod,
            savedTo: jsonPath
          }
        }
      });
      
      jobLogger.info('Transcription job completed successfully');
    } catch (error) {
      jobLogger.error('Transcription job failed', {}, error as Error);
      throw error;
    }
  }

  /**
   * Execute summarization job
   */
  private async executeSummarizationJob(job: ProcessingJob): Promise<void> {
    const jobLogger = createJobLogger(job.id, job.filename);
    
    try {
      const { OpenAI } = await import('openai');
      const { AzureStorageService } = await import('./azure-storage');
      
      jobLogger.info('Starting summarization job');
      
      // Get transcription from job results or from Azure storage
      let transcriptionText: string;
      
      if (job.results?.transcription?.text) {
        transcriptionText = job.results.transcription.text;
        jobLogger.info('Using transcription from job results');
      } else {
        // Load transcription from Azure storage
        const jsonPath = job.filename.replace(/\.[^/.]+$/, '.json');
        const transcriptionData = await AzureStorageService.downloadJson(jsonPath);
        
        if (!transcriptionData?.transcription) {
          throw new Error(`No transcription found for ${job.filename}`);
        }
        
        transcriptionText = transcriptionData.transcription;
        jobLogger.info('Loaded transcription from Azure storage');
      }
      
      if (!transcriptionText || transcriptionText.trim().length === 0) {
        throw new Error('Empty transcription text provided for summarization');
      }
      
      // Generate summary using OpenAI
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
      
      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that summarizes clinical notes. Provide concise, professional summaries focusing on key medical information, treatments, and recommendations.',
          },
          {
            role: 'user',
            content: `Please summarize the following clinical transcription:\n\n${transcriptionText}`,
          },
        ],
        temperature: 0.3,
        max_tokens: 500,
      });
      
      const summary = completion.choices[0]?.message?.content;
      
      if (!summary) {
        throw new Error('OpenAI returned empty summary');
      }
      
      // Save summary to Azure storage
      const jsonPath = job.filename.replace(/\.[^/.]+$/, '.json');
      
      try {
        const existingData = await AzureStorageService.downloadJson(jsonPath);
        const updatedData = {
          ...existingData,
          summary,
          summarizedAt: new Date().toISOString(),
          summaryModel: 'gpt-3.5-turbo'
        };
        await AzureStorageService.uploadJson(jsonPath, updatedData);
      } catch {
        // If no existing data, create new
        const summaryData = {
          summary,
          summarizedAt: new Date().toISOString(),
          summaryModel: 'gpt-3.5-turbo',
          transcription: transcriptionText,
          transcriptionLength: transcriptionText.length
        };
        await AzureStorageService.uploadJson(jsonPath, summaryData);
      }
      
      // Update job with summarization results
      await jobStore.updateJob(job.id, {
        results: {
          ...job.results,
          summarization: {
            summary,
            confidence: 0.9,
            processingTimeMs: Date.now() - new Date(job.startedAt || job.createdAt).getTime(),
            savedTo: jsonPath
          }
        }
      });
      
      jobLogger.info('Summarization job completed successfully');
    } catch (error) {
      jobLogger.error('Summarization job failed', {}, error as Error);
      throw error;
    }
  }

  /**
   * Execute full processing job (transcription + summarization)
   */
  private async executeFullProcessingJob(job: ProcessingJob): Promise<void> {
    const jobLogger = createJobLogger(job.id, job.filename);
    
    try {
      jobLogger.info('Starting full processing job (transcription + summarization)');
      
      // Execute transcription first
      await this.executeTranscriptionJob(job);
      
      // Reload job to get updated results from transcription
      const updatedJob = await jobStore.getJob(job.id);
      if (!updatedJob) {
        throw new Error(`Job ${job.id} not found after transcription`);
      }
      
      // Execute summarization with updated job data
      await this.executeSummarizationJob(updatedJob);
      
      jobLogger.info('Full processing job completed successfully');
    } catch (error) {
      jobLogger.error('Full processing job failed', {}, error as Error);
      throw error;
    }
  }

  /**
   * Handle job errors and determine retry strategy
   */
  private async handleJobError(job: ProcessingJob, error: Error): Promise<void> {
    const jobLogger = createJobLogger(job.id, job.filename);
    const errorInfo = ErrorHandler.classifyError(error);
    
    const errorDetails: JobErrorDetails = {
      code: errorInfo.code || 'UNKNOWN',
      message: error.message,
      category: this.mapErrorToCategory(errorInfo.type),
      retryable: errorInfo.retryable,
      stackTrace: error.stack,
      context: {
        operation: 'job_execution',
        jobId: job.id,
        filename: job.filename,
        retryCount: job.retryCount
      }
    };

    if (errorInfo.retryable && job.retryCount < this.defaultRetryConfig.maxRetries) {
      // Mark for retry
      await jobStore.updateJob(job.id, {
        status: 'retrying',
        errorDetails,
        retryCount: job.retryCount + 1,
        updatedAt: new Date().toISOString()
      });

      jobLogger.warn('Job failed, will retry', {
        jobId: job.id,
        retryCount: job.retryCount + 1,
        errorType: errorInfo.type
      });

      // Schedule retry
      await this.retryFailedJob(job.id);
    } else {
      // Mark as permanently failed
      await jobStore.updateJob(job.id, {
        status: 'failed',
        errorDetails,
        completedAt: new Date().toISOString()
      });

      jobLogger.error('Job permanently failed', {
        jobId: job.id,
        retryCount: job.retryCount,
        errorType: errorInfo.type
      });
    }
  }

  /**
   * Map error types to job error categories
   */
  private mapErrorToCategory(errorType: string): JobErrorDetails['category'] {
    const mapping: Record<string, JobErrorDetails['category']> = {
      'NetworkError': 'network_error',
      'TimeoutError': 'timeout_error',
      'QuotaExceededError': 'quota_exceeded',
      'FileNotFoundError': 'file_not_found',
      'ServiceUnavailableError': 'api_error',
      'ValidationError': 'validation_error',
      'ConfigurationError': 'configuration_error',
      'StorageError': 'storage_error'
    };

    return mapping[errorType] || 'system_error';
  }

  /**
   * Start background processing loop
   */
  private startProcessing(): void {
    // Process queue every 5 seconds
    this.processingInterval = setInterval(async () => {
      try {
        await this.processNextJob();
      } catch (error) {
        logger.error('Error in processing loop', {}, error as Error);
      }
    }, 5000);

    logger.info('Job queue processing started');
  }

  /**
   * Start periodic cleanup scheduler
   */
  private startCleanupScheduler(): void {
    // Run cleanup every hour
    this.cleanupInterval = setInterval(async () => {
      try {
        await this.cleanupOldJobs();
      } catch (error) {
        logger.error('Error in cleanup scheduler', {}, error as Error);
      }
    }, 60 * 60 * 1000);

    logger.info('Job queue cleanup scheduler started');
  }

  /**
   * Calculate average processing time from completed jobs
   */
  private calculateAverageProcessingTime(completedJobs: ProcessingJob[]): number {
    if (completedJobs.length === 0) return 0;

    const validJobs = completedJobs.filter(job => 
      job.startedAt && job.completedAt
    );

    if (validJobs.length === 0) return 0;

    const totalTime = validJobs.reduce((sum, job) => {
      const startTime = new Date(job.startedAt!).getTime();
      const endTime = new Date(job.completedAt!).getTime();
      return sum + (endTime - startTime);
    }, 0);

    return Math.round(totalTime / validJobs.length);
  }

  /**
   * Get count of stuck jobs
   */
  private async getStuckJobsCount(): Promise<number> {
    try {
      const stuckJobs = await jobStore.getStuckJobs();
      return stuckJobs.length;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Determine overall queue health
   */
  private determineQueueHealth(stats: any, stuckJobs: number): JobQueueStats['queueHealth'] {
    // Calculate failure rate
    const totalProcessed = stats.completed + stats.failed;
    const failureRate = totalProcessed > 0 ? stats.failed / totalProcessed : 0;

    // Health checks
    if (stuckJobs > 10 || failureRate > 0.5) {
      return 'unhealthy';
    } else if (stuckJobs > 5 || failureRate > 0.2) {
      return 'degraded';
    } else {
      return 'healthy';
    }
  }
}

// Export singleton instance
export const jobQueueService = JobQueueService.getInstance();

// Export types
export type { JobQueueStats, JobEnqueueOptions, JobRetryConfig };