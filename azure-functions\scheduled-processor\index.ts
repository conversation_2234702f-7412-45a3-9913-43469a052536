import { app, InvocationContext, Timer } from "@azure/functions";
import { BlobServiceClient } from "@azure/storage-blob";

const AZURE_STORAGE_CONNECTION_STRING = process.env.AZURE_STORAGE_CONNECTION_STRING!;
const CONTAINER_NAME = 'recordings';

/**
 * Scheduled Azure Function that runs every 10 minutes to:
 * 1. Process any missed files (in case blob trigger failed)
 * 2. Retry failed transcriptions (non-permanent failures)
 * 3. Update processing statistics
 * 4. Clean up old temporary files
 */
export async function scheduledProcessor(myTimer: Timer, context: InvocationContext): Promise<void> {
  const timestamp = new Date().toISOString();
  context.log(`🔄 Scheduled processor started at: ${timestamp}`);

  try {
    const blobServiceClient = BlobServiceClient.fromConnectionString(AZURE_STORAGE_CONNECTION_STRING);
    const containerClient = blobServiceClient.getContainerClient(CONTAINER_NAME);

    // 1. Scan for unprocessed audio files
    await scanForUnprocessedFiles(context, containerClient);

    // 2. Update processing statistics for the UI
    await updateProcessingStatistics(context, containerClient);

    // 3. Retry failed transcriptions (if they're retryable)
    await retryFailedTranscriptions(context, containerClient);

    // 4. Clean up old temporary files (optional)
    await cleanupOldFiles(context, containerClient);

    context.log(`✅ Scheduled processor completed successfully`);

  } catch (error) {
    context.log(`❌ Scheduled processor failed:`, error);
  }
};

/**
 * Scan for audio files that don't have corresponding .json or .failure.json files
 */
async function scanForUnprocessedFiles(context: InvocationContext, containerClient: any): Promise<void> {
  context.log(`🔍 Scanning for unprocessed files...`);
  
  const audioFiles = [];
  const processedFiles = new Set();
  const failedFiles = new Set();

  // Get all blobs
  for await (const blob of containerClient.listBlobsFlat()) {
    const fileName = blob.name;
    
    if (isAudioFile(fileName)) {
      audioFiles.push(fileName);
    } else if (fileName.endsWith('.json') && !fileName.endsWith('.failure.json')) {
      const baseName = fileName.replace(/\.json$/, '');
      processedFiles.add(baseName);
    } else if (fileName.endsWith('.failure.json')) {
      const baseName = fileName.replace(/\.failure\.json$/, '');
      failedFiles.add(baseName);
    }
  }

  // Find unprocessed files
  const unprocessedFiles = audioFiles.filter(fileName => {
    const baseName = fileName.replace(/\.[^/.]+$/, '');
    return !processedFiles.has(baseName) && !failedFiles.has(baseName);
  });

  context.log(`📊 Processing status:`);
  context.log(`  - Total audio files: ${audioFiles.length}`);
  context.log(`  - Processed: ${processedFiles.size}`);
  context.log(`  - Failed (permanent): ${failedFiles.size}`);
  context.log(`  - Unprocessed: ${unprocessedFiles.length}`);

  // Trigger processing for unprocessed files (up to 5 at a time to avoid overwhelming)
  const filesToProcess = unprocessedFiles.slice(0, 5);
  
  for (const fileName of filesToProcess) {
    context.log(`🎵 Triggering processing for missed file: ${fileName}`);
    await triggerFileProcessing(context, fileName);
  }
}

/**
 * Update processing statistics for the UI dashboard
 */
async function updateProcessingStatistics(context: InvocationContext, containerClient: any): Promise<void> {
  const stats = {
    lastUpdated: new Date().toISOString(),
    totalFiles: 0,
    processed: 0,
    permanentlyFailed: 0,
    unprocessed: 0,
    processingQueue: 0,
    systemStatus: 'active',
    breakdown: {
      transcribed: 0,
      summarized: 0,
      complete: 0,
      validationFailed: 0,
      processingFailed: 0
    }
  };

  let audioFiles = 0;
  const processedFiles = new Set();
  const failedFiles = new Set();
  let completeFiles = 0;

  // Analyze all files
  for await (const blob of containerClient.listBlobsFlat()) {
    const fileName = blob.name;
    
    if (isAudioFile(fileName)) {
      audioFiles++;
      const baseName = fileName.replace(/\.[^/.]+$/, '');
      
      // Check for completion status
      const transcriptionFile = `${baseName}.json`;
      const failureFile = `${baseName}.failure.json`;
      
      try {
        const transcriptionExists = await checkFileExists(containerClient, transcriptionFile);
        const failureExists = await checkFileExists(containerClient, failureFile);
        
        if (transcriptionExists) {
          processedFiles.add(baseName);
          
          // Check if it has both transcription and summary
          const transcriptionBlob = containerClient.getBlobClient(transcriptionFile);
          const download = await transcriptionBlob.download(0);
          const content = await streamToBuffer(download.readableStreamBody);
          const data = JSON.parse(content.toString());
          
          if (data.transcription) stats.breakdown.transcribed++;
          if (data.summary) stats.breakdown.summarized++;
          if (data.transcription && data.summary) {
            stats.breakdown.complete++;
            completeFiles++;
          }
        } else if (failureExists) {
          failedFiles.add(baseName);
          
          // Categorize failure type
          const failureBlob = containerClient.getBlobClient(failureFile);
          const download = await failureBlob.download(0);
          const content = await streamToBuffer(download.readableStreamBody);
          const failureData = JSON.parse(content.toString());
          
          if (failureData.failureType === 'validation_failed') {
            stats.breakdown.validationFailed++;
          } else {
            stats.breakdown.processingFailed++;
          }
        }
      } catch (error) {
        // File might be currently being processed or there was an error
      }
    }
  }

  stats.totalFiles = audioFiles;
  stats.processed = processedFiles.size;
  stats.permanentlyFailed = failedFiles.size;
  stats.unprocessed = audioFiles - processedFiles.size - failedFiles.size;

  // Save statistics to blob storage for UI consumption
  const statsFileName = '_processing_stats.json';
  await uploadJsonToBlob(containerClient, statsFileName, stats);
  
  context.log(`📊 Updated processing statistics: ${stats.processed}/${stats.totalFiles} processed, ${stats.unprocessed} pending`);
}

/**
 * Retry failed transcriptions that are marked as retryable
 */
async function retryFailedTranscriptions(context: InvocationContext, containerClient: any): Promise<void> {
  context.log(`🔄 Checking for retryable failures...`);
  
  // Implementation would check for temporary failures and retry them
  // For now, we focus on permanent failures which shouldn't be retried
}

/**
 * Clean up old temporary files
 */
async function cleanupOldFiles(context: InvocationContext, containerClient: any): Promise<void> {
  // Clean up any temporary files older than 24 hours
  // This is optional and can be implemented later
}

/**
 * Helper functions (same as in blob trigger)
 */
function isAudioFile(fileName: string): boolean {
  const audioExtensions = ['.mp3', '.wav', '.m4a', '.mp4', '.webm', '.ogg', '.aac', '.flac'];
  const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return audioExtensions.includes(ext);
}

async function checkFileExists(containerClient: any, fileName: string): Promise<boolean> {
  try {
    const blobClient = containerClient.getBlobClient(fileName);
    return await blobClient.exists();
  } catch {
    return false;
  }
}

async function streamToBuffer(readableStream: NodeJS.ReadableStream): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    readableStream.on('data', (data) => {
      chunks.push(data instanceof Buffer ? data : Buffer.from(data));
    });
    readableStream.on('end', () => {
      resolve(Buffer.concat(chunks));
    });
    readableStream.on('error', reject);
  });
}

async function uploadJsonToBlob(containerClient: any, fileName: string, data: any): Promise<void> {
  const blobClient = containerClient.getBlockBlobClient(fileName);
  const jsonContent = JSON.stringify(data, null, 2);
  await blobClient.upload(jsonContent, jsonContent.length, {
    blobHTTPHeaders: { blobContentType: 'application/json' }
  });
}

async function triggerFileProcessing(context: InvocationContext, fileName: string): Promise<void> {
  try {
    const transcriptionUrl = process.env.TRANSCRIPTION_API_URL || 'https://dentalapp-5va08j5gu-suncoastdcs-projects.vercel.app/api/voice/transcribe';
    
    const response = await fetch(transcriptionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Internal-Call': 'true'
      },
      body: JSON.stringify({
        fileName: fileName,
        forceRetranscribe: false
      })
    });

    if (response.ok) {
      context.log(`✅ Successfully triggered processing for: ${fileName}`);
    } else {
      context.log(`⚠️ Failed to trigger processing for: ${fileName} - ${response.status}`);
    }
  } catch (error) {
    context.log(`❌ Error triggering processing for ${fileName}:`, error);
  }
}

// Register the function with Azure Functions v4
app.timer('scheduledProcessor', {
  schedule: '0 */10 * * * *', // Every 10 minutes
  handler: scheduledProcessor
});