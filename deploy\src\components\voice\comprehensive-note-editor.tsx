"use client";

import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Mic, 
  Plus, 
  <PERSON>rkles, 
  <PERSON><PERSON>, 
  Check, 
  X, 
  <PERSON><PERSON>reshC<PERSON>,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronUp,
  Stethoscope,
  User,
  MapPin,
  Calendar
} from 'lucide-react';

interface ProcedureCode {
  code: string;
  description: string;
}

interface OriginalNote {
  id: string;
  text: string;
  provider: string;
  date: string;
  type: string;
}

interface Recording {
  id: string;
  name: string;
  transcription: string;
  duration: number;
  isEdited: boolean;
}

interface AppointmentData {
  appointmentId: string;
  patientName: string;
  appointmentDate: string;
  appointmentType: string;
  provider: string;
  operatory: string;
  procedureCodes: ProcedureCode[];
  originalNotes: OriginalNote[];
  recordings: Recording[];
}

interface ComprehensiveNoteEditorProps {
  appointmentData: AppointmentData;
  isDarkMode?: boolean;
  isMobile?: boolean;
  onSave?: (noteData: any) => void;
  onClose?: () => void;
}

export function ComprehensiveNoteEditor({
  appointmentData,
  isDarkMode = false,
  isMobile = false,
  onSave,
  onClose
}: ComprehensiveNoteEditorProps) {
  // Safety check for appointmentData
  if (!appointmentData) {
    return (
      <div className={`p-6 text-center ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
        <p>No appointment data available.</p>
        {onClose && (
          <button
            onClick={onClose}
            className="mt-4 px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Close
          </button>
        )}
      </div>
    );
  }
  // State for editable content
  const [editedNotes, setEditedNotes] = useState('');
  const [editedTranscriptions, setEditedTranscriptions] = useState<Record<string, string>>({});
  const [additionalNotes, setAdditionalNotes] = useState('');
  const [professionalNote, setProfessionalNote] = useState('');
  
  // State for workflow
  const [isGenerating, setIsGenerating] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [isAccepted, setIsAccepted] = useState(false);
  const [rejectionFeedback, setRejectionFeedback] = useState<string[]>([]);
  const [customFeedback, setCustomFeedback] = useState('');
  
  // State for mobile toggles
  const [includeOriginalNotes, setIncludeOriginalNotes] = useState(true);
  const [includeRecordings, setIncludeRecordings] = useState<Record<string, boolean>>({});
  
  // State for UI
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    procedures: true,
    originalNotes: true,
    recordings: true,
    additional: true
  });

  // Initialize state
  useEffect(() => {
    // Combine original notes
    const combinedNotes = appointmentData.originalNotes
      .map(note => `${note.provider} (${note.date}):\n${note.text}`)
      .join('\n\n');
    setEditedNotes(combinedNotes);

    // Initialize transcription edits
    const transcriptionEdits: Record<string, string> = {};
    appointmentData.recordings.forEach(recording => {
      transcriptionEdits[recording.id] = recording.transcription || '';
    });
    setEditedTranscriptions(transcriptionEdits);

    // Initialize recording includes for mobile
    const recordingIncludes: Record<string, boolean> = {};
    appointmentData.recordings.forEach(recording => {
      recordingIncludes[recording.id] = true;
    });
    setIncludeRecordings(recordingIncludes);
  }, [appointmentData]);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const generateProfessionalNote = async () => {
    setIsGenerating(true);
    try {
      // Prepare transcriptions data with validation
      const transcriptions = Object.entries(editedTranscriptions)
        .filter(([id, transcription]) => includeRecordings[id] && transcription?.trim())
        .map(([id, transcription]) => ({
          recordingName: appointmentData.recordings.find(r => r.id === id)?.name || id,
          transcription: transcription.trim()
        }));

      console.log('Generating professional note with data:', {
        patientName: appointmentData.patientName,
        transcriptionsCount: transcriptions.length,
        hasOriginalNotes: includeOriginalNotes && editedNotes.trim(),
        procedureCodesCount: appointmentData.procedureCodes.length
      });

      // Prepare data for AI
      const noteData = {
        appointmentDetails: {
          patientName: appointmentData.patientName,
          date: appointmentData.appointmentDate,
          provider: appointmentData.provider,
          operatory: appointmentData.operatory,
          appointmentType: appointmentData.appointmentType
        },
        procedureCodes: appointmentData.procedureCodes,
        originalNotes: includeOriginalNotes ? editedNotes.trim() : '',
        transcriptions,
        additionalNotes: additionalNotes.trim(),
        rejectionFeedback: rejectionFeedback.length > 0 ? rejectionFeedback : undefined,
        customFeedback: customFeedback?.trim() || undefined
      };

      // Validate that we have some content to work with
      if (transcriptions.length === 0 && !noteData.originalNotes && !noteData.additionalNotes) {
        alert('Please include at least one voice recording, original notes, or additional notes to generate a professional note.');
        return;
      }

      const response = await fetch('/api/voice/generate-professional-note', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(noteData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('API Error:', response.status, errorData);
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      const result = await response.json();

      if (!result.professionalNote) {
        throw new Error('No professional note was generated');
      }

      setProfessionalNote(result.professionalNote);
      setShowPreview(true);
      setIsAccepted(false);

    } catch (error) {
      console.error('Error generating professional note:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      alert(`Failed to generate professional note: ${errorMessage}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const acceptNote = async () => {
    setIsAccepted(true);
    
    // Mark as copied to chart
    try {
      await fetch('/api/voice/mark-copied', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          appointmentId: appointmentData.appointmentId,
          patientName: appointmentData.patientName,
          noteContent: professionalNote
        })
      });
    } catch (error) {
      console.error('Error marking note as copied:', error);
    }

    // Copy to clipboard
    try {
      await navigator.clipboard.writeText(professionalNote);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }

    // Save the note data
    if (onSave) {
      onSave({
        appointmentId: appointmentData.appointmentId,
        editedNotes,
        editedTranscriptions,
        additionalNotes,
        professionalNote,
        isAccepted: true,
        lastModified: new Date().toISOString()
      });
    }
  };


  const rejectNote = () => {
    setShowPreview(false);
    setIsAccepted(false);
    // Keep rejection feedback for next generation
  };

  const handleRejectionFeedback = (feedback: string, checked: boolean) => {
    if (checked) {
      setRejectionFeedback(prev => [...prev, feedback]);
    } else {
      setRejectionFeedback(prev => prev.filter(f => f !== feedback));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isMobile) {
    return (
      <div className={`p-4 space-y-4 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
        {/* Mobile Header */}
        <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-lg font-semibold">{appointmentData.patientName}</h2>
            {onClose && (
              <button
                onClick={onClose}
                className="p-1 text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {formatDate(appointmentData.appointmentDate)}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {appointmentData.provider} • {appointmentData.operatory}
          </div>
        </div>

        {/* Mobile Procedure Codes */}
        {appointmentData.procedureCodes.length > 0 && (
          <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <h3 className="font-medium mb-2 flex items-center">
              <Stethoscope className="h-4 w-4 mr-2 text-blue-600" />
              Procedures ({appointmentData.procedureCodes.length})
            </h3>
            <div className="space-y-1">
              {appointmentData.procedureCodes.map((proc, index) => (
                <div key={index} className="text-xs">
                  <span className="font-mono text-blue-600">{proc.code}</span>
                  <span className="text-gray-600 dark:text-gray-400 ml-2">{proc.description}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Mobile Content Toggles */}
        <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <h3 className="font-medium mb-3">Include in Professional Note:</h3>
          
          {/* Original Notes Toggle */}
          {appointmentData.originalNotes.length > 0 && (
            <div className="flex items-center justify-between py-2">
              <span className="text-sm">Original Notes ({appointmentData.originalNotes.length})</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={includeOriginalNotes}
                  onChange={(e) => setIncludeOriginalNotes(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          )}

          {/* Recording Toggles */}
          {appointmentData.recordings.map((recording) => (
            <div key={recording.id} className="flex items-center justify-between py-2">
              <span className="text-sm">{recording.name}</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={includeRecordings[recording.id] || false}
                  onChange={(e) => setIncludeRecordings(prev => ({
                    ...prev,
                    [recording.id]: e.target.checked
                  }))}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          ))}
        </div>

        {/* Mobile Additional Notes */}
        <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <h3 className="font-medium mb-2">Additional Notes</h3>
          <textarea
            value={additionalNotes}
            onChange={(e) => setAdditionalNotes(e.target.value)}
            placeholder="Add any additional notes..."
            className={`w-full h-20 p-2 text-sm rounded border resize-none ${
              isDarkMode 
                ? 'bg-gray-900 border-gray-600 text-gray-100' 
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          />
        </div>

        {/* Mobile Generate Button */}
        <button
          onClick={generateProfessionalNote}
          disabled={isGenerating}
          className="w-full flex items-center justify-center space-x-2 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {isGenerating ? (
            <>
              <RefreshCw className="h-4 w-4 animate-spin" />
              <span>Generating...</span>
            </>
          ) : (
            <>
              <Sparkles className="h-4 w-4" />
              <span>Generate Professional Note</span>
            </>
          )}
        </button>

        {/* Mobile Preview/Accept/Reject */}
        {showPreview && (
          <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            <h3 className="font-medium mb-2">Professional Note Preview</h3>
            <div className={`p-3 rounded border text-sm whitespace-pre-wrap ${isDarkMode ? 'bg-gray-900 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
              {professionalNote}
            </div>
            
            {!isAccepted && (
              <div className="mt-4 space-y-3">
                <div className="flex space-x-2">
                  <button
                    onClick={acceptNote}
                    className="flex-1 flex items-center justify-center space-x-1 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    <Check className="h-4 w-4" />
                    <span>Accept & Copy</span>
                  </button>
                  <button
                    onClick={rejectNote}
                    className="flex-1 flex items-center justify-center space-x-1 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    <X className="h-4 w-4" />
                    <span>Reject</span>
                  </button>
                </div>
                
                {/* Mobile Rejection Feedback */}
                <div className="space-y-2">
                  <div className="text-sm font-medium">What needs improvement?</div>
                  {['Too brief', 'Missing procedure details', 'Unclear language', 'Missing clinical findings'].map((feedback) => (
                    <label key={feedback} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={rejectionFeedback.includes(feedback)}
                        onChange={(e) => handleRejectionFeedback(feedback, e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-sm">{feedback}</span>
                    </label>
                  ))}
                  <textarea
                    value={customFeedback}
                    onChange={(e) => setCustomFeedback(e.target.value)}
                    placeholder="Other feedback..."
                    className={`w-full h-16 p-2 text-sm rounded border resize-none ${
                      isDarkMode 
                        ? 'bg-gray-900 border-gray-600 text-gray-100' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>
              </div>
            )}

            {isAccepted && (
              <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <div className="flex items-center space-x-2 text-green-700 dark:text-green-300">
                  <Check className="h-4 w-4" />
                  <span className="text-sm font-medium">Note accepted and copied to clipboard!</span>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  // Desktop version
  return (
    <div className={`p-6 space-y-6 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
      {/* Desktop Header */}
      <div className={`p-6 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-semibold">{appointmentData.patientName}</h2>
            <p className="text-gray-600 dark:text-gray-400">{formatDate(appointmentData.appointmentDate)}</p>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className={`px-4 py-2 rounded transition-colors ${
                isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Close
            </button>
          )}
        </div>

        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <User className="h-4 w-4 text-blue-600" />
            <span>{appointmentData.provider}</span>
          </div>
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 text-green-600" />
            <span>{appointmentData.operatory}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-purple-600" />
            <span>{appointmentData.appointmentType}</span>
          </div>
        </div>
      </div>

      {/* Desktop Procedure Codes */}
      {appointmentData.procedureCodes.length > 0 && (
        <div className={`p-6 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <button
            onClick={() => toggleSection('procedures')}
            className="flex items-center justify-between w-full mb-4"
          >
            <h3 className="text-lg font-medium flex items-center">
              <Stethoscope className="h-5 w-5 mr-2 text-blue-600" />
              Procedures Completed ({appointmentData.procedureCodes.length})
            </h3>
            {expandedSections.procedures ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </button>

          {expandedSections.procedures && (
            <div className="space-y-4">
              {/* Completed Procedures */}
              {appointmentData.procedureCodes.filter((proc: any) => proc.type === 'completed').length > 0 && (
                <div>
                  <div className="text-sm font-medium text-green-600 dark:text-green-400 mb-2">
                    ✅ Completed Procedures:
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {appointmentData.procedureCodes
                      .filter((proc: any) => proc.type === 'completed')
                      .map((proc: any, index: number) => (
                        <div key={`completed-${index}`} className={`p-3 rounded border ${isDarkMode ? 'bg-green-900/20 border-green-600' : 'bg-green-50 border-green-200'}`}>
                          <div className="font-mono text-green-600 font-medium">{proc.code}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">{proc.description}</div>
                          {proc.amount && (
                            <div className="text-xs text-green-600 dark:text-green-400 mt-1">
                              Amount: ${proc.amount}
                            </div>
                          )}
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* Scheduled Procedures */}
              {appointmentData.procedureCodes.filter((proc: any) => proc.type === 'scheduled').length > 0 && (
                <div>
                  <div className="text-sm font-medium text-blue-600 dark:text-blue-400 mb-2">
                    📅 Scheduled Procedures:
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {appointmentData.procedureCodes
                      .filter((proc: any) => proc.type === 'scheduled')
                      .map((proc: any, index: number) => (
                        <div key={`scheduled-${index}`} className={`p-3 rounded border ${isDarkMode ? 'bg-blue-900/20 border-blue-600' : 'bg-blue-50 border-blue-200'}`}>
                          <div className="font-mono text-blue-600 font-medium">{proc.code}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">{proc.description}</div>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* Legacy procedures (no type specified) */}
              {appointmentData.procedureCodes.filter((proc: any) => !proc.type).length > 0 && (
                <div>
                  <div className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                    📋 Procedures:
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {appointmentData.procedureCodes
                      .filter((proc: any) => !proc.type)
                      .map((proc: any, index: number) => (
                        <div key={`legacy-${index}`} className={`p-3 rounded border ${isDarkMode ? 'bg-gray-900 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                          <div className="font-mono text-gray-600 font-medium">{proc.code}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">{proc.description}</div>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Desktop Original Notes */}
      {appointmentData.originalNotes.length > 0 && (
        <div className={`p-6 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <button
            onClick={() => toggleSection('originalNotes')}
            className="flex items-center justify-between w-full mb-4"
          >
            <h3 className="text-lg font-medium flex items-center">
              <FileText className="h-5 w-5 mr-2 text-green-600" />
              Original Notes from Dentrix ({appointmentData.originalNotes.length})
            </h3>
            {expandedSections.originalNotes ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </button>

          {expandedSections.originalNotes && (
            <div className="space-y-4">
              {/* Read-only original notes */}
              <div>
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Original (Read-only):</div>
                <div className={`p-3 rounded border ${isDarkMode ? 'bg-gray-900 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                  {appointmentData.originalNotes.map((note, index) => (
                    <div key={index} className="mb-3 last:mb-0">
                      <div className="text-xs text-gray-500 mb-1">
                        {note.provider} • {note.date} • {note.type}
                      </div>
                      <div className="text-sm whitespace-pre-wrap">{note.text}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Editable notes */}
              <div>
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">Edit Clinical Notes:</div>
                <textarea
                  value={editedNotes}
                  onChange={(e) => setEditedNotes(e.target.value)}
                  className={`w-full h-32 p-3 rounded border resize-y ${
                    isDarkMode
                      ? 'bg-gray-900 border-gray-600 text-gray-100'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  placeholder="Edit the clinical notes as needed..."
                />
              </div>
            </div>
          )}
        </div>
      )}

      {/* Desktop Recordings */}
      {appointmentData.recordings.length > 0 && (
        <div className={`p-6 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <button
            onClick={() => toggleSection('recordings')}
            className="flex items-center justify-between w-full mb-4"
          >
            <h3 className="text-lg font-medium flex items-center">
              <Mic className="h-5 w-5 mr-2 text-orange-600" />
              Voice Recordings ({appointmentData.recordings.length})
            </h3>
            {expandedSections.recordings ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </button>

          {expandedSections.recordings && (
            <div className="space-y-4">
              {appointmentData.recordings.map((recording) => (
                <div key={recording.id} className={`p-4 rounded border ${isDarkMode ? 'bg-gray-900 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-medium">{recording.name}</div>
                    <div className="text-xs text-gray-500">
                      {Math.floor(recording.duration / 60)}:{(recording.duration % 60).toString().padStart(2, '0')}
                    </div>
                  </div>
                  <textarea
                    value={editedTranscriptions[recording.id] || ''}
                    onChange={(e) => setEditedTranscriptions(prev => ({
                      ...prev,
                      [recording.id]: e.target.value
                    }))}
                    className={`w-full h-24 p-3 rounded border resize-y ${
                      isDarkMode
                        ? 'bg-gray-800 border-gray-600 text-gray-100'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                    placeholder="Edit transcription..."
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Desktop Additional Notes */}
      <div className={`p-6 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
        <button
          onClick={() => toggleSection('additional')}
          className="flex items-center justify-between w-full mb-4"
        >
          <h3 className="text-lg font-medium flex items-center">
            <Plus className="h-5 w-5 mr-2 text-purple-600" />
            Additional Notes
          </h3>
          {expandedSections.additional ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
        </button>

        {expandedSections.additional && (
          <textarea
            value={additionalNotes}
            onChange={(e) => setAdditionalNotes(e.target.value)}
            className={`w-full h-24 p-3 rounded border resize-y ${
              isDarkMode
                ? 'bg-gray-900 border-gray-600 text-gray-100'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
            placeholder="Add any additional notes, observations, or instructions..."
          />
        )}
      </div>

      {/* Desktop Generate Button */}
      <div className="flex justify-center">
        <button
          onClick={generateProfessionalNote}
          disabled={isGenerating}
          className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isGenerating ? (
            <>
              <RefreshCw className="h-5 w-5 animate-spin" />
              <span>Generating Professional Note...</span>
            </>
          ) : (
            <>
              <Sparkles className="h-5 w-5" />
              <span>Generate Professional Note</span>
            </>
          )}
        </button>
      </div>

      {/* Desktop Preview/Accept/Reject */}
      {showPreview && (
        <div className={`p-6 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <h3 className="text-lg font-medium mb-4">Professional Note Preview</h3>

          <div className={`p-4 rounded border mb-4 ${isDarkMode ? 'bg-gray-900 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
            <textarea
              value={professionalNote}
              onChange={(e) => setProfessionalNote(e.target.value)}
              className={`w-full h-48 p-3 rounded border resize-y ${
                isDarkMode
                  ? 'bg-gray-800 border-gray-600 text-gray-100'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            />
          </div>

          {!isAccepted && (
            <div className="space-y-4">
              <div className="flex space-x-4">
                <button
                  onClick={acceptNote}
                  className="flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                >
                  <Check className="h-4 w-4" />
                  <span>Accept & Copy to Clipboard</span>
                </button>
                <button
                  onClick={rejectNote}
                  className="flex items-center space-x-2 px-6 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  <X className="h-4 w-4" />
                  <span>Reject & Rework</span>
                </button>
              </div>

              {/* Desktop Rejection Feedback */}
              <div className={`p-4 rounded border ${isDarkMode ? 'bg-gray-900 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
                <div className="font-medium mb-3">What needs improvement?</div>
                <div className="grid grid-cols-2 gap-3 mb-3">
                  {['Too brief', 'Missing procedure details', 'Unclear language', 'Missing clinical findings', 'Too technical', 'Missing patient response'].map((feedback) => (
                    <label key={feedback} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={rejectionFeedback.includes(feedback)}
                        onChange={(e) => handleRejectionFeedback(feedback, e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-sm">{feedback}</span>
                    </label>
                  ))}
                </div>
                <textarea
                  value={customFeedback}
                  onChange={(e) => setCustomFeedback(e.target.value)}
                  placeholder="Additional feedback or specific instructions..."
                  className={`w-full h-20 p-3 rounded border resize-y ${
                    isDarkMode
                      ? 'bg-gray-800 border-gray-600 text-gray-100'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>
            </div>
          )}

          {isAccepted && (
            <div className="p-4 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <div className="flex items-center space-x-2 text-green-700 dark:text-green-300">
                <Check className="h-5 w-5" />
                <span className="font-medium">Professional note accepted and copied to clipboard!</span>
              </div>
              <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                You can now paste this note into Dentrix for the patient's chart.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
