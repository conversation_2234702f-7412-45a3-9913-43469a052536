import fs from 'fs';
import path from 'path';
import { VoiceRecordingsSQLiteDBInstance } from './voice-recordings-sqlite-db';

interface LegacyTranscription {
  [filename: string]: string;
}

interface LegacySummary {
  [filename: string]: string;
}

export async function migrateJsonToSQLite() {
  console.log('🔄 Starting migration from JSON to SQLite...');
  
  const dataDir = path.join(process.cwd(), 'data');
  const transcriptionsPath = path.join(dataDir, 'transcriptions.json');
  const summariesPath = path.join(dataDir, 'summaries.json');
  
  // Skip migration in serverless environments where JSON files won't exist
  if (process.env.VERCEL && !fs.existsSync(transcriptionsPath) && !fs.existsSync(summariesPath)) {
    console.log('⏭️ Skipping migration in serverless environment - no JSON files found');
    return { success: true, migratedCount: 0, skippedCount: 0, stats: { total: 0, pending: 0, transcribed: 0, summarized: 0, error: 0 } };
  }
  
  let migratedCount = 0;
  const skippedCount = 0;

  try {
    // Migrate transcriptions
    if (fs.existsSync(transcriptionsPath)) {
      console.log('📄 Found transcriptions.json, migrating...');
      const transcriptionsData = JSON.parse(fs.readFileSync(transcriptionsPath, 'utf8')) as LegacyTranscription;
      
      for (const [filename, transcription] of Object.entries(transcriptionsData)) {
        // Check if recording already exists in SQLite
        const existing = VoiceRecordingsSQLiteDBInstance.getRecordingByFilename(filename);
        
        if (existing) {
          // Update existing recording with transcription
          VoiceRecordingsSQLiteDBInstance.updateRecording(existing.id, {
            transcription,
            transcribed_at: existing.transcribed_at || new Date().toISOString(),
            status: 'transcribed'
          });
          console.log(`✅ Updated existing recording: ${filename}`);
        } else {
          // Create new recording from transcription
          VoiceRecordingsSQLiteDBInstance.createRecording({
            id: filename,
            filename,
            device_id: 'migrated',
            status: 'transcribed',
            transcription,
            transcribed_at: new Date().toISOString(),
            category: 'clinical',
            posted_to_dentrix: false,
            isUploaded: true
          });
          console.log(`✅ Created new recording from transcription: ${filename}`);
        }
        migratedCount++;
      }
    }

    // Migrate summaries
    if (fs.existsSync(summariesPath)) {
      console.log('📄 Found summaries.json, migrating...');
      const summariesData = JSON.parse(fs.readFileSync(summariesPath, 'utf8')) as LegacySummary;
      
      for (const [filename, summary] of Object.entries(summariesData)) {
        const existing = VoiceRecordingsSQLiteDBInstance.getRecordingByFilename(filename);
        
        if (existing) {
          // Update existing recording with summary
          VoiceRecordingsSQLiteDBInstance.updateRecording(existing.id, {
            clinical_summary: summary,
            status: 'summarized'
          });
          console.log(`✅ Updated existing recording with summary: ${filename}`);
        } else {
          // Create new recording from summary (this shouldn't happen often)
          VoiceRecordingsSQLiteDBInstance.createRecording({
            id: filename,
            filename,
            device_id: 'migrated',
            status: 'summarized',
            clinical_summary: summary,
            category: 'clinical',
            posted_to_dentrix: false,
            isUploaded: true
          });
          console.log(`⚠️ Created new recording from summary (no transcription): ${filename}`);
        }
        migratedCount++;
      }
    }

    // Get final statistics
    const stats = VoiceRecordingsSQLiteDBInstance.getStats();
    
    console.log('\n📊 Migration completed!');
    console.log(`✅ Migrated: ${migratedCount} records`);
    console.log(`⏭️ Skipped: ${skippedCount} records`);
    console.log('\n📈 Database Statistics:');
    console.log(`   Total: ${stats.total}`);
    console.log(`   Pending: ${stats.pending}`);
    console.log(`   Transcribed: ${stats.transcribed}`);
    console.log(`   Summarized: ${stats.summarized}`);
    console.log(`   Error: ${stats.error}`);

    // Create backup of old JSON files
    if (fs.existsSync(transcriptionsPath)) {
      const backupPath = path.join(dataDir, 'transcriptions.json.backup');
      fs.copyFileSync(transcriptionsPath, backupPath);
      console.log(`💾 Backed up transcriptions.json to: ${backupPath}`);
    }

    if (fs.existsSync(summariesPath)) {
      const backupPath = path.join(dataDir, 'summaries.json.backup');
      fs.copyFileSync(summariesPath, backupPath);
      console.log(`💾 Backed up summaries.json to: ${backupPath}`);
    }

    return {
      success: true,
      migratedCount,
      skippedCount,
      stats
    };

  } catch (error) {
    console.error('❌ Migration failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Auto-run migration if this file is executed directly
if (require.main === module) {
  migrateJsonToSQLite()
    .then(result => {
      if (result.success) {
        console.log('🎉 Migration completed successfully!');
        process.exit(0);
      } else {
        console.error('💥 Migration failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Migration error:', error);
      process.exit(1);
    });
} 