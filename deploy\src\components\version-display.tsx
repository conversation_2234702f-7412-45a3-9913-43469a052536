'use client';

import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import { VERSION_INFO } from '../lib/version';

interface VersionDisplayProps {
  className?: string;
}

export function VersionDisplay({ className = '' }: VersionDisplayProps) {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [buildInfo, setBuildInfo] = useState<{
    version: string;
    buildDate: string;
    buildTime: string;
    buildNumber: string;
    commitHash?: string;
  } | null>(null);

  useEffect(() => {
    setMounted(true);
    setBuildInfo(VERSION_INFO);
  }, []);

  if (!mounted || !buildInfo) {
    return null;
  }

  const isDarkMode = theme === 'dark';

  return (
    <div className={`
      fixed bottom-2 right-2 z-50
      ${isDarkMode ? 'bg-gray-800/80 text-gray-300' : 'bg-white/80 text-gray-600'}
      backdrop-blur-sm border rounded-lg px-3 py-1.5 text-xs
      ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}
      shadow-sm hover:shadow-md transition-shadow duration-200
      ${className}
    `}>
      <div className="flex items-center space-x-2">
        <span className="font-medium">v{buildInfo.version}</span>
        <span className="opacity-60">•</span>
        <span className="opacity-75">#{buildInfo.buildNumber}</span>
        <span className="opacity-60">•</span>
        <span className="opacity-75">{buildInfo.buildDate}</span>
        <span className="opacity-60">•</span>
        <span className="opacity-75 font-mono">{buildInfo.buildTime}</span>
        {buildInfo.commitHash && buildInfo.commitHash !== 'dev' && (
          <>
            <span className="opacity-60">•</span>
            <span className="opacity-75 font-mono">{buildInfo.commitHash.substring(0, 7)}</span>
          </>
        )}
      </div>
    </div>
  );
}
