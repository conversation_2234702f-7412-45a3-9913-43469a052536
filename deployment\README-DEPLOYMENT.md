# Dental App Deployment Guide

## 🎯 Deployment Strategy: USB + Remote Updates

### **Initial Setup: USB Transfer**
- Copy deployment package to USB stick
- Transfer to office PC (C:\DentalApp\)
- Run setup scripts
- App runs as Windows Service

### **Future Updates: Remote**
- Push code to GitHub
- Run remote update script
- Automatic deployment and restart

### **Backup Option: USB Updates**
- If remote fails, use USB stick
- Copy new app files
- Run update script

---

## 📁 USB Deployment Package Structure

```
USB:\DentalApp-Deploy\
├── 📄 INSTALL.bat                 ← Double-click to install everything
├── 📄 README-INSTALL.txt          ← Simple installation instructions
├── 📁 app\                        ← Complete dental application
├── 📁 scripts\                    ← Management scripts
│   ├── setup.bat                  ← Initial setup
│   ├── start-service.bat          ← Start the service
│   ├── stop-service.bat           ← Stop the service
│   ├── restart-service.bat        ← Restart the service
│   ├── update-remote.bat          ← Update from GitHub
│   ├── update-usb.bat             ← Update from USB
│   ├── backup.bat                 ← Backup all data
│   ├── status.bat                 ← Check service status
│   └── logs.bat                   ← View logs
├── 📁 config\                     ← Configuration files
│   ├── ecosystem.config.js        ← PM2 service config
│   ├── .env.production            ← Environment variables
│   └── package.json               ← Dependencies
└── 📁 tools\                      ← Required tools
    ├── node-installer.msi          ← Node.js installer
    └── git-installer.exe           ← Git installer (for remote updates)
```

---

## 🚀 Installation Process

### **Step 1: Prepare USB Stick**
1. Copy entire `DentalApp-Deploy` folder to USB
2. Safely eject USB

### **Step 2: Install on Office PC**
1. Insert USB into office PC
2. Copy `DentalApp-Deploy` to `C:\`
3. Rename to `C:\DentalApp\`
4. Right-click `INSTALL.bat` → "Run as Administrator"
5. Follow prompts (installs Node.js, PM2, starts service)

### **Step 3: Verify Installation**
1. Open browser to `http://localhost:3000`
2. Should see dental app running
3. Check `C:\DentalApp\logs\` for any issues

---

## 🔄 Update Methods

### **Method 1: Remote Update (Preferred)**
```batch
# From your development machine
git push origin main

# On office PC (or remotely)
C:\DentalApp\scripts\update-remote.bat
```

### **Method 2: USB Update (Backup)**
```batch
# Copy new app files to USB:\DentalApp-Update\
# On office PC
C:\DentalApp\scripts\update-usb.bat
```

---

## 🛠️ Management Commands

### **Service Management**
- **Start:** `C:\DentalApp\scripts\start-service.bat`
- **Stop:** `C:\DentalApp\scripts\stop-service.bat`
- **Restart:** `C:\DentalApp\scripts\restart-service.bat`
- **Status:** `C:\DentalApp\scripts\status.bat`

### **Maintenance**
- **View Logs:** `C:\DentalApp\scripts\logs.bat`
- **Backup Data:** `C:\DentalApp\scripts\backup.bat`
- **Update Remote:** `C:\DentalApp\scripts\update-remote.bat`
- **Update USB:** `C:\DentalApp\scripts\update-usb.bat`

---

## 📊 Monitoring & Troubleshooting

### **Check if App is Running**
1. Open browser to `http://localhost:3000`
2. Or run `C:\DentalApp\scripts\status.bat`

### **View Logs**
1. Run `C:\DentalApp\scripts\logs.bat`
2. Or check `C:\DentalApp\logs\` folder

### **Common Issues**
- **Port 3000 in use:** Change PORT in `.env.production`
- **Service won't start:** Check logs, restart as admin
- **Network share issues:** Check Dentrix server connection

---

## 🔐 Security & Network

### **Network Access**
- **Internal:** `http://office-pc-ip:3000`
- **Your Desk:** `http://localhost:3000`
- **Domain Users:** Automatic authentication

### **Data Storage**
- **Voice Recordings:** `\\***********\share\RECORDINGS`
- **Local Backup:** `C:\DentalApp\data\voice-recordings`
- **Database:** `C:\DentalApp\data\database.sqlite`
- **Logs:** `C:\DentalApp\logs\`

### **Backup Strategy**
- **Daily Auto-Backup:** To `C:\DentalApp\backups\`
- **Manual Backup:** Run `backup.bat`
- **USB Backup:** Copy entire `C:\DentalApp\data\` folder

---

## 📞 Support

### **Self-Service**
1. Check logs: `logs.bat`
2. Restart service: `restart-service.bat`
3. Check status: `status.bat`

### **Remote Support**
- TeamViewer/Remote Desktop access
- Check GitHub for latest updates
- Review deployment logs

### **Emergency Recovery**
1. Stop service: `stop-service.bat`
2. Restore from backup
3. Start service: `start-service.bat`

---

## 🎯 Next Steps

1. **Create USB deployment package**
2. **Test on development machine**
3. **Deploy to office PC**
4. **Set up remote access**
5. **Configure automated backups**
6. **Test update procedures**
