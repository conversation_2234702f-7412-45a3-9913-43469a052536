import { NextRequest, NextResponse } from 'next/server';
import { SikkaApiClient } from '@/lib/api/sikka-client';
import { loadCredentials } from '@/lib/api/credentials';
import OpenAI from 'openai';

export async function POST(request: NextRequest) {
  try {
    const { message, history } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json({
        response: 'AI functionality is not currently available. Please contact your system administrator to configure the OpenAI API key.'
      });
    }

    console.log('AI Chat: Processing query:', message);

    // Step 1: Use OpenAI to interpret the query and determine what data is needed
    const queryPlan = await interpretQuery(message);
    console.log('AI Chat: Query plan:', queryPlan);

    // Step 2: Execute the plan locally (fetch data from Sikka API)
    const aggregatedStats = await executeQueryPlan(queryPlan);
    console.log('AI Chat: Aggregated stats:', Object.keys(aggregatedStats));

    // Step 3: Use OpenAI to generate a response based on aggregated statistics only
    const response = await generateResponse(message, aggregatedStats, queryPlan);

    return NextResponse.json({
      response,
      metadata: {
        privacy_compliant: true,
        data_processed: aggregatedStats.totalRecords || 0,
        query_type: queryPlan.intent,
        endpoints_used: queryPlan.endpoints
      }
    });

  } catch (error) {
    console.error('AI Chat: Error processing request:', error);

    return NextResponse.json({
      response: 'I apologize, but I encountered an unexpected error while processing your request. Please try again or contact support if the issue persists.'
    }, { status: 500 });
  }
}

// Step 1: Use OpenAI to interpret the query and determine what data is needed
async function interpretQuery(message: string) {
  const systemPrompt = `You are a dental practice management AI assistant. Your job is to interpret user queries and determine what aggregated statistics are needed to answer them.

You have access to these data sources:
- Appointments (date, time, operatory, provider, status)
- Operatories (which ones are active, provider assignments)
- Practice metrics (appointment counts, operatory utilization)

IMPORTANT: You will NEVER see individual patient data. You only work with aggregated statistics.

For each query, determine:
1. What type of information is being requested
2. What date range is relevant
3. What aggregated statistics would be needed
4. Which data endpoints to query

Respond with JSON in this format:
{
  "intent": "brief description of what user wants",
  "dateRange": {
    "start": "YYYY-MM-DD",
    "end": "YYYY-MM-DD"
  },
  "endpoints": ["appointments", "operatories"],
  "aggregations": ["count", "by_operatory", "by_provider", "by_time"],
  "filters": {
    "operatories": ["DL01", "DL02"] // if specific operatories mentioned
  }
}`;

  try {
    // Initialize OpenAI client only when needed
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: message }
      ],
      max_tokens: 500,
      temperature: 0.3
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No response from OpenAI');
    }

    return JSON.parse(content);
  } catch (error) {
    console.error('Error interpreting query:', error);
    // Fallback plan
    return {
      intent: "General practice information request",
      dateRange: {
        start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end: new Date().toISOString().split('T')[0]
      },
      endpoints: ["appointments"],
      aggregations: ["count"],
      filters: {}
    };
  }
}

// Step 2: Execute the query plan locally and generate aggregated statistics
async function executeQueryPlan(queryPlan: any) {
  const stats: any = {
    totalRecords: 0,
    dateRange: queryPlan.dateRange,
    queryType: queryPlan.intent
  };

  try {
    // Load credentials from the credentials file
    const credentials = loadCredentials();
    if (!credentials.office_id || !credentials.secret_key) {
      console.log('AI Chat: Sikka credentials not configured, using mock data');
      return generateMockStats(queryPlan);
    }

    const sikkaClient = new SikkaApiClient(credentials);

    // Fetch appointments if requested
    if (queryPlan.endpoints.includes('appointments')) {
      const appointments = await sikkaClient.getAppointments(
        queryPlan.dateRange.start,
        queryPlan.filters.operatories
      );

      // Generate aggregated statistics (NO individual patient data)
      stats.appointments = {
        total: appointments.length,
        byOperatory: {},
        byProvider: {},
        byStatus: {},
        byTimeSlot: {},
        byAppointmentType: {},
        totalRecords: appointments.length
      };

      // Aggregate by operatory (no patient names)
      appointments.forEach(apt => {
        stats.appointments.byOperatory[apt.operatory] =
          (stats.appointments.byOperatory[apt.operatory] || 0) + 1;

        if (apt.provider) {
          stats.appointments.byProvider[apt.provider] =
            (stats.appointments.byProvider[apt.provider] || 0) + 1;
        }

        stats.appointments.byStatus[apt.status || 'unknown'] =
          (stats.appointments.byStatus[apt.status || 'unknown'] || 0) + 1;

        // Time slot aggregation (hour of day)
        if (apt.startTime) {
          const hour = apt.startTime.split(':')[0];
          stats.appointments.byTimeSlot[hour] =
            (stats.appointments.byTimeSlot[hour] || 0) + 1;
        }

        // Appointment type aggregation
        if (apt.type) {
          stats.appointments.byAppointmentType[apt.type] =
            (stats.appointments.byAppointmentType[apt.type] || 0) + 1;
        }
      });

      stats.totalRecords += appointments.length;
    }

    // Fetch patient statistics if requested
    if (queryPlan.endpoints.includes('patients')) {
      try {
        // Get aggregated patient statistics without individual data
        const patientStats = await getPatientStatistics(sikkaClient, queryPlan.dateRange);
        stats.patients = patientStats;
        stats.totalRecords += patientStats.totalRecords || 0;
      } catch (error) {
        console.error('Error fetching patient statistics:', error);
      }
    }

    // Fetch clinical notes statistics if requested
    if (queryPlan.endpoints.includes('notes')) {
      try {
        const notesStats = await getClinicalNotesStatistics(sikkaClient, queryPlan.dateRange);
        stats.clinicalNotes = notesStats;
        stats.totalRecords += notesStats.totalRecords || 0;
      } catch (error) {
        console.error('Error fetching clinical notes statistics:', error);
      }
    }

    // Fetch operatories if requested
    if (queryPlan.endpoints.includes('operatories')) {
      const operatories = await sikkaClient.getOperatories(queryPlan.dateRange.start);

      stats.operatories = {
        total: operatories.length,
        byProvider: {},
        activeToday: operatories.length
      };

      operatories.forEach(op => {
        if (op.provider) {
          stats.operatories.byProvider[op.provider] =
            (stats.operatories.byProvider[op.provider] || 0) + 1;
        }
      });
    }

    return stats;

  } catch (error) {
    console.error('Error executing query plan:', error);
    return generateMockStats(queryPlan);
  }
}

// Generate mock statistics for demo purposes
function generateMockStats(queryPlan: any) {
  return {
    totalRecords: 25,
    dateRange: queryPlan.dateRange,
    queryType: queryPlan.intent,
    appointments: {
      total: 25,
      byOperatory: {
        'DL01': 8,
        'DL02': 6,
        'HYG1': 7,
        'HYG2': 4
      },
      byProvider: {
        'Dr. Lowell': 14,
        'Hygiene': 11
      },
      byStatus: {
        'Scheduled': 20,
        'Completed': 3,
        'Cancelled': 2
      }
    },
    operatories: {
      total: 4,
      activeToday: 4,
      byProvider: {
        'Dr. Lowell': 2,
        'Hygiene': 2
      }
    },
    note: 'Using demo data - configure Sikka API credentials for live data'
  };
}

// Step 3: Generate response using OpenAI based on aggregated statistics only
async function generateResponse(message: string, stats: any, queryPlan: any) {
  const systemPrompt = `You are an AI Office Manager for a dental practice. You help analyze practice data and provide insights.

CRITICAL PRIVACY RULES:
- You NEVER see individual patient data
- You ONLY work with aggregated statistics
- You NEVER mention specific patient names, appointments, or personal information
- All data you receive is already anonymized and aggregated

Your role is to:
- Analyze aggregated practice statistics
- Provide insights about operatory utilization, appointment patterns, etc.
- Help with practice management decisions
- Answer questions about practice metrics

Be helpful, professional, and focus on actionable insights from the aggregated data.

The user asked: "${message}"

Based on the aggregated statistics provided, give a helpful response that answers their question while maintaining complete patient privacy.`;

  try {
    // Initialize OpenAI client only when needed
    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        { role: "system", content: systemPrompt },
        {
          role: "user",
          content: `Query: "${message}"\n\nAggregated Statistics: ${JSON.stringify(stats, null, 2)}\n\nQuery Plan: ${JSON.stringify(queryPlan, null, 2)}`
        }
      ],
      max_tokens: 1000,
      temperature: 0.7
    });

    return response.choices[0]?.message?.content || 'I apologize, but I was unable to generate a response for your query.';
  } catch (error) {
    console.error('Error generating response:', error);
    return 'I encountered an error while processing your request. Please try rephrasing your question.';
  }
}

// Helper function to get patient statistics
async function getPatientStatistics(sikkaClient: any, dateRange: any) {
  // Note: This would need actual patient search implementation
  // For now, return mock statistics
  return {
    totalRecords: 0,
    newPatients: 0,
    returningPatients: 0,
    byAgeGroup: {},
    byGender: {},
    note: 'Patient statistics not yet implemented'
  };
}

// Helper function to get clinical notes statistics
async function getClinicalNotesStatistics(sikkaClient: any, dateRange: any) {
  try {
    const clinicalNotes = await sikkaClient.getClinicalNotesByDate(dateRange.start);
    
    const stats = {
      totalRecords: clinicalNotes.length,
      byProvider: {},
      byType: {},
      averageNoteLength: 0,
      totalNoteLength: 0
    };

    let totalLength = 0;
    clinicalNotes.forEach(note => {
      // Provider aggregation
      if (note.provider) {
        stats.byProvider[note.provider] = (stats.byProvider[note.provider] || 0) + 1;
      }

      // Type aggregation
      if (note.type) {
        stats.byType[note.type] = (stats.byType[note.type] || 0) + 1;
      }

      // Length calculation
      if (note.text) {
        totalLength += note.text.length;
      }
    });

    stats.totalNoteLength = totalLength;
    stats.averageNoteLength = clinicalNotes.length > 0 ? Math.round(totalLength / clinicalNotes.length) : 0;

    return stats;
  } catch (error) {
    console.error('Error fetching clinical notes statistics:', error);
    return {
      totalRecords: 0,
      byProvider: {},
      byType: {},
      averageNoteLength: 0,
      totalNoteLength: 0,
      error: 'Failed to fetch clinical notes statistics'
    };
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

