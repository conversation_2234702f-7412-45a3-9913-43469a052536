@echo off
setlocal enabledelayedexpansion

REM ===================================================================
REM Environment Validation Script for Dental Voice Processing System
REM ===================================================================
REM This script validates that all required environment variables and
REM services are properly configured for the voice processing pipeline.
REM
REM Usage: validate-environment.bat [--fix] [--verbose] [--connectivity]
REM   --fix          Attempt to create missing .env file from template
REM   --verbose      Show detailed output
REM   --connectivity Test actual service connectivity (requires network)
REM ===================================================================

echo.
echo =========================================
echo   Environment Validation Script
echo   Dental Voice Processing System
echo =========================================
echo.

REM Parse command line arguments
set "FIX_MODE=0"
set "VERBOSE=0"
set "TEST_CONNECTIVITY=0"

:parse_args
if "%~1"=="--fix" (
    set "FIX_MODE=1"
    shift
    goto parse_args
)
if "%~1"=="--verbose" (
    set "VERBOSE=1"
    shift
    goto parse_args
)
if "%~1"=="--connectivity" (
    set "TEST_CONNECTIVITY=1"
    shift
    goto parse_args
)
if "%~1" neq "" (
    shift
    goto parse_args
)

REM Change to application directory
cd /d "%~dp0..\.."
if %VERBOSE%==1 echo [VERBOSE] Working directory: %CD%

REM Check if .env file exists
if not exist ".env" (
    echo [WARNING] No .env file found!
    if %FIX_MODE%==1 (
        if exist ".env.sample" (
            echo [FIX] Copying .env.sample to .env...
            copy ".env.sample" ".env" >nul
            echo [SUCCESS] Created .env file from template
            echo [ACTION] Please edit .env file with your actual credentials
        ) else (
            echo [ERROR] .env.sample not found - cannot create .env file
            goto :error_exit
        )
    ) else (
        echo [ACTION] Run with --fix to create .env from template
        echo [ACTION] Or manually copy .env.sample to .env and configure
        goto :error_exit
    )
)

echo [INFO] Loading environment variables from .env file...

REM Load environment variables from .env file
for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
    if "%%a" neq "" if "%%b" neq "" (
        REM Skip comments (lines starting with #)
        echo %%a | findstr /r /c:"^#" >nul
        if errorlevel 1 (
            set "%%a=%%b"
            if %VERBOSE%==1 echo [VERBOSE] Loaded: %%a
        )
    )
)

REM Define validation criteria
echo.
echo [STEP 1] Validating critical environment variables...
echo ================================================

set "CRITICAL_MISSING=0"
set "OPTIONAL_MISSING=0"
set "CONFIGURED_COUNT=0"

REM Critical variables for voice processing
call :check_var "OPENAI_API_KEY" "OpenAI API key for transcription/summarization" "CRITICAL"
call :check_var "AZURE_STORAGE_CONNECTION_STRING" "Azure Storage for voice recordings" "CRITICAL"

REM Important but not critical
call :check_var "SIKKA_API_KEY" "Sikka API for dental practice integration" "OPTIONAL"
call :check_var "SIKKA_PRACTICE_ID" "Sikka practice ID for patient data" "OPTIONAL"

REM Monitoring (recommended)
call :check_var "APPINSIGHTS_INSTRUMENTATIONKEY" "Application Insights monitoring" "OPTIONAL"
call :check_var "SENTRY_DSN" "Sentry error tracking" "OPTIONAL"

REM Azure Functions (deployment-specific)
call :check_var "AzureWebJobsStorage" "Azure Functions storage" "OPTIONAL"

echo.
echo [STEP 2] Configuration Summary
echo ===============================
echo Configured variables: %CONFIGURED_COUNT%
echo Missing critical: %CRITICAL_MISSING%
echo Missing optional: %OPTIONAL_MISSING%

if %CRITICAL_MISSING% gtr 0 (
    echo.
    echo [CRITICAL ERROR] Voice processing will not work!
    echo [ACTION] Configure missing critical variables in .env file
    echo [DOCS] See .env.sample for examples and VOICE_PROCESSING_RECOVERY_GUIDE.md
    goto :error_exit
)

if %OPTIONAL_MISSING% gtr 0 (
    echo.
    echo [WARNING] Some optional features will be disabled
    echo [ACTION] Configure optional variables for full functionality
)

REM Step 3: File system validation
echo.
echo [STEP 3] Validating file system permissions...
echo ===============================================

REM Check if we can create files in required directories
call :check_directory "data" "Database and local storage"
call :check_directory "logs" "Application logs"

REM Step 4: Node.js and dependencies
echo.
echo [STEP 4] Validating Node.js environment...
echo ===========================================

node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found or not in PATH
    goto :error_exit
) else (
    for /f "tokens=*" %%a in ('node --version') do echo [SUCCESS] Node.js version: %%a
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm not found or not in PATH
    goto :error_exit
) else (
    for /f "tokens=*" %%a in ('npm --version') do echo [SUCCESS] npm version: %%a
)

REM Check if package.json exists and dependencies are installed
if not exist "package.json" (
    echo [ERROR] package.json not found
    goto :error_exit
)

if not exist "node_modules" (
    echo [WARNING] node_modules not found
    echo [ACTION] Run 'npm install' to install dependencies
    if %FIX_MODE%==1 (
        echo [FIX] Installing dependencies...
        npm install
        if errorlevel 1 (
            echo [ERROR] Failed to install dependencies
            goto :error_exit
        )
    )
) else (
    echo [SUCCESS] Dependencies appear to be installed
)

REM Step 5: Service connectivity (if requested)
if %TEST_CONNECTIVITY%==1 (
    echo.
    echo [STEP 5] Testing service connectivity...
    echo ========================================
    
    REM Test OpenAI API
    if defined OPENAI_API_KEY (
        echo [INFO] Testing OpenAI API connectivity...
        node -e "
        const https = require('https');
        const options = {
            hostname: 'api.openai.com',
            path: '/v1/models',
            method: 'GET',
            headers: { 'Authorization': 'Bearer ' + process.env.OPENAI_API_KEY },
            timeout: 10000
        };
        const req = https.request(options, (res) => {
            if (res.statusCode === 200) {
                console.log('[SUCCESS] OpenAI API is accessible');
            } else {
                console.log('[WARNING] OpenAI API returned status:', res.statusCode);
            }
        });
        req.on('error', (e) => {
            console.log('[ERROR] OpenAI API connection failed:', e.message);
        });
        req.on('timeout', () => {
            console.log('[ERROR] OpenAI API connection timed out');
            req.destroy();
        });
        req.end();
        " 2>nul
    )
    
    REM Test Azure Storage (basic connection string format check)
    if defined AZURE_STORAGE_CONNECTION_STRING (
        echo [INFO] Validating Azure Storage connection string format...
        echo !AZURE_STORAGE_CONNECTION_STRING! | findstr /c:"AccountName" | findstr /c:"AccountKey" >nul
        if errorlevel 1 (
            echo [ERROR] Azure Storage connection string format appears invalid
        ) else (
            echo [SUCCESS] Azure Storage connection string format is valid
        )
    )
    
    REM Test Sikka API
    if defined SIKKA_API_KEY (
        echo [INFO] Testing Sikka API connectivity...
        REM Note: Actual connectivity test would require valid API endpoint
        echo [INFO] Sikka API key is configured (connectivity test requires valid endpoint)
    )
)

REM Final summary
echo.
echo =========================================
echo   Validation Complete
echo =========================================

if %CRITICAL_MISSING%==0 (
    echo [SUCCESS] Environment is ready for voice processing!
    echo.
    echo Next steps:
    echo 1. Start the application: npm run dev
    echo 2. Test voice upload and transcription
    echo 3. Monitor logs for any runtime issues
    
    if %OPTIONAL_MISSING% gtr 0 (
        echo.
        echo Optional improvements:
        echo - Configure monitoring variables for better observability
        echo - Set up Sikka integration for dental practice features
    )
    
    echo.
    echo For production deployment:
    echo - Use PM2: pm2 start deployment/config/ecosystem.config.js
    echo - Monitor with: deployment/scripts/status.bat
    echo - View logs with: deployment/scripts/logs.bat
    
    exit /b 0
) else (
    echo [FAILED] Critical environment variables missing
    echo [ACTION] Fix the issues above and run validation again
    goto :error_exit
)

REM ===================================================================
REM Helper Functions
REM ===================================================================

:check_var
set "var_name=%~1"
set "var_desc=%~2"
set "var_type=%~3"

call set "var_value=%%%var_name%%%"

if "!var_value!"=="" (
    echo [MISSING] %var_name% - %var_desc%
    if "%var_type%"=="CRITICAL" (
        set /a CRITICAL_MISSING+=1
    ) else (
        set /a OPTIONAL_MISSING+=1
    )
) else (
    REM Check if it's a placeholder value
    echo !var_value! | findstr /c:"your_" | findstr /c:"_here" >nul
    if errorlevel 1 (
        echo [SUCCESS] %var_name% - configured
        set /a CONFIGURED_COUNT+=1
    ) else (
        echo [WARNING] %var_name% - appears to be placeholder value
        if "%var_type%"=="CRITICAL" (
            set /a CRITICAL_MISSING+=1
        ) else (
            set /a OPTIONAL_MISSING+=1
        )
    )
)
goto :eof

:check_directory
set "dir_name=%~1"
set "dir_desc=%~2"

if not exist "%dir_name%" (
    echo [CREATE] Creating directory: %dir_name% (%dir_desc%)
    mkdir "%dir_name%" 2>nul
    if errorlevel 1 (
        echo [ERROR] Failed to create directory: %dir_name%
        goto :error_exit
    )
) else (
    echo [SUCCESS] Directory exists: %dir_name%
)

REM Test write permissions
echo test > "%dir_name%\test.tmp" 2>nul
if errorlevel 1 (
    echo [ERROR] No write permission in directory: %dir_name%
    goto :error_exit
) else (
    del "%dir_name%\test.tmp" 2>nul
    echo [SUCCESS] Write permission confirmed: %dir_name%
)
goto :eof

:error_exit
echo.
echo =========================================
echo   Validation Failed
echo =========================================
echo [ERROR] Please fix the issues above and run validation again
echo.
echo For help:
echo - Review .env.sample for configuration examples
echo - Check VOICE_PROCESSING_RECOVERY_GUIDE.md for troubleshooting
echo - Run with --verbose for detailed output
echo - Run with --fix to attempt automatic fixes
echo.
exit /b 1