/**
 * Sikka API Client
 * Handles authentication and API requests to the Sikka API
 */

import { Appointment, AuthResponse, Credentials, MedicalNote, Operatory } from './types';

// API base URLs
const API_BASE_V2 = 'https://api.sikkasoft.com/v2';
const API_BASE_V4 = 'https://api.sikkasoft.com/v4';

// Default timeout for API requests (in milliseconds)
const API_TIMEOUT = 30000;

// Cache for API responses
const apiCache: Record<string, { data: any, timestamp: number }> = {};

// Helper function to add delay between API requests to avoid rate limiting
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Helper function to make API requests with caching
 */
async function cachedApiRequest<T>(
  cacheKey: string,
  requestFn: () => Promise<T>,
  cacheDuration: number = 5 * 60 * 1000 // 5 minutes by default
): Promise<T> {
  // Check if we have a valid cached response
  const cachedResponse = apiCache[cacheKey];
  if (cachedResponse && Date.now() - cachedResponse.timestamp < cacheDuration) {
    return cachedResponse.data as T;
  }

  // Add a small delay to avoid rate limiting
  await delay(300);

  // Make the actual request
  const data = await requestFn();

  // Cache the response
  apiCache[cacheKey] = {
    data,
    timestamp: Date.now()
  };

  return data;
}

/**
 * Sikka API Client class
 */
export class SikkaApiClient {
  private credentials: Credentials;
  private requestKey: string | null = null;

  constructor(credentials: Credentials) {
    this.credentials = credentials;
  }

  /**
   * Authenticate with Sikka API and get request key
   */
  async authenticate(): Promise<string> {
    try {
      const response = await fetch(`${API_BASE_V4}/request_key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          grant_type: 'request_key',
          office_id: this.credentials.office_id,
          secret_key: this.credentials.secret_key,
          app_id: this.credentials.app_id,
          app_key: this.credentials.app_key,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Authentication failed with status ${response.status}: ${errorText}`);
      }

      const data = await response.json() as AuthResponse;
      this.requestKey = data.request_key;

      if (!this.requestKey) {
        throw new Error('No request key in response');
      }

      return this.requestKey;
    } catch (error) {
      console.error('Error during authentication:', error);
      throw new Error(`Authentication failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get the current request key or authenticate to get a new one
   */
  async getRequestKey(): Promise<string> {
    if (!this.requestKey) {
      return this.authenticate();
    }
    return this.requestKey;
  }

  /**
   * Fetch operatories for a specific date
   */
  async getOperatories(date: string): Promise<Operatory[]> {
    // Create a cache key based on date
    const cacheKey = `operatories-${date}`;

    // Check if we have a valid cached response (cache for 5 minutes)
    const cachedResponse = apiCache[cacheKey];
    if (cachedResponse && Date.now() - cachedResponse.timestamp < 5 * 60 * 1000) {
      console.log('Using cached operatories data');
      return cachedResponse.data as Operatory[];
    }

    // Add a small delay to avoid rate limiting
    await delay(500);

    const requestKey = await this.getRequestKey();

    try {
      // Use the appointments endpoint with date filtering to get only appointments for the specific date
      const response = await fetch(`${API_BASE_V2}/appointments?date=${date}&startdate=${date}&enddate=${date}&date_filter_on=appointment_date`, {
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch operatories: ${response.status}`);
      }

      const data = await response.json();
      console.log('Raw appointments data:', JSON.stringify(data, null, 2));

      // Extract operatories from appointments
      const operatories = new Set<string>();
      let appointmentCount = 0;

      if (Array.isArray(data) && data.length > 0 && data[0].items) {
        console.log(`Found ${data[0].items.length} appointments in the response`);
        
        for (const item of data[0].items) {
          const operatory = item.operatory;
          const appointmentDate = item.date || item.appointment_date;
          
          // Debug log for each appointment
          console.log('Processing appointment:', {
            patient: item.patient_name || 'unknown',
            operatory,
            date: appointmentDate,
            status: item.status,
            description: item.description
          });
          
          // Skip if no operatory or invalid operatory
          if (!operatory || operatory === '' || operatory === 'N/A') {
            console.log('Skipping - Invalid operatory');
            continue;
          }
          
          // Skip DAZ1 and DAZ2 operatories
          if (operatory === 'DAZ1' || operatory === 'DAZ2') {
            console.log('Skipping - DAZ1/DAZ2 operatory');
            continue;
          }
          
          // Verify the appointment is for the correct date
          if (appointmentDate && appointmentDate !== date) {
            console.log(`Skipping - Appointment date (${appointmentDate}) doesn't match requested date (${date})`);
            continue;
          }
          
          // Skip blocked or cancelled appointments
          if (item.status === 'Blocked' || item.status === 'Cancelled' || 
              (item.description && item.description.toLowerCase().includes('blocked'))) {
            console.log('Skipping - Blocked or cancelled appointment');
            continue;
          }
          
          console.log(`Adding operatory: ${operatory}`);
          operatories.add(operatory);
          appointmentCount++;
        }
      }
      
      console.log(`Found ${operatories.size} unique operatories with ${appointmentCount} valid appointments`);

      // Map operatory codes to provider names
      const operatoryToProvider: Record<string, string> = {
        'DL01': 'Dr. Lowell',
        'DL02': 'Dr. Lowell',
        'NS01': 'Dr. Soto',
        'NS02': 'Dr. Soto',
        'HYG1': 'Hygiene',
        'HYG2': 'Hygiene',
        'HYG3': 'Hygiene',
        'CONS': 'Consult Room',
        'LAB': 'Lab',
      };

      // Convert to array of Operatory objects with provider information
      const operatoryList = Array.from(operatories).map(name => {
        // Determine the provider group
        let provider = operatoryToProvider[name];

        // If no mapping exists, use the operatory name itself as the provider group
        if (!provider) {
          // Try to extract a provider name from the operatory code
          const match = name.match(/^([A-Za-z]+)\d*$/);
          if (match && match[1]) {
            provider = match[1].toUpperCase();
          } else {
            provider = name;
          }
        }

        
        return {
          id: name,
          name,
          provider,
          sortKey: name.toLowerCase(),
        };
      });

      // Sort operatories by provider first, then by name
      const sortedOperatories = operatoryList.sort((a, b) => {
        // First sort by provider
        if (a.provider !== b.provider) {
          return a.provider.localeCompare(b.provider);
        }
        // Then sort by operatory name
        return a.sortKey.localeCompare(b.sortKey);
      });

      console.log(`Found ${sortedOperatories.length} operatories with appointments for ${date}:`, 
        sortedOperatories.map(op => op.name).join(', '));

      // Cache the response
      apiCache[cacheKey] = {
        data: sortedOperatories,
        timestamp: Date.now()
      };

      return sortedOperatories;
    } catch (error) {
      console.error('Error fetching operatories:', error);
      throw new Error(`Failed to fetch operatories: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Fetch appointments for a specific date and operatories
   */
  async getAppointments(date: string, operatories?: string[]): Promise<Appointment[]> {
    // Create a cache key based on date and operatories (create a copy to avoid modifying the original)
    const operatoriesKey = operatories ? [...operatories].sort().join('-') : 'all';
    const cacheKey = `appointments-${date}-${operatoriesKey}`;

    // Log the request details
    console.log(`SikkaClient: Fetching appointments for date: ${date}, operatories: ${operatories ? operatories.join(', ') : 'all'}`);

    // Check if we have a valid cached response (cache for 5 minutes)
    const cachedResponse = apiCache[cacheKey];
    if (cachedResponse && Date.now() - cachedResponse.timestamp < 5 * 60 * 1000) {
      console.log('SikkaClient: Using cached appointments data');
      return cachedResponse.data as Appointment[];
    }

    // Add a small delay to avoid rate limiting
    await delay(500);

    const requestKey = await this.getRequestKey();

    try {
      // Use v2 appointments endpoint with date_filter_on=appointment_date to filter by appointment date
      // Also use startdate and enddate to ensure we only get appointments for the specific date
      const response = await fetch(`${API_BASE_V2}/appointments?date=${date}&startdate=${date}&enddate=${date}&date_filter_on=appointment_date`, {
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch appointments: ${response.status}`);
      }

      const data = await response.json();

      // Extract appointments
      const appointments: Appointment[] = [];

      if (Array.isArray(data) && data.length > 0 && data[0].items) {
        for (const item of data[0].items) {
          // Skip DAZ1 and DAZ2 operatories
          if (item.operatory === 'DAZ1' || item.operatory === 'DAZ2') {
            // console.log(`SikkaClient: Skipping appointment in operatory ${item.operatory}`);
            continue;
          }

          // Skip if operatory filter is provided and this appointment's operatory is not in the list
          if (operatories && operatories.length > 0 && !operatories.includes(item.operatory)) {
            console.log(`SikkaClient: Filtering out appointment in operatory ${item.operatory} (not in requested operatories: ${operatories.join(', ')})`);
            continue;
          }

          // Skip if the appointment date doesn't match the requested date
          // This is a double-check in case the API doesn't filter correctly
          if (item.date && item.date !== date) {
            console.log(`SikkaClient: Filtering out appointment on date ${item.date} (requested date: ${date})`);
            continue;
          }

          // Log that we're including this appointment
          console.log(`SikkaClient: Including appointment for ${item.patient_name || 'unknown'} in operatory ${item.operatory}`);

          // Log the appointment details for debugging with full provider information
          console.log(`PROVIDER INFO for ${item.patient_name}: ${JSON.stringify(item.provider)}`);
          console.log(`Appointment details: Time=${item.time}, Length=${item.length}`);


          // Determine if this is a blocked time slot
          const isBlocked = item.status === 'Blocked' ||
                           (item.description && item.description.toLowerCase().includes('blocked'));

          // Skip appointments that only say "Blocked" with no other information
          if (isBlocked && item.description && item.description.trim().toLowerCase() === 'blocked') {
            continue;
          }

          // Calculate end time from start time and length
          let startTime = '8:00 AM';
          let endTime = '9:00 AM';

          if (item.time) {
            // Convert 24-hour format to 12-hour format for display
            const [hours, minutes] = item.time.split(':').map(Number);
            const period = hours >= 12 ? 'PM' : 'AM';
            const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
            startTime = `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;

            // Calculate end time
            const lengthMinutes = parseFloat(item.length) || 60;
            const endMinutes = hours * 60 + minutes + lengthMinutes;
            const endHours = Math.floor(endMinutes / 60);
            const endMins = endMinutes % 60;
            const endPeriod = endHours >= 12 ? 'PM' : 'AM';
            const endDisplayHours = endHours === 0 ? 12 : endHours > 12 ? endHours - 12 : endHours;
            endTime = `${endDisplayHours}:${endMins.toString().padStart(2, '0')} ${endPeriod}`;
          }

          // Format the appointment
          appointments.push({
            id: item.appointment_sr_no || `${date}-${item.operatory}-${item.time}`,
            appointment_sr_no: item.appointment_sr_no,
            patient_id: item.patient_id,
            patient_name: item.patient_name || 'No Patient',
            provider: item.provider,
            operatory: item.operatory,
            date: item.date || date,
            startTime: startTime,
            endTime: endTime,
            length: item.length || 60,
            type: item.description || 'Unknown',
            description: item.description || '',
            isBlocked,
            status: item.status,
          });
        }
      }

      // Cache the response
      apiCache[cacheKey] = {
        data: appointments,
        timestamp: Date.now()
      };

      return appointments;
    } catch (error) {
      console.error('Error fetching appointments:', error);
      throw new Error(`Failed to fetch appointments: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get appointment by ID
   */
  async getAppointmentById(appointmentId: string): Promise<Appointment | null> {
    try {
      console.log(`SikkaClient: Getting appointment by ID: ${appointmentId}`);

      const requestKey = await this.getRequestKey();
      const url = `${API_BASE_V2}/appointments/${appointmentId}`;
      console.log(`SikkaClient: Appointment by ID URL: ${url}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Request-Key': requestKey,
        },
      });

      console.log(`SikkaClient: Appointment by ID response status: ${response.status}`);

      if (!response.ok) {
        if (response.status === 404) {
          console.log(`SikkaClient: Appointment ${appointmentId} not found`);
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Handle 204 No Content response (appointment not found)
      if (response.status === 204) {
        console.log(`SikkaClient: Appointment ${appointmentId} not found (204 No Content)`);
        return null;
      }

      const data = await response.json();
      console.log(`SikkaClient: Appointment by ID response:`, data);

      // Handle different response formats
      let appointmentData = null;
      if (Array.isArray(data) && data.length > 0) {
        appointmentData = data[0].items?.[0] || data[0];
      } else if (data.items && Array.isArray(data.items) && data.items.length > 0) {
        appointmentData = data.items[0];
      } else if (data.id || data.appointment_sr_no) {
        appointmentData = data;
      }

      if (!appointmentData) {
        console.log(`SikkaClient: No appointment data found for ID: ${appointmentId}`);
        return null;
      }

      // Format the appointment data
      let startTime = '8:00 AM';
      let endTime = '9:00 AM';

      if (appointmentData.time) {
        // Convert 24-hour format to 12-hour format for display
        const [hours, minutes] = appointmentData.time.split(':').map(Number);
        const period = hours >= 12 ? 'PM' : 'AM';
        const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
        startTime = `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;

        // Calculate end time
        const lengthMinutes = parseFloat(appointmentData.length) || 60;
        const endMinutes = hours * 60 + minutes + lengthMinutes;
        const endHours = Math.floor(endMinutes / 60);
        const endMins = endMinutes % 60;
        const endPeriod = endHours >= 12 ? 'PM' : 'AM';
        const endDisplayHours = endHours === 0 ? 12 : endHours > 12 ? endHours - 12 : endHours;
        endTime = `${endDisplayHours}:${endMins.toString().padStart(2, '0')} ${endPeriod}`;
      }

      const appointment: Appointment = {
        id: appointmentData.appointment_sr_no || appointmentData.id || appointmentId,
        appointment_sr_no: appointmentData.appointment_sr_no,
        patient_id: appointmentData.patient_id,
        patient_name: appointmentData.patient_name || 'No Patient',
        provider: appointmentData.provider,
        operatory: appointmentData.operatory,
        date: appointmentData.date,
        startTime: startTime,
        endTime: endTime,
        length: appointmentData.length || 60,
        type: appointmentData.description || 'Unknown',
        description: appointmentData.description || '',
        isBlocked: appointmentData.status === 'Blocked' ||
                  (appointmentData.description && appointmentData.description.toLowerCase().includes('blocked')),
        status: appointmentData.status,
      };

      return appointment;

    } catch (error) {
      console.error('Error fetching appointment by ID:', error);
      throw new Error(`Failed to fetch appointment: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Fetch medical notes for a specific date
   */
  // Get patient by ID
  async getPatientById(patientId: string): Promise<any | null> {
    try {
      console.log(`SikkaClient: Getting patient by ID: ${patientId}`);

      const requestKey = await this.getRequestKey();
      const url = `${API_BASE_V2}/patients/${patientId}`;
      console.log(`SikkaClient: Patient by ID URL: ${url}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Request-Key': requestKey,
        },
      });

      console.log(`SikkaClient: Patient by ID response status: ${response.status}`);

      if (!response.ok) {
        if (response.status === 404) {
          console.log(`SikkaClient: Patient ${patientId} not found`);
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseText = await response.text();
      console.log(`SikkaClient: Patient by ID raw response:`, responseText);

      if (!responseText.trim()) {
        console.log('SikkaClient: Empty response from patient by ID API');
        return null;
      }

      const data = JSON.parse(responseText);
      console.log(`SikkaClient: Patient by ID response:`, data);

      // Transform the patient data to match our interface
      if (data) {
        return this.transformPatientData(data);
      }

      return null;
    } catch (error) {
      console.error('SikkaClient: Error getting patient by ID:', error);
      return null;
    }
  }

  // Helper method to transform patient data
  private transformPatientData(patient: any): any {
    // Extract name components
    const firstName = patient.firstname || patient.first_name || patient.firstName || '';
    const lastName = patient.lastname || patient.last_name || patient.lastName || '';
    const middleName = patient.middlename || patient.middle_name || patient.middleName || '';
    const preferredName = patient.preferred_name || patient.preferredName || '';

    // Create middle initial from middle name
    const middleInitial = middleName ? middleName.charAt(0).toUpperCase() : '';

    // Build the display name: "FirstName (PreferredName) MiddleInitial LastName"
    let displayName = '';
    const nameParts = [];

    // Add first name
    if (firstName && firstName.trim() !== '') {
      nameParts.push(firstName.trim());
    }

    // Add preferred name in parentheses if it exists and is different from first name
    if (preferredName && preferredName.trim() !== '' && preferredName.trim() !== firstName.trim()) {
      nameParts.push(`(${preferredName.trim()})`);
    }

    // Add middle initial
    if (middleInitial) {
      nameParts.push(middleInitial);
    }

    // Add last name
    if (lastName && lastName.trim() !== '') {
      nameParts.push(lastName.trim());
    }

    displayName = nameParts.join(' ').trim();

    // Calculate age from birthdate
    const birthDate = patient.birthdate || patient.date_of_birth || patient.dateOfBirth || patient.dob;
    let age = '';
    if (birthDate) {
      const today = new Date();
      const birth = new Date(birthDate);
      const ageYears = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age = `${ageYears - 1}`;
      } else {
        age = `${ageYears}`;
      }
    }

    // Get gender
    const gender = patient.gender || '';
    const genderInitial = gender ? gender.charAt(0).toUpperCase() : '';

    return {
      id: patient.id || patient.patient_id || patient.patient_sr_no,
      name: displayName,
      firstName: firstName,
      lastName: lastName,
      middleName: middleName,
      middleInitial: middleInitial,
      preferredName: preferredName,
      dateOfBirth: birthDate,
      age: age,
      gender: gender,
      genderInitial: genderInitial,
      phone: patient.cell || patient.homephone || patient.phone || patient.phone_number || patient.home_phone,
      email: patient.email || patient.email_address,
      lastVisit: patient.last_visit || patient.lastVisit || '',
      firstVisit: patient.first_visit || patient.firstVisit || '',
      chartNumber: patient.chart_number || patient.chartNumber || patient.id || patient.patient_id
    };
  }

  async searchPatients(query: string): Promise<any[]> {
    try {
      console.log(`SikkaClient: Searching for patients with query: ${query}`);

      const requestKey = await this.getRequestKey();
      const url = `${API_BASE_V2}/patients`;
      
      const params = new URLSearchParams({
        limit: '50' // Limit to 50 results
      });

      if (isNaN(parseInt(query))) {
        params.append('search', query);
      } else {
        params.append('chart_number', query);
      }

      const response = await fetch(`${url}?${params}`, {
        method: 'GET',
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const responseText = await response.text();
      console.log(`SikkaClient: Patient search raw response:`, responseText);

      if (!responseText.trim()) {
        console.log('SikkaClient: Empty response from patient search API');
        return [];
      }

      const data = JSON.parse(responseText);
      console.log(`SikkaClient: Patient search response:`, data);

      // Extract patients from the response - the API returns an array with one object containing items
      let patients = [];
      if (Array.isArray(data) && data.length > 0 && data[0].items) {
        patients = data[0].items;
      } else if (data.items) {
        patients = data.items;
      } else if (data.data) {
        patients = data.data;
      }

      console.log(`SikkaClient: Extracted ${patients.length} patients from response`);

      // Log the first patient to see the structure
      if (patients.length > 0) {
        console.log(`SikkaClient: First patient structure:`, JSON.stringify(patients[0], null, 2));
      }

      // Format patient data
      const formattedPatients = patients.map((patient: any, index: number) => {
        console.log(`SikkaClient: Processing patient ${index}:`, JSON.stringify(patient, null, 2));

        // Extract name components
        const firstName = patient.firstname || patient.first_name || patient.firstName || '';
        const lastName = patient.lastname || patient.last_name || patient.lastName || '';
        const middleName = patient.middlename || patient.middle_name || patient.middleName || '';
        const preferredName = patient.preferred_name || patient.preferredName || '';

        // Create middle initial from middle name
        const middleInitial = middleName ? middleName.charAt(0).toUpperCase() : '';

        // Build the display name: "FirstName (PreferredName) MiddleInitial LastName"
        let displayName = '';
        const nameParts = [];

        // Add first name
        if (firstName && firstName.trim() !== '') {
          nameParts.push(firstName.trim());
        }

        // Add preferred name in parentheses if it exists and is different from first name
        if (preferredName && preferredName.trim() !== '' && preferredName.trim() !== firstName.trim()) {
          nameParts.push(`(${preferredName.trim()})`);
        }

        // Add middle initial
        if (middleInitial) {
          nameParts.push(middleInitial);
        }

        // Add last name
        if (lastName && lastName.trim() !== '') {
          nameParts.push(lastName.trim());
        }

        displayName = nameParts.join(' ').trim();

        // Calculate age from birthdate
        const birthDate = patient.birthdate || patient.date_of_birth || patient.dateOfBirth || patient.dob;
        let age = '';
        if (birthDate) {
          const today = new Date();
          const birth = new Date(birthDate);
          const ageYears = today.getFullYear() - birth.getFullYear();
          const monthDiff = today.getMonth() - birth.getMonth();

          if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age = `${ageYears - 1}`;
          } else {
            age = `${ageYears}`;
          }
        }

        // Get gender
        const gender = patient.gender || '';
        const genderInitial = gender ? gender.charAt(0).toUpperCase() : '';

        const formatted = {
          id: patient.id || patient.patient_id || patient.patient_sr_no,
          name: displayName,
          firstName: firstName,
          lastName: lastName,
          middleName: middleName,
          middleInitial: middleInitial,
          preferredName: preferredName,
          dateOfBirth: birthDate,
          age: age,
          gender: gender,
          genderInitial: genderInitial,
          phone: patient.cell || patient.homephone || patient.phone || patient.phone_number || patient.home_phone,
          email: patient.email || patient.email_address,
          lastVisit: patient.last_visit || patient.lastVisit || '',
          firstVisit: patient.first_visit || patient.firstVisit || ''
        };

        console.log(`SikkaClient: Formatted patient ${index}:`, formatted);
        return formatted;
      });

      // Sort patients by most recent visit date (newest first)
      const sortedPatients = formattedPatients.sort((a: any, b: any) => {
        const dateA = a.lastVisit ? new Date(a.lastVisit).getTime() : 0;
        const dateB = b.lastVisit ? new Date(b.lastVisit).getTime() : 0;

        // Sort by last visit date descending (newest first)
        // If no last visit date, put at the end
        if (dateA === 0 && dateB === 0) return 0;
        if (dateA === 0) return 1;
        if (dateB === 0) return -1;
        return dateB - dateA;
      });

      console.log(`SikkaClient: Found ${sortedPatients.length} patients, sorted by most recent visit`);
      return sortedPatients;

    } catch (error) {
      console.error('Error searching patients:', error);
      throw error;
    }
  }

  async getMedicalNotes(patientId: string, date?: string): Promise<any[]> {
    try {
      console.log(`SikkaClient: Fetching medical notes for patient: ${patientId}, date: ${date}`);

      const requestKey = await this.getRequestKey();

      // Try v4 API first for medical notes
      const url = `${API_BASE_V4}/medical_notes`;
      const params = new URLSearchParams({
        patient_id: patientId,
        limit: '50'
      });

      if (date) {
        params.append('date', date);
      }

      const response = await fetch(`${url}?${params}`, {
        method: 'GET',
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        // Try v2 API as fallback
        const v2Url = `${API_BASE_V2}/medical_notes`;
        const v2Response = await fetch(`${v2Url}?${params}`, {
          method: 'GET',
          headers: {
            'Request-Key': requestKey,
          },
        });

        if (!v2Response.ok) {
          console.log(`No medical notes found for patient ${patientId}`);
          return [];
        }

        const v2Data = await v2Response.json();
        return Array.isArray(v2Data) ? v2Data : v2Data.items || [];
      }

      const data = await response.json();
      console.log(`SikkaClient: Raw medical notes response:`, data);

      // Extract notes from the response
      const allNotes = Array.isArray(data) ? data : data.items || [];

      // Filter for actual clinical notes - include multiple note types
      const clinicalNotes = allNotes
        .filter((note: any) => {
          // Include multiple clinical note types
          const isClinicalNoteType = note.type === "Clinical Note" || note.type === "PatNote" || note.type === "ProcNote";

          // Must have some text content
          const hasText = note.text && note.text.trim().length > 0;

          if (!isClinicalNoteType || !hasText) {
            return false;
          }

          const noteText = note.text.toLowerCase();

          // Filter out patient communications and administrative messages
          const isPatientCommunication =
                 noteText.includes('welcome to suncoast dental center') ||
                 noteText.includes('please complete your new patient forms') ||
                 noteText.includes('please respond with "c" to confirm') ||
                 noteText.includes('confirmed the appointment') ||
                 noteText.includes('thank you for visiting suncoast dental center') ||
                 noteText.includes('please follow https://mdnt.io') ||
                 noteText.includes('created by engagement') ||
                 noteText.includes('chosen service:') ||
                 noteText.includes('primary insurance:') ||
                 noteText.includes('note from patient:') ||
                 noteText.includes('appointment confirmation') ||
                 noteText.includes('form completion') ||
                 noteText.includes('text message') ||
                 noteText.includes('sms') ||
                 noteText.includes('modento.io') ||
                 noteText.includes('fill in the forms online') ||
                 noteText.includes('we look forward to seeing you') ||
                 noteText.includes('arrive at least 15 minutes early') ||
                 noteText.includes('new patient appointments without paperwork') ||
                 noteText.includes('require confirmation response') ||
                 noteText.includes('unable to complete the forms') ||
                 noteText.includes('call or text us to let us know') ||
                 noteText.includes('new patient appts without forms') ||
                 noteText.includes('where your comfort and health are our priorities') ||
                 noteText.includes('we have just reserved your appointment') ||
                 noteText.includes('our mission is to make your experience') ||
                 // Filter out very short administrative notes
                 (note.text.trim().length < 10) ||
                 // Filter out single character responses like "C"
                 /^[a-z]$/i.test(note.text.trim()) ||
                 // Filter out URL-only messages
                 /^https?:\/\//.test(note.text.trim()) ||
                 // Filter out engagement/appointment booking messages
                 /created by engagement #\d+/i.test(noteText) ||
                 // Filter out form completion URLs
                 /patient\.modento\.io/.test(noteText) ||
                 // Filter out appointment confirmation patterns
                 /confirmed the appointment on \d{2}\/\d{2}\/\d{4}/.test(noteText);

          const isValidClinicalNote = !isPatientCommunication;

          console.log(`Note type: "${note.type}", length: ${note.text?.length}, isValid: ${isValidClinicalNote}, text: "${note.text?.substring(0, 100)}..."`);

          return isValidClinicalNote;
        })
        .map((note: any) => ({
          id: note.transaction_sr_no || note.id || `note-${patientId}-${note.date}`,
          date: note.date || note.procedure_date,
          provider: note.provider_id || 'Unknown Provider',
          notes: note.text || note.description || '',
          appointmentType: note.type || '',
          procedures: note.procedure_code_id ? [note.procedure_code_id] : [],
          tooth_number: note.tooth_number || '',
          surface: note.surface || '',
          rawData: note // Keep raw data for debugging
        }));

      console.log(`SikkaClient: Found ${clinicalNotes.length} clinical notes from ${allNotes.length} total medical notes for patient ${patientId}`);
      return clinicalNotes;
    } catch (error) {
      console.error('Error fetching medical notes:', error);
      return [];
    }
  }

  async getMedicalNotesByAppointment(appointmentId: string, patientId?: string): Promise<any[]> {
    try {
      console.log(`SikkaClient: Fetching medical notes for appointment: ${appointmentId}, patient: ${patientId}`);

      const requestKey = await this.getRequestKey();

      const url = `${API_BASE_V4}/medical_notes`;
      const params = new URLSearchParams({
        appointment_id: appointmentId,
        limit: '10'
      });

      // Add patient_id filter if provided for additional safety
      if (patientId) {
        params.append('patient_id', patientId);
      }

      console.log(`SikkaClient: Medical notes API call: ${url}?${params}`);

      const response = await fetch(`${url}?${params}`, {
        method: 'GET',
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        console.log(`No medical notes found for appointment ${appointmentId}`);
        return [];
      }

      const data = await response.json();
      const rawNotes = Array.isArray(data) ? data : data.items || [];

      console.log(`SikkaClient: Raw notes response for appointment ${appointmentId}:`, rawNotes.map((note: any) => ({
        id: note.id,
        patient_id: note.patient_id,
        text_preview: note.text?.substring(0, 50) + '...',
        type: note.type
      })));

      // Transform to consistent format and apply filtering
      const filteredNotes = rawNotes
        .map((note: any) => ({
          id: note.id || note.note_id || `note-${Date.now()}-${Math.random()}`,
          date: note.date || note.created_date || note.appointment_date,
          text: note.text || note.note_text || note.content || note.description,
          provider: note.provider || note.provider_name || note.doctor || 'Unknown Provider',
          type: note.type || 'Clinical Note',
          appointment_id: note.appointment_id || note.appt_id,
          tooth_number: note.tooth_number || '',
          surface: note.surface || '',
          rawData: note // Keep raw data for debugging
        }))
        .filter((note: any) => {
          // Must have text content
          const hasText = note.text && note.text.trim().length > 0;

          if (!hasText) {
            return false;
          }

          const noteText = note.text.toLowerCase();

          // Filter out patient communications and administrative messages
          const isPatientCommunication =
                 noteText.includes('welcome to suncoast dental center') ||
                 noteText.includes('please complete your new patient forms') ||
                 noteText.includes('please respond with "c" to confirm') ||
                 noteText.includes('confirmed the appointment') ||
                 noteText.includes('thank you for visiting suncoast dental center') ||
                 noteText.includes('please follow https://mdnt.io') ||
                 noteText.includes('created by engagement') ||
                 noteText.includes('chosen service:') ||
                 noteText.includes('primary insurance:') ||
                 noteText.includes('note from patient:') ||
                 noteText.includes('appointment confirmation') ||
                 noteText.includes('form completion') ||
                 noteText.includes('text message') ||
                 noteText.includes('sms') ||
                 noteText.includes('modento.io') ||
                 noteText.includes('fill in the forms online') ||
                 noteText.includes('we look forward to seeing you') ||
                 noteText.includes('arrive at least 15 minutes early') ||
                 noteText.includes('new patient appointments without paperwork') ||
                 noteText.includes('require confirmation response') ||
                 noteText.includes('unable to complete the forms') ||
                 noteText.includes('call or text us to let us know') ||
                 noteText.includes('new patient appts without forms') ||
                 noteText.includes('where your comfort and health are our priorities') ||
                 noteText.includes('we have just reserved your appointment') ||
                 noteText.includes('our mission is to make your experience') ||
                 // Filter out very short administrative notes
                 (note.text.trim().length < 10) ||
                 // Filter out single character responses like "C"
                 /^[a-z]$/i.test(note.text.trim()) ||
                 // Filter out URL-only messages
                 /^https?:\/\//.test(note.text.trim()) ||
                 // Filter out engagement/appointment booking messages
                 /created by engagement #\d+/i.test(noteText) ||
                 // Filter out form completion URLs
                 /patient\.modento\.io/.test(noteText) ||
                 // Filter out appointment confirmation patterns
                 /confirmed the appointment on \d{2}\/\d{2}\/\d{4}/.test(noteText);

          const isValidClinicalNote = !isPatientCommunication;

          console.log(`Appointment Clinical Note: type="${note.type}", length=${note.text?.length}, isValid=${isValidClinicalNote}, text="${note.text?.substring(0, 100)}..."`);

          return isValidClinicalNote;
        });

      console.log(`SikkaClient: Found ${filteredNotes.length} filtered clinical notes for appointment ${appointmentId} (from ${rawNotes.length} raw notes)`);
      return filteredNotes;
    } catch (error) {
      console.error('Error fetching medical notes by appointment:', error);
      return [];
    }
  }

  async getMedicalNotesByDate(date: string): Promise<any[]> {
    return this.getMedicalNotesByDateWithPagination(date, 100);
  }

  async getMedicalNotesByDateWithPagination(date: string, maxNotes: number = 500): Promise<any[]> {
    try {
      console.log(`SikkaClient: Fetching medical notes for date: ${date}, max notes: ${maxNotes}`);

      const requestKey = await this.getRequestKey();
      const url = `${API_BASE_V4}/medical_notes`;

      const allNotes: any[] = [];
      let page = 1;
      const pageSize = 100;

      while (allNotes.length < maxNotes) {
        const params = new URLSearchParams({
          date: date,
          limit: pageSize.toString(),
          page: page.toString()
        });

        console.log(`SikkaClient: Fetching medical notes page ${page} for date ${date}`);

        const response = await fetch(`${url}?${params}`, {
          method: 'GET',
          headers: {
            'Request-Key': requestKey,
          },
        });

        if (!response.ok) {
          if (page === 1) {
            console.log(`No medical notes found for date ${date}`);
          } else {
            console.log(`No more medical notes found for date ${date} at page ${page}`);
          }
          break;
        }

        const data = await response.json();
        const pageNotes = Array.isArray(data) ? data : data.items || [];

        console.log(`SikkaClient: Page ${page} returned ${pageNotes.length} medical notes`);

        if (pageNotes.length === 0) {
          break; // No more notes
        }

        allNotes.push(...pageNotes);

        if (pageNotes.length < pageSize) {
          break; // Last page
        }

        page++;
      }

      console.log(`SikkaClient: Total medical notes fetched: ${allNotes.length} across ${page} pages`);
      return allNotes.slice(0, maxNotes);
    } catch (error) {
      console.error('Error fetching medical notes by date:', error);
      return [];
    }
  }

  async getClinicalNotesByDate(date: string, maxNotes: number = 500): Promise<any[]> {
    try {
      console.log(`SikkaClient: Fetching clinical notes for date: ${date}, max notes: ${maxNotes}`);

      const requestKey = await this.getRequestKey();

      // Try the specific clinical notes endpoint first with pagination
      const clinicalUrl = `${API_BASE_V4}/medical_notes/Clinical%20Note`;
      const allClinicalNotes: any[] = [];
      let page = 1;
      const pageSize = 100;

      while (allClinicalNotes.length < maxNotes) {
        const clinicalParams = new URLSearchParams({
          date: date,
          limit: pageSize.toString(),
          page: page.toString()
        });

        console.log(`SikkaClient: Trying clinical notes endpoint page ${page}: ${clinicalUrl}?${clinicalParams}`);

        const clinicalResponse = await fetch(`${clinicalUrl}?${clinicalParams}`, {
          method: 'GET',
          headers: {
            'Request-Key': requestKey,
          },
        });

        if (clinicalResponse.ok) {
          const clinicalData = await clinicalResponse.json();
          const pageNotes = Array.isArray(clinicalData) ? clinicalData : clinicalData.items || [];

          console.log(`SikkaClient: Page ${page} returned ${pageNotes.length} clinical notes`);

          if (pageNotes.length === 0) {
            break; // No more notes
          }

          allClinicalNotes.push(...pageNotes);

          if (pageNotes.length < pageSize) {
            break; // Last page
          }

          page++;
        } else {
          console.log(`Clinical notes endpoint page ${page} returned status: ${clinicalResponse.status}`);
          break;
        }
      }

      if (allClinicalNotes.length > 0) {
        console.log(`SikkaClient: Found ${allClinicalNotes.length} clinical notes from specific endpoint across ${page} pages`);
        return allClinicalNotes.slice(0, maxNotes);
      }

      // Fallback: Get all medical notes and filter for clinical content with pagination
      console.log(`SikkaClient: Falling back to filtered medical notes for date: ${date}`);

      const allNotes = await this.getMedicalNotesByDateWithPagination(date, maxNotes);
      console.log(`SikkaClient: Got ${allNotes.length} total notes, filtering for clinical content`);

      // Focus ONLY on note types, remove all text filtering
      const clinicalNotes = allNotes
        .filter((note: any) => {
          // Include multiple clinical note types
          const isClinicalNoteType = note.type === "Clinical Note" || note.type === "PatNote" || note.type === "ProcNote";

          // Basic requirement: must have some text content
          const hasText = note.text && note.text.trim().length > 0;

          console.log(`Note type: "${note.type}", length: ${note.text?.length}, hasText: ${hasText}, text: "${note.text?.substring(0, 100)}..."`);

          return isClinicalNoteType && hasText;
        });

      console.log(`SikkaClient: Filtered to ${clinicalNotes.length} clinical notes from ${allNotes.length} total notes`);
      return clinicalNotes;

    } catch (error) {
      console.error('Error fetching clinical notes by date:', error);
      return [];
    }
  }

  async createMedicalNote(noteData: {
    patient_id: string;
    appointment_id?: string;
    note_text: string;
    note_type?: string;
    created_date: string;
  }): Promise<any> {
    try {
      console.log(`SikkaClient: Creating medical note for patient: ${noteData.patient_id}`);

      const requestKey = await this.getRequestKey();

      const response = await fetch(`${API_BASE_V4}/medical_notes`, {
        method: 'POST',
        headers: {
          'Request-Key': requestKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(noteData),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error creating medical note:', error);
      throw error;
    }
  }

  async getNoteTypes(): Promise<any[]> {
    try {
      console.log(`SikkaClient: Fetching note types`);

      const requestKey = await this.getRequestKey();

      const url = `${API_BASE_V2}/note_types`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log(`SikkaClient: Note types response:`, data);

      return data.data || data.items || data || [];

    } catch (error) {
      console.error('Error fetching note types:', error);
      return [];
    }
  }

  async getPatientClinicalNotes(patientId: string, maxNotes: number = 500): Promise<any[]> {
    try {
      console.log(`SikkaClient: Fetching clinical notes for patient: ${patientId}, max notes: ${maxNotes}`);

      const requestKey = await this.getRequestKey();

      // Use the correct clinical note endpoint format that works
      console.log(`SikkaClient: Using working Clinical note endpoint format`);
      const url = `${API_BASE_V4}/medical_notes/Clinical note`;
      const allNotes: any[] = [];
      let page = 1;
      const pageSize = 100;

      while (allNotes.length < maxNotes) {
        const params = new URLSearchParams({
          patient_id: patientId,
          limit: pageSize.toString(),
          page: page.toString()
        });

        console.log(`SikkaClient: Fetching clinical notes page ${page} for patient ${patientId}: ${url}?${params}`);

        const response = await fetch(`${url}?${params}`, {
          method: 'GET',
          headers: {
            'Request-Key': requestKey,
          },
        });

        if (!response.ok) {
          if (page === 1) {
            console.log(`Clinical notes endpoint returned status: ${response.status}`);
            // Fallback to legacy method if the new endpoint fails
            return this.getPatientClinicalNotesLegacy(patientId);
          } else {
            console.log(`No more clinical notes found for patient ${patientId} at page ${page}`);
            break;
          }
        }

        const data = await response.json();
        const pageNotes = Array.isArray(data) ? data : data.items || [];

        console.log(`SikkaClient: Page ${page} returned ${pageNotes.length} clinical notes`);

        if (pageNotes.length === 0) {
          break; // No more notes
        }

        allNotes.push(...pageNotes);

        if (pageNotes.length < pageSize) {
          break; // Last page
        }

        page++;
      }

      // Transform to consistent format and apply filtering
      const clinicalNotes = allNotes
        .map((note: any) => ({
          id: note.id || note.note_id || `note-${Date.now()}-${Math.random()}`,
          date: note.date || note.created_date || note.appointment_date,
          text: note.text || note.note_text || note.content || note.description,
          provider: note.provider || note.provider_name || note.doctor || 'Unknown Provider',
          type: note.type || 'Clinical Note',
          appointment_id: note.appointment_id || note.appt_id,
          tooth_number: note.tooth_number || '',
          surface: note.surface || '',
          rawData: note // Keep raw data for debugging
        }))
        .filter((note: any) => {
          // Must have text content
          const hasText = note.text && note.text.trim().length > 0;

          if (!hasText) {
            return false;
          }

          const noteText = note.text.toLowerCase();

          // Filter out patient communications and administrative messages
          const isPatientCommunication =
                 noteText.includes('welcome to suncoast dental center') ||
                 noteText.includes('please complete your new patient forms') ||
                 noteText.includes('please respond with "c" to confirm') ||
                 noteText.includes('confirmed the appointment') ||
                 noteText.includes('thank you for visiting suncoast dental center') ||
                 noteText.includes('please follow https://mdnt.io') ||
                 noteText.includes('created by engagement') ||
                 noteText.includes('chosen service:') ||
                 noteText.includes('primary insurance:') ||
                 noteText.includes('note from patient:') ||
                 noteText.includes('appointment confirmation') ||
                 noteText.includes('form completion') ||
                 noteText.includes('text message') ||
                 noteText.includes('sms') ||
                 noteText.includes('modento.io') ||
                 noteText.includes('fill in the forms online') ||
                 noteText.includes('we look forward to seeing you') ||
                 noteText.includes('arrive at least 15 minutes early') ||
                 noteText.includes('new patient appointments without paperwork') ||
                 noteText.includes('require confirmation response') ||
                 noteText.includes('unable to complete the forms') ||
                 noteText.includes('call or text us to let us know') ||
                 noteText.includes('new patient appts without forms') ||
                 noteText.includes('where your comfort and health are our priorities') ||
                 noteText.includes('we have just reserved your appointment') ||
                 noteText.includes('our mission is to make your experience') ||
                 // Filter out recall processing messages
                 noteText.includes('recall processed on laser postcard') ||
                 noteText.includes('recall processed') ||
                 // Filter out service modification messages
                 noteText.includes('service') && noteText.includes('was modified') ||
                 noteText.includes('provider was changed from') ||
                 // Filter out very short administrative notes
                 (note.text.trim().length < 10) ||
                 // Filter out single character responses like "C"
                 /^[a-z]$/i.test(note.text.trim()) ||
                 // Filter out URL-only messages
                 /^https?:\/\//.test(note.text.trim()) ||
                 // Filter out engagement/appointment booking messages
                 /created by engagement #\d+/i.test(noteText) ||
                 // Filter out form completion URLs
                 /patient\.modento\.io/.test(noteText) ||
                 // Filter out appointment confirmation patterns
                 /confirmed the appointment on \d{2}\/\d{2}\/\d{4}/.test(noteText) ||
                 // Filter out service modification patterns
                 /service \d+ was modified:/i.test(noteText) ||
                 // Filter out provider change patterns
                 /provider was changed from \([^)]*\) to \([^)]*\)/i.test(noteText);

          const isValidClinicalNote = !isPatientCommunication;

          console.log(`Patient Clinical Note: type="${note.type}", length=${note.text?.length}, isValid=${isValidClinicalNote}, text="${note.text?.substring(0, 100)}..."`);

          return isValidClinicalNote;
        })
        .sort((a: any, b: any) => {
          // Sort by date descending (newest first)
          const dateA = new Date(a.date || '1900-01-01').getTime();
          const dateB = new Date(b.date || '1900-01-01').getTime();
          return dateB - dateA;
        });

      console.log(`SikkaClient: Found ${clinicalNotes.length} clinical notes for patient ${patientId} (sorted newest first)`);
      return clinicalNotes.slice(0, maxNotes);
    } catch (error) {
      console.error('Error fetching clinical notes:', error);
      // Fallback to legacy method if there's an error
      return this.getPatientClinicalNotesLegacy(patientId);
    }
  }

  // Legacy method for backward compatibility - keeping the old multi-endpoint approach as fallback
  async getPatientClinicalNotesLegacy(patientId: string): Promise<any[]> {
    try {
      console.log(`SikkaClient: Fetching chart notes for patient: ${patientId} (legacy method)`);

      const requestKey = await this.getRequestKey();

      // First, get note types to understand what types of notes exist
      const noteTypes = await this.getNoteTypes();
      console.log(`Available note types:`, noteTypes);

      // Try chart notes specific endpoints based on Sikka API docs
      const chartEndpoints = [
        // Try the specific chart notes endpoints
        `${API_BASE_V4}/chart_notes?patient_id=${patientId}`,
        `${API_BASE_V2}/chart_notes?patient_id=${patientId}`,
        `${API_BASE_V4}/patients/${patientId}/chart_notes`,
        `${API_BASE_V2}/patients/${patientId}/chart_notes`,
        // Try patient notes (might be the correct endpoint)
        `${API_BASE_V4}/patient_notes?patient_id=${patientId}`,
        `${API_BASE_V2}/patient_notes?patient_id=${patientId}`,
        `${API_BASE_V4}/patients/${patientId}/patient_notes`,
        `${API_BASE_V2}/patients/${patientId}/patient_notes`,
        // Try notes endpoint with patient filter
        `${API_BASE_V4}/notes?patient_id=${patientId}`,
        `${API_BASE_V2}/notes?patient_id=${patientId}`,
        // Try clinical notes with patient filter
        `${API_BASE_V4}/clinical_notes?patient_id=${patientId}`,
        `${API_BASE_V2}/clinical_notes?patient_id=${patientId}`
      ];

      for (const url of chartEndpoints) {
        try {
          console.log(`Trying chart notes endpoint: ${url}`);

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Request-Key': requestKey,
            },
          });

          if (response.ok) {
            const data = await response.json();
            console.log(`SikkaClient: Chart notes response from ${url}:`, data);

            // Extract notes from the response
            let notes = [];
            if (Array.isArray(data)) {
              notes = data;
            } else if (data.data) {
              notes = data.data;
            } else if (data.items) {
              notes = data.items;
            } else if (data.chart_notes) {
              notes = data.chart_notes;
            } else if (data.notes) {
              notes = data.notes;
            }

            if (notes.length > 0) {
              // Format chart notes data
              const formattedNotes = notes.map((note: any) => ({
                id: note.id || note.note_id || note.chart_note_id || note.patient_note_id,
                date: note.date || note.created_date || note.note_date || note.entry_date,
                provider: note.provider || note.provider_name || note.doctor || note.created_by || 'Unknown Provider',
                text: note.notes || note.note_text || note.chart_note || note.description || note.content || note.text || '',
                type: note.type || note.note_type || 'Clinical Note',
                appointmentType: note.appointment_type || note.visit_type || '',
                procedures: note.procedures || note.procedure_codes || [],
                appointment_id: note.appointment_id || note.appt_id,
                tooth_number: note.tooth_number || '',
                surface: note.surface || '',
                rawData: note
              })).filter((note: any) => note.text && note.text.trim().length > 0);

              console.log(`SikkaClient: Found ${formattedNotes.length} chart notes from ${url}`);
              return formattedNotes;
            }
          } else {
            console.log(`Endpoint ${url} returned status: ${response.status}`);
          }
        } catch (endpointError) {
          console.log(`Endpoint ${url} failed:`, endpointError);
          continue;
        }
      }

      console.log('No chart notes found from any endpoint');
      return [];

    } catch (error) {
      console.error('Error fetching chart notes:', error);
      return [];
    }
  }

  async getPatientRecentVisitCount(patientId: string, months: number = 6): Promise<number> {
    try {
      console.log(`SikkaClient: Getting visit count for patient ${patientId} in last ${months} months`);

      const requestKey = await this.getRequestKey();

      // Calculate date range (last 6 months)
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);

      const startDateStr = startDate.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];

      // Get appointments for this patient in the date range
      const url = `${API_BASE_V2}/appointments`;
      const params = new URLSearchParams({
        patient_id: patientId,
        startdate: startDateStr,
        enddate: endDateStr,
        date_filter_on: 'appointment_date'
      });

      const response = await fetch(`${url}?${params}`, {
        method: 'GET',
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Count completed appointments
      let visitCount = 0;
      if (Array.isArray(data) && data.length > 0 && data[0].items) {
        visitCount = data[0].items.filter((item: any) =>
          item.status !== 'Cancelled' &&
          item.status !== 'No Show' &&
          item.status !== 'Scheduled'
        ).length;
      }

      console.log(`SikkaClient: Found ${visitCount} visits for patient ${patientId} in last ${months} months`);
      return visitCount;

    } catch (error) {
      console.error('Error getting patient visit count:', error);
      return 0; // Return 0 if we can't get the count
    }
  }

  async getPatientVisits(patientId: string): Promise<any[]> {
    try {
      console.log(`SikkaClient: Fetching visits for patient: ${patientId}`);

      const requestKey = await this.getRequestKey();

      // Get appointments for this patient (all time)
      const url = `${API_BASE_V2}/appointments`;
      const params = new URLSearchParams({
        patient_id: patientId,
        limit: '100' // Get up to 100 visits
      });

      const response = await fetch(`${url}?${params}`, {
        method: 'GET',
        headers: {
          'Request-Key': requestKey,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log(`SikkaClient: Patient visits response:`, data);

      // Extract visits from the response
      let visits = [];
      if (Array.isArray(data) && data.length > 0 && data[0].items) {
        visits = data[0].items;
      } else if (data.items) {
        visits = data.items;
      } else if (data.data) {
        visits = data.data;
      }

      // Format visit data and filter out cancelled/no-shows
      const formattedVisits = visits
        .filter((visit: any) =>
          visit.status !== 'Cancelled' &&
          visit.status !== 'No Show'
        )
        .map((visit: any, index: number) => {
          // Extract procedures from the visit data
          let procedures: string[] = [];
          if (visit.procedures && Array.isArray(visit.procedures)) {
            procedures = visit.procedures;
          } else if (visit.procedure_codes && Array.isArray(visit.procedure_codes)) {
            procedures = visit.procedure_codes;
          } else if (visit.services && Array.isArray(visit.services)) {
            procedures = visit.services.map((service: any) => service.code || service.procedure_code || service.name);
          } else if (visit.treatments && Array.isArray(visit.treatments)) {
            procedures = visit.treatments.map((treatment: any) => treatment.code || treatment.procedure_code || treatment.name);
          }

          // Clean up provider info - only include if it's meaningful
          let provider = null;
          if (visit.provider && typeof visit.provider === 'string' && visit.provider.trim() !== '' && visit.provider !== 'Unknown Provider') {
            provider = visit.provider;
          } else if (visit.provider?.name && visit.provider.name.trim() !== '' && visit.provider.name !== 'Unknown Provider') {
            provider = visit.provider.name;
          }

          return {
            id: visit.id || visit.appointment_id || `visit-${patientId}-${index}-${Date.now()}`,
            date: visit.date || visit.appointment_date,
            provider: provider, // Only set if meaningful, otherwise null
            appointmentType: typeof visit.type === 'string' ? visit.type : (visit.appointment_type || 'General'),
            status: typeof visit.status === 'string' ? visit.status : 'Completed',
            operatory: typeof visit.operatory === 'string' ? visit.operatory : (visit.operatory?.name || visit.operatory),
            procedures: procedures.filter((proc: any) => proc && proc.trim() !== ''), // Only include non-empty procedures
            // Include potential chart note fields
            notes: visit.notes || visit.chart_notes || visit.clinical_notes || visit.provider_notes || visit.treatment_notes,
            description: visit.description,
            // Include all raw data for debugging
            rawData: visit
          };
        })
        .sort((a: any, b: any) => {
          // Sort by date descending (most recent first)
          const dateA = new Date(a.date).getTime();
          const dateB = new Date(b.date).getTime();
          return dateB - dateA;
        });

      console.log(`SikkaClient: Found ${formattedVisits.length} visits for patient ${patientId}`);
      return formattedVisits;

    } catch (error) {
      console.error('Error fetching patient visits:', error);
      throw error;
    }
  }


}
